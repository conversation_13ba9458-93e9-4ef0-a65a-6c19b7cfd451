import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  Card<PERSON>ody,
  Card<PERSON><PERSON>er,
  Card<PERSON>ooter,
  Button,
  Notice,
  Dashicon,
  DropdownMenu,
  MenuGroup,
  MenuItem,
  ToggleControl
} from '@wordpress/components';
import { useState, useEffect, useContext } from '@wordpress/element';

// Composants
import ModuleCard from '../components/ModuleCard';
import StatCard from '../components/StatCard';
import Header from '../components/Header';
import ScoreGauge from '../components/ScoreGauge';
import TasksList from '../components/TasksList';
import TechnicalIssues from '../components/TechnicalIssues';
import TrafficTrends from '../components/TrafficTrends';
import OptimizationStats from '../components/OptimizationStats';

const Dashboard = ({ setCurrentPage }) => {
  // État pour les widgets configurables
  const [widgets, setWidgets] = useState({
    scoreGauge: true,
    tasksList: true,
    technicalIssues: true,
    trafficTrends: true,
    optimizationStats: true,
    quickActions: true
  });

  // État pour l'ordre des widgets
  const [widgetOrder, setWidgetOrder] = useState([
    'scoreGauge',
    'tasksList',
    'technicalIssues',
    'trafficTrends',
    'optimizationStats',
    'quickActions'
  ]);

  // Données pour les statistiques
  const stats = [
    {
      title: __('Score SEO global', 'boss-seo'),
      value: '87/100',
      icon: 'chart-area',
      color: 'boss-primary',
      trend: '+5'
    },
    {
      title: __('Pages optimisées', 'boss-seo'),
      value: '24/32',
      icon: 'yes-alt',
      color: 'boss-success',
      trend: '+3'
    },
    {
      title: __('Erreurs critiques', 'boss-seo'),
      value: '2',
      icon: 'warning',
      color: 'boss-error',
      trend: '-1'
    },
    {
      title: __('Mots-clés suivis', 'boss-seo'),
      value: '18',
      icon: 'tag',
      color: 'boss-warning',
      trend: '+2'
    }
  ];

  // Données pour les modules
  const modules = [
    {
      title: __('Boss Optimizer', 'boss-seo'),
      description: __('Optimisation IA de vos contenus et mots-clés', 'boss-seo'),
      icon: 'performance',
      color: 'boss-primary',
      actionId: 'optimizer'
    },
    {
      title: __('Analyse technique', 'boss-seo'),
      description: __('Audit technique complet de votre site', 'boss-seo'),
      icon: 'code-standards',
      color: 'boss-secondary',
      actionId: 'technical'
    },
    {
      title: __('Optimisation de contenu', 'boss-seo'),
      description: __('Améliorez vos contenus pour le SEO', 'boss-seo'),
      icon: 'edit',
      color: 'boss-success',
      actionId: 'content'
    },
    {
      title: __('Schémas structurés', 'boss-seo'),
      description: __('Ajoutez des données structurées à votre site', 'boss-seo'),
      icon: 'networking',
      color: 'boss-warning',
      actionId: 'schema'
    },
    {
      title: __('Intégrations analytics', 'boss-seo'),
      description: __('Connectez vos outils d\'analyse', 'boss-seo'),
      icon: 'chart-bar',
      color: 'boss-error',
      actionId: 'analytics'
    },
    {
      title: __('SEO local & e-commerce', 'boss-seo'),
      description: __('Optimisez votre présence locale et e-commerce', 'boss-seo'),
      icon: 'store',
      color: 'boss-dark',
      actionId: 'local'
    }
  ];

  // Données pour les tâches prioritaires
  const tasks = [
    {
      id: 'task1',
      title: __('Optimiser les balises title et meta description', 'boss-seo'),
      description: __('5 pages ont des balises title trop longues ou dupliquées', 'boss-seo'),
      priority: 'high',
      completed: false,
      actionLabel: __('Voir les pages', 'boss-seo'),
      onAction: () => console.log('Voir les pages')
    },
    {
      id: 'task2',
      title: __('Améliorer la vitesse de chargement', 'boss-seo'),
      description: __('Le temps de chargement moyen est de 3.5s (objectif: <2s)', 'boss-seo'),
      priority: 'high',
      completed: false,
      actionLabel: __('Optimiser', 'boss-seo'),
      onAction: () => console.log('Optimiser')
    },
    {
      id: 'task3',
      title: __('Corriger les liens brisés', 'boss-seo'),
      description: __('3 liens brisés détectés sur votre site', 'boss-seo'),
      priority: 'medium',
      completed: true
    },
    {
      id: 'task4',
      title: __('Ajouter des balises alt aux images', 'boss-seo'),
      description: __('12 images n\'ont pas de texte alternatif', 'boss-seo'),
      priority: 'medium',
      completed: false,
      actionLabel: __('Corriger', 'boss-seo'),
      onAction: () => console.log('Corriger')
    },
    {
      id: 'task5',
      title: __('Optimiser pour les appareils mobiles', 'boss-seo'),
      description: __('2 pages ne sont pas optimisées pour mobile', 'boss-seo'),
      priority: 'low',
      completed: false,
      actionLabel: __('Voir les pages', 'boss-seo'),
      onAction: () => console.log('Voir les pages')
    }
  ];

  // Données pour les problèmes techniques
  const technicalIssues = [
    {
      id: 'issue1',
      title: __('Temps de chargement trop long', 'boss-seo'),
      description: __('La page d\'accueil met 3.5s à charger', 'boss-seo'),
      category: __('Performance', 'boss-seo'),
      severity: 'critical'
    },
    {
      id: 'issue2',
      title: __('Contenu dupliqué', 'boss-seo'),
      description: __('2 pages ont un contenu similaire à plus de 80%', 'boss-seo'),
      category: __('Contenu', 'boss-seo'),
      severity: 'warning'
    },
    {
      id: 'issue3',
      title: __('Liens brisés', 'boss-seo'),
      description: __('3 liens pointent vers des pages inexistantes', 'boss-seo'),
      category: __('Liens', 'boss-seo'),
      severity: 'warning'
    },
    {
      id: 'issue4',
      title: __('Images sans attribut alt', 'boss-seo'),
      description: __('12 images n\'ont pas de texte alternatif', 'boss-seo'),
      category: __('Accessibilité', 'boss-seo'),
      severity: 'warning'
    },
    {
      id: 'issue5',
      title: __('Balises title dupliquées', 'boss-seo'),
      description: __('2 pages ont des balises title identiques', 'boss-seo'),
      category: __('Balises', 'boss-seo'),
      severity: 'critical'
    },
    {
      id: 'issue6',
      title: __('Sitemap non soumis', 'boss-seo'),
      description: __('Le sitemap n\'a pas été soumis à Google', 'boss-seo'),
      category: __('Indexation', 'boss-seo'),
      severity: 'info'
    },
    {
      id: 'issue7',
      title: __('Certificat SSL expire bientôt', 'boss-seo'),
      description: __('Le certificat SSL expire dans 30 jours', 'boss-seo'),
      category: __('Sécurité', 'boss-seo'),
      severity: 'info'
    }
  ];

  // Données pour les tendances de trafic
  const trafficData = {
    '7days': [
      { label: __('Lundi', 'boss-seo'), shortLabel: __('L', 'boss-seo'), value: 120 },
      { label: __('Mardi', 'boss-seo'), shortLabel: __('M', 'boss-seo'), value: 145 },
      { label: __('Mercredi', 'boss-seo'), shortLabel: __('M', 'boss-seo'), value: 132 },
      { label: __('Jeudi', 'boss-seo'), shortLabel: __('J', 'boss-seo'), value: 160 },
      { label: __('Vendredi', 'boss-seo'), shortLabel: __('V', 'boss-seo'), value: 180 },
      { label: __('Samedi', 'boss-seo'), shortLabel: __('S', 'boss-seo'), value: 110 },
      { label: __('Dimanche', 'boss-seo'), shortLabel: __('D', 'boss-seo'), value: 90 }
    ],
    '30days': [
      { label: __('Semaine 1', 'boss-seo'), shortLabel: __('S1', 'boss-seo'), value: 620 },
      { label: __('Semaine 2', 'boss-seo'), shortLabel: __('S2', 'boss-seo'), value: 720 },
      { label: __('Semaine 3', 'boss-seo'), shortLabel: __('S3', 'boss-seo'), value: 750 },
      { label: __('Semaine 4', 'boss-seo'), shortLabel: __('S4', 'boss-seo'), value: 800 }
    ],
    '3months': [
      { label: __('Janvier', 'boss-seo'), shortLabel: __('Jan', 'boss-seo'), value: 2500 },
      { label: __('Février', 'boss-seo'), shortLabel: __('Fév', 'boss-seo'), value: 2800 },
      { label: __('Mars', 'boss-seo'), shortLabel: __('Mar', 'boss-seo'), value: 3200 }
    ],
    '12months': [
      { label: __('Jan', 'boss-seo'), shortLabel: __('J', 'boss-seo'), value: 2500 },
      { label: __('Fév', 'boss-seo'), shortLabel: __('F', 'boss-seo'), value: 2800 },
      { label: __('Mar', 'boss-seo'), shortLabel: __('M', 'boss-seo'), value: 3200 },
      { label: __('Avr', 'boss-seo'), shortLabel: __('A', 'boss-seo'), value: 3100 },
      { label: __('Mai', 'boss-seo'), shortLabel: __('M', 'boss-seo'), value: 3400 },
      { label: __('Juin', 'boss-seo'), shortLabel: __('J', 'boss-seo'), value: 3600 },
      { label: __('Juil', 'boss-seo'), shortLabel: __('J', 'boss-seo'), value: 3300 },
      { label: __('Août', 'boss-seo'), shortLabel: __('A', 'boss-seo'), value: 3200 },
      { label: __('Sep', 'boss-seo'), shortLabel: __('S', 'boss-seo'), value: 3500 },
      { label: __('Oct', 'boss-seo'), shortLabel: __('O', 'boss-seo'), value: 3800 },
      { label: __('Nov', 'boss-seo'), shortLabel: __('N', 'boss-seo'), value: 4000 },
      { label: __('Déc', 'boss-seo'), shortLabel: __('D', 'boss-seo'), value: 4200 }
    ]
  };

  // Données pour les statistiques d'optimisation
  const optimizationStats = [
    {
      title: __('Pages optimisées', 'boss-seo'),
      value: 24,
      total: 32,
      percentage: 75,
      icon: 'yes-alt',
      iconColor: 'boss-text-boss-success',
      bgColor: 'boss-bg-green-100',
      progressColor: 'boss-bg-boss-success',
      description: __('75% des pages sont optimisées', 'boss-seo'),
      trend: 5
    },
    {
      title: __('Mots-clés en top 10', 'boss-seo'),
      value: 12,
      total: 18,
      percentage: 67,
      icon: 'tag',
      iconColor: 'boss-text-boss-primary',
      bgColor: 'boss-bg-indigo-100',
      progressColor: 'boss-bg-boss-primary',
      description: __('67% des mots-clés sont bien positionnés', 'boss-seo'),
      trend: 8
    },
    {
      title: __('Score de contenu', 'boss-seo'),
      value: 82,
      percentage: 82,
      icon: 'edit',
      iconColor: 'boss-text-boss-success',
      bgColor: 'boss-bg-green-100',
      progressColor: 'boss-bg-boss-success',
      description: __('Qualité du contenu', 'boss-seo'),
      trend: 3
    },
    {
      title: __('Vitesse de chargement', 'boss-seo'),
      value: '2.8s',
      icon: 'performance',
      iconColor: 'boss-text-boss-warning',
      bgColor: 'boss-bg-yellow-100',
      description: __('Temps de chargement moyen', 'boss-seo'),
      trend: -12
    }
  ];

  // Données pour les notifications
  const notifications = [
    {
      title: __('Analyse terminée', 'boss-seo'),
      message: __('L\'analyse de votre site est terminée. Consultez les résultats.', 'boss-seo'),
      time: __('Il y a 2 heures', 'boss-seo'),
      type: 'success',
      read: false,
      actionLabel: __('Voir les résultats', 'boss-seo')
    },
    {
      title: __('Nouvelle tâche prioritaire', 'boss-seo'),
      message: __('Optimisez vos balises title pour améliorer votre référencement.', 'boss-seo'),
      time: __('Il y a 1 jour', 'boss-seo'),
      type: 'warning',
      read: false,
      actionLabel: __('Voir la tâche', 'boss-seo')
    },
    {
      title: __('Problème critique détecté', 'boss-seo'),
      message: __('Temps de chargement trop long sur la page d\'accueil.', 'boss-seo'),
      time: __('Il y a 2 jours', 'boss-seo'),
      type: 'error',
      read: true,
      actionLabel: __('Corriger', 'boss-seo')
    }
  ];

  // Fonction pour afficher un widget en fonction de son type
  const renderWidget = (widgetType) => {
    switch (widgetType) {
      case 'scoreGauge':
        return <ScoreGauge score={87} maxScore={100} />;
      case 'tasksList':
        return <TasksList tasks={tasks} />;
      case 'technicalIssues':
        return <TechnicalIssues issues={technicalIssues} />;
      case 'trafficTrends':
        return <TrafficTrends data={trafficData} />;
      case 'optimizationStats':
        return <OptimizationStats stats={optimizationStats} />;
      case 'quickActions':
        return (
          <Card className="boss-card boss-h-full">
            <CardHeader>
              <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                {__('Actions rapides', 'boss-seo')}
              </h3>
            </CardHeader>
            <CardBody>
              <div className="boss-grid boss-grid-cols-2 boss-gap-3">
                <Button isPrimary className="boss-flex boss-items-center boss-justify-center">
                  <Dashicon icon="search" className="boss-mr-2" />
                  {__('Analyser', 'boss-seo')}
                </Button>
                <Button isSecondary className="boss-flex boss-items-center boss-justify-center">
                  <Dashicon icon="admin-tools" className="boss-mr-2" />
                  {__('Optimiser', 'boss-seo')}
                </Button>
                <Button isSecondary className="boss-flex boss-items-center boss-justify-center">
                  <Dashicon icon="clipboard" className="boss-mr-2" />
                  {__('Rapports', 'boss-seo')}
                </Button>
                <Button isSecondary className="boss-flex boss-items-center boss-justify-center">
                  <Dashicon icon="admin-settings" className="boss-mr-2" />
                  {__('Paramètres', 'boss-seo')}
                </Button>
              </div>
            </CardBody>
          </Card>
        );
      default:
        return null;
    }
  };

  // Fonction pour gérer l'activation/désactivation d'un widget
  const toggleWidget = (widgetKey) => {
    setWidgets({
      ...widgets,
      [widgetKey]: !widgets[widgetKey]
    });
  };

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      {/* Header avec logo, navigation et indicateurs */}
      <Header seoScore={87} notifications={notifications} />

      <div className="boss-p-6">
        {/* Barre d'outils du tableau de bord */}
        <div className="boss-flex boss-justify-between boss-items-center boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark">
            {__('Tableau de bord', 'boss-seo')}
          </h1>

          <div className="boss-flex boss-items-center boss-space-x-3">
            <Button
              isPrimary
              className="boss-flex boss-items-center"
            >
              <Dashicon icon="search" className="boss-mr-2" />
              {__('Lancer une analyse complète', 'boss-seo')}
            </Button>

            <DropdownMenu
              icon="admin-generic"
              label={__('Personnaliser le tableau de bord', 'boss-seo')}
              toggleProps={{
                className: 'boss-p-2 boss-rounded-full boss-text-boss-gray boss-hover:boss-text-boss-primary boss-hover:boss-bg-gray-100'
              }}
            >
              {() => (
                <MenuGroup label={__('Widgets visibles', 'boss-seo')}>
                  <div className="boss-p-3 boss-space-y-3">
                    <ToggleControl
                      label={__('Score SEO global', 'boss-seo')}
                      checked={widgets.scoreGauge}
                      onChange={() => toggleWidget('scoreGauge')}
                    />
                    <ToggleControl
                      label={__('Tâches prioritaires', 'boss-seo')}
                      checked={widgets.tasksList}
                      onChange={() => toggleWidget('tasksList')}
                    />
                    <ToggleControl
                      label={__('Problèmes techniques', 'boss-seo')}
                      checked={widgets.technicalIssues}
                      onChange={() => toggleWidget('technicalIssues')}
                    />
                    <ToggleControl
                      label={__('Tendances de trafic', 'boss-seo')}
                      checked={widgets.trafficTrends}
                      onChange={() => toggleWidget('trafficTrends')}
                    />
                    <ToggleControl
                      label={__('Statistiques d\'optimisation', 'boss-seo')}
                      checked={widgets.optimizationStats}
                      onChange={() => toggleWidget('optimizationStats')}
                    />
                    <ToggleControl
                      label={__('Actions rapides', 'boss-seo')}
                      checked={widgets.quickActions}
                      onChange={() => toggleWidget('quickActions')}
                    />
                  </div>
                </MenuGroup>
              )}
            </DropdownMenu>
          </div>
        </div>

        {/* Notifications */}
        <div className="boss-mb-6">
          <Notice status="warning" isDismissible={false}>
            {__('3 tâches d\'optimisation IA sont en attente de validation.', 'boss-seo')}
          </Notice>
        </div>

        {/* Vue d'ensemble - Indicateurs clés */}
        <div className="boss-mb-8">
          <h2 className="boss-text-xl boss-font-semibold boss-mb-4">
            {__('Vue d\'ensemble', 'boss-seo')}
          </h2>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-4 boss-gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="boss-transition-all boss-duration-300 hover:boss-translate-y-[-5px]">
                <StatCard
                  title={stat.title}
                  value={stat.value}
                  icon={stat.icon}
                  color={stat.color}
                  trend={stat.trend}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Grille de widgets configurables */}
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-6 boss-mb-8">
          {widgetOrder
            .filter(widgetType => widgets[widgetType])
            .map(widgetType => (
              <div key={widgetType} className="boss-h-full">
                {renderWidget(widgetType)}
              </div>
            ))}
        </div>

        {/* Modules principaux */}
        <div>
          <h2 className="boss-text-xl boss-font-semibold boss-mb-4">
            {__('Modules', 'boss-seo')}
          </h2>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-6">
            {modules.map((module, index) => (
              <div key={index} className="boss-transition-all boss-duration-300 hover:boss-translate-y-[-5px]">
                <ModuleCard
                  title={module.title}
                  description={module.description}
                  icon={module.icon}
                  color={module.color}
                  actionId={module.actionId}
                  onNavigate={setCurrentPage}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
