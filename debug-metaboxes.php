<?php
/**
 * Script de diagnostic pour les metaboxes Boss SEO
 * À placer dans le dossier du plugin Boss SEO
 *
 * Usage: wp-admin/admin.php?page=debug-metaboxes
 */

// Sécurité WordPress
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Vérifier les permissions
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'Accès refusé' );
}

echo '<div class="wrap">';
echo '<h1>🔍 Diagnostic Metaboxes Boss SEO</h1>';

echo '<div class="notice notice-info"><p><strong>ℹ️ Ce diagnostic va analyser l\'état des metaboxes Boss SEO</strong></p></div>';

// Section 1: Hooks enregistrés
echo '<h2>📋 1. Hooks WordPress enregistrés</h2>';

global $wp_filter;

$hooks_to_check = array(
    'add_meta_boxes',
    'save_post',
    'admin_enqueue_scripts'
);

echo '<table class="wp-list-table widefat fixed striped">';
echo '<thead><tr><th>Hook</th><th>Callbacks enregistrés</th><th>Statut</th></tr></thead>';
echo '<tbody>';

foreach ( $hooks_to_check as $hook ) {
    echo '<tr>';
    echo '<td><strong>' . esc_html( $hook ) . '</strong></td>';

    $callbacks = array();
    $status = '❌ Aucun callback';

    if ( isset( $wp_filter[ $hook ] ) ) {
        foreach ( $wp_filter[ $hook ]->callbacks as $priority => $priority_callbacks ) {
            foreach ( $priority_callbacks as $callback ) {
                if ( is_array( $callback['function'] ) ) {
                    if ( is_object( $callback['function'][0] ) ) {
                        $class_name = get_class( $callback['function'][0] );
                        $method_name = $callback['function'][1];
                        $callbacks[] = $class_name . '::' . $method_name . ' (priorité: ' . $priority . ')';
                    } else {
                        $callbacks[] = $callback['function'][0] . '::' . $callback['function'][1] . ' (priorité: ' . $priority . ')';
                    }
                } else {
                    $callbacks[] = $callback['function'] . ' (priorité: ' . $priority . ')';
                }
            }
        }

        if ( ! empty( $callbacks ) ) {
            $boss_seo_callbacks = array_filter( $callbacks, function( $callback ) {
                return strpos( $callback, 'Boss' ) !== false || strpos( $callback, 'boss' ) !== false;
            } );

            if ( ! empty( $boss_seo_callbacks ) ) {
                $status = '✅ ' . count( $boss_seo_callbacks ) . ' callback(s) Boss SEO';
            } else {
                $status = '⚠️ ' . count( $callbacks ) . ' callback(s) (aucun Boss SEO)';
            }
        }
    }

    echo '<td>';
    if ( ! empty( $callbacks ) ) {
        echo '<details>';
        echo '<summary>' . count( $callbacks ) . ' callback(s) - Cliquer pour voir</summary>';
        echo '<ul style="margin: 5px 0;">';
        foreach ( $callbacks as $callback ) {
            $is_boss_seo = strpos( $callback, 'Boss' ) !== false || strpos( $callback, 'boss' ) !== false;
            echo '<li style="color: ' . ( $is_boss_seo ? 'green' : 'gray' ) . ';">' . esc_html( $callback ) . '</li>';
        }
        echo '</ul>';
        echo '</details>';
    } else {
        echo '<em>Aucun callback</em>';
    }
    echo '</td>';

    echo '<td>' . $status . '</td>';
    echo '</tr>';
}

echo '</tbody></table>';

// Section 2: Metaboxes enregistrées
echo '<h2>📦 2. Metaboxes enregistrées</h2>';

global $wp_meta_boxes;

// Récupérer tous les types de posts publics + types courants
$public_post_types = get_post_types( array( 'public' => true ), 'names' );
$additional_post_types = array( 'product', 'portfolio', 'testimonial', 'service', 'event' );
$all_post_types = array_merge( array( 'post', 'page' ), $public_post_types, $additional_post_types );
$all_post_types = array_unique( $all_post_types );
$excluded_types = array( 'attachment', 'revision', 'nav_menu_item', 'custom_css', 'customize_changeset' );
$post_types = array_diff( $all_post_types, $excluded_types );

foreach ( $post_types as $post_type ) {
    echo '<h3>Type de post: ' . esc_html( $post_type ) . '</h3>';

    if ( isset( $wp_meta_boxes[ $post_type ] ) ) {
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>ID</th><th>Titre</th><th>Contexte</th><th>Priorité</th><th>Callback</th></tr></thead>';
        echo '<tbody>';

        foreach ( $wp_meta_boxes[ $post_type ] as $context => $priorities ) {
            foreach ( $priorities as $priority => $metaboxes ) {
                foreach ( $metaboxes as $metabox_id => $metabox ) {
                    $is_boss_seo = strpos( $metabox_id, 'boss' ) !== false || strpos( $metabox['title'], 'Boss' ) !== false;

                    echo '<tr style="' . ( $is_boss_seo ? 'background-color: #e7f3ff;' : '' ) . '">';
                    echo '<td><code>' . esc_html( $metabox_id ) . '</code></td>';
                    echo '<td><strong>' . esc_html( $metabox['title'] ) . '</strong></td>';
                    echo '<td>' . esc_html( $context ) . '</td>';
                    echo '<td>' . esc_html( $priority ) . '</td>';

                    $callback_info = 'N/A';
                    if ( is_array( $metabox['callback'] ) ) {
                        if ( is_object( $metabox['callback'][0] ) ) {
                            $callback_info = get_class( $metabox['callback'][0] ) . '::' . $metabox['callback'][1];
                        } else {
                            $callback_info = $metabox['callback'][0] . '::' . $metabox['callback'][1];
                        }
                    } elseif ( is_string( $metabox['callback'] ) ) {
                        $callback_info = $metabox['callback'];
                    }

                    echo '<td><code>' . esc_html( $callback_info ) . '</code></td>';
                    echo '</tr>';
                }
            }
        }

        echo '</tbody></table>';
    } else {
        echo '<p><em>Aucune metabox enregistrée pour ce type de post.</em></p>';
    }
}

// Section 3: Classes chargées
echo '<h2>🏗️ 3. Classes Boss SEO chargées</h2>';

$boss_classes = array(
    'Boss_Optimizer_Metabox',
    'Boss_Optimizer_Metabox_Secure',
    'Boss_Optimizer_Metabox_Tabbed',
    'Boss_Optimizer',
    'Boss_SEO_Optimizer',
    'Boss_SEO_Analyzer'
);

echo '<table class="wp-list-table widefat fixed striped">';
echo '<thead><tr><th>Classe</th><th>Statut</th><th>Fichier</th></tr></thead>';
echo '<tbody>';

foreach ( $boss_classes as $class ) {
    echo '<tr>';
    echo '<td><code>' . esc_html( $class ) . '</code></td>';

    if ( class_exists( $class ) ) {
        echo '<td><span style="color: green;">✅ Chargée</span></td>';

        $reflection = new ReflectionClass( $class );
        $filename = $reflection->getFileName();
        echo '<td><small>' . esc_html( str_replace( ABSPATH, '', $filename ) ) . '</small></td>';
    } else {
        echo '<td><span style="color: red;">❌ Non chargée</span></td>';
        echo '<td><em>N/A</em></td>';
    }

    echo '</tr>';
}

echo '</tbody></table>';

// Section 4: Configuration actuelle
echo '<h2>⚙️ 4. Configuration actuelle</h2>';

$options = array(
    'boss_seo_use_tabbed_metabox' => get_option( 'boss_seo_use_tabbed_metabox', 'non défini' ),
    'boss_seo_use_secure_metabox' => get_option( 'boss_seo_use_secure_metabox', 'non défini' ),
);

echo '<table class="wp-list-table widefat fixed striped">';
echo '<thead><tr><th>Option</th><th>Valeur</th></tr></thead>';
echo '<tbody>';

foreach ( $options as $option => $value ) {
    echo '<tr>';
    echo '<td><code>' . esc_html( $option ) . '</code></td>';
    echo '<td>';
    if ( $value === 'non défini' ) {
        echo '<em style="color: gray;">Non défini</em>';
    } else {
        echo '<strong>' . ( $value ? 'true' : 'false' ) . '</strong>';
    }
    echo '</td>';
    echo '</tr>';
}

echo '</tbody></table>';

// Section 5: Actions recommandées
echo '<h2>💡 5. Actions recommandées</h2>';

$boss_seo_metaboxes = 0;
if ( isset( $wp_meta_boxes['post'] ) ) {
    foreach ( $wp_meta_boxes['post'] as $context => $priorities ) {
        foreach ( $priorities as $priority => $metaboxes ) {
            foreach ( $metaboxes as $metabox_id => $metabox ) {
                if ( strpos( $metabox_id, 'boss' ) !== false || strpos( $metabox['title'], 'Boss' ) !== false ) {
                    $boss_seo_metaboxes++;
                }
            }
        }
    }
}

if ( $boss_seo_metaboxes === 0 ) {
    echo '<div class="notice notice-error">';
    echo '<p><strong>❌ Aucune metabox Boss SEO détectée</strong></p>';
    echo '<p>Actions à effectuer :</p>';
    echo '<ol>';
    echo '<li>Vérifier que le plugin Boss SEO est activé</li>';
    echo '<li>Vérifier que vous êtes sur une page d\'édition de post/page</li>';
    echo '<li>Désactiver et réactiver le plugin</li>';
    echo '</ol>';
    echo '</div>';
} elseif ( $boss_seo_metaboxes > 1 ) {
    echo '<div class="notice notice-warning">';
    echo '<p><strong>⚠️ Plusieurs metaboxes Boss SEO détectées (' . $boss_seo_metaboxes . ')</strong></p>';
    echo '<p>Il peut y avoir des conflits. Actions recommandées :</p>';
    echo '<ol>';
    echo '<li>Désactiver le plugin Boss SEO</li>';
    echo '<li>Supprimer les options de configuration</li>';
    echo '<li>Réactiver le plugin</li>';
    echo '</ol>';
    echo '</div>';
} else {
    echo '<div class="notice notice-success">';
    echo '<p><strong>✅ Configuration normale détectée</strong></p>';
    echo '<p>Une seule metabox Boss SEO est enregistrée, ce qui est correct.</p>';
    echo '</div>';
}

// Actions
echo '<h2>🔧 6. Actions</h2>';
echo '<p>';
echo '<a href="' . admin_url( 'post-new.php' ) . '" class="button button-primary">📝 Créer un nouveau post</a> ';
echo '<a href="' . admin_url( 'edit.php' ) . '" class="button">📋 Liste des posts</a> ';
echo '<a href="' . admin_url( 'admin.php?page=debug-metaboxes' ) . '" class="button">🔄 Actualiser le diagnostic</a>';
echo '</p>';

echo '</div>';

// CSS pour améliorer l'affichage
echo '<style>
.wp-list-table td { vertical-align: top; }
.wp-list-table code { background: #f1f1f1; padding: 2px 4px; border-radius: 2px; }
details { margin: 5px 0; }
details summary { cursor: pointer; font-weight: bold; }
details ul { margin: 5px 0 5px 20px; }
h2 { border-bottom: 2px solid #0073aa; padding-bottom: 5px; }
h3 { color: #0073aa; margin-top: 20px; }
</style>';
?>
