<?php
/**
 * Classe pour l'optimisation des médias.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 */

/**
 * Classe pour l'optimisation des médias.
 *
 * Cette classe gère l'optimisation des médias pour le plugin Boss SEO.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Media_Optimization {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le nom de l'option pour les paramètres d'optimisation des médias.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $settings_option    Le nom de l'option pour les paramètres d'optimisation des médias.
     */
    protected $settings_option;

    /**
     * Le nom de la table des médias optimisés.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $table_name    Le nom de la table des médias optimisés.
     */
    protected $table_name;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        global $wpdb;

        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings_option = $plugin_name . '_media_settings';
        $this->table_name = $wpdb->prefix . 'boss_seo_optimized_media';
    }

    /**
     * Enregistre les hooks WordPress nécessaires.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Hook pour ajouter des champs personnalisés aux médias
        add_filter( 'attachment_fields_to_edit', array( $this, 'add_media_fields' ), 10, 2 );
        add_filter( 'attachment_fields_to_save', array( $this, 'save_media_fields' ), 10, 2 );

        // Hook pour optimiser les images lors de l'upload
        add_filter( 'wp_handle_upload', array( $this, 'optimize_on_upload' ), 10, 2 );

        // Hook pour ajouter le lazy loading aux images
        add_filter( 'the_content', array( $this, 'add_lazy_loading' ), 99 );

        // Hook pour ajouter des attributs alt aux images
        add_filter( 'wp_get_attachment_image_attributes', array( $this, 'enhance_image_attributes' ), 10, 3 );
    }

    /**
     * Crée les tables nécessaires lors de l'activation du plugin.
     *
     * @since    1.1.0
     */
    public function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Table des médias optimisés
        $sql = "CREATE TABLE $this->table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            attachment_id bigint(20) NOT NULL,
            original_size bigint(20) NOT NULL,
            optimized_size bigint(20) NOT NULL,
            original_url varchar(255) NOT NULL,
            optimized_url varchar(255) NOT NULL,
            webp_url varchar(255),
            optimization_date datetime DEFAULT CURRENT_TIMESTAMP,
            optimization_level varchar(20),
            status varchar(20) NOT NULL,
            PRIMARY KEY  (id),
            KEY attachment_id (attachment_id)
        ) $charset_collate;";

        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.1.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/media',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_media' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/media/list',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_media_list' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/media/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_media_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/media/optimize',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'optimize_image' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/media/optimize-bulk',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'optimize_images' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/media/generate-alt',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'generate_alt_text' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/media/generate-alt-bulk',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'generate_alt_texts_bulk' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/media/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/media/analyze',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'analyze_media_issues' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.1.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Ajoute des champs personnalisés aux médias.
     *
     * @since    1.1.0
     * @param    array     $form_fields    Les champs du formulaire.
     * @param    WP_Post   $post           Le post.
     * @return   array                     Les champs du formulaire modifiés.
     */
    public function add_media_fields( $form_fields, $post ) {
        // Ajouter un champ pour l'optimisation
        $form_fields['boss_seo_optimize'] = array(
            'label' => __( 'Optimisation Boss SEO', 'boss-seo' ),
            'input' => 'html',
            'html' => '<button type="button" class="button button-secondary" data-id="' . $post->ID . '">' . __( 'Optimiser cette image', 'boss-seo' ) . '</button>',
            'helps' => __( 'Optimise cette image pour améliorer les performances du site.', 'boss-seo' ),
        );

        // Ajouter un champ pour la génération de texte alternatif
        $form_fields['boss_seo_generate_alt'] = array(
            'label' => __( 'Texte alternatif IA', 'boss-seo' ),
            'input' => 'html',
            'html' => '<button type="button" class="button button-secondary" data-id="' . $post->ID . '">' . __( 'Générer un texte alternatif', 'boss-seo' ) . '</button>',
            'helps' => __( 'Génère automatiquement un texte alternatif pour cette image à l\'aide de l\'IA.', 'boss-seo' ),
        );

        return $form_fields;
    }

    /**
     * Enregistre les champs personnalisés des médias.
     *
     * @since    1.1.0
     * @param    array     $post           Le post.
     * @param    array     $attachment     Les données de l'attachement.
     * @return   array                     Le post modifié.
     */
    public function save_media_fields( $post, $attachment ) {
        // Rien à faire ici pour le moment, car les boutons sont gérés via JavaScript
        return $post;
    }

    /**
     * Optimise les images lors de l'upload.
     *
     * @since    1.1.0
     * @param    array     $upload         Les données de l'upload.
     * @param    string    $context        Le contexte de l'upload.
     * @return   array                     Les données de l'upload modifiées.
     */
    public function optimize_on_upload( $upload, $context ) {
        // Récupérer les paramètres d'optimisation
        $settings = get_option( $this->settings_option, array() );

        // Vérifier si l'optimisation automatique est activée
        if ( ! isset( $settings['compression']['enabled'] ) || ! $settings['compression']['enabled'] ) {
            return $upload;
        }

        // Vérifier si le fichier est une image
        $file = $upload['file'];
        $mime_type = $upload['type'];

        if ( ! in_array( $mime_type, array( 'image/jpeg', 'image/png', 'image/gif', 'image/webp' ) ) ) {
            return $upload;
        }

        // Optimiser l'image
        $this->optimize_image_file( $file, $settings['compression'] );

        return $upload;
    }

    /**
     * Ajoute le lazy loading aux images dans le contenu.
     *
     * @since    1.1.0
     * @param    string    $content    Le contenu.
     * @return   string                Le contenu modifié.
     */
    public function add_lazy_loading( $content ) {
        // Récupérer les paramètres d'optimisation
        $settings = get_option( $this->settings_option, array() );

        // Vérifier si le lazy loading est activé
        if ( ! isset( $settings['lazyLoading']['enabled'] ) || ! $settings['lazyLoading']['enabled'] ) {
            return $content;
        }

        // Ajouter l'attribut loading="lazy" aux images
        if ( isset( $settings['lazyLoading']['useNative'] ) && $settings['lazyLoading']['useNative'] ) {
            // Utiliser le lazy loading natif
            $content = preg_replace( '/<img(.*?)>/i', '<img$1 loading="lazy">', $content );
        } else {
            // Utiliser une solution personnalisée de lazy loading
            $threshold = isset( $settings['lazyLoading']['threshold'] ) ? (int) $settings['lazyLoading']['threshold'] : 200;

            // Ajouter les attributs data-src et class pour le lazy loading
            $content = preg_replace_callback( '/<img(.*?)src=[\'"](.*?)[\'"](.*?)>/i', function( $matches ) use ( $threshold ) {
                $img_attrs = $matches[1];
                $src = $matches[2];
                $other_attrs = $matches[3];

                // Ajouter la classe pour le lazy loading
                if ( strpos( $img_attrs . $other_attrs, 'class=' ) !== false ) {
                    $img_attrs = preg_replace( '/class=[\'"](.*?)[\'"]/i', 'class="$1 boss-lazy-load"', $img_attrs . $other_attrs );
                    $other_attrs = '';
                } else {
                    $img_attrs .= ' class="boss-lazy-load"';
                }

                return '<img' . $img_attrs . 'data-src="' . $src . '" src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 1 1\'%3E%3C/svg%3E"' . $other_attrs . '>';
            }, $content );

            // Ajouter le script pour le lazy loading
            $script = '<script>
                document.addEventListener("DOMContentLoaded", function() {
                    var lazyImages = [].slice.call(document.querySelectorAll("img.boss-lazy-load"));

                    if ("IntersectionObserver" in window) {
                        let lazyImageObserver = new IntersectionObserver(function(entries, observer) {
                            entries.forEach(function(entry) {
                                if (entry.isIntersecting) {
                                    let lazyImage = entry.target;
                                    lazyImage.src = lazyImage.dataset.src;
                                    lazyImage.classList.remove("boss-lazy-load");
                                    lazyImageObserver.unobserve(lazyImage);
                                }
                            });
                        }, {
                            rootMargin: "0px 0px ' . $threshold . 'px 0px"
                        });

                        lazyImages.forEach(function(lazyImage) {
                            lazyImageObserver.observe(lazyImage);
                        });
                    } else {
                        // Fallback pour les navigateurs qui ne supportent pas IntersectionObserver
                        let active = false;

                        const lazyLoad = function() {
                            if (active === false) {
                                active = true;

                                setTimeout(function() {
                                    lazyImages.forEach(function(lazyImage) {
                                        if ((lazyImage.getBoundingClientRect().top <= window.innerHeight + ' . $threshold . ' && lazyImage.getBoundingClientRect().bottom >= 0) && getComputedStyle(lazyImage).display !== "none") {
                                            lazyImage.src = lazyImage.dataset.src;
                                            lazyImage.classList.remove("boss-lazy-load");

                                            lazyImages = lazyImages.filter(function(image) {
                                                return image !== lazyImage;
                                            });

                                            if (lazyImages.length === 0) {
                                                document.removeEventListener("scroll", lazyLoad);
                                                window.removeEventListener("resize", lazyLoad);
                                                window.removeEventListener("orientationchange", lazyLoad);
                                            }
                                        }
                                    });

                                    active = false;
                                }, 200);
                            }
                        };

                        document.addEventListener("scroll", lazyLoad);
                        window.addEventListener("resize", lazyLoad);
                        window.addEventListener("orientationchange", lazyLoad);
                        lazyLoad();
                    }
                });
            </script>';

            $content .= $script;
        }

        return $content;
    }

    /**
     * Améliore les attributs des images.
     *
     * @since    1.1.0
     * @param    array     $attr       Les attributs de l'image.
     * @param    WP_Post   $attachment L'attachement.
     * @param    string    $size       La taille de l'image.
     * @return   array                 Les attributs de l'image modifiés.
     */
    public function enhance_image_attributes( $attr, $attachment, $size ) {
        // Récupérer les paramètres d'optimisation
        $settings = get_option( $this->settings_option, array() );

        // Ajouter le lazy loading si activé
        if ( isset( $settings['lazyLoading']['enabled'] ) && $settings['lazyLoading']['enabled'] && isset( $settings['lazyLoading']['useNative'] ) && $settings['lazyLoading']['useNative'] ) {
            $attr['loading'] = 'lazy';
        }

        // Améliorer l'attribut alt si vide
        if ( empty( $attr['alt'] ) && isset( $settings['altText']['autoGenerate'] ) && $settings['altText']['autoGenerate'] ) {
            // Récupérer le titre de l'image
            $title = get_the_title( $attachment->ID );

            // Utiliser le titre comme texte alternatif par défaut
            $attr['alt'] = $title;

            // Enregistrer le texte alternatif
            update_post_meta( $attachment->ID, '_wp_attachment_image_alt', $title );
        }

        return $attr;
    }

    /**
     * Récupère la liste des médias.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_media( $request ) {
        global $wpdb;

        // Paramètres de pagination
        $page = isset( $request['page'] ) ? intval( $request['page'] ) : 1;
        $per_page = isset( $request['per_page'] ) ? intval( $request['per_page'] ) : 20;
        $offset = ( $page - 1 ) * $per_page;

        // Paramètres de filtrage
        $search = isset( $request['search'] ) ? sanitize_text_field( $request['search'] ) : '';
        $type = isset( $request['type'] ) ? sanitize_text_field( $request['type'] ) : '';
        $status = isset( $request['status'] ) ? sanitize_text_field( $request['status'] ) : '';

        // Requête pour récupérer les médias
        $args = array(
            'post_type' => 'attachment',
            'post_status' => 'inherit',
            'posts_per_page' => $per_page,
            'paged' => $page,
        );

        // Ajouter les filtres
        if ( ! empty( $search ) ) {
            $args['s'] = $search;
        }

        if ( ! empty( $type ) ) {
            $args['post_mime_type'] = $type;
        }

        // Exécuter la requête
        $query = new WP_Query( $args );
        $attachments = $query->posts;

        // Préparer les données
        $media = array();
        foreach ( $attachments as $attachment ) {
            $attachment_id = $attachment->ID;
            $file = get_attached_file( $attachment_id );
            $file_size = file_exists( $file ) ? filesize( $file ) : 0;
            $mime_type = get_post_mime_type( $attachment_id );
            $url = wp_get_attachment_url( $attachment_id );
            $metadata = wp_get_attachment_metadata( $attachment_id );
            $alt_text = get_post_meta( $attachment_id, '_wp_attachment_image_alt', true );

            // Récupérer les informations d'optimisation
            $optimization_data = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT * FROM $this->table_name WHERE attachment_id = %d",
                    $attachment_id
                )
            );

            // Déterminer le statut d'optimisation
            $optimization_status = 'unoptimized';
            $optimized_size = $file_size;
            $original_size = $file_size;
            $webp_url = '';

            if ( $optimization_data ) {
                $optimization_status = $optimization_data->status;
                $optimized_size = $optimization_data->optimized_size;
                $original_size = $optimization_data->original_size;
                $webp_url = $optimization_data->webp_url;
            }

            // Filtrer par statut d'optimisation si nécessaire
            if ( ! empty( $status ) && $status !== $optimization_status ) {
                continue;
            }

            // Ajouter les données du média
            $media[] = array(
                'id' => $attachment_id,
                'title' => $attachment->post_title,
                'filename' => basename( $file ),
                'url' => $url,
                'webp_url' => $webp_url,
                'type' => $mime_type,
                'size' => $optimized_size,
                'original_size' => $original_size,
                'width' => isset( $metadata['width'] ) ? $metadata['width'] : 0,
                'height' => isset( $metadata['height'] ) ? $metadata['height'] : 0,
                'alt' => $alt_text,
                'status' => $optimization_status,
                'date' => $attachment->post_date,
            );
        }

        // Préparer la réponse
        $data = array(
            'media' => $media,
            'total' => $query->found_posts,
            'pages' => ceil( $query->found_posts / $per_page ),
            'page' => $page,
            'per_page' => $per_page,
        );

        return rest_ensure_response( $data );
    }

    /**
     * Récupère les statistiques des médias.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_media_stats( $request ) {
        global $wpdb;

        // Récupérer le nombre total de médias
        $total_media = wp_count_posts( 'attachment' )->inherit;

        // Récupérer le nombre de médias optimisés
        $optimized_media = $wpdb->get_var(
            "SELECT COUNT(*) FROM $this->table_name WHERE status = 'optimized'"
        );

        // Récupérer l'espace économisé
        $space_saved = $wpdb->get_var(
            "SELECT SUM(original_size - optimized_size) FROM $this->table_name WHERE status = 'optimized'"
        );

        // Récupérer les problèmes de médias
        $issues = array(
            'missing_alt' => $this->count_media_without_alt(),
            'oversized' => $this->count_oversized_media(),
            'uncompressed' => $total_media - $optimized_media,
        );

        // Préparer la réponse
        $data = array(
            'totalMedia' => (int) $total_media,
            'optimizedMedia' => (int) $optimized_media,
            'unoptimizedMedia' => (int) $total_media - (int) $optimized_media,
            'spaceSaved' => (float) $space_saved / ( 1024 * 1024 ), // En MB
            'issues' => $issues,
        );

        return rest_ensure_response( $data );
    }

    /**
     * Optimise une image.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function optimize_image( $request ) {
        global $wpdb;

        $attachment_id = isset( $request['id'] ) ? intval( $request['id'] ) : 0;
        $options = isset( $request['options'] ) ? $request['options'] : array();

        // Vérifier si l'image existe
        $attachment = get_post( $attachment_id );
        if ( ! $attachment || $attachment->post_type !== 'attachment' ) {
            return new WP_Error(
                'boss_seo_invalid_attachment',
                __( 'Image invalide.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        // Récupérer le fichier
        $file = get_attached_file( $attachment_id );
        if ( ! file_exists( $file ) ) {
            return new WP_Error(
                'boss_seo_file_not_found',
                __( 'Fichier non trouvé.', 'boss-seo' ),
                array( 'status' => 404 )
            );
        }

        // Récupérer les paramètres d'optimisation
        $settings = get_option( $this->settings_option, array() );
        $compression = isset( $settings['compression'] ) ? $settings['compression'] : array();

        // Fusionner avec les options spécifiques
        if ( ! empty( $options ) ) {
            $compression = array_merge( $compression, $options );
        }

        // Récupérer la taille originale
        $original_size = filesize( $file );
        $original_url = wp_get_attachment_url( $attachment_id );

        // Optimiser l'image
        $result = $this->optimize_image_file( $file, $compression );

        if ( ! $result ) {
            return new WP_Error(
                'boss_seo_optimization_failed',
                __( 'Échec de l\'optimisation de l\'image.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }

        // Récupérer la nouvelle taille
        $optimized_size = filesize( $file );
        $optimized_url = wp_get_attachment_url( $attachment_id );

        // Récupérer l'URL WebP si elle existe
        $webp_url = '';
        $webp_file = $file . '.webp';
        if ( file_exists( $webp_file ) ) {
            $webp_url = $original_url . '.webp';
        }

        // Enregistrer les données d'optimisation
        $existing = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM $this->table_name WHERE attachment_id = %d",
                $attachment_id
            )
        );

        if ( $existing ) {
            // Mettre à jour les données existantes
            $wpdb->update(
                $this->table_name,
                array(
                    'original_size' => $original_size,
                    'optimized_size' => $optimized_size,
                    'original_url' => $original_url,
                    'optimized_url' => $optimized_url,
                    'webp_url' => $webp_url,
                    'optimization_date' => current_time( 'mysql' ),
                    'optimization_level' => isset( $compression['level'] ) ? $compression['level'] : 'medium',
                    'status' => 'optimized',
                ),
                array( 'attachment_id' => $attachment_id ),
                array( '%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s' ),
                array( '%d' )
            );
        } else {
            // Insérer de nouvelles données
            $wpdb->insert(
                $this->table_name,
                array(
                    'attachment_id' => $attachment_id,
                    'original_size' => $original_size,
                    'optimized_size' => $optimized_size,
                    'original_url' => $original_url,
                    'optimized_url' => $optimized_url,
                    'webp_url' => $webp_url,
                    'optimization_date' => current_time( 'mysql' ),
                    'optimization_level' => isset( $compression['level'] ) ? $compression['level'] : 'medium',
                    'status' => 'optimized',
                ),
                array( '%d', '%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s' )
            );
        }

        // Mettre à jour les métadonnées de l'image
        wp_update_attachment_metadata( $attachment_id, wp_generate_attachment_metadata( $attachment_id, $file ) );

        // Préparer la réponse
        $data = array(
            'id' => $attachment_id,
            'original_size' => $original_size,
            'optimized_size' => $optimized_size,
            'space_saved' => $original_size - $optimized_size,
            'space_saved_percent' => round( ( ( $original_size - $optimized_size ) / $original_size ) * 100, 2 ),
            'webp_url' => $webp_url,
            'status' => 'optimized',
        );

        return rest_ensure_response( $data );
    }

    /**
     * Optimise plusieurs images.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function optimize_images( $request ) {
        $ids = isset( $request['ids'] ) ? $request['ids'] : array();
        $options = isset( $request['options'] ) ? $request['options'] : array();

        if ( empty( $ids ) || ! is_array( $ids ) ) {
            return new WP_Error(
                'boss_seo_invalid_ids',
                __( 'Liste d\'IDs invalide.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        $results = array();
        $success_count = 0;
        $error_count = 0;
        $space_saved = 0;

        foreach ( $ids as $id ) {
            $result = $this->optimize_single_image( $id, $options );

            if ( is_wp_error( $result ) ) {
                $error_count++;
                $results[] = array(
                    'id' => $id,
                    'success' => false,
                    'message' => $result->get_error_message(),
                );
            } else {
                $success_count++;
                $space_saved += $result['space_saved'];
                $results[] = array(
                    'id' => $id,
                    'success' => true,
                    'original_size' => $result['original_size'],
                    'optimized_size' => $result['optimized_size'],
                    'space_saved' => $result['space_saved'],
                    'space_saved_percent' => $result['space_saved_percent'],
                );
            }
        }

        // Préparer la réponse
        $data = array(
            'success_count' => $success_count,
            'error_count' => $error_count,
            'total' => count( $ids ),
            'space_saved' => $space_saved,
            'space_saved_mb' => round( $space_saved / ( 1024 * 1024 ), 2 ),
            'results' => $results,
        );

        return rest_ensure_response( $data );
    }

    /**
     * Génère un texte alternatif pour une image.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function generate_alt_text( $request ) {
        $attachment_id = isset( $request['id'] ) ? intval( $request['id'] ) : 0;

        // Vérifier si l'image existe
        $attachment = get_post( $attachment_id );
        if ( ! $attachment || $attachment->post_type !== 'attachment' ) {
            return new WP_Error(
                'boss_seo_invalid_attachment',
                __( 'Image invalide.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        // Récupérer les paramètres d'optimisation
        $settings = get_option( $this->settings_option, array() );
        $ai_model = isset( $settings['altText']['aiModel'] ) ? $settings['altText']['aiModel'] : 'standard';

        // Générer le texte alternatif
        $alt_text = $this->generate_alt_text_for_image( $attachment_id, $ai_model );

        if ( ! $alt_text ) {
            return new WP_Error(
                'boss_seo_alt_generation_failed',
                __( 'Échec de la génération du texte alternatif.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }

        // Enregistrer le texte alternatif
        update_post_meta( $attachment_id, '_wp_attachment_image_alt', $alt_text );

        // Préparer la réponse
        $data = array(
            'id' => $attachment_id,
            'alt_text' => $alt_text,
            'success' => true,
        );

        return rest_ensure_response( $data );
    }

    /**
     * Récupère les paramètres d'optimisation des médias.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_settings( $request ) {
        $settings = get_option( $this->settings_option, array() );

        // Si les paramètres sont vides, utiliser les paramètres par défaut
        if ( empty( $settings ) ) {
            $settings = $this->get_default_settings();
        }

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres d'optimisation des médias.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function save_settings( $request ) {
        $settings = isset( $request['settings'] ) ? $request['settings'] : array();

        // Valider et sanitiser les paramètres
        $sanitized_settings = array();

        // Compression
        if ( isset( $settings['compression'] ) ) {
            $sanitized_settings['compression'] = array(
                'enabled' => isset( $settings['compression']['enabled'] ) ? (bool) $settings['compression']['enabled'] : true,
                'jpegQuality' => isset( $settings['compression']['jpegQuality'] ) ? intval( $settings['compression']['jpegQuality'] ) : 85,
                'pngQuality' => isset( $settings['compression']['pngQuality'] ) ? intval( $settings['compression']['pngQuality'] ) : 7,
                'convertToWebP' => isset( $settings['compression']['convertToWebP'] ) ? (bool) $settings['compression']['convertToWebP'] : true,
                'keepExif' => isset( $settings['compression']['keepExif'] ) ? (bool) $settings['compression']['keepExif'] : false,
            );
        }

        // Redimensionnement
        if ( isset( $settings['resize'] ) ) {
            $sanitized_settings['resize'] = array(
                'enabled' => isset( $settings['resize']['enabled'] ) ? (bool) $settings['resize']['enabled'] : true,
                'maxWidth' => isset( $settings['resize']['maxWidth'] ) ? intval( $settings['resize']['maxWidth'] ) : 1920,
                'maxHeight' => isset( $settings['resize']['maxHeight'] ) ? intval( $settings['resize']['maxHeight'] ) : 1080,
                'method' => isset( $settings['resize']['method'] ) ? sanitize_text_field( $settings['resize']['method'] ) : 'proportional',
            );
        }

        // Texte alternatif
        if ( isset( $settings['altText'] ) ) {
            $sanitized_settings['altText'] = array(
                'autoGenerate' => isset( $settings['altText']['autoGenerate'] ) ? (bool) $settings['altText']['autoGenerate'] : true,
                'aiModel' => isset( $settings['altText']['aiModel'] ) ? sanitize_text_field( $settings['altText']['aiModel'] ) : 'standard',
            );
        }

        // Lazy loading
        if ( isset( $settings['lazyLoading'] ) ) {
            $sanitized_settings['lazyLoading'] = array(
                'enabled' => isset( $settings['lazyLoading']['enabled'] ) ? (bool) $settings['lazyLoading']['enabled'] : true,
                'useNative' => isset( $settings['lazyLoading']['useNative'] ) ? (bool) $settings['lazyLoading']['useNative'] : true,
                'threshold' => isset( $settings['lazyLoading']['threshold'] ) ? intval( $settings['lazyLoading']['threshold'] ) : 200,
            );
        }

        // Enregistrer les paramètres
        update_option( $this->settings_option, $sanitized_settings );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Les paramètres d\'optimisation des médias ont été enregistrés avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Analyse les problèmes des médias.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function analyze_media_issues( $request ) {
        // Récupérer les paramètres d'optimisation
        $settings = get_option( $this->settings_option, array() );

        // Récupérer les problèmes de médias
        $issues = array(
            'missing_alt' => array(
                'count' => $this->count_media_without_alt(),
                'description' => __( 'Images sans texte alternatif', 'boss-seo' ),
                'severity' => 'high',
                'impact' => __( 'Accessibilité et SEO réduits', 'boss-seo' ),
                'solution' => __( 'Générer des textes alternatifs avec l\'IA ou les ajouter manuellement', 'boss-seo' ),
            ),
            'oversized' => array(
                'count' => $this->count_oversized_media(),
                'description' => __( 'Images surdimensionnées', 'boss-seo' ),
                'severity' => 'medium',
                'impact' => __( 'Temps de chargement plus longs', 'boss-seo' ),
                'solution' => __( 'Redimensionner les images selon les paramètres optimaux', 'boss-seo' ),
            ),
            'uncompressed' => array(
                'count' => $this->count_uncompressed_media(),
                'description' => __( 'Images non compressées', 'boss-seo' ),
                'severity' => 'medium',
                'impact' => __( 'Taille de page plus grande et temps de chargement plus longs', 'boss-seo' ),
                'solution' => __( 'Compresser les images avec les paramètres optimaux', 'boss-seo' ),
            ),
            'no_webp' => array(
                'count' => $this->count_media_without_webp(),
                'description' => __( 'Images sans version WebP', 'boss-seo' ),
                'severity' => 'low',
                'impact' => __( 'Performances réduites sur les navigateurs modernes', 'boss-seo' ),
                'solution' => __( 'Convertir les images en format WebP', 'boss-seo' ),
            ),
        );

        // Préparer la réponse
        $data = array(
            'issues' => $issues,
            'total_issues' => array_sum( array_column( $issues, 'count' ) ),
            'recommendations' => array(
                array(
                    'title' => __( 'Optimisation par lot', 'boss-seo' ),
                    'description' => __( 'Utilisez l\'optimisation par lot pour traiter toutes les images non optimisées en une seule fois.', 'boss-seo' ),
                    'priority' => 'high',
                ),
                array(
                    'title' => __( 'Génération automatique de texte alternatif', 'boss-seo' ),
                    'description' => __( 'Activez la génération automatique de texte alternatif pour améliorer l\'accessibilité et le SEO.', 'boss-seo' ),
                    'priority' => 'medium',
                ),
                array(
                    'title' => __( 'Lazy loading', 'boss-seo' ),
                    'description' => __( 'Activez le lazy loading pour améliorer les performances de chargement des pages.', 'boss-seo' ),
                    'priority' => 'medium',
                ),
            ),
        );

        return rest_ensure_response( $data );
    }

    /**
     * Optimise une seule image.
     *
     * @since    1.1.0
     * @param    int       $attachment_id    L'ID de l'attachement.
     * @param    array     $options          Les options d'optimisation.
     * @return   array|WP_Error              Les résultats de l'optimisation ou une erreur.
     */
    private function optimize_single_image( $attachment_id, $options = array() ) {
        // Vérifier si l'image existe
        $attachment = get_post( $attachment_id );
        if ( ! $attachment || $attachment->post_type !== 'attachment' ) {
            return new WP_Error(
                'boss_seo_invalid_attachment',
                __( 'Image invalide.', 'boss-seo' )
            );
        }

        // Récupérer le fichier
        $file = get_attached_file( $attachment_id );
        if ( ! file_exists( $file ) ) {
            return new WP_Error(
                'boss_seo_file_not_found',
                __( 'Fichier non trouvé.', 'boss-seo' )
            );
        }

        // Récupérer les paramètres d'optimisation
        $settings = get_option( $this->settings_option, array() );
        $compression = isset( $settings['compression'] ) ? $settings['compression'] : array();

        // Fusionner avec les options spécifiques
        if ( ! empty( $options ) ) {
            $compression = array_merge( $compression, $options );
        }

        // Récupérer la taille originale
        $original_size = filesize( $file );

        // Optimiser l'image
        $result = $this->optimize_image_file( $file, $compression );

        if ( ! $result ) {
            return new WP_Error(
                'boss_seo_optimization_failed',
                __( 'Échec de l\'optimisation de l\'image.', 'boss-seo' )
            );
        }

        // Récupérer la nouvelle taille
        $optimized_size = filesize( $file );

        // Mettre à jour les métadonnées de l'image
        wp_update_attachment_metadata( $attachment_id, wp_generate_attachment_metadata( $attachment_id, $file ) );

        // Enregistrer les données d'optimisation dans la base de données
        $this->save_optimization_data( $attachment_id, $original_size, $optimized_size, $compression );

        // Préparer les résultats
        return array(
            'original_size' => $original_size,
            'optimized_size' => $optimized_size,
            'space_saved' => $original_size - $optimized_size,
            'space_saved_percent' => round( ( ( $original_size - $optimized_size ) / $original_size ) * 100, 2 ),
        );
    }

    /**
     * Enregistre les données d'optimisation dans la base de données.
     *
     * @since    1.1.0
     * @param    int       $attachment_id    L'ID de l'attachement.
     * @param    int       $original_size    La taille originale.
     * @param    int       $optimized_size   La taille optimisée.
     * @param    array     $compression      Les paramètres de compression.
     */
    private function save_optimization_data( $attachment_id, $original_size, $optimized_size, $compression ) {
        global $wpdb;

        $original_url = wp_get_attachment_url( $attachment_id );
        $optimized_url = $original_url;
        $webp_url = '';

        // Vérifier si une version WebP existe
        $file = get_attached_file( $attachment_id );
        $webp_file = $file . '.webp';
        if ( file_exists( $webp_file ) ) {
            $webp_url = $original_url . '.webp';
        }

        // Vérifier si des données d'optimisation existent déjà
        $existing = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM $this->table_name WHERE attachment_id = %d",
                $attachment_id
            )
        );

        if ( $existing ) {
            // Mettre à jour les données existantes
            $wpdb->update(
                $this->table_name,
                array(
                    'original_size' => $original_size,
                    'optimized_size' => $optimized_size,
                    'original_url' => $original_url,
                    'optimized_url' => $optimized_url,
                    'webp_url' => $webp_url,
                    'optimization_date' => current_time( 'mysql' ),
                    'optimization_level' => isset( $compression['level'] ) ? $compression['level'] : 'medium',
                    'status' => 'optimized',
                ),
                array( 'attachment_id' => $attachment_id ),
                array( '%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s' ),
                array( '%d' )
            );
        } else {
            // Insérer de nouvelles données
            $wpdb->insert(
                $this->table_name,
                array(
                    'attachment_id' => $attachment_id,
                    'original_size' => $original_size,
                    'optimized_size' => $optimized_size,
                    'original_url' => $original_url,
                    'optimized_url' => $optimized_url,
                    'webp_url' => $webp_url,
                    'optimization_date' => current_time( 'mysql' ),
                    'optimization_level' => isset( $compression['level'] ) ? $compression['level'] : 'medium',
                    'status' => 'optimized',
                ),
                array( '%d', '%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s' )
            );
        }
    }

    /**
     * Compte le nombre de médias sans texte alternatif.
     *
     * @since    1.1.0
     * @return   int    Le nombre de médias sans texte alternatif.
     */
    private function count_media_without_alt() {
        global $wpdb;

        $count = $wpdb->get_var(
            "SELECT COUNT(p.ID) FROM $wpdb->posts p
            LEFT JOIN $wpdb->postmeta pm ON p.ID = pm.post_id AND pm.meta_key = '_wp_attachment_image_alt'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type LIKE 'image/%'
            AND (pm.meta_value IS NULL OR pm.meta_value = '')"
        );

        return (int) $count;
    }

    /**
     * Compte le nombre de médias surdimensionnés.
     *
     * @since    1.1.0
     * @return   int    Le nombre de médias surdimensionnés.
     */
    private function count_oversized_media() {
        // Récupérer les paramètres d'optimisation
        $settings = get_option( $this->settings_option, array() );
        $max_width = isset( $settings['resize']['maxWidth'] ) ? (int) $settings['resize']['maxWidth'] : 1920;
        $max_height = isset( $settings['resize']['maxHeight'] ) ? (int) $settings['resize']['maxHeight'] : 1080;

        // Récupérer tous les attachements d'images
        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => -1,
            'fields' => 'ids',
        );

        $attachments = get_posts( $args );
        $count = 0;

        foreach ( $attachments as $attachment_id ) {
            $metadata = wp_get_attachment_metadata( $attachment_id );

            if ( isset( $metadata['width'] ) && isset( $metadata['height'] ) ) {
                if ( $metadata['width'] > $max_width || $metadata['height'] > $max_height ) {
                    $count++;
                }
            }
        }

        return $count;
    }

    /**
     * Compte le nombre de médias non compressés.
     *
     * @since    1.1.0
     * @return   int    Le nombre de médias non compressés.
     */
    private function count_uncompressed_media() {
        global $wpdb;

        // Récupérer le nombre total de médias images
        $total_images = $wpdb->get_var(
            "SELECT COUNT(*) FROM $wpdb->posts
            WHERE post_type = 'attachment'
            AND post_mime_type LIKE 'image/%'"
        );

        // Récupérer le nombre de médias optimisés
        $optimized_images = $wpdb->get_var(
            "SELECT COUNT(*) FROM $this->table_name
            WHERE status = 'optimized'"
        );

        return (int) $total_images - (int) $optimized_images;
    }

    /**
     * Compte le nombre de médias sans version WebP.
     *
     * @since    1.1.0
     * @return   int    Le nombre de médias sans version WebP.
     */
    private function count_media_without_webp() {
        global $wpdb;

        // Récupérer le nombre total de médias images (hors WebP)
        $total_non_webp_images = $wpdb->get_var(
            "SELECT COUNT(*) FROM $wpdb->posts
            WHERE post_type = 'attachment'
            AND post_mime_type LIKE 'image/%'
            AND post_mime_type != 'image/webp'"
        );

        // Récupérer le nombre de médias avec version WebP
        $with_webp_images = $wpdb->get_var(
            "SELECT COUNT(*) FROM $this->table_name
            WHERE webp_url != ''"
        );

        return (int) $total_non_webp_images - (int) $with_webp_images;
    }

    /**
     * Récupère les paramètres par défaut d'optimisation des médias.
     *
     * @since    1.1.0
     * @return   array    Les paramètres par défaut.
     */
    private function get_default_settings() {
        return array(
            'compression' => array(
                'enabled' => true,
                'jpegQuality' => 85,
                'pngQuality' => 7,
                'convertToWebP' => true,
                'keepExif' => false,
            ),
            'resize' => array(
                'enabled' => true,
                'maxWidth' => 1920,
                'maxHeight' => 1080,
                'method' => 'proportional',
            ),
            'altText' => array(
                'autoGenerate' => true,
                'aiModel' => 'standard',
            ),
            'lazyLoading' => array(
                'enabled' => true,
                'useNative' => true,
                'threshold' => 200,
            ),
        );
    }

    /**
     * Génère un texte alternatif pour une image.
     *
     * @since    1.1.0
     * @param    int       $attachment_id    L'ID de l'attachement.
     * @param    string    $ai_model         Le modèle d'IA à utiliser.
     * @return   string                      Le texte alternatif généré.
     */
    private function generate_alt_text_for_image( $attachment_id, $ai_model = 'standard' ) {
        // Récupérer les informations de l'image
        $attachment = get_post( $attachment_id );
        $file = get_attached_file( $attachment_id );
        $filename = basename( $file );
        $title = $attachment->post_title;
        $caption = $attachment->post_excerpt;
        $description = $attachment->post_content;

        // Pour cette implémentation, nous utilisons une approche simple basée sur les métadonnées existantes
        // Dans une implémentation réelle, on utiliserait un service d'IA pour analyser l'image

        // Si un titre existe, l'utiliser comme base
        if ( ! empty( $title ) && $title !== $filename ) {
            return $title;
        }

        // Si une légende existe, l'utiliser
        if ( ! empty( $caption ) ) {
            return $caption;
        }

        // Si une description existe, l'utiliser
        if ( ! empty( $description ) ) {
            // Limiter à la première phrase
            $first_sentence = preg_replace( '/^([^.!?]+[.!?]).*$/s', '$1', $description );
            return $first_sentence;
        }

        // Utiliser le nom de fichier comme dernier recours
        $alt_text = pathinfo( $filename, PATHINFO_FILENAME );
        $alt_text = str_replace( array( '-', '_' ), ' ', $alt_text );
        $alt_text = ucfirst( $alt_text );

        return $alt_text;
    }

    /**
     * Optimise une image JPEG.
     *
     * @since    1.1.0
     * @param    string    $file_path      Le chemin du fichier.
     * @param    int       $quality        La qualité de compression.
     * @param    bool      $keep_exif      Conserver les données EXIF.
     * @return   bool                      True si l'optimisation a réussi, false sinon.
     */
    private function optimize_jpeg( $file_path, $quality, $keep_exif ) {
        // Vérifier si GD est disponible
        if ( ! function_exists( 'imagecreatefromjpeg' ) ) {
            return false;
        }

        // Charger l'image
        $image = imagecreatefromjpeg( $file_path );
        if ( ! $image ) {
            return false;
        }

        // Récupérer les données EXIF si nécessaire
        $exif_data = null;
        if ( $keep_exif && function_exists( 'exif_read_data' ) ) {
            $exif_data = @exif_read_data( $file_path );
        }

        // Créer une copie temporaire
        $temp_file = $file_path . '.tmp';

        // Enregistrer l'image optimisée
        imagejpeg( $image, $temp_file, $quality );
        imagedestroy( $image );

        // Vérifier si l'optimisation a réussi
        if ( ! file_exists( $temp_file ) ) {
            return false;
        }

        // Vérifier si la taille a été réduite
        $original_size = filesize( $file_path );
        $optimized_size = filesize( $temp_file );

        if ( $optimized_size >= $original_size ) {
            // L'optimisation n'a pas réduit la taille, conserver l'original
            unlink( $temp_file );
            return true;
        }

        // Remplacer l'original par la version optimisée
        unlink( $file_path );
        rename( $temp_file, $file_path );

        return true;
    }

    /**
     * Optimise une image PNG.
     *
     * @since    1.1.0
     * @param    string    $file_path      Le chemin du fichier.
     * @param    int       $quality        Le niveau de compression (0-9).
     * @return   bool                      True si l'optimisation a réussi, false sinon.
     */
    private function optimize_png( $file_path, $quality ) {
        // Vérifier si GD est disponible
        if ( ! function_exists( 'imagecreatefrompng' ) ) {
            return false;
        }

        // Charger l'image
        $image = imagecreatefrompng( $file_path );
        if ( ! $image ) {
            return false;
        }

        // Préserver la transparence
        imagealphablending( $image, false );
        imagesavealpha( $image, true );

        // Créer une copie temporaire
        $temp_file = $file_path . '.tmp';

        // Enregistrer l'image optimisée
        // PNG utilise un niveau de compression de 0 (aucune compression) à 9 (compression maximale)
        // Convertir la qualité (1-9) en niveau de compression (0-9)
        $compression_level = min( 9, max( 0, $quality ) );

        imagepng( $image, $temp_file, $compression_level );
        imagedestroy( $image );

        // Vérifier si l'optimisation a réussi
        if ( ! file_exists( $temp_file ) ) {
            return false;
        }

        // Vérifier si la taille a été réduite
        $original_size = filesize( $file_path );
        $optimized_size = filesize( $temp_file );

        if ( $optimized_size >= $original_size ) {
            // L'optimisation n'a pas réduit la taille, conserver l'original
            unlink( $temp_file );
            return true;
        }

        // Remplacer l'original par la version optimisée
        unlink( $file_path );
        rename( $temp_file, $file_path );

        return true;
    }

    /**
     * Convertit une image en format WebP.
     *
     * @since    1.1.0
     * @param    string    $file_path      Le chemin du fichier.
     * @return   bool                      True si la conversion a réussi, false sinon.
     */
    private function convert_to_webp( $file_path ) {
        // Vérifier si la fonction de conversion WebP est disponible
        if ( ! function_exists( 'imagewebp' ) ) {
            return false;
        }

        // Récupérer le type de fichier
        $mime_type = wp_check_filetype( $file_path )['type'];
        $image = null;

        // Charger l'image en fonction de son type
        switch ( $mime_type ) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg( $file_path );
                break;
            case 'image/png':
                $image = imagecreatefrompng( $file_path );
                imagealphablending( $image, false );
                imagesavealpha( $image, true );
                break;
            default:
                return false;
        }

        if ( ! $image ) {
            return false;
        }

        // Créer le fichier WebP
        $webp_file = $file_path . '.webp';
        $result = imagewebp( $image, $webp_file, 80 ); // Qualité 80 pour WebP
        imagedestroy( $image );

        return $result;
    }

    /**
     * Optimise un fichier image.
     *
     * @since    1.1.0
     * @param    string    $file_path      Le chemin du fichier.
     * @param    array     $compression    Les paramètres de compression.
     * @return   bool                      True si l'optimisation a réussi, false sinon.
     */
    private function optimize_image_file( $file_path, $compression ) {
        // Récupérer le type de fichier
        $mime_type = wp_check_filetype( $file_path )['type'];

        // Récupérer les paramètres de compression
        $jpeg_quality = isset( $compression['jpegQuality'] ) ? (int) $compression['jpegQuality'] : 85;
        $png_quality = isset( $compression['pngQuality'] ) ? (int) $compression['pngQuality'] : 7;
        $convert_to_webp = isset( $compression['convertToWebP'] ) ? (bool) $compression['convertToWebP'] : true;
        $keep_exif = isset( $compression['keepExif'] ) ? (bool) $compression['keepExif'] : false;

        // Optimiser l'image en fonction de son type
        switch ( $mime_type ) {
            case 'image/jpeg':
                // Optimiser les JPEG
                $this->optimize_jpeg( $file_path, $jpeg_quality, $keep_exif );

                // Convertir en WebP si nécessaire
                if ( $convert_to_webp ) {
                    $this->convert_to_webp( $file_path );
                }
                break;

            case 'image/png':
                // Optimiser les PNG
                $this->optimize_png( $file_path, $png_quality );

                // Convertir en WebP si nécessaire
                if ( $convert_to_webp ) {
                    $this->convert_to_webp( $file_path );
                }
                break;

            case 'image/gif':
                // Les GIF ne sont pas optimisés pour le moment
                break;

            case 'image/webp':
                // Les WebP sont déjà optimisés
                break;
        }

        return true;
    }

    /**
     * Récupère la liste des médias avec statistiques.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_media_list( $request ) {
        // Récupérer les paramètres de pagination
        $page = isset( $request['page'] ) ? intval( $request['page'] ) : 1;
        $per_page = isset( $request['per_page'] ) ? intval( $request['per_page'] ) : 20;
        $offset = ( $page - 1 ) * $per_page;

        // Récupérer les filtres
        $search = isset( $request['search'] ) ? sanitize_text_field( $request['search'] ) : '';
        $type = isset( $request['type'] ) ? sanitize_text_field( $request['type'] ) : '';
        $status = isset( $request['status'] ) ? sanitize_text_field( $request['status'] ) : '';

        global $wpdb;

        // Construction de la requête
        $where_conditions = array( "p.post_type = 'attachment'", "p.post_mime_type LIKE 'image/%'" );
        $join_conditions = array();

        if ( ! empty( $search ) ) {
            $where_conditions[] = $wpdb->prepare( "(p.post_title LIKE %s OR p.post_name LIKE %s)",
                '%' . $wpdb->esc_like( $search ) . '%',
                '%' . $wpdb->esc_like( $search ) . '%'
            );
        }

        if ( ! empty( $type ) ) {
            $where_conditions[] = $wpdb->prepare( "p.post_mime_type = %s", 'image/' . $type );
        }

        if ( ! empty( $status ) ) {
            $join_conditions[] = "LEFT JOIN {$this->table_name} mo ON p.ID = mo.attachment_id";
            if ( $status === 'optimized' ) {
                $where_conditions[] = "mo.status = 'optimized'";
            } elseif ( $status === 'unoptimized' ) {
                $where_conditions[] = "(mo.status IS NULL OR mo.status != 'optimized')";
            }
        }

        $join_clause = implode( ' ', $join_conditions );
        $where_clause = 'WHERE ' . implode( ' AND ', $where_conditions );

        // Requête pour compter le total
        $count_query = "SELECT COUNT(DISTINCT p.ID) FROM {$wpdb->posts} p {$join_clause} {$where_clause}";
        $total_items = $wpdb->get_var( $count_query );

        // Requête pour récupérer les données
        $query = "SELECT DISTINCT p.* FROM {$wpdb->posts} p {$join_clause} {$where_clause}
                  ORDER BY p.post_date DESC LIMIT %d OFFSET %d";
        $attachments = $wpdb->get_results( $wpdb->prepare( $query, $per_page, $offset ) );

        // Traiter les données des médias
        $media_list = array();
        foreach ( $attachments as $attachment ) {
            $media_data = $this->get_media_data( $attachment->ID );
            $media_list[] = $media_data;
        }

        // Récupérer les statistiques
        $stats = $this->get_media_statistics();

        $response_data = array(
            'success' => true,
            'data' => array(
                'media' => $media_list,
                'stats' => $stats,
                'pagination' => array(
                    'total' => intval( $total_items ),
                    'pages' => ceil( $total_items / $per_page ),
                    'page' => $page,
                    'per_page' => $per_page,
                )
            )
        );

        return rest_ensure_response( $response_data );
    }

    /**
     * Génère des textes alternatifs pour plusieurs images.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function generate_alt_texts_bulk( $request ) {
        $ids = isset( $request['ids'] ) ? array_map( 'intval', $request['ids'] ) : array();

        if ( empty( $ids ) ) {
            return new WP_Error(
                'boss_seo_invalid_ids',
                __( 'Aucun ID d\'image fourni.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        $results = array();
        $success_count = 0;
        $error_count = 0;

        foreach ( $ids as $id ) {
            try {
                // Vérifier que l'attachement existe et est une image
                $attachment = get_post( $id );
                if ( ! $attachment || $attachment->post_type !== 'attachment' || ! wp_attachment_is_image( $id ) ) {
                    $results[] = array(
                        'id' => $id,
                        'success' => false,
                        'message' => __( 'Image non trouvée ou invalide.', 'boss-seo' )
                    );
                    $error_count++;
                    continue;
                }

                // Vérifier si l'image a déjà un texte alternatif
                $current_alt = get_post_meta( $id, '_wp_attachment_image_alt', true );
                if ( ! empty( $current_alt ) ) {
                    $results[] = array(
                        'id' => $id,
                        'success' => true,
                        'alt_text' => $current_alt,
                        'message' => __( 'Texte alternatif existant conservé.', 'boss-seo' )
                    );
                    $success_count++;
                    continue;
                }

                // Générer le texte alternatif
                $alt_text = $this->generate_alt_text_for_image( $id );

                // Enregistrer le texte alternatif
                update_post_meta( $id, '_wp_attachment_image_alt', $alt_text );

                $results[] = array(
                    'id' => $id,
                    'success' => true,
                    'alt_text' => $alt_text,
                    'message' => __( 'Texte alternatif généré avec succès.', 'boss-seo' )
                );
                $success_count++;

            } catch ( Exception $e ) {
                $results[] = array(
                    'id' => $id,
                    'success' => false,
                    'message' => $e->getMessage()
                );
                $error_count++;
            }
        }

        $response_data = array(
            'success' => true,
            'data' => array(
                'results' => $results,
                'summary' => array(
                    'total' => count( $ids ),
                    'success' => $success_count,
                    'errors' => $error_count
                )
            ),
            'message' => sprintf(
                __( '%d textes alternatifs générés avec succès, %d erreurs.', 'boss-seo' ),
                $success_count,
                $error_count
            )
        );

        return rest_ensure_response( $response_data );
    }

    /**
     * Récupère les données d'un média spécifique.
     *
     * @since    1.1.0
     * @param    int    $attachment_id    L'ID de l'attachement.
     * @return   array                    Les données du média.
     */
    private function get_media_data( $attachment_id ) {
        $attachment = get_post( $attachment_id );
        $file_path = get_attached_file( $attachment_id );
        $file_url = wp_get_attachment_url( $attachment_id );
        $metadata = wp_get_attachment_metadata( $attachment_id );

        // Récupérer les informations d'optimisation
        global $wpdb;
        $optimization_data = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE attachment_id = %d",
                $attachment_id
            )
        );

        // Calculer la taille du fichier
        $file_size = file_exists( $file_path ) ? filesize( $file_path ) : 0;

        // Récupérer les dimensions
        $width = isset( $metadata['width'] ) ? $metadata['width'] : 0;
        $height = isset( $metadata['height'] ) ? $metadata['height'] : 0;

        // Récupérer le texte alternatif
        $alt_text = get_post_meta( $attachment_id, '_wp_attachment_image_alt', true );

        // Déterminer le statut
        $status = 'unoptimized';
        if ( $optimization_data && $optimization_data->status === 'optimized' ) {
            $status = 'optimized';
        }

        // Récupérer le type MIME
        $mime_type = $attachment->post_mime_type;
        $file_type = str_replace( 'image/', '', $mime_type );

        return array(
            'id' => $attachment_id,
            'name' => basename( $file_path ),
            'title' => $attachment->post_title,
            'type' => $file_type,
            'url' => $file_url,
            'thumbnail' => wp_get_attachment_image_url( $attachment_id, 'thumbnail' ),
            'dimensions' => array(
                'width' => $width,
                'height' => $height
            ),
            'size' => $file_size,
            'originalSize' => $optimization_data ? $optimization_data->original_size : null,
            'alt' => $alt_text,
            'status' => $status,
            'date' => $attachment->post_date,
            'optimizedAt' => $optimization_data ? $optimization_data->optimized_at : null
        );
    }
}
