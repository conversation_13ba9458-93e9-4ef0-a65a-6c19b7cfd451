import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  ButtonGroup,
  Dashicon,
  SelectControl,
  Notice
} from '@wordpress/components';
import { TrafficChart, TrendsChart } from './ChartComponents';

const TrendsAnalysis = ({ data, trafficData, selectedPeriod, onPeriodChange }) => {
  // États
  const [selectedMetric, setSelectedMetric] = useState('traffic');
  const [showAnomalies, setShowAnomalies] = useState(true);
  
  // Fonction pour formater les métriques
  const formatMetric = (value, type = 'number') => {
    if (type === 'percentage') {
      return `${value.toFixed(2)}%`;
    } else {
      return new Intl.NumberFormat('fr-FR').format(value);
    }
  };
  
  // Fonction pour obtenir la classe de couleur en fonction de la variation
  const getChangeColorClass = (change, isInverse = false) => {
    if (change === 0) return 'boss-text-boss-gray';
    if (isInverse) {
      return change > 0 ? 'boss-text-red-500' : 'boss-text-green-500';
    }
    return change > 0 ? 'boss-text-green-500' : 'boss-text-red-500';
  };
  
  // Fonction pour obtenir l'icône en fonction de la variation
  const getChangeIcon = (change, isInverse = false) => {
    if (change === 0) return 'minus';
    if (isInverse) {
      return change > 0 ? 'arrow-up' : 'arrow-down';
    }
    return change > 0 ? 'arrow-up' : 'arrow-down';
  };
  
  // Simuler des anomalies détectées
  const anomalies = [
    {
      date: '2023-06-15',
      type: 'traffic',
      description: __('Pic de trafic inhabituel (+45%)', 'boss-seo'),
      severity: 'positive',
      metric: 'users',
      value: 450
    },
    {
      date: '2023-06-10',
      type: 'keywords',
      description: __('Chute soudaine des positions (-8 positions en moyenne)', 'boss-seo'),
      severity: 'negative',
      metric: 'avgPosition',
      value: -8
    },
    {
      date: '2023-06-05',
      type: 'traffic',
      description: __('Baisse significative du taux de conversion (-15%)', 'boss-seo'),
      severity: 'negative',
      metric: 'conversions',
      value: -15
    }
  ];
  
  // Filtrer les anomalies par type de métrique
  const filteredAnomalies = anomalies.filter(anomaly => 
    selectedMetric === 'all' || anomaly.type === selectedMetric
  );

  return (
    <div>
      {/* Métriques principales */}
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-4 boss-gap-6 boss-mb-6">
        {/* Trafic */}
        <Card className="boss-card">
          <CardBody>
            <div className="boss-flex boss-justify-between boss-items-start">
              <div>
                <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                  {__('Trafic', 'boss-seo')}
                </h3>
                <div className="boss-flex boss-items-baseline">
                  <span className="boss-text-2xl boss-font-bold boss-mr-2">
                    {formatMetric(data.traffic.current)}
                  </span>
                  <span className={`boss-text-sm boss-font-medium ${getChangeColorClass(data.traffic.change)}`}>
                    <Dashicon icon={getChangeIcon(data.traffic.change)} />
                    {formatMetric(Math.abs(data.traffic.change), 'percentage')}
                  </span>
                </div>
              </div>
              <div className="boss-bg-blue-100 boss-p-2 boss-rounded-lg">
                <Dashicon icon="chart-line" className="boss-text-blue-600 boss-text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>
        
        {/* Mots-clés */}
        <Card className="boss-card">
          <CardBody>
            <div className="boss-flex boss-justify-between boss-items-start">
              <div>
                <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                  {__('Mots-clés', 'boss-seo')}
                </h3>
                <div className="boss-flex boss-items-baseline">
                  <span className="boss-text-2xl boss-font-bold boss-mr-2">
                    {formatMetric(data.keywords.current)}
                  </span>
                  <span className={`boss-text-sm boss-font-medium ${getChangeColorClass(data.keywords.change)}`}>
                    <Dashicon icon={getChangeIcon(data.keywords.change)} />
                    {formatMetric(Math.abs(data.keywords.change), 'percentage')}
                  </span>
                </div>
              </div>
              <div className="boss-bg-green-100 boss-p-2 boss-rounded-lg">
                <Dashicon icon="search" className="boss-text-green-600 boss-text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>
        
        {/* Position moyenne */}
        <Card className="boss-card">
          <CardBody>
            <div className="boss-flex boss-justify-between boss-items-start">
              <div>
                <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                  {__('Position moyenne', 'boss-seo')}
                </h3>
                <div className="boss-flex boss-items-baseline">
                  <span className="boss-text-2xl boss-font-bold boss-mr-2">
                    {formatMetric(data.avgPosition.current.toFixed(1))}
                  </span>
                  <span className={`boss-text-sm boss-font-medium ${getChangeColorClass(data.avgPosition.change, true)}`}>
                    <Dashicon icon={getChangeIcon(data.avgPosition.change, true)} />
                    {formatMetric(Math.abs(data.avgPosition.change), 'percentage')}
                  </span>
                </div>
              </div>
              <div className="boss-bg-yellow-100 boss-p-2 boss-rounded-lg">
                <Dashicon icon="arrow-up-alt" className="boss-text-yellow-600 boss-text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>
        
        {/* Conversions */}
        <Card className="boss-card">
          <CardBody>
            <div className="boss-flex boss-justify-between boss-items-start">
              <div>
                <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                  {__('Conversions', 'boss-seo')}
                </h3>
                <div className="boss-flex boss-items-baseline">
                  <span className="boss-text-2xl boss-font-bold boss-mr-2">
                    {formatMetric(data.conversions.current)}
                  </span>
                  <span className={`boss-text-sm boss-font-medium ${getChangeColorClass(data.conversions.change)}`}>
                    <Dashicon icon={getChangeIcon(data.conversions.change)} />
                    {formatMetric(Math.abs(data.conversions.change), 'percentage')}
                  </span>
                </div>
              </div>
              <div className="boss-bg-purple-100 boss-p-2 boss-rounded-lg">
                <Dashicon icon="yes-alt" className="boss-text-purple-600 boss-text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
      
      {/* Sélecteurs de période et de métrique */}
      <div className="boss-flex boss-flex-col md:boss-flex-row boss-justify-between boss-items-start md:boss-items-center boss-gap-4 boss-mb-6">
        <ButtonGroup>
          <Button
            isPrimary={selectedPeriod === 'last7days'}
            isSecondary={selectedPeriod !== 'last7days'}
            onClick={() => onPeriodChange('last7days')}
          >
            {__('7 derniers jours', 'boss-seo')}
          </Button>
          <Button
            isPrimary={selectedPeriod === 'last30days'}
            isSecondary={selectedPeriod !== 'last30days'}
            onClick={() => onPeriodChange('last30days')}
          >
            {__('30 derniers jours', 'boss-seo')}
          </Button>
          <Button
            isPrimary={selectedPeriod === 'last90days'}
            isSecondary={selectedPeriod !== 'last90days'}
            onClick={() => onPeriodChange('last90days')}
          >
            {__('90 derniers jours', 'boss-seo')}
          </Button>
          <Button
            isPrimary={selectedPeriod === 'lastYear'}
            isSecondary={selectedPeriod !== 'lastYear'}
            onClick={() => onPeriodChange('lastYear')}
          >
            {__('12 derniers mois', 'boss-seo')}
          </Button>
        </ButtonGroup>
        
        <SelectControl
          value={selectedMetric}
          options={[
            { label: __('Trafic', 'boss-seo'), value: 'traffic' },
            { label: __('Mots-clés', 'boss-seo'), value: 'keywords' },
            { label: __('Positions', 'boss-seo'), value: 'positions' },
            { label: __('Conversions', 'boss-seo'), value: 'conversions' }
          ]}
          onChange={setSelectedMetric}
        />
      </div>
      
      {/* Graphique de tendances */}
      <Card className="boss-mb-6">
        <CardHeader className="boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {selectedMetric === 'traffic' 
                ? __('Tendances du trafic', 'boss-seo') 
                : selectedMetric === 'keywords' 
                  ? __('Tendances des mots-clés', 'boss-seo')
                  : selectedMetric === 'positions'
                    ? __('Tendances des positions', 'boss-seo')
                    : __('Tendances des conversions', 'boss-seo')}
            </h2>
            <div className="boss-flex boss-items-center boss-space-x-2">
              <Button
                isSmall
                isSecondary
                icon={showAnomalies ? 'visibility' : 'hidden'}
                onClick={() => setShowAnomalies(!showAnomalies)}
              >
                {showAnomalies ? __('Masquer les anomalies', 'boss-seo') : __('Afficher les anomalies', 'boss-seo')}
              </Button>
              <Button
                isSmall
                isSecondary
                icon="download"
              >
                {__('Exporter', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardBody>
          {selectedMetric === 'traffic' ? (
            <TrafficChart data={trafficData} />
          ) : (
            <TrendsChart data={trafficData} metric={selectedMetric} />
          )}
        </CardBody>
      </Card>
      
      {/* Anomalies détectées */}
      {showAnomalies && (
        <Card>
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Anomalies détectées', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            {filteredAnomalies.length === 0 ? (
              <div className="boss-text-center boss-py-6 boss-text-boss-gray">
                {__('Aucune anomalie détectée pour la période sélectionnée.', 'boss-seo')}
              </div>
            ) : (
              <div className="boss-space-y-4">
                {filteredAnomalies.map((anomaly, index) => (
                  <div 
                    key={index} 
                    className={`boss-p-4 boss-rounded-lg boss-border-l-4 ${
                      anomaly.severity === 'positive' 
                        ? 'boss-bg-green-50 boss-border-green-500' 
                        : 'boss-bg-red-50 boss-border-red-500'
                    }`}
                  >
                    <div className="boss-flex boss-items-start">
                      <div className={`boss-p-2 boss-rounded-full ${
                        anomaly.severity === 'positive' 
                          ? 'boss-bg-green-100' 
                          : 'boss-bg-red-100'
                      } boss-mr-3`}>
                        <Dashicon 
                          icon={anomaly.severity === 'positive' ? 'arrow-up-alt' : 'arrow-down-alt'} 
                          className={`${
                            anomaly.severity === 'positive' 
                              ? 'boss-text-green-600' 
                              : 'boss-text-red-600'
                          }`} 
                        />
                      </div>
                      <div>
                        <div className="boss-flex boss-items-center boss-mb-1">
                          <span className="boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded-full boss-bg-gray-100 boss-text-boss-gray boss-mr-2">
                            {anomaly.date}
                          </span>
                          <span className="boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded-full boss-bg-blue-100 boss-text-blue-600">
                            {anomaly.type === 'traffic' 
                              ? __('Trafic', 'boss-seo') 
                              : anomaly.type === 'keywords' 
                                ? __('Mots-clés', 'boss-seo')
                                : anomaly.type === 'positions'
                                  ? __('Positions', 'boss-seo')
                                  : __('Conversions', 'boss-seo')}
                          </span>
                        </div>
                        <h3 className="boss-font-medium boss-mb-1">{anomaly.description}</h3>
                        <p className="boss-text-sm boss-text-boss-gray">
                          {__('Métrique affectée:', 'boss-seo')} {anomaly.metric === 'users' 
                            ? __('Utilisateurs', 'boss-seo') 
                            : anomaly.metric === 'avgPosition' 
                              ? __('Position moyenne', 'boss-seo')
                              : anomaly.metric === 'conversions'
                                ? __('Conversions', 'boss-seo')
                                : anomaly.metric}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default TrendsAnalysis;
