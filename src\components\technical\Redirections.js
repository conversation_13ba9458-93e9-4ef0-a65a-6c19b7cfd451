import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Tab<PERSON>anel,
  Notice,
  Spinner
} from '@wordpress/components';

// Importer les sous-composants
import RedirectionsList from './redirections/RedirectionsList';
import RedirectionForm from './redirections/RedirectionForm';
import RedirectionLogs from './redirections/RedirectionLogs';
import RedirectionImportExport from './redirections/RedirectionImportExport';
import RedirectionFilters from './redirections/RedirectionFilters';

// Importer le service
import RedirectionsService from '../../services/RedirectionsService';

const Redirections = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [redirections, setRedirections] = useState([]);
  const [logs, setLogs] = useState([]);
  const [notFoundUrls, setNotFoundUrls] = useState([]);
  const [editingRedirection, setEditingRedirection] = useState(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedItems, setSelectedItems] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [activeTab, setActiveTab] = useState('list');

  // Effet pour charger les données
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Charger les redirections
        const filters = {};
        if (searchQuery) filters.search = searchQuery;
        if (filterType !== 'all') filters.type = filterType;
        if (filterStatus !== 'all') filters.status = filterStatus;

        const redirectionsData = await RedirectionsService.getRedirections(1, 20, filters);
        setRedirections(redirectionsData.redirections || []);

        // Charger les logs
        const logsData = await RedirectionsService.getRedirectionLogs(1, 50);
        setLogs(logsData.logs || []);

        // Pour l'instant, on utilise des données fictives pour les URLs 404
        // Cette fonctionnalité devra être implémentée côté serveur
        const mockNotFoundUrls = [
          '/page-inexistante-1',
          '/ancienne-page-produit',
          '/categorie/supprimee',
          '/blog/article-deplace',
          '/contact-old'
        ];
        setNotFoundUrls(mockNotFoundUrls);

        setIsLoading(false);
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        setIsLoading(false);
      }
    };

    loadData();
  }, [searchQuery, filterType, filterStatus]);

  // Effet pour gérer la sélection de tous les éléments
  useEffect(() => {
    if (selectAll) {
      const filteredRedirections = redirections.filter(redirection => {
        // Filtrer par type
        const matchesType = filterType === 'all' || redirection.type === filterType;

        // Filtrer par statut
        const matchesStatus = filterStatus === 'all' || redirection.status === filterStatus;

        // Filtrer par recherche
        const matchesSearch = searchQuery === '' ||
          redirection.source.toLowerCase().includes(searchQuery.toLowerCase()) ||
          redirection.target.toLowerCase().includes(searchQuery.toLowerCase());

        return matchesType && matchesStatus && matchesSearch;
      });

      setSelectedItems(filteredRedirections.map(r => r.id));
    } else {
      setSelectedItems([]);
    }
  }, [selectAll, redirections, filterType, filterStatus, searchQuery]);

  // Fonction pour ajouter ou mettre à jour une redirection
  const handleSaveRedirection = async (redirectionData) => {
    setIsLoading(true);

    try {
      let result;

      if (redirectionData.id) {
        // Mise à jour d'une redirection existante
        result = await RedirectionsService.updateRedirection(redirectionData.id, redirectionData);

        // Mettre à jour la liste des redirections
        const updatedRedirections = redirections.map(redirection => {
          if (redirection.id === redirectionData.id) {
            return result;
          }
          return redirection;
        });

        setRedirections(updatedRedirections);
        setSuccessMessage(__('Redirection mise à jour avec succès !', 'boss-seo'));
      } else {
        // Ajout d'une nouvelle redirection
        result = await RedirectionsService.addRedirection(redirectionData);

        setRedirections([...redirections, result]);
        setSuccessMessage(__('Redirection ajoutée avec succès !', 'boss-seo'));
      }

      setEditingRedirection(null);
      setIsLoading(false);
      setShowSuccess(true);
      setActiveTab('list');

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de la redirection:', error);
      setIsLoading(false);
      setSuccessMessage(__('Erreur lors de l\'enregistrement de la redirection.', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }
  };

  // Fonction pour supprimer une redirection
  const handleDeleteRedirection = async (id) => {
    setIsLoading(true);

    try {
      // Appeler le service pour supprimer la redirection
      await RedirectionsService.deleteRedirection(id);

      // Mettre à jour la liste des redirections
      const updatedRedirections = redirections.filter(redirection => redirection.id !== id);
      setRedirections(updatedRedirections);

      setIsLoading(false);
      setSuccessMessage(__('Redirection supprimée avec succès !', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de la suppression de la redirection:', error);
      setIsLoading(false);
      setSuccessMessage(__('Erreur lors de la suppression de la redirection.', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }
  };

  // Fonction pour effectuer une action en masse
  const handleBulkAction = async (action) => {
    if (selectedItems.length === 0) return;

    setIsLoading(true);

    try {
      let updatedRedirections = [...redirections];
      let message = '';

      switch (action) {
        case 'activate':
          // Activer les redirections sélectionnées
          for (const id of selectedItems) {
            const redirection = redirections.find(r => r.id === id);
            if (redirection) {
              await RedirectionsService.updateRedirection(id, { ...redirection, status: 'active' });
            }
          }

          updatedRedirections = redirections.map(redirection => {
            if (selectedItems.includes(redirection.id)) {
              return { ...redirection, status: 'active' };
            }
            return redirection;
          });

          message = __('Redirections activées avec succès !', 'boss-seo');
          break;

        case 'deactivate':
          // Désactiver les redirections sélectionnées
          for (const id of selectedItems) {
            const redirection = redirections.find(r => r.id === id);
            if (redirection) {
              await RedirectionsService.updateRedirection(id, { ...redirection, status: 'inactive' });
            }
          }

          updatedRedirections = redirections.map(redirection => {
            if (selectedItems.includes(redirection.id)) {
              return { ...redirection, status: 'inactive' };
            }
            return redirection;
          });

          message = __('Redirections désactivées avec succès !', 'boss-seo');
          break;

        case 'delete':
          // Supprimer les redirections sélectionnées
          for (const id of selectedItems) {
            await RedirectionsService.deleteRedirection(id);
          }

          updatedRedirections = redirections.filter(redirection => !selectedItems.includes(redirection.id));
          message = __('Redirections supprimées avec succès !', 'boss-seo');
          break;

        default:
          break;
      }

      setRedirections(updatedRedirections);
      setSelectedItems([]);
      setSelectAll(false);
      setIsLoading(false);
      setSuccessMessage(message);
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'action en masse:', error);
      setIsLoading(false);
      setSuccessMessage(__('Erreur lors de l\'action en masse.', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }
  };

  // Fonction pour effacer les logs
  const handleClearLogs = async () => {
    setIsLoading(true);

    try {
      // Appeler le service pour effacer les logs
      await RedirectionsService.clearLogs();

      // Mettre à jour l'état local
      setLogs([]);

      setIsLoading(false);
      setSuccessMessage(__('Logs effacés avec succès !', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'effacement des logs:', error);
      setIsLoading(false);
      setSuccessMessage(__('Erreur lors de l\'effacement des logs.', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }
  };

  // Fonction pour importer des redirections
  const handleImport = async (data, format) => {
    setIsImporting(true);

    try {
      // Préparer les données pour l'importation
      let redirectionsToImport = [];

      if (format === 'csv') {
        // Traiter les données CSV
        const lines = data.split('\n');
        const headers = lines[0].split(',');

        for (let i = 1; i < lines.length; i++) {
          if (!lines[i].trim()) continue;

          const values = lines[i].split(',');
          const redirection = {};

          headers.forEach((header, index) => {
            redirection[header.trim()] = values[index] ? values[index].trim() : '';
          });

          redirectionsToImport.push(redirection);
        }
      } else if (format === 'json') {
        // Traiter les données JSON
        try {
          redirectionsToImport = JSON.parse(data);
        } catch (e) {
          throw new Error(__('Format JSON invalide', 'boss-seo'));
        }
      } else if (format === 'htaccess') {
        // Traiter les données .htaccess
        const lines = data.split('\n');

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();

          if (line.startsWith('Redirect')) {
            const parts = line.split(' ');
            if (parts.length >= 4) {
              const type = parts[1];
              const source = parts[2];
              const target = parts.slice(3).join(' ');

              redirectionsToImport.push({
                source,
                target,
                type,
                status: 'active'
              });
            }
          } else if (line.startsWith('RewriteRule')) {
            // Traiter les règles de réécriture (plus complexe, simplifié ici)
            const match = line.match(/RewriteRule \^(.*?)\$ (.*?) \[(.*?)\]/);
            if (match) {
              const source = '/' + match[1];
              const target = match[2] !== '-' ? match[2] : '';
              const flags = match[3].split(',');

              let type = '301';
              if (flags.includes('R=302')) type = '302';
              if (flags.includes('R=307')) type = '307';
              if (flags.includes('G')) type = '410';

              redirectionsToImport.push({
                source,
                target,
                type,
                status: 'active'
              });
            }
          }
        }
      }

      // Appeler le service pour importer les redirections
      const result = await RedirectionsService.importRedirections(redirectionsToImport);

      // Recharger les redirections
      const redirectionsData = await RedirectionsService.getRedirections();
      setRedirections(redirectionsData.redirections || []);

      setIsImporting(false);
      setSuccessMessage(result.message || __('Redirections importées avec succès !', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'importation des redirections:', error);
      setIsImporting(false);
      setSuccessMessage(__('Erreur lors de l\'importation des redirections.', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }
  };

  // Fonction pour exporter des redirections
  const handleExport = async (format) => {
    setIsExporting(true);

    try {
      // Appeler le service pour exporter les redirections
      const result = await RedirectionsService.exportRedirections(format);

      let exportData = '';

      if (format === 'csv' && result.format === 'csv') {
        // Convertir les données CSV en chaîne
        exportData = 'source,target,type,status,match_case,regex,notes\n';
        result.data.forEach(row => {
          exportData += row.join(',') + '\n';
        });
      } else if (format === 'json' && result.format === 'json') {
        // Convertir les données JSON en chaîne
        exportData = JSON.stringify(result.data, null, 2);
      } else if (format === 'htaccess') {
        // Générer un .htaccess à partir des données JSON
        result.data.forEach(redirection => {
          if (redirection.status === 'active') {
            if (redirection.type === '410') {
              exportData += `RewriteRule ^${redirection.source.replace(/^\//, '')}$ - [G,L]\n`;
            } else {
              exportData += `Redirect ${redirection.type} ${redirection.source} ${redirection.target}\n`;
            }
          }
        });
      }

      setIsExporting(false);
      return exportData;
    } catch (error) {
      console.error('Erreur lors de l\'exportation des redirections:', error);
      setIsExporting(false);
      setSuccessMessage(__('Erreur lors de l\'exportation des redirections.', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);

      return '';
    }
  };

  return (
    <div>
      {isLoading && (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      )}

      {showSuccess && (
        <Notice status="success" isDismissible={false} className="boss-mb-6">
          {successMessage}
        </Notice>
      )}

      <TabPanel
        className="boss-mb-6"
        activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
        onSelect={(tabName) => setActiveTab(tabName)}
        tabs={[
          {
            name: 'list',
            title: __('Liste', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'add',
            title: editingRedirection
              ? __('Modifier', 'boss-seo')
              : __('Ajouter', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'logs',
            title: __('Logs', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'import-export',
            title: __('Import/Export', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          }
        ]}
      >
        {(tab) => {
          if (tab.name === 'list') {
            return (
              <div>
                <RedirectionFilters
                  searchQuery={searchQuery}
                  setSearchQuery={setSearchQuery}
                  filterType={filterType}
                  setFilterType={setFilterType}
                  filterStatus={filterStatus}
                  setFilterStatus={setFilterStatus}
                />

                <RedirectionsList
                  redirections={redirections}
                  onEdit={(redirection) => {
                    setEditingRedirection(redirection);
                    setActiveTab('add');
                  }}
                  onDelete={handleDeleteRedirection}
                  onBulkAction={handleBulkAction}
                  searchQuery={searchQuery}
                  setSearchQuery={setSearchQuery}
                  filterType={filterType}
                  setFilterType={setFilterType}
                  filterStatus={filterStatus}
                  setFilterStatus={setFilterStatus}
                  selectedItems={selectedItems}
                  setSelectedItems={setSelectedItems}
                  selectAll={selectAll}
                  setSelectAll={setSelectAll}
                />
              </div>
            );
          } else if (tab.name === 'add') {
            return (
              <RedirectionForm
                editingRedirection={editingRedirection}
                onSave={handleSaveRedirection}
                onCancel={() => {
                  setEditingRedirection(null);
                  setActiveTab('list');
                }}
                notFoundUrls={notFoundUrls}
              />
            );
          } else if (tab.name === 'logs') {
            return (
              <RedirectionLogs
                logs={logs}
                onClearLogs={handleClearLogs}
                isLoading={isLoading}
              />
            );
          } else if (tab.name === 'import-export') {
            return (
              <RedirectionImportExport
                onImport={handleImport}
                onExport={handleExport}
                isImporting={isImporting}
                isExporting={isExporting}
              />
            );
          }
        }}
      </TabPanel>
    </div>
  );
};

export default Redirections;
