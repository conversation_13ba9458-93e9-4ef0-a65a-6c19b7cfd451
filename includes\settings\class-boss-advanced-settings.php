<?php
/**
 * Classe pour gérer les paramètres avancés du plugin Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.0.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/settings
 */

/**
 * Classe pour gérer les paramètres avancés du plugin Boss SEO.
 *
 * Cette classe gère les paramètres avancés du plugin, tels que le nettoyage
 * de l'en-tête, l'optimisation WordPress, etc.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/settings
 * <AUTHOR> SEO Team
 */
class Boss_Advanced_Settings {

    /**
     * Le nom du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_seo_advanced_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.0.0
     */
    public function register_hooks() {
        // Récupère les paramètres
        $settings = $this->get_settings();
        
        // Hooks pour le nettoyage de l'en-tête
        if ( $settings['advanced']['removeShortlinks'] ) {
            remove_action( 'wp_head', 'wp_shortlink_wp_head', 10 );
        }
        
        if ( $settings['advanced']['removeRSD'] ) {
            remove_action( 'wp_head', 'rsd_link' );
        }
        
        if ( $settings['advanced']['removeWLWManifest'] ) {
            remove_action( 'wp_head', 'wlwmanifest_link' );
        }
        
        if ( $settings['advanced']['cleanupHeader'] ) {
            remove_action( 'wp_head', 'wp_generator' );
            remove_action( 'wp_head', 'adjacent_posts_rel_link_wp_head', 10 );
            remove_action( 'wp_head', 'feed_links_extra', 3 );
        }
        
        // Hooks pour l'optimisation WordPress
        if ( $settings['advanced']['disableEmojis'] ) {
            add_action( 'init', array( $this, 'disable_emojis' ) );
        }
        
        if ( $settings['advanced']['disableEmbeds'] ) {
            add_action( 'init', array( $this, 'disable_embeds' ), 9999 );
        }
        
        // Hooks pour le sitemap et robots.txt
        if ( $settings['advanced']['enableSitemap'] ) {
            add_action( 'init', array( $this, 'enable_sitemap' ) );
        }
        
        if ( $settings['advanced']['enableRobotsTxt'] ) {
            add_filter( 'robots_txt', array( $this, 'custom_robots_txt' ), 10, 2 );
        }
    }

    /**
     * Récupère les paramètres avancés.
     *
     * @since    1.0.0
     * @return   array    Les paramètres avancés.
     */
    public function get_settings() {
        $default_settings = $this->get_default_settings();
        $settings = get_option( $this->option_prefix . 'settings', $default_settings );
        
        return wp_parse_args( $settings, $default_settings );
    }

    /**
     * Récupère les paramètres par défaut.
     *
     * @since    1.0.0
     * @return   array    Les paramètres par défaut.
     */
    public function get_default_settings() {
        return array(
            'advanced' => array(
                'removeShortlinks' => true,
                'removeRSD' => true,
                'removeWLWManifest' => true,
                'disableEmojis' => false,
                'disableEmbeds' => false,
                'cleanupHeader' => true,
                'enableSitemap' => true,
                'enableRobotsTxt' => true
            )
        );
    }

    /**
     * Enregistre les paramètres avancés.
     *
     * @since    1.0.0
     * @param    array    $settings    Les paramètres à enregistrer.
     * @return   bool                  True si les paramètres ont été enregistrés, false sinon.
     */
    public function save_settings( $settings ) {
        // Sanitize les paramètres
        $sanitized_settings = $this->sanitize_settings( $settings );
        
        // Enregistre les paramètres
        return update_option( $this->option_prefix . 'settings', $sanitized_settings );
    }

    /**
     * Sanitize les paramètres avancés.
     *
     * @since    1.0.0
     * @param    array    $settings    Les paramètres à sanitize.
     * @return   array                 Les paramètres sanitized.
     */
    public function sanitize_settings( $settings ) {
        $sanitized = array();
        
        // Sanitize les paramètres avancés
        if ( isset( $settings['advanced'] ) ) {
            $sanitized['advanced'] = array(
                'removeShortlinks' => isset( $settings['advanced']['removeShortlinks'] ) ? (bool) $settings['advanced']['removeShortlinks'] : true,
                'removeRSD' => isset( $settings['advanced']['removeRSD'] ) ? (bool) $settings['advanced']['removeRSD'] : true,
                'removeWLWManifest' => isset( $settings['advanced']['removeWLWManifest'] ) ? (bool) $settings['advanced']['removeWLWManifest'] : true,
                'disableEmojis' => isset( $settings['advanced']['disableEmojis'] ) ? (bool) $settings['advanced']['disableEmojis'] : false,
                'disableEmbeds' => isset( $settings['advanced']['disableEmbeds'] ) ? (bool) $settings['advanced']['disableEmbeds'] : false,
                'cleanupHeader' => isset( $settings['advanced']['cleanupHeader'] ) ? (bool) $settings['advanced']['cleanupHeader'] : true,
                'enableSitemap' => isset( $settings['advanced']['enableSitemap'] ) ? (bool) $settings['advanced']['enableSitemap'] : true,
                'enableRobotsTxt' => isset( $settings['advanced']['enableRobotsTxt'] ) ? (bool) $settings['advanced']['enableRobotsTxt'] : true
            );
        }
        
        return $sanitized;
    }

    /**
     * Désactive les emojis dans WordPress.
     *
     * @since    1.0.0
     */
    public function disable_emojis() {
        remove_action( 'wp_head', 'print_emoji_detection_script', 7 );
        remove_action( 'admin_print_scripts', 'print_emoji_detection_script' );
        remove_action( 'wp_print_styles', 'print_emoji_styles' );
        remove_action( 'admin_print_styles', 'print_emoji_styles' );
        remove_filter( 'the_content_feed', 'wp_staticize_emoji' );
        remove_filter( 'comment_text_rss', 'wp_staticize_emoji' );
        remove_filter( 'wp_mail', 'wp_staticize_emoji_for_email' );
        
        // Supprime le support des emojis dans TinyMCE
        add_filter( 'tiny_mce_plugins', array( $this, 'disable_emojis_tinymce' ) );
        
        // Supprime le DNS prefetch pour les emojis
        add_filter( 'emoji_svg_url', '__return_false' );
    }

    /**
     * Filtre les plugins TinyMCE pour supprimer le plugin emoji.
     *
     * @since    1.0.0
     * @param    array    $plugins    Les plugins TinyMCE.
     * @return   array                Les plugins TinyMCE filtrés.
     */
    public function disable_emojis_tinymce( $plugins ) {
        if ( is_array( $plugins ) ) {
            return array_diff( $plugins, array( 'wpemoji' ) );
        }
        
        return $plugins;
    }

    /**
     * Désactive les embeds dans WordPress.
     *
     * @since    1.0.0
     */
    public function disable_embeds() {
        // Supprime le support des embeds
        remove_action( 'rest_api_init', 'wp_oembed_register_route' );
        remove_filter( 'oembed_dataparse', 'wp_filter_oembed_result', 10 );
        remove_action( 'wp_head', 'wp_oembed_add_discovery_links' );
        remove_action( 'wp_head', 'wp_oembed_add_host_js' );
        
        // Supprime le filtre oEmbed
        add_filter( 'embed_oembed_discover', '__return_false' );
        
        // Supprime le script wp-embed.min.js
        add_action( 'wp_footer', function() {
            wp_deregister_script( 'wp-embed' );
        } );
        
        // Désactive l'API oEmbed
        add_filter( 'rewrite_rules_array', function( $rules ) {
            foreach ( $rules as $rule => $rewrite ) {
                if ( false !== strpos( $rewrite, 'embed=true' ) ) {
                    unset( $rules[ $rule ] );
                }
            }
            return $rules;
        } );
    }

    /**
     * Active le sitemap XML.
     *
     * @since    1.0.0
     */
    public function enable_sitemap() {
        // WordPress 5.5+ a un sitemap intégré, nous l'utilisons
        if ( function_exists( 'wp_sitemaps_get_server' ) ) {
            // Personnalise le sitemap intégré si nécessaire
            add_filter( 'wp_sitemaps_enabled', '__return_true' );
            
            // Ajoute des types de contenu personnalisés au sitemap
            add_filter( 'wp_sitemaps_post_types', array( $this, 'add_custom_post_types_to_sitemap' ) );
            
            // Ajoute des taxonomies personnalisées au sitemap
            add_filter( 'wp_sitemaps_taxonomies', array( $this, 'add_custom_taxonomies_to_sitemap' ) );
        } else {
            // Pour les versions antérieures de WordPress, nous pourrions implémenter notre propre sitemap
            // ou recommander un plugin de sitemap
        }
    }

    /**
     * Ajoute des types de contenu personnalisés au sitemap.
     *
     * @since    1.0.0
     * @param    array    $post_types    Les types de contenu.
     * @return   array                   Les types de contenu filtrés.
     */
    public function add_custom_post_types_to_sitemap( $post_types ) {
        // Récupère tous les types de contenu personnalisés publics
        $custom_post_types = get_post_types( array(
            'public'      => true,
            '_builtin'    => false
        ), 'objects' );
        
        // Ajoute chaque type de contenu personnalisé au sitemap
        foreach ( $custom_post_types as $post_type ) {
            $post_types[ $post_type->name ] = $post_type;
        }
        
        return $post_types;
    }

    /**
     * Ajoute des taxonomies personnalisées au sitemap.
     *
     * @since    1.0.0
     * @param    array    $taxonomies    Les taxonomies.
     * @return   array                   Les taxonomies filtrées.
     */
    public function add_custom_taxonomies_to_sitemap( $taxonomies ) {
        // Récupère toutes les taxonomies personnalisées publiques
        $custom_taxonomies = get_taxonomies( array(
            'public'      => true,
            '_builtin'    => false
        ), 'objects' );
        
        // Ajoute chaque taxonomie personnalisée au sitemap
        foreach ( $custom_taxonomies as $taxonomy ) {
            $taxonomies[ $taxonomy->name ] = $taxonomy;
        }
        
        return $taxonomies;
    }

    /**
     * Personnalise le fichier robots.txt.
     *
     * @since    1.0.0
     * @param    string    $output    Le contenu du fichier robots.txt.
     * @param    bool      $public    Si le site est indexable.
     * @return   string               Le contenu personnalisé du fichier robots.txt.
     */
    public function custom_robots_txt( $output, $public ) {
        if ( $public ) {
            $output = "User-agent: *\n";
            $output .= "Disallow: /wp-admin/\n";
            $output .= "Disallow: /wp-includes/\n";
            $output .= "Disallow: /wp-content/plugins/\n";
            $output .= "Disallow: /wp-content/themes/\n";
            $output .= "Disallow: /wp-login.php\n";
            $output .= "Disallow: /wp-register.php\n";
            $output .= "Disallow: /xmlrpc.php\n";
            $output .= "Disallow: /readme.html\n";
            $output .= "Disallow: *?s=*\n";
            $output .= "Disallow: *?p=*\n";
            
            // Ajoute le sitemap
            if ( function_exists( 'get_sitemap_url' ) ) {
                $output .= "\nSitemap: " . get_sitemap_url( 'index' ) . "\n";
            } else {
                $output .= "\nSitemap: " . home_url( '/sitemap.xml' ) . "\n";
            }
        }
        
        return $output;
    }
}
