import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Dashicon,
  Notice,
  Panel,
  PanelBody,
  PanelRow,
  SelectControl,
  Spinner,
  TabPanel,
  TextareaControl,
  TextControl,
  ToggleControl,
  Tooltip
} from '@wordpress/components';

/**
 * Composant pour afficher un élément de l'historique des générations AI
 */
const HistoryItem = ({ item, onUse }) => {
  // Fonction pour formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Fonction pour obtenir l'icône en fonction du type de génération
  const getTypeIcon = (type) => {
    switch (type) {
      case 'draft':
        return 'media-document';
      case 'enhance':
        return 'editor-spellcheck';
      case 'recycle':
        return 'update';
      case 'template':
        return 'layout';
      default:
        return 'admin-generic';
    }
  };

  // Fonction pour obtenir le libellé en fonction du type de génération
  const getTypeLabel = (type) => {
    switch (type) {
      case 'draft':
        return __('Brouillon', 'boss-seo');
      case 'enhance':
        return __('Enrichissement', 'boss-seo');
      case 'recycle':
        return __('Recyclage', 'boss-seo');
      case 'template':
        return __('Template', 'boss-seo');
      default:
        return type;
    }
  };

  // État pour gérer l'expansion de l'élément
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-mb-4 boss-overflow-hidden">
      <div
        className="boss-p-4 boss-flex boss-justify-between boss-items-center boss-cursor-pointer boss-bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="boss-flex boss-items-center">
          <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-boss-primary boss-bg-opacity-10 boss-flex boss-items-center boss-justify-center boss-mr-3">
            <Dashicon icon={getTypeIcon(item.type)} className="boss-text-boss-primary" />
          </div>
          <div>
            <div className="boss-font-medium boss-text-boss-dark">
              {getTypeLabel(item.type)}
            </div>
            <div className="boss-text-xs boss-text-boss-gray">
              {formatDate(item.date)}
            </div>
          </div>
        </div>
        <div className="boss-flex boss-items-center">
          <Button
            isSmall
            isSecondary
            onClick={(e) => {
              e.stopPropagation();
              onUse(item);
            }}
            className="boss-mr-2"
          >
            {__('Utiliser', 'boss-seo')}
          </Button>
          <Dashicon
            icon={isExpanded ? 'arrow-up-alt2' : 'arrow-down-alt2'}
            className="boss-text-boss-gray"
          />
        </div>
      </div>

      {isExpanded && (
        <div className="boss-p-4 boss-border-t boss-border-gray-200">
          <div className="boss-mb-3">
            <div className="boss-text-sm boss-font-medium boss-text-boss-gray boss-mb-1">
              {__('Prompt', 'boss-seo')}
            </div>
            <div className="boss-bg-gray-50 boss-p-3 boss-rounded boss-text-sm boss-text-boss-dark">
              {item.prompt}
            </div>
          </div>

          <div>
            <div className="boss-text-sm boss-font-medium boss-text-boss-gray boss-mb-1">
              {__('Contenu généré', 'boss-seo')}
            </div>
            <div className="boss-bg-gray-50 boss-p-3 boss-rounded boss-text-sm boss-text-boss-dark boss-max-h-60 boss-overflow-y-auto">
              <div dangerouslySetInnerHTML={{ __html: item.content }} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Composant principal de l'AI Content Studio
 */
const AiContentStudio = ({ content, focusKeyword, isLoading, aiHistory, onGenerateContent }) => {
  // État pour gérer les options de génération
  const [generationType, setGenerationType] = useState('draft');
  const [prompt, setPrompt] = useState('');
  const [template, setTemplate] = useState('article');
  const [options, setOptions] = useState({
    includeFocusKeyword: true,
    optimizeForSeo: true,
    includeHeadings: true,
    includeIntroConclusion: true,
    tone: 'professional'
  });

  // Effet pour mettre à jour le prompt en fonction du type de génération et des options
  useEffect(() => {
    let newPrompt = '';

    if (generationType === 'draft') {
      newPrompt = `Créer un article complet sur "${focusKeyword || 'le sujet'}"`;

      if (options.includeHeadings) {
        newPrompt += ' avec une structure claire et des sous-titres';
      }

      if (options.includeIntroConclusion) {
        newPrompt += ', une introduction et une conclusion';
      }

      if (options.optimizeForSeo) {
        newPrompt += `. Optimiser pour le référencement avec le mot-clé "${focusKeyword || 'principal'}"`;
      }
    } else if (generationType === 'enhance') {
      newPrompt = `Améliorer et enrichir le contenu existant`;

      if (options.optimizeForSeo) {
        newPrompt += ` en l'optimisant pour le référencement avec le mot-clé "${focusKeyword || 'principal'}"`;
      }

      if (options.includeHeadings) {
        newPrompt += ', ajouter des sous-titres pertinents';
      }
    } else if (generationType === 'recycle') {
      newPrompt = `Recycler et mettre à jour le contenu existant pour le rendre plus actuel`;

      if (options.optimizeForSeo) {
        newPrompt += ` et optimisé pour le référencement avec le mot-clé "${focusKeyword || 'principal'}"`;
      }
    }

    // Ajouter le ton
    switch (options.tone) {
      case 'professional':
        newPrompt += '. Utiliser un ton professionnel et informatif';
        break;
      case 'conversational':
        newPrompt += '. Utiliser un ton conversationnel et accessible';
        break;
      case 'persuasive':
        newPrompt += '. Utiliser un ton persuasif et convaincant';
        break;
      case 'educational':
        newPrompt += '. Utiliser un ton éducatif et explicatif';
        break;
    }

    newPrompt += '.';

    setPrompt(newPrompt);
  }, [generationType, focusKeyword, options]);

  // Fonction pour utiliser un contenu généré précédemment
  const handleUseGeneratedContent = (item) => {
    // Dans une implémentation réelle, cette fonction mettrait à jour le contenu dans l'éditeur
    console.log('Using generated content:', item);
  };

  // État pour les erreurs
  const [error, setError] = useState('');

  // Fonction pour générer du contenu
  const handleGenerate = () => {
    // Réinitialiser les erreurs
    setError('');

    // Vérifier que le prompt n'est pas vide
    if (!prompt.trim()) {
      setError(__('Le prompt ne peut pas être vide.', 'boss-seo'));
      return;
    }

    // Vérifier que le prompt n'est pas trop court
    if (prompt.trim().length < 10) {
      setError(__('Le prompt est trop court. Veuillez fournir plus de détails.', 'boss-seo'));
      return;
    }

    // Appeler la fonction de génération de contenu
    onGenerateContent(prompt, generationType);
  };

  return (
    <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-6">
      {/* Colonne de gauche : Options de génération */}
      <div className="boss-col-span-1">
        <Card className="boss-mb-6">
          <CardHeader>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Options de génération', 'boss-seo')}
            </h3>
          </CardHeader>
          <CardBody>
            {error && (
              <Notice status="error" isDismissible={true} onRemove={() => setError('')} className="boss-mb-4">
                {error}
              </Notice>
            )}

            <SelectControl
              label={__('Type de génération', 'boss-seo')}
              value={generationType}
              options={[
                { label: __('Brouillon complet', 'boss-seo'), value: 'draft' },
                { label: __('Enrichir le contenu existant', 'boss-seo'), value: 'enhance' },
                { label: __('Recycler le contenu ancien', 'boss-seo'), value: 'recycle' },
                { label: __('Utiliser un template', 'boss-seo'), value: 'template' }
              ]}
              onChange={setGenerationType}
              className="boss-mb-4"
            />

            {generationType === 'template' && (
              <SelectControl
                label={__('Template', 'boss-seo')}
                value={template}
                options={[
                  { label: __('Article de blog', 'boss-seo'), value: 'article' },
                  { label: __('Page produit', 'boss-seo'), value: 'product' },
                  { label: __('Page à propos', 'boss-seo'), value: 'about' },
                  { label: __('FAQ', 'boss-seo'), value: 'faq' },
                  { label: __('Étude de cas', 'boss-seo'), value: 'case-study' }
                ]}
                onChange={setTemplate}
                className="boss-mb-4"
              />
            )}

            <div className="boss-mb-4">
              <h4 className="boss-text-sm boss-font-medium boss-text-boss-gray boss-mb-2">
                {__('Options avancées', 'boss-seo')}
              </h4>

              <ToggleControl
                label={__('Inclure le mot-clé principal', 'boss-seo')}
                checked={options.includeFocusKeyword}
                onChange={() => setOptions({...options, includeFocusKeyword: !options.includeFocusKeyword})}
                className="boss-mb-3"
              />

              <ToggleControl
                label={__('Optimiser pour le SEO', 'boss-seo')}
                checked={options.optimizeForSeo}
                onChange={() => setOptions({...options, optimizeForSeo: !options.optimizeForSeo})}
                className="boss-mb-3"
              />

              <ToggleControl
                label={__('Inclure des sous-titres', 'boss-seo')}
                checked={options.includeHeadings}
                onChange={() => setOptions({...options, includeHeadings: !options.includeHeadings})}
                className="boss-mb-3"
              />

              <ToggleControl
                label={__('Inclure introduction et conclusion', 'boss-seo')}
                checked={options.includeIntroConclusion}
                onChange={() => setOptions({...options, includeIntroConclusion: !options.includeIntroConclusion})}
                className="boss-mb-3"
              />

              <SelectControl
                label={__('Ton', 'boss-seo')}
                value={options.tone}
                options={[
                  { label: __('Professionnel', 'boss-seo'), value: 'professional' },
                  { label: __('Conversationnel', 'boss-seo'), value: 'conversational' },
                  { label: __('Persuasif', 'boss-seo'), value: 'persuasive' },
                  { label: __('Éducatif', 'boss-seo'), value: 'educational' }
                ]}
                onChange={(value) => setOptions({...options, tone: value})}
              />
            </div>

            <TextareaControl
              label={__('Prompt personnalisé', 'boss-seo')}
              value={prompt}
              onChange={setPrompt}
              help={__('Vous pouvez modifier le prompt généré automatiquement ou écrire le vôtre.', 'boss-seo')}
              className="boss-mb-4"
            />

            <Button
              isPrimary
              isBusy={isLoading}
              disabled={isLoading || !prompt}
              onClick={handleGenerate}
              className="boss-w-full boss-justify-center"
            >
              {isLoading ? __('Génération en cours...', 'boss-seo') : __('Générer le contenu', 'boss-seo')}
            </Button>
          </CardBody>
        </Card>

        {/* Informations sur les crédits AI */}
        <Card>
          <CardHeader>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Crédits AI', 'boss-seo')}
            </h3>
          </CardHeader>
          <CardBody>
            <div className="boss-flex boss-justify-between boss-items-center boss-mb-4">
              <span className="boss-text-boss-gray">
                {__('Crédits restants', 'boss-seo')}
              </span>
              <span className="boss-text-xl boss-font-bold boss-text-boss-dark">
                42
              </span>
            </div>

            <div className="boss-flex boss-justify-between boss-items-center boss-mb-4">
              <span className="boss-text-boss-gray">
                {__('Générations ce mois', 'boss-seo')}
              </span>
              <span className="boss-text-boss-dark">
                58
              </span>
            </div>

            <Button
              isSecondary
              className="boss-w-full boss-justify-center"
            >
              {__('Acheter plus de crédits', 'boss-seo')}
            </Button>
          </CardBody>
        </Card>
      </div>

      {/* Colonne de droite : Historique des générations */}
      <div className="boss-col-span-2">
        <Card>
          <CardHeader>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Historique des générations', 'boss-seo')}
            </h3>
          </CardHeader>
          <CardBody>
            {aiHistory.length === 0 ? (
              <div className="boss-text-center boss-p-8">
                <Dashicon icon="admin-tools" className="boss-text-4xl boss-text-boss-gray boss-mb-2" />
                <p className="boss-text-boss-gray">
                  {__('Aucune génération dans l\'historique.', 'boss-seo')}
                </p>
                <p className="boss-text-sm boss-text-boss-gray boss-mt-2">
                  {__('Utilisez les options à gauche pour générer du contenu avec l\'IA.', 'boss-seo')}
                </p>
              </div>
            ) : (
              <div>
                {aiHistory.map((item, index) => (
                  <HistoryItem
                    key={index}
                    item={item}
                    onUse={handleUseGeneratedContent}
                  />
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default AiContentStudio;
