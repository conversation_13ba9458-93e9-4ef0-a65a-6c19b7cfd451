import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  TabPanel,
  Notice,
  Spinner
} from '@wordpress/components';

// Importer les sous-composants
import HeadersList from './headers/HeadersList';
import HeaderForm from './headers/HeaderForm';
import HeadersTest from './headers/HeadersTest';
import PreconnectSettings from './headers/PreconnectSettings';

const HttpHeaders = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isRunningTest, setIsRunningTest] = useState(false);
  const [headers, setHeaders] = useState([]);
  const [commonHeaders, setCommonHeaders] = useState([]);
  const [preconnectUrls, setPreconnectUrls] = useState([]);
  const [editingHeader, setEditingHeader] = useState(null);
  const [testResults, setTestResults] = useState(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('headers-list');
  const [settings, setSettings] = useState({
    enableGzip: true,
    enableBrotli: false,
    enableBrowserCache: true,
    imageCacheDuration: '1m',
    assetsCacheDuration: '1w',
    includeEtag: true
  });

  // Charger les données
  useEffect(() => {
    const loadHeadersData = async () => {
      try {
        setIsLoading(true);

        // Charger les données réelles depuis l'API
        const response = await HttpHeadersService.getHeaders();

        if (response.success) {
          setHeaders(response.data.headers || []);
          setCommonHeaders(response.data.commonHeaders || []);
          setPreconnectUrls(response.data.preconnectUrls || []);
        } else {
          console.error('Erreur lors du chargement des en-têtes:', response.message);
          // Initialiser avec des données vides en cas d'erreur
          setHeaders([]);
          setCommonHeaders([]);
          setPreconnectUrls([]);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des en-têtes:', error);
        setHeaders([]);
        setCommonHeaders([]);
        setPreconnectUrls([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadHeadersData();
  }, []);

  // Fonction pour ajouter ou mettre à jour un en-tête
  const handleSaveHeader = (headerData) => {
    setIsSaving(true);

    // Simuler l'enregistrement
    setTimeout(() => {
      if (headerData.id) {
        // Mise à jour d'un en-tête existant
        const updatedHeaders = headers.map(header => {
          if (header.id === headerData.id) {
            return headerData;
          }
          return header;
        });

        setHeaders(updatedHeaders);
        setSuccessMessage(__('En-tête mis à jour avec succès !', 'boss-seo'));
      } else {
        // Ajout d'un nouvel en-tête
        const newHeader = {
          ...headerData,
          id: headers.length > 0 ? Math.max(...headers.map(h => h.id)) + 1 : 1
        };

        setHeaders([...headers, newHeader]);
        setSuccessMessage(__('En-tête ajouté avec succès !', 'boss-seo'));
      }

      setEditingHeader(null);
      setIsSaving(false);
      setShowSuccess(true);
      setActiveTab('headers-list');

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  // Fonction pour supprimer un en-tête
  const handleDeleteHeader = (id) => {
    setIsSaving(true);

    // Simuler la suppression
    setTimeout(() => {
      const updatedHeaders = headers.filter(header => header.id !== id);
      setHeaders(updatedHeaders);
      setIsSaving(false);
      setSuccessMessage(__('En-tête supprimé avec succès !', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  // Fonction pour activer/désactiver un en-tête
  const handleToggleHeader = (id) => {
    setIsSaving(true);

    // Simuler la mise à jour
    setTimeout(() => {
      const updatedHeaders = headers.map(header => {
        if (header.id === id) {
          return {
            ...header,
            active: !header.active
          };
        }
        return header;
      });

      setHeaders(updatedHeaders);
      setIsSaving(false);
      setSuccessMessage(__('Statut de l\'en-tête mis à jour avec succès !', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  // Fonction pour exécuter un test d'en-têtes
  const handleRunTest = async (url) => {
    setIsRunningTest(true);
    setTestResults(null);

    try {
      const response = await HttpHeadersService.testHeaders(url);

      if (response.success) {
        setTestResults(response.data);
      } else {
        console.error('Erreur lors du test des en-têtes:', response.message);
        setTestResults({
          url: url,
          statusCode: 0,
          responseTime: 0,
          headers: [],
          recommendations: [],
          error: response.message || __('Erreur lors du test des en-têtes', 'boss-seo')
        });
      }
    } catch (error) {
      console.error('Erreur lors du test des en-têtes:', error);
      setTestResults({
        url: url,
        statusCode: 0,
        responseTime: 0,
        headers: [],
        recommendations: [],
        error: __('Erreur de connexion lors du test', 'boss-seo')
      });
    } finally {
      setIsRunningTest(false);
    }
  };

  // Fonction pour ajouter une URL de préconnexion
  const handleAddPreconnect = (url, type) => {
    setPreconnectUrls([...preconnectUrls, { url, type }]);
  };

  // Fonction pour supprimer une URL de préconnexion
  const handleRemovePreconnect = (index) => {
    const updatedUrls = [...preconnectUrls];
    updatedUrls.splice(index, 1);
    setPreconnectUrls(updatedUrls);
  };

  // Fonction pour enregistrer les paramètres
  const handleSaveSettings = (newSettings) => {
    setIsSaving(true);

    // Simuler l'enregistrement
    setTimeout(() => {
      setSettings(newSettings);
      setIsSaving(false);
      setSuccessMessage(__('Paramètres enregistrés avec succès !', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {successMessage}
            </Notice>
          )}

          <TabPanel
            className="boss-mb-6"
            activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
            onSelect={(tabName) => setActiveTab(tabName)}
            tabs={[
              {
                name: 'headers-list',
                title: __('En-têtes HTTP', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'add-header',
                title: editingHeader
                  ? __('Modifier l\'en-tête', 'boss-seo')
                  : __('Ajouter un en-tête', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'preconnect',
                title: __('Préconnexion & Cache', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'test',
                title: __('Tester', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              }
            ]}
          >
            {(tab) => {
              if (tab.name === 'headers-list') {
                return (
                  <HeadersList
                    headers={headers}
                    onEdit={(header) => {
                      setEditingHeader(header);
                      setActiveTab('add-header');
                    }}
                    onDelete={handleDeleteHeader}
                    onToggle={handleToggleHeader}
                    searchQuery={searchQuery}
                    setSearchQuery={setSearchQuery}
                  />
                );
              } else if (tab.name === 'add-header') {
                return (
                  <HeaderForm
                    editingHeader={editingHeader}
                    onSave={handleSaveHeader}
                    onCancel={() => {
                      setEditingHeader(null);
                      setActiveTab('headers-list');
                    }}
                    commonHeaders={commonHeaders}
                  />
                );
              } else if (tab.name === 'preconnect') {
                return (
                  <PreconnectSettings
                    preconnectUrls={preconnectUrls}
                    onAddPreconnect={handleAddPreconnect}
                    onRemovePreconnect={handleRemovePreconnect}
                    onSaveSettings={handleSaveSettings}
                    isSaving={isSaving}
                    settings={settings}
                  />
                );
              } else if (tab.name === 'test') {
                return (
                  <HeadersTest
                    onRunTest={handleRunTest}
                    isRunningTest={isRunningTest}
                    testResults={testResults}
                  />
                );
              }
            }}
          </TabPanel>
        </div>
      )}
    </div>
  );
};

export default HttpHeaders;
