<?php
/**
 * Classe pour gérer les paramètres généraux du plugin Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.0.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/settings
 */

/**
 * Classe pour gérer les paramètres généraux du plugin Boss SEO.
 *
 * Cette classe gère les paramètres généraux du plugin, tels que le titre du site,
 * la description, le séparateur de titre, les méta de la page d'accueil, etc.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/settings
 * <AUTHOR> SEO Team
 */
class Boss_General_Settings {

    /**
     * Le nom du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_seo_general_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.0.0
     */
    public function register_hooks() {
        // Hooks pour les paramètres généraux
        add_action( 'init', array( $this, 'apply_indexation_settings' ) );
        add_action( 'wp_head', array( $this, 'output_meta_tags' ), 1 );
        add_filter( 'document_title_separator', array( $this, 'filter_title_separator' ) );
        add_filter( 'the_content', array( $this, 'process_content' ) );
        add_filter( 'wp_img_tag_add_srcset_and_sizes_attr', array( $this, 'maybe_add_image_attributes' ), 10, 2 );
        add_filter( 'the_content', array( $this, 'process_links' ) );
    }

    /**
     * Récupère les paramètres généraux.
     *
     * @since    1.0.0
     * @return   array    Les paramètres généraux.
     */
    public function get_settings() {
        $default_settings = $this->get_default_settings();
        $settings = get_option( $this->option_prefix . 'settings', $default_settings );

        return wp_parse_args( $settings, $default_settings );
    }

    /**
     * Récupère les paramètres par défaut.
     *
     * @since    1.0.0
     * @return   array    Les paramètres par défaut.
     */
    public function get_default_settings() {
        return array(
            'general' => array(
                'siteTitle' => get_bloginfo( 'name' ),
                'siteDescription' => get_bloginfo( 'description' ),
                'separator' => '|',
                'homePageMetaTitle' => '%%site_title%% %%separator%% %%site_description%%',
                'homePageMetaDescription' => get_bloginfo( 'description' ),
                'noindexArchives' => false,
                'noindexCategories' => false,
                'noindexTags' => false,
                'noindexAuthorPages' => true
            ),
            'content' => array(
                'autoAddAltText' => true,
                'autoAddTitleAttribute' => false,
                'addNoopener' => true,
                'openExternalLinksInNewTab' => true,
                'maxTitleLength' => 60,
                'maxDescriptionLength' => 160,
                'maxKeywords' => 5
            )
        );
    }

    /**
     * Enregistre les paramètres généraux.
     *
     * @since    1.0.0
     * @param    array    $settings    Les paramètres à enregistrer.
     * @return   bool                  True si les paramètres ont été enregistrés, false sinon.
     */
    public function save_settings( $settings ) {
        // Sanitize les paramètres
        $sanitized_settings = $this->sanitize_settings( $settings );

        // Enregistre les paramètres
        return update_option( $this->option_prefix . 'settings', $sanitized_settings );
    }

    /**
     * Sanitize les paramètres généraux.
     *
     * @since    1.0.0
     * @param    array    $settings    Les paramètres à sanitize.
     * @return   array                 Les paramètres sanitized.
     */
    public function sanitize_settings( $settings ) {
        $sanitized = array();

        // Sanitize les paramètres généraux
        if ( isset( $settings['general'] ) ) {
            $sanitized['general'] = array(
                'siteTitle' => isset( $settings['general']['siteTitle'] ) ? sanitize_text_field( $settings['general']['siteTitle'] ) : '',
                'siteDescription' => isset( $settings['general']['siteDescription'] ) ? sanitize_text_field( $settings['general']['siteDescription'] ) : '',
                'separator' => isset( $settings['general']['separator'] ) ? sanitize_text_field( $settings['general']['separator'] ) : '|',
                'homePageMetaTitle' => isset( $settings['general']['homePageMetaTitle'] ) ? sanitize_text_field( $settings['general']['homePageMetaTitle'] ) : '',
                'homePageMetaDescription' => isset( $settings['general']['homePageMetaDescription'] ) ? sanitize_text_field( $settings['general']['homePageMetaDescription'] ) : '',
                'noindexArchives' => isset( $settings['general']['noindexArchives'] ) ? (bool) $settings['general']['noindexArchives'] : false,
                'noindexCategories' => isset( $settings['general']['noindexCategories'] ) ? (bool) $settings['general']['noindexCategories'] : false,
                'noindexTags' => isset( $settings['general']['noindexTags'] ) ? (bool) $settings['general']['noindexTags'] : false,
                'noindexAuthorPages' => isset( $settings['general']['noindexAuthorPages'] ) ? (bool) $settings['general']['noindexAuthorPages'] : true
            );
        }

        // Sanitize les paramètres de contenu
        if ( isset( $settings['content'] ) ) {
            $sanitized['content'] = array(
                'autoAddAltText' => isset( $settings['content']['autoAddAltText'] ) ? (bool) $settings['content']['autoAddAltText'] : true,
                'autoAddTitleAttribute' => isset( $settings['content']['autoAddTitleAttribute'] ) ? (bool) $settings['content']['autoAddTitleAttribute'] : false,
                'addNoopener' => isset( $settings['content']['addNoopener'] ) ? (bool) $settings['content']['addNoopener'] : true,
                'openExternalLinksInNewTab' => isset( $settings['content']['openExternalLinksInNewTab'] ) ? (bool) $settings['content']['openExternalLinksInNewTab'] : true,
                'maxTitleLength' => isset( $settings['content']['maxTitleLength'] ) ? absint( $settings['content']['maxTitleLength'] ) : 60,
                'maxDescriptionLength' => isset( $settings['content']['maxDescriptionLength'] ) ? absint( $settings['content']['maxDescriptionLength'] ) : 160,
                'maxKeywords' => isset( $settings['content']['maxKeywords'] ) ? absint( $settings['content']['maxKeywords'] ) : 5
            );
        }

        return $sanitized;
    }

    /**
     * Applique les paramètres d'indexation.
     *
     * @since    1.0.0
     */
    public function apply_indexation_settings() {
        $settings = $this->get_settings();

        // Applique les paramètres d'indexation
        if ( $settings['general']['noindexArchives'] && is_archive() && ! is_category() && ! is_tag() && ! is_author() ) {
            add_filter( 'wp_robots', function( $robots ) {
                $robots['noindex'] = true;
                return $robots;
            } );
        }

        if ( $settings['general']['noindexCategories'] && is_category() ) {
            add_filter( 'wp_robots', function( $robots ) {
                $robots['noindex'] = true;
                return $robots;
            } );
        }

        if ( $settings['general']['noindexTags'] && is_tag() ) {
            add_filter( 'wp_robots', function( $robots ) {
                $robots['noindex'] = true;
                return $robots;
            } );
        }

        if ( $settings['general']['noindexAuthorPages'] && is_author() ) {
            add_filter( 'wp_robots', function( $robots ) {
                $robots['noindex'] = true;
                return $robots;
            } );
        }
    }

    /**
     * Génère et affiche les balises meta dans l'en-tête.
     *
     * @since    1.0.0
     */
    public function output_meta_tags() {
        $settings = $this->get_settings();

        // Page d'accueil
        if ( is_front_page() && is_home() ) {
            $title = $this->parse_template_tags( $settings['general']['homePageMetaTitle'] );
            $description = $this->parse_template_tags( $settings['general']['homePageMetaDescription'] );

            if ( ! empty( $title ) ) {
                echo '<meta name="title" content="' . esc_attr( $title ) . '" />' . "\n";
            }

            if ( ! empty( $description ) ) {
                echo '<meta name="description" content="' . esc_attr( $description ) . '" />' . "\n";
            }
        }
        // Articles et pages individuels
        elseif ( is_singular() ) {
            $this->output_singular_meta_tags();
        }
        // Pages d'archives (catégories, tags, etc.)
        elseif ( is_archive() ) {
            $this->output_archive_meta_tags();
        }
    }

    /**
     * Affiche les balises meta pour les articles et pages individuels.
     *
     * @since    1.2.0
     */
    private function output_singular_meta_tags() {
        global $post;

        if ( ! $post ) {
            return;
        }

        // Récupérer les métadonnées SEO personnalisées
        $seo_title = get_post_meta( $post->ID, '_boss_seo_title', true );
        $meta_description = get_post_meta( $post->ID, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post->ID, '_boss_seo_focus_keyword', true );
        $secondary_keywords = get_post_meta( $post->ID, '_boss_seo_secondary_keywords', true );
        $canonical_url = get_post_meta( $post->ID, '_boss_seo_canonical_url', true );
        $robots_index = get_post_meta( $post->ID, '_boss_seo_robots_index', true ) ?: 'index';
        $robots_follow = get_post_meta( $post->ID, '_boss_seo_robots_follow', true ) ?: 'follow';

        // Titre SEO
        if ( ! empty( $seo_title ) ) {
            // Modifier le titre de la page
            add_filter( 'pre_get_document_title', function() use ( $seo_title ) {
                return $seo_title;
            }, 10 );
        }

        // Meta description
        if ( ! empty( $meta_description ) ) {
            echo '<meta name="description" content="' . esc_attr( $meta_description ) . '" />' . "\n";
        }

        // Mots-clés (keywords)
        $all_keywords = array();
        if ( ! empty( $focus_keyword ) ) {
            $all_keywords[] = $focus_keyword;
        }
        if ( ! empty( $secondary_keywords ) ) {
            $secondary_array = is_array( $secondary_keywords ) ? $secondary_keywords : explode( ',', $secondary_keywords );
            $all_keywords = array_merge( $all_keywords, array_map( 'trim', $secondary_array ) );
        }
        if ( ! empty( $all_keywords ) ) {
            echo '<meta name="keywords" content="' . esc_attr( implode( ', ', $all_keywords ) ) . '" />' . "\n";
        }

        // URL canonique
        if ( ! empty( $canonical_url ) ) {
            echo '<link rel="canonical" href="' . esc_url( $canonical_url ) . '" />' . "\n";
        } else {
            echo '<link rel="canonical" href="' . esc_url( get_permalink( $post->ID ) ) . '" />' . "\n";
        }

        // Robots meta
        $robots_content = array();
        if ( $robots_index === 'noindex' ) {
            $robots_content[] = 'noindex';
        } else {
            $robots_content[] = 'index';
        }
        if ( $robots_follow === 'nofollow' ) {
            $robots_content[] = 'nofollow';
        } else {
            $robots_content[] = 'follow';
        }
        echo '<meta name="robots" content="' . esc_attr( implode( ', ', $robots_content ) ) . '" />' . "\n";

        // Open Graph
        $this->output_open_graph_tags( $post );
    }

    /**
     * Affiche les balises meta pour les pages d'archives.
     *
     * @since    1.2.0
     */
    private function output_archive_meta_tags() {
        if ( is_category() ) {
            $term = get_queried_object();
            $description = $term->description ?: sprintf( __( 'Articles de la catégorie %s', 'boss-seo' ), $term->name );
            echo '<meta name="description" content="' . esc_attr( $description ) . '" />' . "\n";
        } elseif ( is_tag() ) {
            $term = get_queried_object();
            $description = $term->description ?: sprintf( __( 'Articles avec le tag %s', 'boss-seo' ), $term->name );
            echo '<meta name="description" content="' . esc_attr( $description ) . '" />' . "\n";
        } elseif ( is_author() ) {
            $author = get_queried_object();
            $description = sprintf( __( 'Articles de %s', 'boss-seo' ), $author->display_name );
            echo '<meta name="description" content="' . esc_attr( $description ) . '" />' . "\n";
        }

        // Robots pour les archives
        echo '<meta name="robots" content="index, follow" />' . "\n";
    }

    /**
     * Affiche les balises Open Graph.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post actuel.
     */
    private function output_open_graph_tags( $post ) {
        // Open Graph Title
        $og_title = get_post_meta( $post->ID, '_boss_seo_og_title', true );
        if ( empty( $og_title ) ) {
            $og_title = get_post_meta( $post->ID, '_boss_seo_title', true ) ?: $post->post_title;
        }
        echo '<meta property="og:title" content="' . esc_attr( $og_title ) . '" />' . "\n";

        // Open Graph Description
        $og_description = get_post_meta( $post->ID, '_boss_seo_og_description', true );
        if ( empty( $og_description ) ) {
            $og_description = get_post_meta( $post->ID, '_boss_seo_meta_description', true );
        }
        if ( empty( $og_description ) ) {
            $og_description = wp_trim_words( strip_tags( $post->post_content ), 30 );
        }
        echo '<meta property="og:description" content="' . esc_attr( $og_description ) . '" />' . "\n";

        // Open Graph URL
        echo '<meta property="og:url" content="' . esc_url( get_permalink( $post->ID ) ) . '" />' . "\n";

        // Open Graph Type
        echo '<meta property="og:type" content="article" />' . "\n";

        // Open Graph Image
        $og_image = get_post_meta( $post->ID, '_boss_seo_og_image', true );
        if ( empty( $og_image ) && has_post_thumbnail( $post->ID ) ) {
            $og_image = get_the_post_thumbnail_url( $post->ID, 'large' );
        }
        if ( ! empty( $og_image ) ) {
            echo '<meta property="og:image" content="' . esc_url( $og_image ) . '" />' . "\n";
        }

        // Twitter Card
        echo '<meta name="twitter:card" content="summary_large_image" />' . "\n";
        echo '<meta name="twitter:title" content="' . esc_attr( $og_title ) . '" />' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr( $og_description ) . '" />' . "\n";
        if ( ! empty( $og_image ) ) {
            echo '<meta name="twitter:image" content="' . esc_url( $og_image ) . '" />' . "\n";
        }
    }

    /**
     * Filtre le séparateur de titre.
     *
     * @since    1.0.0
     * @param    string    $separator    Le séparateur de titre.
     * @return   string                  Le séparateur de titre filtré.
     */
    public function filter_title_separator( $separator ) {
        $settings = $this->get_settings();

        if ( ! empty( $settings['general']['separator'] ) ) {
            return $settings['general']['separator'];
        }

        return $separator;
    }

    /**
     * Traite le contenu pour ajouter des attributs aux images.
     *
     * @since    1.0.0
     * @param    string    $content    Le contenu à traiter.
     * @return   string                Le contenu traité.
     */
    public function process_content( $content ) {
        $settings = $this->get_settings();

        // Ajoute automatiquement le texte alternatif aux images
        if ( $settings['content']['autoAddAltText'] ) {
            $content = $this->add_alt_text_to_images( $content );
        }

        return $content;
    }

    /**
     * Ajoute des attributs aux balises d'image.
     *
     * @since    1.0.0
     * @param    bool      $add        Si les attributs doivent être ajoutés.
     * @param    string    $image      La balise d'image.
     * @return   bool                  Si les attributs doivent être ajoutés.
     */
    public function maybe_add_image_attributes( $add, $image ) {
        $settings = $this->get_settings();

        // Ajoute automatiquement l'attribut title aux images
        if ( $settings['content']['autoAddTitleAttribute'] ) {
            // Extrait l'attribut alt
            preg_match( '/alt=[\'"]([^\'"]+)[\'"]/i', $image, $alt_matches );
            $alt_text = isset( $alt_matches[1] ) ? $alt_matches[1] : '';

            // Extrait le nom du fichier
            preg_match( '/src=[\'"]([^\'"]+)[\'"]/i', $image, $src_matches );
            $src = isset( $src_matches[1] ) ? $src_matches[1] : '';
            $filename = basename( $src );
            $filename = pathinfo( $filename, PATHINFO_FILENAME );
            $filename = str_replace( array( '-', '_' ), ' ', $filename );
            $filename = ucwords( $filename );

            // Utilise l'attribut alt ou le nom du fichier comme attribut title
            $title = ! empty( $alt_text ) ? $alt_text : $filename;

            if ( ! empty( $title ) && ! preg_match( '/title=[\'"]([^\'"]+)[\'"]/i', $image ) ) {
                $image = str_replace( '<img ', '<img title="' . esc_attr( $title ) . '" ', $image );
            }
        }

        return $add;
    }

    /**
     * Traite les liens dans le contenu.
     *
     * @since    1.0.0
     * @param    string    $content    Le contenu à traiter.
     * @return   string                Le contenu traité.
     */
    public function process_links( $content ) {
        $settings = $this->get_settings();

        // Traite les liens externes
        if ( $settings['content']['addNoopener'] || $settings['content']['openExternalLinksInNewTab'] ) {
            $content = preg_replace_callback( '/<a([^>]*)href=[\'"]([^\'"]+)[\'"](.*?)>/i', function( $matches ) use ( $settings ) {
                $atts = $matches[1];
                $url = $matches[2];
                $rest = $matches[3];

                // Vérifie si le lien est externe
                $is_external = strpos( $url, home_url() ) === false && preg_match( '/^https?:\/\//i', $url );

                if ( $is_external ) {
                    // Ajoute rel="noopener" aux liens externes
                    if ( $settings['content']['addNoopener'] ) {
                        if ( preg_match( '/rel=[\'"]([^\'"]+)[\'"]/i', $atts . $rest, $rel_matches ) ) {
                            $rel = $rel_matches[1];
                            if ( strpos( $rel, 'noopener' ) === false ) {
                                $new_rel = $rel . ' noopener';
                                $combined = $atts . $rest;
                                $combined = str_replace( 'rel="' . $rel . '"', 'rel="' . $new_rel . '"', $combined );
                                $atts = '';
                                $rest = $combined;
                            }
                        } else {
                            $rest .= ' rel="noopener"';
                        }
                    }

                    // Ouvre les liens externes dans un nouvel onglet
                    if ( $settings['content']['openExternalLinksInNewTab'] ) {
                        if ( ! preg_match( '/target=[\'"]([^\'"]+)[\'"]/i', $atts . $rest ) ) {
                            $rest .= ' target="_blank"';
                        }
                    }
                }

                return '<a' . $atts . 'href="' . $url . '"' . $rest . '>';
            }, $content );
        }

        return $content;
    }

    /**
     * Ajoute un texte alternatif aux images qui n'en ont pas.
     *
     * @since    1.0.0
     * @param    string    $content    Le contenu à traiter.
     * @return   string                Le contenu traité.
     */
    private function add_alt_text_to_images( $content ) {
        return preg_replace_callback( '/<img([^>]*)>/i', function( $matches ) {
            $img_tag = $matches[0];
            $img_atts = $matches[1];

            // Vérifie si l'image a déjà un attribut alt
            if ( ! preg_match( '/alt=[\'"]([^\'"]*)[\'"]/', $img_atts ) ) {
                // Extrait le nom du fichier
                preg_match( '/src=[\'"]([^\'"]+)[\'"]/i', $img_atts, $src_matches );
                $src = isset( $src_matches[1] ) ? $src_matches[1] : '';
                $filename = basename( $src );
                $filename = pathinfo( $filename, PATHINFO_FILENAME );
                $filename = str_replace( array( '-', '_' ), ' ', $filename );
                $filename = ucwords( $filename );

                // Ajoute l'attribut alt
                $img_tag = str_replace( '<img ', '<img alt="' . esc_attr( $filename ) . '" ', $img_tag );
            }

            return $img_tag;
        }, $content );
    }

    /**
     * Remplace les tags de template par leurs valeurs.
     *
     * @since    1.0.0
     * @param    string    $text    Le texte à traiter.
     * @return   string             Le texte traité.
     */
    private function parse_template_tags( $text ) {
        $settings = $this->get_settings();

        $tags = array(
            '%%site_title%%' => $settings['general']['siteTitle'],
            '%%site_description%%' => $settings['general']['siteDescription'],
            '%%separator%%' => $settings['general']['separator']
        );

        return str_replace( array_keys( $tags ), array_values( $tags ), $text );
    }
}
