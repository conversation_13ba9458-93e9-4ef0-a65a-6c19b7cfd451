import { __ } from '@wordpress/i18n';
import { <PERSON>, Card<PERSON><PERSON>, Card<PERSON><PERSON>er, CardFooter, Button, Dashicon, CheckboxControl } from '@wordpress/components';
import { useState } from '@wordpress/element';

/**
 * Composant pour afficher une liste de tâches prioritaires
 */
const TasksList = ({ tasks }) => {
  const [taskStatus, setTaskStatus] = useState(
    tasks.reduce((acc, task) => {
      acc[task.id] = task.completed || false;
      return acc;
    }, {})
  );

  const handleTaskToggle = (taskId) => {
    setTaskStatus({
      ...taskStatus,
      [taskId]: !taskStatus[taskId]
    });
  };

  // Calcul du pourcentage de complétion
  const completedCount = Object.values(taskStatus).filter(Boolean).length;
  const totalCount = tasks.length;
  const completionPercentage = Math.round((completedCount / totalCount) * 100);

  return (
    <Card className="boss-card boss-h-full">
      <CardHeader>
        <div className="boss-flex boss-justify-between boss-items-center">
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Tâches prioritaires', 'boss-seo')}
          </h3>
          <span className="boss-text-sm boss-text-boss-gray">
            {completedCount}/{totalCount} {__('complétées', 'boss-seo')}
          </span>
        </div>
      </CardHeader>
      <CardBody>
        {/* Barre de progression */}
        <div className="boss-mb-4">
          <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2.5">
            <div
              className="boss-bg-boss-primary boss-h-2.5 boss-rounded-full boss-transition-all boss-duration-500 boss-ease-out"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Liste des tâches */}
        <ul className="boss-space-y-3">
          {tasks.map((task) => (
            <li 
              key={task.id} 
              className={`boss-p-3 boss-rounded-lg boss-border boss-border-gray-200 boss-transition-all boss-duration-200 ${
                taskStatus[task.id] ? 'boss-bg-gray-50 boss-opacity-70' : 'boss-bg-white'
              }`}
            >
              <div className="boss-flex boss-items-start">
                <CheckboxControl
                  checked={taskStatus[task.id]}
                  onChange={() => handleTaskToggle(task.id)}
                  className="boss-mt-0"
                />
                <div className="boss-ml-2 boss-flex-1">
                  <p className={`boss-font-medium ${taskStatus[task.id] ? 'boss-line-through boss-text-boss-gray' : 'boss-text-boss-dark'}`}>
                    {task.title}
                  </p>
                  <p className="boss-text-sm boss-text-boss-gray boss-mt-1">
                    {task.description}
                  </p>
                  {!taskStatus[task.id] && task.actionLabel && (
                    <Button
                      isSmall
                      className="boss-mt-2 boss-text-boss-primary boss-hover:boss-text-boss-primary-dark"
                      onClick={() => task.onAction && task.onAction()}
                    >
                      {task.actionLabel}
                    </Button>
                  )}
                </div>
                <div className={`boss-ml-2 boss-p-1 boss-rounded boss-text-white boss-text-xs boss-font-medium ${
                  task.priority === 'high' ? 'boss-bg-boss-error' :
                  task.priority === 'medium' ? 'boss-bg-boss-warning' :
                  'boss-bg-boss-success'
                }`}>
                  {task.priority === 'high' ? __('Haute', 'boss-seo') :
                   task.priority === 'medium' ? __('Moyenne', 'boss-seo') :
                   __('Basse', 'boss-seo')}
                </div>
              </div>
            </li>
          ))}
        </ul>
      </CardBody>
      <CardFooter>
        <Button
          isSecondary
          className="boss-w-full boss-justify-center"
        >
          {__('Voir toutes les tâches', 'boss-seo')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TasksList;
