<?php
/**
 * Classe pour la gestion des performances
 *
 * @package Boss_Optimizer
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/**
 * Classe pour gérer les performances du site
 */
class Boss_Optimizer_Performance {
    /**
     * Instance unique de la classe
     *
     * @var Boss_Optimizer_Performance
     */
    private static $instance = null;

    /**
     * Obtenir l'instance unique de la classe
     *
     * @return Boss_Optimizer_Performance
     */
    public static function get_instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructeur
     */
    private function __construct() {
        // Enregistrer les endpoints REST API
        add_action( 'rest_api_init', array( $this, 'register_rest_routes' ) );
    }

    /**
     * Enregistre les routes REST API
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/performance/core-web-vitals',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_core_web_vitals' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/performance/history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_performance_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/performance/analyze',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'analyze_performance' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/performance/recommendations',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_performance_recommendations' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/performance/pagespeed',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_pagespeed_insights' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur
     *
     * @return bool
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les métriques Core Web Vitals
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_core_web_vitals( $request ) {
        // Récupérer les données de PageSpeed Insights si disponibles
        $pagespeed_data = $this->get_pagespeed_data();

        if ( $pagespeed_data ) {
            // Utiliser les données de PageSpeed Insights
            $core_web_vitals = $pagespeed_data;
        } else {
            // Utiliser des données fictives pour la démo
            $core_web_vitals = array(
                'lcp' => array(
                    'name' => 'LCP',
                    'value' => 3.2,
                    'unit' => 's',
                    'status' => 'needs-improvement',
                    'description' => __( 'Largest Contentful Paint', 'boss-seo' ),
                ),
                'fid' => array(
                    'name' => 'FID',
                    'value' => 75,
                    'unit' => 'ms',
                    'status' => 'good',
                    'description' => __( 'First Input Delay', 'boss-seo' ),
                ),
                'cls' => array(
                    'name' => 'CLS',
                    'value' => 0.12,
                    'unit' => '',
                    'status' => 'needs-improvement',
                    'description' => __( 'Cumulative Layout Shift', 'boss-seo' ),
                ),
                'ttfb' => array(
                    'name' => 'TTFB',
                    'value' => 520,
                    'unit' => 'ms',
                    'status' => 'needs-improvement',
                    'description' => __( 'Time to First Byte', 'boss-seo' ),
                ),
                'fcp' => array(
                    'name' => 'FCP',
                    'value' => 1.8,
                    'unit' => 's',
                    'status' => 'good',
                    'description' => __( 'First Contentful Paint', 'boss-seo' ),
                ),
            );
        }

        // Récupérer l'historique des performances
        $history = $this->get_performance_history_data();

        return rest_ensure_response( array(
            'coreWebVitals' => $core_web_vitals,
            'history' => $history,
        ) );
    }

    /**
     * Récupère l'historique des performances
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_performance_history( $request ) {
        // Récupérer le nombre de jours
        $days = isset( $request['days'] ) ? intval( $request['days'] ) : 30;

        // Récupérer l'historique des performances
        $history = $this->get_performance_history_data();

        // Limiter l'historique au nombre de jours demandé
        if ( count( $history ) > $days ) {
            $history = array_slice( $history, 0, $days );
        }

        return rest_ensure_response( $history );
    }

    /**
     * Lance une analyse de performance
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function analyze_performance( $request ) {
        // Récupérer les données de PageSpeed Insights
        $pagespeed_data = $this->get_pagespeed_data();

        if ( $pagespeed_data ) {
            // Mettre à jour les données de performance
            $this->update_performance_data( $pagespeed_data );

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Analyse de performance effectuée avec succès', 'boss-seo' ),
            ) );
        } else {
            // Générer des données fictives pour la démo
            $core_web_vitals = array(
                'lcp' => array(
                    'name' => 'LCP',
                    'value' => mt_rand( 25, 40 ) / 10,
                    'unit' => 's',
                    'status' => 'needs-improvement',
                    'description' => __( 'Largest Contentful Paint', 'boss-seo' ),
                ),
                'fid' => array(
                    'name' => 'FID',
                    'value' => mt_rand( 50, 100 ),
                    'unit' => 'ms',
                    'status' => 'good',
                    'description' => __( 'First Input Delay', 'boss-seo' ),
                ),
                'cls' => array(
                    'name' => 'CLS',
                    'value' => mt_rand( 10, 15 ) / 100,
                    'unit' => '',
                    'status' => 'needs-improvement',
                    'description' => __( 'Cumulative Layout Shift', 'boss-seo' ),
                ),
                'ttfb' => array(
                    'name' => 'TTFB',
                    'value' => mt_rand( 400, 600 ),
                    'unit' => 'ms',
                    'status' => 'needs-improvement',
                    'description' => __( 'Time to First Byte', 'boss-seo' ),
                ),
                'fcp' => array(
                    'name' => 'FCP',
                    'value' => mt_rand( 15, 25 ) / 10,
                    'unit' => 's',
                    'status' => 'good',
                    'description' => __( 'First Contentful Paint', 'boss-seo' ),
                ),
            );

            // Mettre à jour les statuts
            foreach ( $core_web_vitals as $metric => &$data ) {
                $data['status'] = $this->get_metric_status( $metric, $data['value'] );
            }

            // Mettre à jour les données de performance
            $this->update_performance_data( $core_web_vitals );

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Analyse de performance simulée effectuée avec succès', 'boss-seo' ),
            ) );
        }
    }

    /**
     * Récupère les recommandations de performance
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_performance_recommendations( $request ) {
        // Récupérer les données de performance
        $performance_data = get_option( 'boss_optimizer_performance_data', array() );

        if ( empty( $performance_data ) ) {
            return rest_ensure_response( array() );
        }

        // Générer des recommandations en fonction des métriques
        $recommendations = array();

        // Recommandations pour LCP
        if ( isset( $performance_data['lcp'] ) ) {
            if ( $performance_data['lcp']['status'] === 'poor' ) {
                $recommendations[] = array(
                    'title' => __( 'LCP très lent', 'boss-seo' ),
                    'description' => sprintf(
                        __( 'Votre Largest Contentful Paint (LCP) est de %s, ce qui est bien supérieur à la valeur recommandée de 2.5s. Cela affecte considérablement la perception de chargement de votre page.', 'boss-seo' ),
                        $performance_data['lcp']['value'] . $performance_data['lcp']['unit']
                    ),
                    'priority' => 'high',
                    'link' => 'https://web.dev/lcp/',
                );
            } elseif ( $performance_data['lcp']['status'] === 'needs-improvement' ) {
                $recommendations[] = array(
                    'title' => __( 'Optimiser le Largest Contentful Paint (LCP)', 'boss-seo' ),
                    'description' => sprintf(
                        __( 'Votre LCP est de %s, ce qui est supérieur à la valeur recommandée de 2.5s. Cela affecte la perception de chargement de votre page.', 'boss-seo' ),
                        $performance_data['lcp']['value'] . $performance_data['lcp']['unit']
                    ),
                    'priority' => 'medium',
                    'link' => 'https://web.dev/lcp/',
                );
            }
        }

        // Recommandations pour CLS
        if ( isset( $performance_data['cls'] ) ) {
            if ( $performance_data['cls']['status'] === 'poor' ) {
                $recommendations[] = array(
                    'title' => __( 'CLS très élevé', 'boss-seo' ),
                    'description' => sprintf(
                        __( 'Votre Cumulative Layout Shift (CLS) est de %s, ce qui est bien supérieur à la valeur recommandée de 0.1. Cela cause une expérience utilisateur très frustrante.', 'boss-seo' ),
                        $performance_data['cls']['value']
                    ),
                    'priority' => 'high',
                    'link' => 'https://web.dev/cls/',
                );
            } elseif ( $performance_data['cls']['status'] === 'needs-improvement' ) {
                $recommendations[] = array(
                    'title' => __( 'Réduire le Cumulative Layout Shift (CLS)', 'boss-seo' ),
                    'description' => sprintf(
                        __( 'Votre CLS est de %s, ce qui est légèrement supérieur à la valeur recommandée de 0.1. Cela peut causer une expérience utilisateur frustrante.', 'boss-seo' ),
                        $performance_data['cls']['value']
                    ),
                    'priority' => 'medium',
                    'link' => 'https://web.dev/cls/',
                );
            }
        }

        // Recommandations pour TTFB
        if ( isset( $performance_data['ttfb'] ) ) {
            if ( $performance_data['ttfb']['status'] === 'poor' ) {
                $recommendations[] = array(
                    'title' => __( 'TTFB très lent', 'boss-seo' ),
                    'description' => sprintf(
                        __( 'Votre Time to First Byte (TTFB) est de %s, ce qui est bien supérieur à la valeur recommandée de 200ms. Cela indique des problèmes sérieux avec votre serveur ou votre configuration.', 'boss-seo' ),
                        $performance_data['ttfb']['value'] . $performance_data['ttfb']['unit']
                    ),
                    'priority' => 'high',
                    'link' => 'https://web.dev/ttfb/',
                );
            } elseif ( $performance_data['ttfb']['status'] === 'needs-improvement' ) {
                $recommendations[] = array(
                    'title' => __( 'Améliorer le Time to First Byte (TTFB)', 'boss-seo' ),
                    'description' => sprintf(
                        __( 'Votre TTFB est de %s, ce qui est supérieur à la valeur recommandée de 200ms. Cela indique des problèmes potentiels avec votre serveur ou votre configuration.', 'boss-seo' ),
                        $performance_data['ttfb']['value'] . $performance_data['ttfb']['unit']
                    ),
                    'priority' => 'medium',
                    'link' => 'https://web.dev/ttfb/',
                );
            }
        }

        // Recommandations générales
        $recommendations[] = array(
            'title' => __( 'Optimiser les images', 'boss-seo' ),
            'description' => __( 'Les images non optimisées peuvent considérablement ralentir le chargement de votre site. Utilisez des formats modernes comme WebP et assurez-vous que vos images sont correctement dimensionnées.', 'boss-seo' ),
            'priority' => 'low',
            'link' => 'https://web.dev/optimize-images/',
        );

        $recommendations[] = array(
            'title' => __( 'Utiliser un CDN', 'boss-seo' ),
            'description' => __( 'Un réseau de distribution de contenu (CDN) peut améliorer les performances en distribuant votre contenu à partir de serveurs plus proches de vos utilisateurs.', 'boss-seo' ),
            'priority' => 'low',
            'link' => 'https://web.dev/content-delivery-networks/',
        );

        return rest_ensure_response( $recommendations );
    }

    /**
     * Récupère les données de PageSpeed Insights
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_pagespeed_insights( $request ) {
        // Récupérer la stratégie
        $strategy = isset( $request['strategy'] ) ? sanitize_text_field( $request['strategy'] ) : 'mobile';

        // Récupérer la clé API PageSpeed Insights depuis la nouvelle structure
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $api_key = isset( $external_services['google_pagespeed']['api_key'] ) ? $external_services['google_pagespeed']['api_key'] : '';

        // Fallback vers l'ancienne structure
        if ( empty( $api_key ) ) {
            $api_key = get_option( 'boss_optimizer_pagespeed_api_key', '' );
        }

        if ( empty( $api_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API PageSpeed Insights manquante. Veuillez la configurer dans les paramètres.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // URL du site
        $site_url = home_url();

        // URL de l'API PageSpeed Insights
        $api_url = add_query_arg(
            array(
                'url' => urlencode( $site_url ),
                'key' => $api_key,
                'strategy' => $strategy,
                'category' => 'performance',
                'locale' => 'fr_FR',
            ),
            'https://www.googleapis.com/pagespeedonline/v5/runPagespeed'
        );

        // Effectuer la requête
        $response = wp_remote_get( $api_url );

        if ( is_wp_error( $response ) ) {
            return new WP_Error( 'api_error', $response->get_error_message(), array( 'status' => 500 ) );
        }

        $body = wp_remote_retrieve_body( $response );
        $data = json_decode( $body, true );

        if ( empty( $data ) || ! isset( $data['lighthouseResult'] ) ) {
            return new WP_Error( 'invalid_response', __( 'Réponse invalide de l\'API PageSpeed Insights', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( $data );
    }

    /**
     * Récupère les données de PageSpeed Insights
     *
     * @return array|false Données de PageSpeed Insights ou false en cas d'erreur
     */
    private function get_pagespeed_data() {
        // Récupérer la clé API PageSpeed Insights depuis la nouvelle structure
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $api_key = isset( $external_services['google_pagespeed']['api_key'] ) ? $external_services['google_pagespeed']['api_key'] : '';

        // Fallback vers l'ancienne structure
        if ( empty( $api_key ) ) {
            $api_key = get_option( 'boss_optimizer_pagespeed_api_key', '' );
        }

        if ( empty( $api_key ) ) {
            return false;
        }

        // URL du site
        $site_url = home_url();

        // URL de l'API PageSpeed Insights
        $api_url = add_query_arg(
            array(
                'url' => urlencode( $site_url ),
                'key' => $api_key,
                'strategy' => 'mobile',
                'category' => 'performance',
                'locale' => 'fr_FR',
            ),
            'https://www.googleapis.com/pagespeedonline/v5/runPagespeed'
        );

        // Effectuer la requête
        $response = wp_remote_get( $api_url );

        if ( is_wp_error( $response ) ) {
            return false;
        }

        $body = wp_remote_retrieve_body( $response );
        $data = json_decode( $body, true );

        if ( empty( $data ) || ! isset( $data['lighthouseResult'] ) ) {
            return false;
        }

        // Extraire les métriques
        $metrics = $data['lighthouseResult']['audits'];

        // Formater les données
        $core_web_vitals = array(
            'lcp' => array(
                'name' => 'LCP',
                'value' => round( $metrics['largest-contentful-paint']['numericValue'] / 1000, 1 ),
                'unit' => 's',
                'status' => $this->get_metric_status( 'lcp', $metrics['largest-contentful-paint']['numericValue'] / 1000 ),
                'description' => __( 'Largest Contentful Paint', 'boss-seo' ),
            ),
            'fid' => array(
                'name' => 'FID',
                'value' => round( $metrics['max-potential-fid']['numericValue'] ),
                'unit' => 'ms',
                'status' => $this->get_metric_status( 'fid', $metrics['max-potential-fid']['numericValue'] ),
                'description' => __( 'First Input Delay', 'boss-seo' ),
            ),
            'cls' => array(
                'name' => 'CLS',
                'value' => round( $metrics['cumulative-layout-shift']['numericValue'], 2 ),
                'unit' => '',
                'status' => $this->get_metric_status( 'cls', $metrics['cumulative-layout-shift']['numericValue'] ),
                'description' => __( 'Cumulative Layout Shift', 'boss-seo' ),
            ),
            'ttfb' => array(
                'name' => 'TTFB',
                'value' => round( $metrics['server-response-time']['numericValue'] ),
                'unit' => 'ms',
                'status' => $this->get_metric_status( 'ttfb', $metrics['server-response-time']['numericValue'] ),
                'description' => __( 'Time to First Byte', 'boss-seo' ),
            ),
            'fcp' => array(
                'name' => 'FCP',
                'value' => round( $metrics['first-contentful-paint']['numericValue'] / 1000, 1 ),
                'unit' => 's',
                'status' => $this->get_metric_status( 'fcp', $metrics['first-contentful-paint']['numericValue'] / 1000 ),
                'description' => __( 'First Contentful Paint', 'boss-seo' ),
            ),
        );

        return $core_web_vitals;
    }

    /**
     * Détermine le statut d'une métrique en fonction de sa valeur
     *
     * @param string $metric Nom de la métrique
     * @param float $value Valeur de la métrique
     * @return string Statut de la métrique (good, needs-improvement, poor)
     */
    private function get_metric_status( $metric, $value ) {
        switch ( $metric ) {
            case 'lcp':
                if ( $value <= 2.5 ) {
                    return 'good';
                } elseif ( $value <= 4 ) {
                    return 'needs-improvement';
                } else {
                    return 'poor';
                }

            case 'fid':
                if ( $value <= 100 ) {
                    return 'good';
                } elseif ( $value <= 300 ) {
                    return 'needs-improvement';
                } else {
                    return 'poor';
                }

            case 'cls':
                if ( $value <= 0.1 ) {
                    return 'good';
                } elseif ( $value <= 0.25 ) {
                    return 'needs-improvement';
                } else {
                    return 'poor';
                }

            case 'ttfb':
                if ( $value <= 200 ) {
                    return 'good';
                } elseif ( $value <= 500 ) {
                    return 'needs-improvement';
                } else {
                    return 'poor';
                }

            case 'fcp':
                if ( $value <= 1.8 ) {
                    return 'good';
                } elseif ( $value <= 3 ) {
                    return 'needs-improvement';
                } else {
                    return 'poor';
                }

            default:
                return 'unknown';
        }
    }

    /**
     * Récupère l'historique des performances
     *
     * @return array Historique des performances
     */
    private function get_performance_history_data() {
        // Récupérer l'historique des performances depuis la base de données
        $history = get_option( 'boss_optimizer_performance_history', array() );

        if ( empty( $history ) ) {
            // Générer des données fictives pour la démo
            $current_month = intval( date( 'n' ) );
            $history = array();

            for ( $i = 4; $i >= 0; $i-- ) {
                $month = $current_month - $i;
                if ( $month <= 0 ) {
                    $month += 12;
                }

                $history[] = array(
                    'date' => date( 'Y-m', strtotime( "-{$i} months" ) ),
                    'score' => mt_rand( 60, 90 ),
                );
            }

            // Trier par date (du plus récent au plus ancien)
            usort( $history, function( $a, $b ) {
                return strcmp( $b['date'], $a['date'] );
            } );
        }

        return $history;
    }

    /**
     * Met à jour les données de performance
     *
     * @param array $core_web_vitals Données des Core Web Vitals
     */
    private function update_performance_data( $core_web_vitals ) {
        // Sauvegarder les données de performance
        update_option( 'boss_optimizer_performance_data', $core_web_vitals );

        // Calculer le score de performance
        $performance_score = 0;
        $good_count = 0;
        $needs_improvement_count = 0;
        $total_count = count( $core_web_vitals );

        foreach ( $core_web_vitals as $metric ) {
            if ( $metric['status'] === 'good' ) {
                $good_count++;
            } elseif ( $metric['status'] === 'needs-improvement' ) {
                $needs_improvement_count++;
            }
        }

        // Pondération : bon = 1, à améliorer = 0.5, mauvais = 0
        $performance_score = round( ( $good_count + $needs_improvement_count * 0.5 ) / $total_count * 100 );

        // Mettre à jour l'historique des performances
        $history = $this->get_performance_history_data();

        // Ajouter le nouveau score à l'historique
        $current_date = date( 'Y-m' );
        $found = false;

        foreach ( $history as &$item ) {
            if ( $item['date'] === $current_date ) {
                $item['score'] = $performance_score;
                $found = true;
                break;
            }
        }

        if ( ! $found ) {
            array_unshift( $history, array(
                'date' => $current_date,
                'score' => $performance_score,
            ) );
        }

        // Limiter l'historique à 12 mois
        if ( count( $history ) > 12 ) {
            $history = array_slice( $history, 0, 12 );
        }

        // Sauvegarder l'historique
        update_option( 'boss_optimizer_performance_history', $history );
    }
}