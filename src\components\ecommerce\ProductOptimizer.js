import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  SelectControl,
  TextControl,
  ToggleControl,
  CheckboxControl,
  Modal,
  Notice,
  Spinner
} from '@wordpress/components';

// Importer le service
import EcommerceService from '../../services/EcommerceService';

const ProductOptimizer = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showBulkEditModal, setShowBulkEditModal] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [bulkEditSettings, setBulkEditSettings] = useState({
    updateTitles: false,
    updateDescriptions: false,
    updateImages: false,
    updateAttributes: false,
    updateCategories: false,
    generateSchemas: false
  });

  // Créer une instance du service
  const ecommerceService = new EcommerceService();

  // Charger les données
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Récupérer les produits
        const productsResponse = await ecommerceService.getProducts();

        // Extraire les catégories uniques des produits
        const uniqueCategories = [];
        const categoryIds = new Set();

        productsResponse.products.forEach(product => {
          if (product.categoryId && !categoryIds.has(product.categoryId)) {
            categoryIds.add(product.categoryId);
            uniqueCategories.push({
              id: product.categoryId,
              name: product.category
            });
          }
        });

        // Trier les catégories par nom
        uniqueCategories.sort((a, b) => a.name.localeCompare(b.name));

        // Mettre à jour les états
        setCategories(uniqueCategories);
        setProducts(productsResponse.products || []);
      } catch (err) {
        console.error('Erreur lors du chargement des produits:', err);

        // Vérifier si c'est une erreur spécifique de WooCommerce
        if (err && err.code) {
          if (err.code === 'woocommerce_not_available') {
            // WooCommerce n'est pas installé ou activé
            setError(__('WooCommerce n\'est pas installé ou activé. Veuillez installer et activer WooCommerce pour utiliser cette fonctionnalité.', 'boss-seo'));
          } else if (err.code === 'no_products_available') {
            // Aucun produit n'est disponible
            setError(__('Aucun produit n\'est disponible dans WooCommerce. Veuillez ajouter des produits pour utiliser cette fonctionnalité.', 'boss-seo'));
          } else {
            // Autre erreur
            setError(err.message || __('Erreur lors du chargement des produits. Veuillez réessayer.', 'boss-seo'));
          }
        } else {
          // Erreur générique
          setError(__('Erreur lors du chargement des produits. Veuillez réessayer.', 'boss-seo'));
        }

        // Réinitialiser les états
        setCategories([]);
        setProducts([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Effet pour gérer la sélection de tous les produits
  useEffect(() => {
    if (selectAll) {
      setSelectedProducts(filteredProducts.map(product => product.id));
    } else {
      setSelectedProducts([]);
    }
  }, [selectAll]);

  // Fonction pour filtrer les produits
  const getFilteredProducts = () => {
    return products.filter(product => {
      // Filtrer par catégorie
      const matchesCategory = selectedCategory === 'all' || product.categoryId === parseInt(selectedCategory);

      // Filtrer par statut
      const matchesStatus = selectedStatus === 'all' || product.status === selectedStatus;

      // Filtrer par recherche
      const matchesSearch = searchQuery === '' ||
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.category.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesCategory && matchesStatus && matchesSearch;
    });
  };

  // Obtenir les produits filtrés
  const filteredProducts = getFilteredProducts();

  // Fonction pour obtenir la classe de couleur en fonction du score
  const getScoreColorClass = (score) => {
    if (score >= 80) return 'boss-text-green-600';
    if (score >= 60) return 'boss-text-yellow-600';
    return 'boss-text-red-600';
  };

  // Fonction pour obtenir le texte du statut
  const getStatusText = (status) => {
    switch (status) {
      case 'optimized':
        return __('Optimisé', 'boss-seo');
      case 'needs_attention':
        return __('À améliorer', 'boss-seo');
      case 'critical':
        return __('Critique', 'boss-seo');
      default:
        return status;
    }
  };

  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColorClass = (status) => {
    switch (status) {
      case 'optimized':
        return 'boss-bg-green-100 boss-text-green-800';
      case 'needs_attention':
        return 'boss-bg-yellow-100 boss-text-yellow-800';
      case 'critical':
        return 'boss-bg-red-100 boss-text-red-800';
      default:
        return 'boss-bg-gray-100 boss-text-gray-800';
    }
  };

  // Fonction pour obtenir le texte du problème
  const getIssueText = (issue) => {
    switch (issue) {
      case 'title_too_short':
        return __('Titre trop court', 'boss-seo');
      case 'missing_meta_description':
        return __('Meta description manquante', 'boss-seo');
      case 'no_alt_text':
        return __('Texte alternatif manquant', 'boss-seo');
      case 'duplicate_content':
        return __('Contenu dupliqué', 'boss-seo');
      case 'missing_schema':
        return __('Schéma produit manquant', 'boss-seo');
      default:
        return issue;
    }
  };

  // Fonction pour gérer la sélection d'un produit
  const handleProductSelection = (productId) => {
    if (selectedProducts.includes(productId)) {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
    } else {
      setSelectedProducts([...selectedProducts, productId]);
    }
  };

  // Fonction pour gérer l'édition en masse
  const handleBulkEdit = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Appeler le service pour optimiser les produits en masse
      const response = await ecommerceService.bulkOptimizeProducts(selectedProducts, bulkEditSettings);

      // Mettre à jour les produits dans l'état local
      const updatedProducts = products.map(product => {
        // Trouver le produit correspondant dans la réponse
        const updatedProduct = response.products.find(p => p.id === product.id);

        // Si le produit a été mis à jour, utiliser les nouvelles données
        if (updatedProduct) {
          return updatedProduct;
        }

        // Sinon, conserver le produit tel quel
        return product;
      });

      // Mettre à jour l'état
      setProducts(updatedProducts);
      setSelectedProducts([]);
      setSelectAll(false);
      setShowBulkEditModal(false);
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (err) {
      console.error('Erreur lors de l\'optimisation en masse des produits:', err);
      setError(__('Erreur lors de l\'optimisation en masse des produits. Veuillez réessayer.', 'boss-seo'));

      // Simuler l'optimisation en cas d'erreur (pour le développement)
      // À supprimer en production
      const updatedProducts = products.map(product => {
        if (selectedProducts.includes(product.id)) {
          // Simuler l'amélioration du score
          const newScore = Math.min(100, product.score + Math.floor(Math.random() * 15) + 5);

          // Déterminer le nouveau statut en fonction du score
          let newStatus;
          if (newScore >= 80) {
            newStatus = 'optimized';
          } else if (newScore >= 60) {
            newStatus = 'needs_attention';
          } else {
            newStatus = 'critical';
          }

          // Mettre à jour les problèmes
          let newIssues = [...product.issues];

          if (bulkEditSettings.updateTitles) {
            newIssues = newIssues.filter(issue => issue !== 'title_too_short');
          }

          if (bulkEditSettings.updateDescriptions) {
            newIssues = newIssues.filter(issue => issue !== 'missing_meta_description');
          }

          if (bulkEditSettings.updateImages) {
            newIssues = newIssues.filter(issue => issue !== 'no_alt_text');
          }

          if (bulkEditSettings.generateSchemas) {
            newIssues = newIssues.filter(issue => issue !== 'missing_schema');
          }

          return {
            ...product,
            score: newScore,
            status: newStatus,
            issues: newIssues,
            hasSchema: bulkEditSettings.generateSchemas ? true : product.hasSchema,
            lastUpdated: new Date().toISOString().split('T')[0]
          };
        }

        return product;
      });

      setProducts(updatedProducts);
      setSelectedProducts([]);
      setSelectAll(false);
      setShowBulkEditModal(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {error && (
            <div className="boss-text-center boss-p-8 boss-bg-white boss-rounded-lg boss-shadow boss-mb-6">
              <Dashicon icon="warning" size={36} className="boss-text-yellow-500 boss-mb-4" />
              <h2 className="boss-text-xl boss-font-bold boss-mb-2">{__('Attention', 'boss-seo')}</h2>
              <p className="boss-text-gray-600 boss-mb-4">{error}</p>
              {error.includes('WooCommerce') && (
                <a
                  href="https://wordpress.org/plugins/woocommerce/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="boss-inline-block boss-bg-blue-500 boss-text-white boss-px-4 boss-py-2 boss-rounded boss-hover:boss-bg-blue-600 boss-transition"
                >
                  {__('Installer WooCommerce', 'boss-seo')}
                </a>
              )}
            </div>
          )}

          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {__('Les produits ont été optimisés avec succès !', 'boss-seo')}
            </Notice>
          )}

          {!error && (
            <>
            {/* Filtres */}
            <Card className="boss-mb-6">
              <CardBody>
                <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4">
                  <TextControl
                    placeholder={__('Rechercher un produit...', 'boss-seo')}
                    value={searchQuery}
                    onChange={setSearchQuery}
                  />

                  <SelectControl
                    label=""
                    value={selectedCategory}
                    options={[
                      { label: __('Toutes les catégories', 'boss-seo'), value: 'all' },
                      ...categories.map(category => ({
                        label: category.name,
                        value: category.id.toString()
                      }))
                    ]}
                    onChange={setSelectedCategory}
                  />

                <SelectControl
                  label=""
                  value={selectedStatus}
                  options={[
                    { label: __('Tous les statuts', 'boss-seo'), value: 'all' },
                    { label: __('Optimisés', 'boss-seo'), value: 'optimized' },
                    { label: __('À améliorer', 'boss-seo'), value: 'needs_attention' },
                    { label: __('Critiques', 'boss-seo'), value: 'critical' }
                  ]}
                  onChange={setSelectedStatus}
                />
              </div>
            </CardBody>
          </Card>

          {/* Liste des produits */}
          <Card>
            <CardHeader className="boss-border-b boss-border-gray-200">
              <div className="boss-flex boss-justify-between boss-items-center">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Optimisation des produits', 'boss-seo')}
                </h2>
                <div className="boss-flex boss-space-x-2">
                  <Button
                    isPrimary
                    onClick={() => setShowBulkEditModal(true)}
                    disabled={selectedProducts.length === 0}
                  >
                    {__('Optimiser en masse', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardBody className="boss-p-0">
              <div className="boss-overflow-x-auto">
                <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
                  <thead className="boss-bg-gray-50">
                    <tr>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-w-10">
                        <CheckboxControl
                          checked={selectAll}
                          onChange={setSelectAll}
                          label=""
                        />
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Produit', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Catégorie', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Score SEO', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Statut', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Problèmes', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Schéma', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-right boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Actions', 'boss-seo')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                    {filteredProducts.length === 0 ? (
                      <tr>
                        <td colSpan="8" className="boss-px-6 boss-py-4 boss-text-center boss-text-boss-gray">
                          {__('Aucun produit trouvé.', 'boss-seo')}
                        </td>
                      </tr>
                    ) : (
                      filteredProducts.map(product => (
                        <tr key={product.id} className="boss-hover:boss-bg-gray-50">
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <CheckboxControl
                              checked={selectedProducts.includes(product.id)}
                              onChange={() => handleProductSelection(product.id)}
                              label=""
                            />
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <div className="boss-font-medium boss-text-boss-dark">{product.name}</div>
                            <div className="boss-text-xs boss-text-boss-gray">{__('Mis à jour le', 'boss-seo')} {product.lastUpdated}</div>
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <div className="boss-text-boss-gray">{product.category}</div>
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <div className={`boss-font-medium ${getScoreColorClass(product.score)}`}>
                              {product.score}/100
                            </div>
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <span className={`boss-px-2 boss-py-1 boss-text-xs boss-rounded-full ${getStatusColorClass(product.status)}`}>
                              {getStatusText(product.status)}
                            </span>
                          </td>
                          <td className="boss-px-6 boss-py-4">
                            <div className="boss-flex boss-flex-wrap boss-gap-1">
                              {product.issues.length === 0 ? (
                                <span className="boss-text-green-600 boss-text-xs">
                                  {__('Aucun problème', 'boss-seo')}
                                </span>
                              ) : (
                                product.issues.map((issue, index) => (
                                  <span
                                    key={index}
                                    className="boss-px-2 boss-py-1 boss-bg-red-100 boss-text-red-800 boss-text-xs boss-rounded"
                                  >
                                    {getIssueText(issue)}
                                  </span>
                                ))
                              )}
                            </div>
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            {product.hasSchema ? (
                              <span className="boss-text-green-600 boss-flex boss-items-center">
                                <Dashicon icon="yes-alt" className="boss-mr-1" />
                                {__('Présent', 'boss-seo')}
                              </span>
                            ) : (
                              <span className="boss-text-red-600 boss-flex boss-items-center">
                                <Dashicon icon="no-alt" className="boss-mr-1" />
                                {__('Manquant', 'boss-seo')}
                              </span>
                            )}
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-right">
                            <Button
                              isSecondary
                              isSmall
                              href={`/wp-admin/post.php?post=${product.id}&action=edit`}
                              target="_blank"
                            >
                              {__('Éditer', 'boss-seo')}
                            </Button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardBody>
            <CardFooter className="boss-border-t boss-border-gray-200">
              <div className="boss-flex boss-justify-between boss-items-center">
                <div className="boss-text-boss-gray boss-text-sm">
                  {filteredProducts.length} {__('produits trouvés', 'boss-seo')}
                </div>
                <div className="boss-text-boss-gray boss-text-sm">
                  {selectedProducts.length} {__('produits sélectionnés', 'boss-seo')}
                </div>
              </div>
            </CardFooter>
          </Card>

          {/* Modal d'édition en masse */}
          {showBulkEditModal && (
            <Modal
              title={__('Optimisation en masse', 'boss-seo')}
              onRequestClose={() => setShowBulkEditModal(false)}
              className="boss-bulk-edit-modal"
            >
              <div className="boss-p-6">
                <p className="boss-mb-4 boss-text-boss-gray">
                  {__('Sélectionnez les optimisations à appliquer aux', 'boss-seo')} <strong>{selectedProducts.length}</strong> {__('produits sélectionnés.', 'boss-seo')}
                </p>

                <div className="boss-space-y-4 boss-mb-6">
                  <ToggleControl
                    label={__('Optimiser les titres', 'boss-seo')}
                    help={__('Génère des titres optimisés pour le SEO', 'boss-seo')}
                    checked={bulkEditSettings.updateTitles}
                    onChange={(checked) => setBulkEditSettings({ ...bulkEditSettings, updateTitles: checked })}
                  />

                  <ToggleControl
                    label={__('Optimiser les descriptions', 'boss-seo')}
                    help={__('Génère des meta descriptions optimisées', 'boss-seo')}
                    checked={bulkEditSettings.updateDescriptions}
                    onChange={(checked) => setBulkEditSettings({ ...bulkEditSettings, updateDescriptions: checked })}
                  />

                  <ToggleControl
                    label={__('Optimiser les images', 'boss-seo')}
                    help={__('Ajoute des textes alternatifs aux images', 'boss-seo')}
                    checked={bulkEditSettings.updateImages}
                    onChange={(checked) => setBulkEditSettings({ ...bulkEditSettings, updateImages: checked })}
                  />

                  <ToggleControl
                    label={__('Optimiser les attributs', 'boss-seo')}
                    help={__('Améliore les attributs des produits pour le SEO', 'boss-seo')}
                    checked={bulkEditSettings.updateAttributes}
                    onChange={(checked) => setBulkEditSettings({ ...bulkEditSettings, updateAttributes: checked })}
                  />

                  <ToggleControl
                    label={__('Optimiser les catégories', 'boss-seo')}
                    help={__('Suggère des catégories supplémentaires pertinentes', 'boss-seo')}
                    checked={bulkEditSettings.updateCategories}
                    onChange={(checked) => setBulkEditSettings({ ...bulkEditSettings, updateCategories: checked })}
                  />

                  <ToggleControl
                    label={__('Générer des schémas produits', 'boss-seo')}
                    help={__('Crée des schémas structurés pour les produits', 'boss-seo')}
                    checked={bulkEditSettings.generateSchemas}
                    onChange={(checked) => setBulkEditSettings({ ...bulkEditSettings, generateSchemas: checked })}
                  />
                </div>

                <div className="boss-flex boss-justify-end boss-space-x-3">
                  <Button
                    isSecondary
                    onClick={() => setShowBulkEditModal(false)}
                  >
                    {__('Annuler', 'boss-seo')}
                  </Button>
                  <Button
                    isPrimary
                    onClick={handleBulkEdit}
                    disabled={!Object.values(bulkEditSettings).some(value => value)}
                  >
                    {__('Optimiser', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </Modal>
          )}
          </>
          )}
        </div>
      )}
    </div>
  );
};

export default ProductOptimizer;
