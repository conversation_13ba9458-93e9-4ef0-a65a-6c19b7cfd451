<?php
/**
 * Classe principale pour le module e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe principale pour le module e-commerce.
 *
 * Cette classe centralise tous les modules e-commerce.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Ecommerce {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * L'instance de la classe Ecommerce.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Ecommerce    $ecommerce    L'instance de la classe Ecommerce.
     */
    protected $ecommerce;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->load_dependencies();
        $this->init_modules();
    }

    /**
     * Charge les dépendances du module.
     *
     * @since    1.2.0
     */
    private function load_dependencies() {
        /**
         * La classe qui gère le module e-commerce.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-ecommerce.php';
    }

    /**
     * Initialise les modules.
     *
     * @since    1.2.0
     */
    private function init_modules() {
        $this->ecommerce = new Boss_Ecommerce( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Enregistrer les hooks du module e-commerce
        $this->ecommerce->register_hooks();
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Enregistrer les routes REST API du module e-commerce
        $this->ecommerce->register_rest_routes();
    }
}
