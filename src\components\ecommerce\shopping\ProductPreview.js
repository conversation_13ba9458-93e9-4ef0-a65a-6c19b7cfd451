import { __ } from '@wordpress/i18n';
import { Dashicon } from '@wordpress/components';

const ProductPreview = ({ product }) => {
  if (!product) return null;

  return (
    <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-mb-6">
      <h3 className="boss-text-md boss-font-semibold boss-mb-3">
        {__('Prévisualisation Google Shopping', 'boss-seo')}
      </h3>
      
      <div className="boss-bg-white boss-shadow boss-rounded-lg boss-overflow-hidden">
        <div className="boss-bg-gray-100 boss-h-48 boss-flex boss-items-center boss-justify-center">
          {product.image ? (
            <img 
              src={product.image} 
              alt={product.name} 
              className="boss-h-full boss-w-auto boss-object-contain"
            />
          ) : (
            <Dashicon icon="format-image" className="boss-text-5xl boss-text-boss-gray" />
          )}
        </div>
        
        <div className="boss-p-4">
          <div className="boss-text-sm boss-text-green-800 boss-mb-1">
            {product.merchant || 'Votre boutique'}
          </div>
          
          <div className="boss-text-lg boss-font-medium boss-mb-2">
            {product.name}
          </div>
          
          <div className="boss-text-xl boss-font-bold boss-text-boss-primary boss-mb-2">
            {product.price} €
          </div>
          
          {product.shipping && (
            <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
              {__('Livraison:', 'boss-seo')} {product.shipping}
            </div>
          )}
          
          {product.availability && (
            <div className={`boss-text-sm ${
              product.availability === 'in_stock' 
                ? 'boss-text-green-600' 
                : 'boss-text-red-600'
            } boss-mb-2`}>
              {product.availability === 'in_stock' 
                ? __('En stock', 'boss-seo') 
                : __('Rupture de stock', 'boss-seo')}
            </div>
          )}
          
          {product.rating && (
            <div className="boss-flex boss-items-center boss-text-yellow-500">
              <Dashicon icon="star-filled" />
              <Dashicon icon="star-filled" />
              <Dashicon icon="star-filled" />
              <Dashicon icon="star-filled" />
              <Dashicon icon="star-half" />
              <span className="boss-ml-1 boss-text-boss-gray boss-text-xs">
                {product.rating.average} ({product.rating.count})
              </span>
            </div>
          )}
        </div>
      </div>
      
      <div className="boss-mt-4 boss-text-sm boss-text-boss-gray">
        <p>
          {__('Cette prévisualisation montre comment votre produit pourrait apparaître dans Google Shopping.', 'boss-seo')}
        </p>
      </div>
    </div>
  );
};

export default ProductPreview;
