/**
 * JavaScript pour le module e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin/js
 */

(function( $ ) {
    'use strict';

    /**
     * Initialise le module e-commerce.
     */
    function init() {
        // Initialiser les onglets
        initTabs();

        // Initialiser les formulaires
        initForms();

        // Initialiser les extraits enrichis
        initRichSnippets();

        // Initialiser l'analyse SEO
        initSeoAnalyzer();

        // Initialiser les marques de produits
        initProductBrands();
    }

    /**
     * Initialise les onglets.
     */
    function initTabs() {
        $('.boss-seo-ecommerce-tabs .nav-tab').on('click', function(e) {
            e.preventDefault();

            // Récupérer l'ID de l'onglet
            var tabId = $(this).attr('href');

            // Activer l'onglet
            $('.boss-seo-ecommerce-tabs .nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');

            // Afficher le contenu de l'onglet
            $('.boss-seo-ecommerce-tabs .tab-content').removeClass('active');
            $(tabId).addClass('active');
        });
    }

    /**
     * Initialise les formulaires.
     */
    function initForms() {
        // Formulaire des extraits enrichis
        $('#boss-seo-rich-snippets-form').on('submit', function(e) {
            e.preventDefault();

            // Récupérer les données du formulaire
            var formData = $(this).serialize();

            // Envoyer les données au serveur
            $.ajax({
                url: boss_seo_ecommerce.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_save_ecommerce_settings',
                    nonce: boss_seo_ecommerce.nonce,
                    settings: {
                        rich_snippets_types: getFormValues($(this), 'rich_snippets_types[]')
                    }
                },
                success: function(response) {
                    if (response.success) {
                        alert(response.data.message);
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('Une erreur est survenue lors de l\'enregistrement des paramètres.');
                }
            });
        });

        // Formulaire de l'analyse SEO
        $('#boss-seo-analyzer-form').on('submit', function(e) {
            e.preventDefault();

            // Récupérer les données du formulaire
            var formData = $(this).serialize();

            // Envoyer les données au serveur
            $.ajax({
                url: boss_seo_ecommerce.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_save_ecommerce_settings',
                    nonce: boss_seo_ecommerce.nonce,
                    settings: {
                        auto_analyze: getFormValue($(this), 'auto_analyze'),
                        analyze_elements: getFormValues($(this), 'analyze_elements[]')
                    }
                },
                success: function(response) {
                    if (response.success) {
                        alert(response.data.message);
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('Une erreur est survenue lors de l\'enregistrement des paramètres.');
                }
            });
        });

        // Formulaire d'ajout de marque
        $('#boss-seo-add-brand-form').on('submit', function(e) {
            e.preventDefault();

            // Récupérer les données du formulaire
            var brandName = getFormValue($(this), 'brand_name');
            var brandDescription = getFormValue($(this), 'brand_description');
            var brandLogo = getFormValue($(this), 'brand_logo');

            // Envoyer les données au serveur
            $.ajax({
                url: boss_seo_ecommerce.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_add_brand',
                    nonce: boss_seo_ecommerce.nonce,
                    brand_name: brandName,
                    brand_description: brandDescription,
                    brand_logo: brandLogo
                },
                success: function(response) {
                    if (response.success) {
                        alert(response.data.message);
                        loadBrands();
                        resetForm('#boss-seo-add-brand-form');
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('Une erreur est survenue lors de l\'ajout de la marque.');
                }
            });
        });

        // Formulaire des paramètres généraux
        $('#boss-seo-ecommerce-settings-form').on('submit', function(e) {
            e.preventDefault();

            // Récupérer les données du formulaire
            var enableRichSnippets = getFormValue($(this), 'enable_rich_snippets');
            var enableSeoAnalyzer = getFormValue($(this), 'enable_seo_analyzer');
            var enableProductBrands = getFormValue($(this), 'enable_product_brands');

            // Envoyer les données au serveur
            $.ajax({
                url: boss_seo_ecommerce.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_save_ecommerce_settings',
                    nonce: boss_seo_ecommerce.nonce,
                    settings: {
                        enable_rich_snippets: enableRichSnippets,
                        enable_seo_analyzer: enableSeoAnalyzer,
                        enable_product_brands: enableProductBrands
                    }
                },
                success: function(response) {
                    if (response.success) {
                        alert(response.data.message);
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('Une erreur est survenue lors de l\'enregistrement des paramètres.');
                }
            });
        });
    }

    /**
     * Initialise les extraits enrichis.
     */
    function initRichSnippets() {
        // Générer un extrait enrichi
        $(document).on('click', '#boss-seo-generate-rich-snippet', function() {
            var productId = $(this).data('product-id');

            // Envoyer la requête au serveur
            $.ajax({
                url: boss_seo_ecommerce.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_generate_rich_snippet',
                    nonce: boss_seo_ecommerce.nonce,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        $('#boss-seo-rich-snippet-preview').html(response.data.snippet_html);
                        $('.boss-seo-rich-snippet-result').show();
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('Une erreur est survenue lors de la génération de l\'extrait enrichi.');
                }
            });
        });

        // Tester un extrait enrichi
        $(document).on('click', '#boss-seo-test-rich-snippet', function() {
            var productId = $(this).data('product-id');

            // Envoyer la requête au serveur
            $.ajax({
                url: boss_seo_ecommerce.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_test_rich_snippet',
                    nonce: boss_seo_ecommerce.nonce,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        $('#boss-seo-rich-snippet-test-result').html(response.data.result);
                        $('.boss-seo-rich-snippet-test-result').show();
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('Une erreur est survenue lors du test de l\'extrait enrichi.');
                }
            });
        });
    }

    /**
     * Initialise l'analyse SEO.
     */
    function initSeoAnalyzer() {
        // Analyser un produit
        $(document).on('click', '#boss-seo-analyze-product', function() {
            var productId = $(this).data('product-id');

            // Envoyer la requête au serveur
            $.ajax({
                url: boss_seo_ecommerce.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_analyze_product',
                    nonce: boss_seo_ecommerce.nonce,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        $('#boss-seo-analyzer-result').html(response.data.analysis);
                        $('.boss-seo-analyzer-result').show();
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('Une erreur est survenue lors de l\'analyse du produit.');
                }
            });
        });

        // Voir l'historique d'analyse
        $(document).on('click', '#boss-seo-view-analysis-history', function() {
            var productId = $(this).data('product-id');

            // Envoyer la requête au serveur
            $.ajax({
                url: boss_seo_ecommerce.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_get_analysis_history',
                    nonce: boss_seo_ecommerce.nonce,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        $('#boss-seo-analyzer-history').html(response.data.history);
                        $('.boss-seo-analyzer-history').show();
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('Une erreur est survenue lors de la récupération de l\'historique d\'analyse.');
                }
            });
        });
    }

    /**
     * Initialise les marques de produits.
     */
    function initProductBrands() {
        // Charger les marques
        loadBrands();

        // Téléverser un logo
        $(document).on('click', '.brand-logo-upload', function() {
            var button = $(this);
            var frame;

            // Si le media uploader existe déjà, l'ouvrir
            if (frame) {
                frame.open();
                return;
            }

            // Créer le media uploader
            frame = wp.media({
                title: 'Sélectionner un logo',
                button: {
                    text: 'Utiliser ce logo'
                },
                multiple: false
            });

            // Lorsqu'une image est sélectionnée
            frame.on('select', function() {
                var attachment = frame.state().get('selection').first().toJSON();
                var logoUrl = attachment.url;
                var logoId = attachment.id;

                // Mettre à jour l'aperçu du logo
                button.closest('td').find('.brand-logo-preview').html('<img src="' + logoUrl + '" alt="Logo" style="max-width: 100px; max-height: 100px;">');
                button.closest('td').find('input[name="brand_logo"]').val(logoId);
                button.closest('td').find('.brand-logo-remove').show();
            });

            // Ouvrir le media uploader
            frame.open();
        });

        // Supprimer un logo
        $(document).on('click', '.brand-logo-remove', function() {
            var button = $(this);

            // Supprimer l'aperçu du logo
            button.closest('td').find('.brand-logo-preview').html('');
            button.closest('td').find('input[name="brand_logo"]').val('');
            button.hide();
        });

        // Modifier une marque
        $(document).on('click', '.edit-brand', function() {
            var brandId = $(this).data('brand-id');
            var brandName = $(this).data('brand-name');
            var brandDescription = $(this).data('brand-description');
            var brandLogo = $(this).data('brand-logo');
            var brandLogoUrl = $(this).data('brand-logo-url');

            // Remplir le formulaire de modification
            $('#boss-seo-edit-brand-form input[name="brand_id"]').val(brandId);
            $('#boss-seo-edit-brand-form input[name="brand_name"]').val(brandName);
            $('#boss-seo-edit-brand-form textarea[name="brand_description"]').val(brandDescription);
            $('#boss-seo-edit-brand-form input[name="brand_logo"]').val(brandLogo);

            // Mettre à jour l'aperçu du logo
            if (brandLogoUrl) {
                $('#boss-seo-edit-brand-form .brand-logo-preview').html('<img src="' + brandLogoUrl + '" alt="Logo" style="max-width: 100px; max-height: 100px;">');
                $('#boss-seo-edit-brand-form .brand-logo-remove').show();
            } else {
                $('#boss-seo-edit-brand-form .brand-logo-preview').html('');
                $('#boss-seo-edit-brand-form .brand-logo-remove').hide();
            }

            // Afficher le formulaire de modification
            $('#boss-seo-edit-brand-modal').show();
        });

        // Fermer le formulaire de modification
        $(document).on('click', '.close-modal', function() {
            $('#boss-seo-edit-brand-modal').hide();
        });

        // Soumettre le formulaire de modification
        $(document).on('submit', '#boss-seo-edit-brand-form', function(e) {
            e.preventDefault();

            // Récupérer les données du formulaire
            var brandId = getFormValue($(this), 'brand_id');
            var brandName = getFormValue($(this), 'brand_name');
            var brandDescription = getFormValue($(this), 'brand_description');
            var brandLogo = getFormValue($(this), 'brand_logo');

            // Envoyer les données au serveur
            $.ajax({
                url: boss_seo_ecommerce.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_edit_brand',
                    nonce: boss_seo_ecommerce.nonce,
                    brand_id: brandId,
                    brand_name: brandName,
                    brand_description: brandDescription,
                    brand_logo: brandLogo
                },
                success: function(response) {
                    if (response.success) {
                        alert(response.data.message);
                        loadBrands();
                        $('#boss-seo-edit-brand-modal').hide();
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('Une erreur est survenue lors de la modification de la marque.');
                }
            });
        });

        // Supprimer une marque
        $(document).on('click', '.delete-brand', function() {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cette marque ?')) {
                return;
            }

            var brandId = $(this).data('brand-id');

            // Envoyer la requête au serveur
            $.ajax({
                url: boss_seo_ecommerce.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_delete_brand',
                    nonce: boss_seo_ecommerce.nonce,
                    brand_id: brandId
                },
                success: function(response) {
                    if (response.success) {
                        alert(response.data.message);
                        loadBrands();
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('Une erreur est survenue lors de la suppression de la marque.');
                }
            });
        });
    }

    /**
     * Charge les marques.
     */
    function loadBrands() {
        // Envoyer la requête au serveur
        $.ajax({
            url: boss_seo_ecommerce.ajax_url,
            type: 'POST',
            data: {
                action: 'boss_seo_get_brands',
                nonce: boss_seo_ecommerce.nonce
            },
            success: function(response) {
                if (response.success) {
                    var brands = response.data.brands;
                    var html = '';

                    if (brands.length === 0) {
                        html = '<p>' + 'Aucune marque trouvée.' + '</p>';
                    } else {
                        html = '<table class="wp-list-table widefat fixed striped">';
                        html += '<thead><tr>';
                        html += '<th>' + 'Logo' + '</th>';
                        html += '<th>' + 'Nom' + '</th>';
                        html += '<th>' + 'Description' + '</th>';
                        html += '<th>' + 'Actions' + '</th>';
                        html += '</tr></thead>';
                        html += '<tbody>';

                        for (var i = 0; i < brands.length; i++) {
                            var brand = brands[i];
                            html += '<tr>';
                            html += '<td>' + (brand.logo_url ? '<img src="' + brand.logo_url + '" alt="Logo" style="max-width: 50px; max-height: 50px;">' : '') + '</td>';
                            html += '<td>' + brand.name + '</td>';
                            html += '<td>' + (brand.description || '') + '</td>';
                            html += '<td>';
                            html += '<button type="button" class="button button-small edit-brand" data-brand-id="' + brand.id + '" data-brand-name="' + brand.name + '" data-brand-description="' + (brand.description || '') + '" data-brand-logo="' + (brand.logo || '') + '" data-brand-logo-url="' + (brand.logo_url || '') + '">' + 'Modifier' + '</button> ';
                            html += '<button type="button" class="button button-small delete-brand" data-brand-id="' + brand.id + '">' + 'Supprimer' + '</button>';
                            html += '</td>';
                            html += '</tr>';
                        }

                        html += '</tbody></table>';
                    }

                    $('#boss-seo-brands-list').html(html);
                } else {
                    alert(response.data.message);
                }
            },
            error: function() {
                alert('Une erreur est survenue lors du chargement des marques.');
            }
        });
    }

    /**
     * Récupère la valeur d'un champ de formulaire.
     *
     * @param {jQuery} form Le formulaire.
     * @param {string} name Le nom du champ.
     * @return {string|boolean} La valeur du champ.
     */
    function getFormValue(form, name) {
        var field = form.find('[name="' + name + '"]');

        if (field.length === 0) {
            return '';
        }

        if (field.attr('type') === 'checkbox') {
            return field.is(':checked') ? '1' : '';
        }

        return field.val();
    }

    /**
     * Récupère les valeurs d'un champ de formulaire multiple.
     *
     * @param {jQuery} form Le formulaire.
     * @param {string} name Le nom du champ.
     * @return {Array} Les valeurs du champ.
     */
    function getFormValues(form, name) {
        var values = [];
        var fields = form.find('[name="' + name + '"]:checked');

        fields.each(function() {
            values.push($(this).val());
        });

        return values;
    }

    /**
     * Réinitialise un formulaire.
     *
     * @param {string} selector Le sélecteur du formulaire.
     */
    function resetForm(selector) {
        $(selector)[0].reset();
        $(selector).find('.brand-logo-preview').html('');
        $(selector).find('.brand-logo-remove').hide();
    }

    // Initialiser le module e-commerce lorsque le document est prêt
    $(document).ready(function() {
        init();
    });

})( jQuery );
