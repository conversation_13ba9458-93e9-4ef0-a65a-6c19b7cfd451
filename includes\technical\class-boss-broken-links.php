<?php

/**
 * Classe pour la gestion des liens cassés
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 */

/**
 * Classe pour la gestion des liens cassés.
 *
 * Cette classe définit toutes les fonctionnalités nécessaires pour détecter,
 * analyser et gérer les liens cassés sur le site.
 *
 * @since      1.2.0
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Broken_Links {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version actuelle du plugin.
     */
    private $version;

    /**
     * Nom de la table des liens cassés.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $table_name    Nom de la table.
     */
    private $table_name;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        global $wpdb;
        $this->table_name = $wpdb->prefix . 'boss_seo_broken_links';

        $this->init_hooks();
    }

    /**
     * Initialise les hooks WordPress.
     *
     * @since    1.2.0
     */
    private function init_hooks() {
        add_action( 'wp_loaded', array( $this, 'create_table' ) );
        add_action( 'boss_seo_broken_links_scan', array( $this, 'run_scheduled_scan' ) );
        add_action( 'boss_seo_broken_links_auto_scan', array( $this, 'run_scheduled_scan' ) );
    }

    /**
     * Enregistre les hooks WordPress nécessaires.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Cette méthode est appelée par le module technique principal
        // Les hooks sont déjà enregistrés dans init_hooks()
    }

    /**
     * Crée la table des liens cassés si elle n'existe pas.
     *
     * @since    1.2.0
     */
    public function create_table() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            url varchar(2048) NOT NULL,
            source_post_id bigint(20) DEFAULT NULL,
            source_url varchar(2048) NOT NULL,
            source_title varchar(255) DEFAULT NULL,
            anchor_text varchar(255) DEFAULT NULL,
            link_type enum('internal','external') NOT NULL DEFAULT 'external',
            status varchar(50) NOT NULL,
            status_code int(11) DEFAULT NULL,
            error_message text DEFAULT NULL,
            last_checked datetime NOT NULL,
            first_detected datetime NOT NULL,
            is_ignored tinyint(1) NOT NULL DEFAULT 0,
            check_count int(11) NOT NULL DEFAULT 1,
            PRIMARY KEY (id),
            KEY url (url(191)),
            KEY source_post_id (source_post_id),
            KEY link_type (link_type),
            KEY status (status),
            KEY is_ignored (is_ignored),
            KEY last_checked (last_checked)
        ) $charset_collate;";

        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Route pour récupérer les liens cassés
        register_rest_route( 'boss-seo/v1', '/technical/broken-links', array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_broken_links' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'type' => array(
                    'default'           => 'all',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'status' => array(
                    'default'           => 'all',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'search' => array(
                    'default'           => '',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'page' => array(
                    'default'           => 1,
                    'sanitize_callback' => 'absint'
                ),
                'per_page' => array(
                    'default'           => 20,
                    'sanitize_callback' => 'absint'
                )
            )
        ) );

        // Route pour les statistiques
        register_rest_route( 'boss-seo/v1', '/technical/broken-links/stats', array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_stats' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour lancer un scan
        register_rest_route( 'boss-seo/v1', '/technical/broken-links/scan', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'start_scan' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour revérifier un lien
        register_rest_route( 'boss-seo/v1', '/technical/broken-links/(?P<id>\d+)/recheck', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'recheck_link' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour ignorer un lien
        register_rest_route( 'boss-seo/v1', '/technical/broken-links/(?P<id>\d+)/ignore', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'ignore_link' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour les paramètres
        register_rest_route( 'boss-seo/v1', '/technical/broken-links/settings', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_settings' ),
                'permission_callback' => array( $this, 'check_permissions' )
            ),
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'save_settings' ),
                'permission_callback' => array( $this, 'check_permissions' )
            )
        ) );
    }

    /**
     * Vérifie les permissions pour les routes REST.
     *
     * @since    1.2.0
     * @return   boolean    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les liens cassés via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_broken_links( $request ) {
        global $wpdb;

        $type = $request->get_param( 'type' );
        $status = $request->get_param( 'status' );
        $search = $request->get_param( 'search' );
        $page = $request->get_param( 'page' );
        $per_page = $request->get_param( 'per_page' );

        $where_conditions = array( 'is_ignored = 0' );
        $where_values = array();

        // Filtrer par type
        if ( $type !== 'all' ) {
            $where_conditions[] = 'link_type = %s';
            $where_values[] = $type;
        }

        // Filtrer par statut
        if ( $status !== 'all' ) {
            $where_conditions[] = 'status = %s';
            $where_values[] = $status;
        }

        // Filtrer par recherche
        if ( ! empty( $search ) ) {
            $where_conditions[] = 'url LIKE %s';
            $where_values[] = '%' . $wpdb->esc_like( $search ) . '%';
        }

        $where_clause = implode( ' AND ', $where_conditions );
        $offset = ( $page - 1 ) * $per_page;

        // Requête principale
        $query = "SELECT * FROM {$this->table_name} WHERE {$where_clause} ORDER BY last_checked DESC LIMIT %d OFFSET %d";
        $where_values[] = $per_page;
        $where_values[] = $offset;

        $links = $wpdb->get_results( $wpdb->prepare( $query, $where_values ), ARRAY_A );

        // Compter le total
        $count_query = "SELECT COUNT(*) FROM {$this->table_name} WHERE {$where_clause}";
        $total = $wpdb->get_var( $wpdb->prepare( $count_query, array_slice( $where_values, 0, -2 ) ) );

        return rest_ensure_response( array(
            'links' => $links,
            'total' => (int) $total,
            'page' => $page,
            'per_page' => $per_page,
            'total_pages' => ceil( $total / $per_page )
        ) );
    }

    /**
     * Récupère les statistiques des liens cassés.
     *
     * @since    1.2.0
     * @return   WP_REST_Response    Réponse REST.
     */
    public function get_stats() {
        global $wpdb;

        $stats = array(
            'total_broken' => $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name} WHERE is_ignored = 0" ),
            'internal_broken' => $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name} WHERE link_type = 'internal' AND is_ignored = 0" ),
            'external_broken' => $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name} WHERE link_type = 'external' AND is_ignored = 0" ),
            'ignored' => $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name} WHERE is_ignored = 1" ),
            'last_scan' => get_option( 'boss_seo_broken_links_last_scan', '' )
        );

        return rest_ensure_response( array( 'stats' => $stats ) );
    }

    /**
     * Lance un scan des liens cassés.
     *
     * @since    1.2.0
     * @return   WP_REST_Response    Réponse REST.
     */
    public function start_scan() {
        // Lancer le scan en arrière-plan
        wp_schedule_single_event( time(), 'boss_seo_broken_links_scan' );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Scan des liens cassés lancé en arrière-plan.', 'boss-seo' )
        ) );
    }

    /**
     * Exécute le scan des liens cassés.
     *
     * @since    1.2.0
     */
    public function run_scheduled_scan() {
        $this->scan_posts_for_links();
        update_option( 'boss_seo_broken_links_last_scan', current_time( 'mysql' ) );
    }

    /**
     * Scanne les posts pour détecter les liens cassés.
     *
     * @since    1.2.0
     */
    private function scan_posts_for_links() {
        $posts = get_posts( array(
            'post_type' => array( 'post', 'page' ),
            'post_status' => 'publish',
            'numberposts' => -1
        ) );

        foreach ( $posts as $post ) {
            $this->scan_post_content( $post );
        }
    }

    /**
     * Scanne le contenu d'un post pour détecter les liens.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post à scanner.
     */
    private function scan_post_content( $post ) {
        $content = $post->post_content;

        // Extraire tous les liens du contenu
        preg_match_all( '/<a[^>]+href=["\']([^"\']+)["\'][^>]*>([^<]*)<\/a>/i', $content, $matches, PREG_SET_ORDER );

        foreach ( $matches as $match ) {
            $url = $match[1];
            $anchor_text = strip_tags( $match[2] );

            // Ignorer les liens internes relatifs et les ancres
            if ( strpos( $url, '#' ) === 0 || strpos( $url, 'mailto:' ) === 0 || strpos( $url, 'tel:' ) === 0 ) {
                continue;
            }

            // Déterminer si c'est un lien interne ou externe
            $is_internal = $this->is_internal_link( $url );
            $link_type = $is_internal ? 'internal' : 'external';

            // Vérifier le lien
            $this->check_link( $url, $post, $anchor_text, $link_type );
        }
    }

    /**
     * Vérifie si un lien est interne.
     *
     * @since    1.2.0
     * @param    string    $url    L'URL à vérifier.
     * @return   boolean           True si le lien est interne, false sinon.
     */
    private function is_internal_link( $url ) {
        $site_url = home_url();
        return strpos( $url, $site_url ) === 0 || strpos( $url, '/' ) === 0;
    }

    /**
     * Vérifie un lien et l'enregistre s'il est cassé.
     *
     * @since    1.2.0
     * @param    string    $url         L'URL à vérifier.
     * @param    WP_Post   $post        Le post source.
     * @param    string    $anchor_text Le texte d'ancrage.
     * @param    string    $link_type   Le type de lien (internal/external).
     */
    private function check_link( $url, $post, $anchor_text, $link_type ) {
        $settings = $this->get_settings_data();

        // Ne pas vérifier les liens externes si désactivé
        if ( $link_type === 'external' && ! $settings['check_external'] ) {
            return;
        }

        $response = wp_remote_head( $url, array(
            'timeout' => $settings['timeout'],
            'user-agent' => $settings['user_agent'],
            'redirection' => 5
        ) );

        $status_code = wp_remote_retrieve_response_code( $response );
        $is_broken = false;
        $status = 'ok';
        $error_message = '';

        if ( is_wp_error( $response ) ) {
            $is_broken = true;
            $error_message = $response->get_error_message();

            // Déterminer le type d'erreur
            if ( strpos( $error_message, 'timeout' ) !== false ) {
                $status = 'timeout';
            } elseif ( strpos( $error_message, 'SSL' ) !== false ) {
                $status = 'ssl';
            } elseif ( strpos( $error_message, 'resolve' ) !== false ) {
                $status = 'dns';
            } else {
                $status = 'other';
            }
        } elseif ( $status_code >= 400 ) {
            $is_broken = true;
            $status = (string) $status_code;
            $error_message = sprintf( __( 'Code de statut HTTP: %d', 'boss-seo' ), $status_code );
        }

        // Enregistrer le lien cassé
        if ( $is_broken ) {
            $this->save_broken_link( $url, $post, $anchor_text, $link_type, $status, $status_code, $error_message );
        }
    }

    /**
     * Enregistre un lien cassé dans la base de données.
     *
     * @since    1.2.0
     * @param    string    $url           L'URL cassée.
     * @param    WP_Post   $post          Le post source.
     * @param    string    $anchor_text   Le texte d'ancrage.
     * @param    string    $link_type     Le type de lien.
     * @param    string    $status        Le statut d'erreur.
     * @param    int       $status_code   Le code de statut HTTP.
     * @param    string    $error_message Le message d'erreur.
     */
    private function save_broken_link( $url, $post, $anchor_text, $link_type, $status, $status_code, $error_message ) {
        global $wpdb;

        // Vérifier si le lien existe déjà
        $existing = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE url = %s AND source_post_id = %d",
            $url,
            $post->ID
        ) );

        $current_time = current_time( 'mysql' );

        if ( $existing ) {
            // Mettre à jour le lien existant
            $wpdb->update(
                $this->table_name,
                array(
                    'status' => $status,
                    'status_code' => $status_code,
                    'error_message' => $error_message,
                    'last_checked' => $current_time,
                    'check_count' => $existing->check_count + 1
                ),
                array( 'id' => $existing->id ),
                array( '%s', '%d', '%s', '%s', '%d' ),
                array( '%d' )
            );
        } else {
            // Insérer un nouveau lien cassé
            $wpdb->insert(
                $this->table_name,
                array(
                    'url' => $url,
                    'source_post_id' => $post->ID,
                    'source_url' => get_permalink( $post->ID ),
                    'source_title' => $post->post_title,
                    'anchor_text' => $anchor_text,
                    'link_type' => $link_type,
                    'status' => $status,
                    'status_code' => $status_code,
                    'error_message' => $error_message,
                    'last_checked' => $current_time,
                    'first_detected' => $current_time,
                    'is_ignored' => 0,
                    'check_count' => 1
                ),
                array( '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%d', '%d' )
            );
        }
    }

    /**
     * Revérifie un lien spécifique.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function recheck_link( $request ) {
        global $wpdb;

        $link_id = $request->get_param( 'id' );

        $link = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE id = %d",
            $link_id
        ) );

        if ( ! $link ) {
            return new WP_Error( 'link_not_found', __( 'Lien non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Revérifier le lien
        $post = get_post( $link->source_post_id );
        if ( $post ) {
            $this->check_link( $link->url, $post, $link->anchor_text, $link->link_type );
        }

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Lien revérifié avec succès.', 'boss-seo' )
        ) );
    }

    /**
     * Ignore un lien cassé.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function ignore_link( $request ) {
        global $wpdb;

        $link_id = $request->get_param( 'id' );

        $result = $wpdb->update(
            $this->table_name,
            array( 'is_ignored' => 1 ),
            array( 'id' => $link_id ),
            array( '%d' ),
            array( '%d' )
        );

        if ( $result === false ) {
            return new WP_Error( 'update_failed', __( 'Erreur lors de la mise à jour.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Lien ajouté à la liste d\'ignorés.', 'boss-seo' )
        ) );
    }

    /**
     * Récupère les paramètres de détection des liens cassés.
     *
     * @since    1.2.0
     * @return   WP_REST_Response    Réponse REST.
     */
    public function get_settings() {
        $settings = $this->get_settings_data();
        return rest_ensure_response( array( 'settings' => $settings ) );
    }

    /**
     * Récupère les données des paramètres.
     *
     * @since    1.2.0
     * @return   array    Les paramètres.
     */
    private function get_settings_data() {
        return get_option( 'boss_seo_broken_links_settings', array(
            'auto_scan' => true,
            'scan_frequency' => 'weekly',
            'check_external' => true,
            'timeout' => 30,
            'user_agent' => 'Boss SEO Link Checker'
        ) );
    }

    /**
     * Sauvegarde les paramètres de détection des liens cassés.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function save_settings( $request ) {
        $settings = array(
            'auto_scan' => (bool) $request->get_param( 'auto_scan' ),
            'scan_frequency' => sanitize_text_field( $request->get_param( 'scan_frequency' ) ),
            'check_external' => (bool) $request->get_param( 'check_external' ),
            'timeout' => absint( $request->get_param( 'timeout' ) ),
            'user_agent' => sanitize_text_field( $request->get_param( 'user_agent' ) )
        );

        update_option( 'boss_seo_broken_links_settings', $settings );

        // Programmer ou déprogrammer le scan automatique
        $this->schedule_auto_scan( $settings );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Paramètres sauvegardés avec succès.', 'boss-seo' )
        ) );
    }

    /**
     * Programme ou déprogramme le scan automatique.
     *
     * @since    1.2.0
     * @param    array    $settings    Les paramètres.
     */
    private function schedule_auto_scan( $settings ) {
        // Supprimer l'ancien cron
        wp_clear_scheduled_hook( 'boss_seo_broken_links_auto_scan' );

        if ( $settings['auto_scan'] ) {
            // Programmer le nouveau cron
            wp_schedule_event( time(), $settings['scan_frequency'], 'boss_seo_broken_links_auto_scan' );
        }
    }
}
