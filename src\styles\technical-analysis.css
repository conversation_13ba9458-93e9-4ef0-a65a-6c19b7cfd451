/* Styles pour le module d'analyse technique amélioré */

/* Grille responsive pour les métriques */
.boss-grid {
  display: grid;
}

.boss-grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.boss-grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.boss-grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.boss-gap-3 {
  gap: 0.75rem;
}

.boss-gap-4 {
  gap: 1rem;
}

.boss-gap-6 {
  gap: 1.5rem;
}

/* Responsive design */
@media (min-width: 768px) {
  .boss-md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .boss-md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* Espacement */
.boss-space-y-3 > * + * {
  margin-top: 0.75rem;
}

.boss-space-y-4 > * + * {
  margin-top: 1rem;
}

.boss-space-y-6 > * + * {
  margin-top: 1.5rem;
}

/* Padding */
.boss-p-3 {
  padding: 0.75rem;
}

.boss-p-4 {
  padding: 1rem;
}

/* Margin */
.boss-mt-3 {
  margin-top: 0.75rem;
}

.boss-mt-4 {
  margin-top: 1rem;
}

.boss-mb-2 {
  margin-bottom: 0.5rem;
}

.boss-mb-3 {
  margin-bottom: 0.75rem;
}

/* Bordures */
.boss-border {
  border-width: 1px;
}

.boss-border-t {
  border-top-width: 1px;
}

.boss-border-gray-100 {
  border-color: #f3f4f6;
}

.boss-border-gray-200 {
  border-color: #e5e7eb;
}

.boss-rounded {
  border-radius: 0.25rem;
}

.boss-rounded-lg {
  border-radius: 0.5rem;
}

/* Positionnement */
.boss-relative {
  position: relative;
}

.boss-absolute {
  position: absolute;
}

.boss-top-2 {
  top: 0.5rem;
}

.boss-right-2 {
  right: 0.5rem;
}

/* Texte */
.boss-text-center {
  text-align: center;
}

.boss-text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.boss-text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.boss-text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.boss-text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.boss-font-bold {
  font-weight: 700;
}

.boss-font-medium {
  font-weight: 500;
}

.boss-text-gray-500 {
  color: #6b7280;
}

.boss-text-gray-600 {
  color: #4b5563;
}

.boss-text-blue-800 {
  color: #1e40af;
}

/* Couleurs de fond */
.boss-bg-blue-100 {
  background-color: #dbeafe;
}

.boss-bg-gray-100 {
  background-color: #f3f4f6;
}

/* Padding pour les badges */
.boss-px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.boss-py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

/* Styles spécifiques pour les Core Web Vitals */
.core-web-vitals-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.core-web-vitals-metric {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.core-web-vitals-metric:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-value {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metric-name {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 4px;
  opacity: 0.9;
}

.metric-description {
  font-size: 0.75rem;
  opacity: 0.7;
  margin-bottom: 12px;
}

.metric-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-status.good {
  background-color: rgba(34, 197, 94, 0.2);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.metric-status.needs-improvement {
  background-color: rgba(251, 191, 36, 0.2);
  color: #d97706;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.metric-status.poor {
  background-color: rgba(239, 68, 68, 0.2);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Styles pour les badges NEW et OBSOLÈTE */
.metric-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 0.625rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-badge.new {
  background-color: #3b82f6;
  color: white;
  animation: pulse 2s infinite;
}

.metric-badge.obsolete {
  background-color: #6b7280;
  color: white;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Styles pour les sections d'analyse Schema et Hreflang */
.analysis-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.analysis-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.analysis-body {
  padding: 20px;
}

.issue-item {
  border-left: 4px solid transparent;
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 0 8px 8px 0;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.issue-item:hover {
  background: #f1f5f9;
  transform: translateX(2px);
}

.issue-item.error {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.issue-item.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.issue-item.success {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.recommendation-item {
  border-left: 4px solid transparent;
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 0 8px 8px 0;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.recommendation-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.recommendation-item.high {
  border-left-color: #ef4444;
}

.recommendation-item.medium {
  border-left-color: #f59e0b;
}

.recommendation-item.low {
  border-left-color: #10b981;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .boss-grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .core-web-vitals-card {
    padding: 16px;
  }
  
  .metric-value {
    font-size: 2rem;
  }
  
  .analysis-header,
  .analysis-body {
    padding: 16px;
  }
}
