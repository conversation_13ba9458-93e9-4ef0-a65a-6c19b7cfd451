import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Dashicon,
  Panel,
  PanelBody,
  PanelRow,
  TabPanel,
  TextControl,
  TextareaControl,
  ToggleControl
} from '@wordpress/components';
import { useState } from '@wordpress/element';

// Services
import optimizerService from '../../services/OptimizerService';

/**
 * Composant pour le panneau latéral de détails
 */
const DetailPanel = ({
  item,
  onClose,
  onOptimize,
  onAnalyze,
  isOptimizing = false,
  optimizationResults = null
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [optimizationSettings, setOptimizationSettings] = useState({
    title: true,
    description: true,
    content: true,
    headings: true,
    images: true,
    links: true,
    useAI: true
  });

  // État pour les résultats d'analyse
  const [analysisResults, setAnalysisResults] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Fonction pour obtenir la classe de couleur en fonction du score SEO
  const getSeoScoreColorClass = (score) => {
    if (score >= 80) return 'boss-text-green-600';
    if (score >= 60) return 'boss-text-yellow-600';
    if (score >= 40) return 'boss-text-orange-600';
    return 'boss-text-red-600';
  };

  // Fonction pour obtenir le libellé du statut
  const getStatusLabel = (status) => {
    switch (status) {
      case 'publish':
        return __('Publié', 'boss-seo');
      case 'draft':
        return __('Brouillon', 'boss-seo');
      case 'pending':
        return __('En attente', 'boss-seo');
      case 'private':
        return __('Privé', 'boss-seo');
      default:
        return status;
    }
  };

  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColorClass = (status) => {
    switch (status) {
      case 'publish':
        return 'boss-text-green-600';
      case 'draft':
        return 'boss-text-gray-600';
      case 'pending':
        return 'boss-text-yellow-600';
      case 'private':
        return 'boss-text-purple-600';
      default:
        return 'boss-text-gray-600';
    }
  };

  // Fonction pour obtenir le libellé du type de contenu
  const getContentTypeLabel = (type) => {
    switch (type) {
      case 'post':
        return __('Article', 'boss-seo');
      case 'page':
        return __('Page', 'boss-seo');
      case 'product':
        return __('Produit', 'boss-seo');
      case 'portfolio':
        return __('Portfolio', 'boss-seo');
      default:
        return type;
    }
  };

  // Fonction pour formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Fonction pour gérer l'optimisation
  const handleOptimize = async () => {
    // Appeler la fonction d'optimisation avec les paramètres
    onOptimize(item.id, optimizationSettings);
  };

  // Fonction pour gérer l'analyse
  const handleAnalyze = async () => {
    setIsAnalyzing(true);

    try {
      // Appeler la fonction d'analyse
      const results = await onAnalyze(item.id);
      setAnalysisResults(results);

      // Passer à l'onglet des recommandations
      if (results && results.success) {
        setActiveTab('recommendations');
      }
    } catch (error) {
      console.error('Erreur lors de l\'analyse:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Fonction pour obtenir l'icône en fonction du type de recommandation
  const getRecommendationIcon = (type) => {
    switch (type) {
      case 'critical':
        return 'warning';
      case 'warning':
        return 'flag';
      case 'info':
        return 'info';
      default:
        return 'info';
    }
  };

  // Fonction pour obtenir la classe de couleur en fonction du type de recommandation
  const getRecommendationColorClass = (type) => {
    switch (type) {
      case 'critical':
        return 'boss-text-red-600';
      case 'warning':
        return 'boss-text-yellow-600';
      case 'info':
        return 'boss-text-blue-600';
      default:
        return 'boss-text-gray-600';
    }
  };

  return (
    <div className="boss-fixed boss-inset-0 boss-bg-black boss-bg-opacity-50 boss-z-50 boss-flex boss-justify-end">
      <div className="boss-bg-white boss-w-full boss-max-w-md boss-h-full boss-overflow-auto boss-shadow-xl boss-transition-transform boss-duration-300 boss-transform boss-translate-x-0">
        {/* En-tête du panneau */}
        <div className="boss-sticky boss-top-0 boss-bg-white boss-z-10 boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center boss-p-4">
            <h2 className="boss-text-xl boss-font-bold boss-text-boss-dark">
              {__('Détails du contenu', 'boss-seo')}
            </h2>
            <Button
              isSecondary
              icon="no-alt"
              onClick={onClose}
              label={__('Fermer', 'boss-seo')}
              showTooltip
            />
          </div>

          {/* Onglets */}
          <div className="boss-border-b boss-border-gray-200">
            <div className="boss-flex">
              <button
                className={`boss-flex-1 boss-py-3 boss-px-4 boss-text-center boss-font-medium boss-transition-colors boss-duration-200 ${
                  activeTab === 'overview'
                    ? 'boss-text-boss-primary boss-border-b-2 boss-border-boss-primary'
                    : 'boss-text-boss-gray boss-hover:boss-text-boss-dark'
                }`}
                onClick={() => setActiveTab('overview')}
              >
                {__('Aperçu', 'boss-seo')}
              </button>
              <button
                className={`boss-flex-1 boss-py-3 boss-px-4 boss-text-center boss-font-medium boss-transition-colors boss-duration-200 ${
                  activeTab === 'recommendations'
                    ? 'boss-text-boss-primary boss-border-b-2 boss-border-boss-primary'
                    : 'boss-text-boss-gray boss-hover:boss-text-boss-dark'
                }`}
                onClick={() => setActiveTab('recommendations')}
              >
                {__('Recommandations', 'boss-seo')}
              </button>
              <button
                className={`boss-flex-1 boss-py-3 boss-px-4 boss-text-center boss-font-medium boss-transition-colors boss-duration-200 ${
                  activeTab === 'optimize'
                    ? 'boss-text-boss-primary boss-border-b-2 boss-border-boss-primary'
                    : 'boss-text-boss-gray boss-hover:boss-text-boss-dark'
                }`}
                onClick={() => setActiveTab('optimize')}
              >
                {__('Optimiser', 'boss-seo')}
              </button>
            </div>
          </div>
        </div>

        {/* Contenu du panneau */}
        <div className="boss-p-6">
          {/* Onglet Aperçu */}
          {activeTab === 'overview' && (
            <div>
              {/* Titre et URL */}
              <div className="boss-mb-6">
                <h3 className="boss-text-xl boss-font-bold boss-text-boss-dark boss-mb-2">
                  {item.title}
                </h3>
                <a
                  href={item.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="boss-text-boss-primary boss-hover:boss-underline boss-text-sm boss-break-all"
                >
                  {item.url}
                </a>
              </div>

              {/* Score SEO */}
              <div className="boss-mb-6 boss-flex boss-items-center boss-justify-between boss-p-4 boss-bg-gray-50 boss-rounded-lg">
                <div>
                  <div className="boss-text-sm boss-text-boss-gray boss-mb-1">
                    {__('Score SEO', 'boss-seo')}
                  </div>
                  <div className={`boss-text-3xl boss-font-bold ${getSeoScoreColorClass(item.seoScore)}`}>
                    {item.seoScore}/100
                  </div>
                </div>
                <div className="boss-w-20 boss-h-20 boss-relative">
                  <svg className="boss-w-full boss-h-full boss-transform boss-rotate-[-90deg]" viewBox="0 0 100 100">
                    <circle
                      cx="50"
                      cy="50"
                      r="45"
                      fill="transparent"
                      stroke="#e5e7eb"
                      strokeWidth="10"
                    />
                    <circle
                      cx="50"
                      cy="50"
                      r="45"
                      fill="transparent"
                      stroke={
                        item.seoScore >= 80 ? '#10B981' :
                        item.seoScore >= 60 ? '#FBBF24' :
                        item.seoScore >= 40 ? '#F97316' :
                        '#EF4444'
                      }
                      strokeWidth="10"
                      strokeDasharray={`${2 * Math.PI * 45 * item.seoScore / 100} ${2 * Math.PI * 45 * (1 - item.seoScore / 100)}`}
                      strokeLinecap="round"
                    />
                  </svg>
                </div>
              </div>

              {/* Informations générales */}
              <div className="boss-mb-6">
                <h4 className="boss-text-lg boss-font-semibold boss-mb-3">
                  {__('Informations générales', 'boss-seo')}
                </h4>
                <div className="boss-space-y-4">
                  <div className="boss-flex boss-justify-between boss-items-center boss-py-2 boss-border-b boss-border-gray-100">
                    <span className="boss-text-boss-gray">{__('Type', 'boss-seo')}</span>
                    <span className="boss-font-medium">{getContentTypeLabel(item.type)}</span>
                  </div>
                  <div className="boss-flex boss-justify-between boss-items-center boss-py-2 boss-border-b boss-border-gray-100">
                    <span className="boss-text-boss-gray">{__('Statut', 'boss-seo')}</span>
                    <span className={`boss-font-medium ${getStatusColorClass(item.status)}`}>
                      {getStatusLabel(item.status)}
                    </span>
                  </div>
                  <div className="boss-flex boss-justify-between boss-items-center boss-py-2 boss-border-b boss-border-gray-100">
                    <span className="boss-text-boss-gray">{__('Date', 'boss-seo')}</span>
                    <span className="boss-font-medium">{formatDate(item.date)}</span>
                  </div>
                  <div className="boss-flex boss-justify-between boss-items-center boss-py-2 boss-border-b boss-border-gray-100">
                    <span className="boss-text-boss-gray">{__('Auteur', 'boss-seo')}</span>
                    <span className="boss-font-medium">{item.author}</span>
                  </div>
                </div>
              </div>

              {/* Extrait */}
              <div className="boss-mb-6">
                <h4 className="boss-text-lg boss-font-semibold boss-mb-3">
                  {__('Extrait', 'boss-seo')}
                </h4>
                <p className="boss-text-boss-gray boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-text-sm">
                  {item.excerpt}
                </p>
              </div>

              {/* Actions rapides */}
              <div className="boss-flex boss-flex-wrap boss-gap-2">
                <Button
                  isPrimary
                  className="boss-flex boss-items-center"
                  onClick={() => setActiveTab('optimize')}
                  isBusy={isOptimizing}
                  disabled={isOptimizing}
                >
                  <Dashicon icon="performance" className="boss-mr-1" />
                  {__('Optimiser', 'boss-seo')}
                </Button>
                <Button
                  isSecondary
                  className="boss-flex boss-items-center"
                  onClick={handleAnalyze}
                  isBusy={isAnalyzing}
                  disabled={isAnalyzing}
                >
                  <Dashicon icon="search" className="boss-mr-1" />
                  {__('Analyser', 'boss-seo')}
                </Button>
                <Button
                  isSecondary
                  className="boss-flex boss-items-center"
                  onClick={() => window.open(item.url, '_blank')}
                >
                  <Dashicon icon="edit" className="boss-mr-1" />
                  {__('Éditer', 'boss-seo')}
                </Button>
                <Button
                  isSecondary
                  className="boss-flex boss-items-center"
                  onClick={() => window.open(item.url, '_blank')}
                >
                  <Dashicon icon="visibility" className="boss-mr-1" />
                  {__('Voir', 'boss-seo')}
                </Button>
              </div>
            </div>
          )}

          {/* Onglet Recommandations */}
          {activeTab === 'recommendations' && (
            <div>
              <div className="boss-mb-6">
                <div className="boss-flex boss-justify-between boss-items-center boss-mb-4">
                  <h3 className="boss-text-lg boss-font-semibold">
                    {__('Recommandations d\'optimisation', 'boss-seo')}
                  </h3>
                  <div className="boss-text-sm boss-text-boss-gray">
                    {item.recommendations.length} {item.recommendations.length === 1
                      ? __('recommandation', 'boss-seo')
                      : __('recommandations', 'boss-seo')
                    }
                  </div>
                </div>

                {/* Compteurs par type */}
                <div className="boss-flex boss-justify-between boss-mb-6 boss-bg-gray-50 boss-p-4 boss-rounded-lg">
                  <div className="boss-text-center">
                    <div className="boss-text-red-600 boss-font-bold boss-text-xl">
                      {item.recommendations.filter(rec => rec.type === 'critical').length}
                    </div>
                    <div className="boss-text-xs boss-text-boss-gray">
                      {__('Critiques', 'boss-seo')}
                    </div>
                  </div>
                  <div className="boss-text-center">
                    <div className="boss-text-yellow-600 boss-font-bold boss-text-xl">
                      {item.recommendations.filter(rec => rec.type === 'warning').length}
                    </div>
                    <div className="boss-text-xs boss-text-boss-gray">
                      {__('Avertissements', 'boss-seo')}
                    </div>
                  </div>
                  <div className="boss-text-center">
                    <div className="boss-text-blue-600 boss-font-bold boss-text-xl">
                      {item.recommendations.filter(rec => rec.type === 'info').length}
                    </div>
                    <div className="boss-text-xs boss-text-boss-gray">
                      {__('Suggestions', 'boss-seo')}
                    </div>
                  </div>
                </div>

                {/* Liste des recommandations */}
                <div className="boss-space-y-3">
                  {/* Afficher les recommandations des résultats d'analyse si disponibles, sinon utiliser celles de l'élément */}
                  {(analysisResults && analysisResults.recommendations ? analysisResults.recommendations : item.recommendations).map((rec) => {
                    // État local pour suivre l'application de la recommandation
                    const [isApplying, setIsApplying] = useState(false);
                    const [isApplied, setIsApplied] = useState(false);

                    // Fonction pour appliquer une recommandation
                    const applyRecommendation = async () => {
                      try {
                        setIsApplying(true);

                        // Appeler l'API pour appliquer la recommandation
                        const result = await optimizerService.applyRecommendation(item.id, rec.id);

                        if (result.success) {
                          setIsApplied(true);

                          // Rafraîchir l'analyse après l'application de la recommandation
                          if (onAnalyze) {
                            onAnalyze(item.id);
                          }
                        }
                      } catch (error) {
                        console.error('Erreur lors de l\'application de la recommandation:', error);
                      } finally {
                        setIsApplying(false);
                      }
                    };

                    return (
                      <div
                        key={rec.id}
                        className={`boss-border boss-rounded-lg boss-p-4 boss-flex boss-items-start ${isApplied ? 'boss-opacity-50 boss-border-green-200 boss-bg-green-50' : 'boss-border-gray-200'}`}
                      >
                        <div className={`boss-mr-3 ${isApplied ? 'boss-text-green-600' : getRecommendationColorClass(rec.type)}`}>
                          <Dashicon icon={isApplied ? 'yes-alt' : getRecommendationIcon(rec.type)} />
                        </div>
                        <div className="boss-flex-1">
                          <p className={`boss-text-boss-dark boss-font-medium ${isApplied ? 'boss-line-through' : ''}`}>
                            {rec.text}
                          </p>
                        </div>
                        {isApplied ? (
                          <span className="boss-text-xs boss-text-green-600 boss-italic boss-ml-2">
                            {__('Appliquée', 'boss-seo')}
                          </span>
                        ) : (
                          <Button
                            isSmall
                            isSecondary
                            className="boss-ml-2"
                            onClick={applyRecommendation}
                            isBusy={isApplying}
                            disabled={isApplying}
                          >
                            {isApplying ? __('Application...', 'boss-seo') : __('Corriger', 'boss-seo')}
                          </Button>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              <Button
                isPrimary
                className="boss-w-full boss-justify-center boss-mt-4"
                onClick={() => setActiveTab('optimize')}
              >
                {__('Optimiser automatiquement', 'boss-seo')}
              </Button>
            </div>
          )}

          {/* Onglet Optimiser */}
          {activeTab === 'optimize' && (
            <div>
              <div className="boss-mb-6">
                <h3 className="boss-text-lg boss-font-semibold boss-mb-4">
                  {__('Paramètres d\'optimisation', 'boss-seo')}
                </h3>

                <div className="boss-space-y-4 boss-mb-6">
                  <ToggleControl
                    label={__('Optimiser le titre', 'boss-seo')}
                    checked={optimizationSettings.title}
                    onChange={() => setOptimizationSettings({
                      ...optimizationSettings,
                      title: !optimizationSettings.title
                    })}
                  />
                  <ToggleControl
                    label={__('Optimiser la méta description', 'boss-seo')}
                    checked={optimizationSettings.description}
                    onChange={() => setOptimizationSettings({
                      ...optimizationSettings,
                      description: !optimizationSettings.description
                    })}
                  />
                  <ToggleControl
                    label={__('Optimiser le contenu', 'boss-seo')}
                    checked={optimizationSettings.content}
                    onChange={() => setOptimizationSettings({
                      ...optimizationSettings,
                      content: !optimizationSettings.content
                    })}
                  />
                  <ToggleControl
                    label={__('Optimiser les titres (H1, H2, H3)', 'boss-seo')}
                    checked={optimizationSettings.headings}
                    onChange={() => setOptimizationSettings({
                      ...optimizationSettings,
                      headings: !optimizationSettings.headings
                    })}
                  />
                  <ToggleControl
                    label={__('Optimiser les images', 'boss-seo')}
                    checked={optimizationSettings.images}
                    onChange={() => setOptimizationSettings({
                      ...optimizationSettings,
                      images: !optimizationSettings.images
                    })}
                  />
                  <ToggleControl
                    label={__('Optimiser les liens', 'boss-seo')}
                    checked={optimizationSettings.links}
                    onChange={() => setOptimizationSettings({
                      ...optimizationSettings,
                      links: !optimizationSettings.links
                    })}
                  />
                </div>

                <div className="boss-bg-indigo-50 boss-border boss-border-indigo-100 boss-rounded-lg boss-p-4 boss-mb-6">
                  <div className="boss-flex boss-items-start">
                    <Dashicon icon="admin-plugins" className="boss-text-indigo-600 boss-mr-3 boss-mt-1" />
                    <div>
                      <h4 className="boss-font-medium boss-text-indigo-800 boss-mb-1">
                        {__('Optimisation IA', 'boss-seo')}
                      </h4>
                      <p className="boss-text-sm boss-text-indigo-700 boss-mb-3">
                        {__('Utilisez notre intelligence artificielle pour optimiser automatiquement votre contenu selon les meilleures pratiques SEO.', 'boss-seo')}
                      </p>
                      <ToggleControl
                        label={__('Utiliser l\'IA pour l\'optimisation', 'boss-seo')}
                        checked={optimizationSettings.useAI}
                        onChange={() => setOptimizationSettings({
                          ...optimizationSettings,
                          useAI: !optimizationSettings.useAI
                        })}
                      />
                    </div>
                  </div>
                </div>

                <Button
                  isPrimary
                  isBusy={isOptimizing}
                  disabled={isOptimizing}
                  className="boss-w-full boss-justify-center"
                  onClick={handleOptimize}
                >
                  {isOptimizing
                    ? __('Optimisation en cours...', 'boss-seo')
                    : __('Lancer l\'optimisation', 'boss-seo')
                  }
                </Button>

                {/* Afficher les résultats d'optimisation s'ils existent */}
                {optimizationResults && optimizationResults.success && (
                  <div className="boss-mt-4 boss-bg-green-50 boss-border boss-border-green-200 boss-rounded-lg boss-p-4">
                    <div className="boss-flex boss-items-start">
                      <Dashicon icon="yes-alt" className="boss-text-green-600 boss-mr-3 boss-mt-1" />
                      <div>
                        <h4 className="boss-font-medium boss-text-green-800 boss-mb-1">
                          {__('Optimisation réussie', 'boss-seo')}
                        </h4>
                        <p className="boss-text-sm boss-text-green-700">
                          {__('Le contenu a été optimisé avec succès. Le score SEO a été amélioré.', 'boss-seo')}
                        </p>
                        {optimizationResults.analysis_results && (
                          <p className="boss-text-sm boss-text-green-700 boss-mt-2">
                            {__('Nouveau score SEO:', 'boss-seo')} <span className="boss-font-bold">{optimizationResults.analysis_results.overall_score}/100</span>
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DetailPanel;
