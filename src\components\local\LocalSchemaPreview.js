import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  SelectControl,
  TextControl,
  ToggleControl,
  Notice,
  Spinner,
  TabPanel
} from '@wordpress/components';

// Importer le service
import LocalSeoService from '../../services/LocalSeoService';

const LocalSchemaPreview = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [locations, setLocations] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [schemaType, setSchemaType] = useState('LocalBusiness');
  const [schemaSettings, setSchemaSettings] = useState({
    includeOpeningHours: true,
    includeGeo: true,
    includeLogo: true,
    includePriceRange: true,
    includePaymentAccepted: true,
    includeReviews: true
  });
  const [schemaJson, setSchemaJson] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);

  // Créer une instance du service
  const localSeoService = new LocalSeoService();

  // Charger les données
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Récupérer les emplacements
        const response = await localSeoService.getLocations();

        // Mettre à jour l'état
        setLocations(response.locations || []);
      } catch (err) {
        console.error('Erreur lors du chargement des emplacements:', err);
        setError(__('Erreur lors du chargement des emplacements. Veuillez réessayer.', 'boss-seo'));

        // Utiliser des données fictives en cas d'erreur pour le développement
        // À supprimer en production
        const mockLocations = [
          {
            id: 1,
            name: 'Paris - Siège social',
            address: '123 Avenue des Champs-Élysées, 75008 Paris',
            phone: '01 23 45 67 89',
            email: '<EMAIL>',
            website: 'https://example.com/paris',
            openingHours: 'Mo-Fr 09:00-18:00, Sa 10:00-17:00',
            description: 'Notre siège social au cœur de Paris',
            priceRange: '€€',
            paymentAccepted: ['cash', 'credit_card', 'paypal'],
            geo: {
              latitude: '48.8698',
              longitude: '2.3075'
            },
            logo: 'https://example.com/logo.png',
            hasSchema: true
          },
          {
            id: 2,
            name: 'Lyon - Succursale',
            address: '45 Rue de la République, 69002 Lyon',
            phone: '04 78 12 34 56',
            email: '<EMAIL>',
            website: 'https://example.com/lyon',
            openingHours: 'Mo-Fr 09:00-18:00, Sa 10:00-16:00',
            description: 'Notre boutique au centre de Lyon',
            priceRange: '€€',
            paymentAccepted: ['cash', 'credit_card'],
            geo: {
              latitude: '45.7640',
              longitude: '4.8357'
            },
            logo: 'https://example.com/logo.png',
            hasSchema: false
          },
          {
            id: 3,
            name: 'Marseille - Boutique',
            address: '78 La Canebière, 13001 Marseille',
            phone: '04 91 23 45 67',
            email: '<EMAIL>',
            website: 'https://example.com/marseille',
            openingHours: 'Mo-Sa 10:00-19:00',
            description: 'Notre nouvelle boutique à Marseille',
            priceRange: '€€',
            paymentAccepted: ['cash', 'credit_card'],
            geo: {
              latitude: '43.2965',
              longitude: '5.3698'
            },
            logo: 'https://example.com/logo.png',
            hasSchema: false
          }
        ];

        setLocations(mockLocations);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLocations();
  }, []);

  // Effet pour générer le JSON-LD lorsque l'emplacement ou les paramètres changent
  useEffect(() => {
    if (selectedLocation) {
      generateSchemaJson();
    }
  }, [selectedLocation, schemaType, schemaSettings]);

  // Fonction pour générer le JSON-LD
  const generateSchemaJson = async () => {
    if (!selectedLocation) return;

    try {
      setIsLoading(true);
      setError(null);

      // Préparer les options pour le schéma
      const options = {
        ...schemaSettings
      };

      // Appeler le service pour générer le schéma
      const response = await localSeoService.generateSchema(selectedLocation.id, schemaType);

      // Mettre à jour l'état avec le schéma généré
      setSchemaJson(JSON.stringify(response.schema, null, 2));
    } catch (err) {
      console.error('Erreur lors de la génération du schéma:', err);
      setError(__('Erreur lors de la génération du schéma. Veuillez réessayer.', 'boss-seo'));

      // Générer un schéma localement en cas d'erreur (pour le développement)
      // Créer l'objet de base du schéma
      const schema = {
        '@context': 'https://schema.org',
        '@type': schemaType,
        '@id': `${selectedLocation.website}#${schemaType.toLowerCase()}`,
        'name': selectedLocation.name,
        'description': selectedLocation.description,
        'url': selectedLocation.website,
        'telephone': selectedLocation.phone,
        'email': selectedLocation.email,
        'address': {
          '@type': 'PostalAddress',
          'streetAddress': selectedLocation.address.split(',')[0],
          'addressLocality': selectedLocation.address.split(',')[1].trim().split(' ')[1],
          'postalCode': selectedLocation.address.split(',')[1].trim().split(' ')[0],
          'addressCountry': 'FR'
        }
      };

      // Ajouter les propriétés optionnelles en fonction des paramètres
      if (schemaSettings.includeOpeningHours) {
        schema.openingHours = selectedLocation.openingHours;
      }

      if (schemaSettings.includeGeo) {
        schema.geo = {
          '@type': 'GeoCoordinates',
          'latitude': selectedLocation.geo.latitude,
          'longitude': selectedLocation.geo.longitude
        };
      }

      if (schemaSettings.includeLogo) {
        schema.logo = selectedLocation.logo;
        schema.image = selectedLocation.logo;
      }

      if (schemaSettings.includePriceRange) {
        schema.priceRange = selectedLocation.priceRange;
      }

      if (schemaSettings.includePaymentAccepted) {
        schema.paymentAccepted = selectedLocation.paymentAccepted.join(', ');
      }

      if (schemaSettings.includeReviews) {
        schema.review = [
          {
            '@type': 'Review',
            'reviewRating': {
              '@type': 'Rating',
              'ratingValue': '5',
              'bestRating': '5'
            },
            'author': {
              '@type': 'Person',
              'name': 'Jean Dupont'
            },
            'datePublished': '2023-05-15',
            'reviewBody': 'Excellent service, personnel très professionnel.'
          },
          {
            '@type': 'Review',
            'reviewRating': {
              '@type': 'Rating',
              'ratingValue': '4',
              'bestRating': '5'
            },
            'author': {
              '@type': 'Person',
              'name': 'Marie Martin'
            },
            'datePublished': '2023-06-02',
            'reviewBody': 'Très satisfait de ma visite, je recommande !'
          }
        ];

        schema.aggregateRating = {
          '@type': 'AggregateRating',
          'ratingValue': '4.5',
          'reviewCount': '27'
        };
      }

      // Convertir l'objet en JSON formaté
      setSchemaJson(JSON.stringify(schema, null, 2));
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour copier le JSON-LD
  const copyJsonLd = () => {
    navigator.clipboard.writeText(schemaJson);

    // Afficher un message de succès
    setShowSuccess(true);

    // Masquer le message après 3 secondes
    setTimeout(() => {
      setShowSuccess(false);
    }, 3000);
  };

  // Fonction pour sauvegarder le schéma
  const saveSchema = async () => {
    if (!selectedLocation) return;

    try {
      setIsLoading(true);
      setError(null);

      // Préparer les données du schéma
      const schemaData = {
        location_id: selectedLocation.id,
        schema_type: schemaType,
        schema_settings: schemaSettings,
        schema_json: JSON.parse(schemaJson)
      };

      // Appeler le service pour enregistrer le schéma
      await localSeoService.saveSchemaSettings(schemaData);

      // Mettre à jour l'emplacement pour indiquer qu'il a un schéma
      const updatedLocations = locations.map(location => {
        if (location.id === selectedLocation.id) {
          return {
            ...location,
            hasSchema: true
          };
        }
        return location;
      });

      setLocations(updatedLocations);
      setSelectedLocation({
        ...selectedLocation,
        hasSchema: true
      });

      // Afficher le message de succès
      setShowSuccess(true);

      // Masquer le message après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (err) {
      console.error('Erreur lors de l\'enregistrement du schéma:', err);
      setError(__('Erreur lors de l\'enregistrement du schéma. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {error && (
            <Notice status="error" isDismissible={false} className="boss-mb-6">
              {error}
            </Notice>
          )}

          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {__('Opération réussie !', 'boss-seo')}
            </Notice>
          )}

          <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
            {/* Panneau principal */}
            <div className="lg:boss-col-span-2">
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Prévisualisation du schéma LocalBusiness', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-mb-6">
                    <SelectControl
                      label={__('Sélectionner un emplacement', 'boss-seo')}
                      value={selectedLocation ? selectedLocation.id : ''}
                      options={[
                        { label: __('-- Sélectionner un emplacement --', 'boss-seo'), value: '' },
                        ...locations.map(location => ({
                          label: `${location.name}${location.hasSchema ? ` (${__('Schéma existant', 'boss-seo')})` : ''}`,
                          value: location.id
                        }))
                      ]}
                      onChange={(value) => {
                        const location = locations.find(loc => loc.id === parseInt(value));
                        setSelectedLocation(location);
                      }}
                    />
                  </div>

                  {selectedLocation && (
                    <div>
                      <div className="boss-mb-6">
                        <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                          {__('Type de schéma', 'boss-seo')}
                        </h3>

                        <SelectControl
                          value={schemaType}
                          options={[
                            { label: 'LocalBusiness', value: 'LocalBusiness' },
                            { label: 'Restaurant', value: 'Restaurant' },
                            { label: 'Store', value: 'Store' },
                            { label: 'MedicalBusiness', value: 'MedicalBusiness' },
                            { label: 'ProfessionalService', value: 'ProfessionalService' },
                            { label: 'FinancialService', value: 'FinancialService' },
                            { label: 'LodgingBusiness', value: 'LodgingBusiness' }
                          ]}
                          onChange={setSchemaType}
                        />
                      </div>

                      <div className="boss-mb-6">
                        <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                          {__('Propriétés à inclure', 'boss-seo')}
                        </h3>

                        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                          <ToggleControl
                            label={__('Horaires d\'ouverture', 'boss-seo')}
                            checked={schemaSettings.includeOpeningHours}
                            onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeOpeningHours: checked })}
                          />

                          <ToggleControl
                            label={__('Coordonnées géographiques', 'boss-seo')}
                            checked={schemaSettings.includeGeo}
                            onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeGeo: checked })}
                          />

                          <ToggleControl
                            label={__('Logo', 'boss-seo')}
                            checked={schemaSettings.includeLogo}
                            onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeLogo: checked })}
                          />

                          <ToggleControl
                            label={__('Gamme de prix', 'boss-seo')}
                            checked={schemaSettings.includePriceRange}
                            onChange={(checked) => setSchemaSettings({ ...schemaSettings, includePriceRange: checked })}
                          />

                          <ToggleControl
                            label={__('Moyens de paiement', 'boss-seo')}
                            checked={schemaSettings.includePaymentAccepted}
                            onChange={(checked) => setSchemaSettings({ ...schemaSettings, includePaymentAccepted: checked })}
                          />

                          <ToggleControl
                            label={__('Avis et évaluations', 'boss-seo')}
                            checked={schemaSettings.includeReviews}
                            onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeReviews: checked })}
                          />
                        </div>
                      </div>

                      <div className="boss-mb-6">
                        <div className="boss-flex boss-justify-between boss-items-center boss-mb-3">
                          <h3 className="boss-text-md boss-font-semibold">
                            {__('Code JSON-LD', 'boss-seo')}
                          </h3>
                          <Button
                            isSmall
                            isSecondary
                            onClick={copyJsonLd}
                            icon="clipboard"
                          >
                            {__('Copier', 'boss-seo')}
                          </Button>
                        </div>

                        <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-overflow-auto boss-max-h-96">
                          <pre className="boss-text-xs boss-font-mono boss-whitespace-pre boss-text-boss-dark">
                            {schemaJson}
                          </pre>
                        </div>
                      </div>
                    </div>
                  )}
                </CardBody>
                <CardFooter className="boss-border-t boss-border-gray-200">
                  <div className="boss-flex boss-justify-end">
                    <Button
                      isPrimary
                      onClick={saveSchema}
                      disabled={!selectedLocation}
                    >
                      {__('Enregistrer le schéma', 'boss-seo')}
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            </div>

            {/* Panneau latéral */}
            <div>
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Prévisualisation', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  {selectedLocation ? (
                    <div>
                      <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-bg-white boss-mb-4">
                        <div className="boss-text-blue-600 boss-text-lg boss-font-medium boss-mb-1">
                          {selectedLocation.name}
                        </div>
                        <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
                          {selectedLocation.description}
                        </div>
                        <div className="boss-text-xs boss-text-boss-gray boss-mb-1">
                          <Dashicon icon="location" className="boss-mr-1" />
                          {selectedLocation.address}
                        </div>
                        <div className="boss-text-xs boss-text-boss-gray">
                          <Dashicon icon="phone" className="boss-mr-1" />
                          {selectedLocation.phone}
                        </div>

                        {schemaSettings.includeReviews && (
                          <div className="boss-mt-2 boss-pt-2 boss-border-t boss-border-gray-100">
                            <div className="boss-flex boss-items-center boss-text-yellow-500">
                              <Dashicon icon="star-filled" />
                              <Dashicon icon="star-filled" />
                              <Dashicon icon="star-filled" />
                              <Dashicon icon="star-filled" />
                              <Dashicon icon="star-half" />
                              <span className="boss-ml-1 boss-text-boss-gray boss-text-xs">4.5 (27 avis)</span>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="boss-text-sm boss-text-boss-gray">
                        <p className="boss-mb-2">
                          {__('Cette prévisualisation montre comment votre entreprise pourrait apparaître dans les résultats de recherche Google avec les données structurées.', 'boss-seo')}
                        </p>
                        <p>
                          {__('L\'apparence réelle peut varier en fonction des décisions de Google.', 'boss-seo')}
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="boss-text-center boss-py-6 boss-text-boss-gray">
                      {__('Sélectionnez un emplacement pour voir la prévisualisation.', 'boss-seo')}
                    </div>
                  )}
                </CardBody>
              </Card>

              <Card>
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Conseils pour les schémas', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4 boss-text-sm">
                    <div>
                      <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Utilisez le type spécifique', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {__('Choisissez le type de schéma le plus spécifique pour votre entreprise (Restaurant, Store, etc.) plutôt que LocalBusiness générique.', 'boss-seo')}
                      </p>
                    </div>

                    <div>
                      <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Horaires d\'ouverture', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {__('Utilisez le format ISO 8601 pour les horaires d\'ouverture (Mo-Fr 09:00-18:00).', 'boss-seo')}
                      </p>
                    </div>

                    <div>
                      <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Avis et évaluations', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {__('Incluez des avis réels et une note moyenne pour améliorer la visibilité dans les résultats de recherche.', 'boss-seo')}
                      </p>
                    </div>

                    <div>
                      <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Coordonnées géographiques', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {__('Ajoutez les coordonnées précises pour améliorer le référencement local et l\'affichage sur Google Maps.', 'boss-seo')}
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LocalSchemaPreview;
