<?php
/**
 * Classe pour gérer les scripts et styles du module de paramètres.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin
 */

/**
 * Classe pour gérer les scripts et styles du module de paramètres.
 *
 * Cette classe gère l'enregistrement et le chargement des scripts et styles
 * nécessaires pour le module de paramètres.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Settings_Scripts {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
    }

    /**
     * Enregistre les scripts et styles pour le module de paramètres.
     *
     * @since    1.2.0
     * @param    string    $hook_suffix    Le suffixe du hook.
     */
    public function enqueue_scripts( $hook_suffix ) {
        // Vérifie si nous sommes sur une page du plugin
        if ( strpos( $hook_suffix, 'boss-seo' ) === false ) {
            return;
        }

        // Enregistre les scripts et styles
        wp_enqueue_style( 'wp-components' );
        wp_enqueue_script( 'wp-element' );
        wp_enqueue_script( 'wp-components' );
        wp_enqueue_script( 'wp-i18n' );
        wp_enqueue_script( 'wp-api-fetch' );
        wp_enqueue_script( 'wp-url' );

        // Enregistre les données localisées pour les scripts
        wp_localize_script( 'wp-api-fetch', 'bossSettings', array(
            'root' => esc_url_raw( rest_url() ),
            'nonce' => wp_create_nonce( 'wp_rest' ),
            'ajaxUrl' => admin_url( 'admin-ajax.php' ),
            'generalSettingsNonce' => wp_create_nonce( 'boss_seo_save_general_settings' ),
            'advancedSettingsNonce' => wp_create_nonce( 'boss_seo_save_advanced_settings' ),
            'backupSettingsNonce' => wp_create_nonce( 'boss_seo_save_backup_settings' ),
            'createBackupNonce' => wp_create_nonce( 'boss_seo_create_backup' ),
            'restoreBackupNonce' => wp_create_nonce( 'boss_seo_restore_backup' ),
            'deleteBackupNonce' => wp_create_nonce( 'boss_seo_delete_backup' ),
            'userPreferencesNonce' => wp_create_nonce( 'boss_seo_save_user_preferences' ),
            'resetUserPreferencesNonce' => wp_create_nonce( 'boss_seo_reset_user_preferences' ),
            'markNotificationReadNonce' => wp_create_nonce( 'boss_seo_mark_notification_read' ),
        ) );
    }
}
