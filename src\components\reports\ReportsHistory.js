import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  SelectControl,
  TextControl,
  Dashicon,
  Modal,
  Spinner
} from '@wordpress/components';

const ReportsHistory = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [reports, setReports] = useState([]);
  const [filterType, setFilterType] = useState('all');
  const [filterDate, setFilterDate] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [showPreview, setShowPreview] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  
  // Charger les données
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour les rapports générés
      const mockReports = [];
      
      // Générer des rapports fictifs
      const reportTypes = [
        { id: 'performance', name: __('Performance SEO', 'boss-seo') },
        { id: 'keywords', name: __('Analyse des mots-clés', 'boss-seo') },
        { id: 'content', name: __('Audit de contenu', 'boss-seo') },
        { id: 'technical', name: __('Audit technique', 'boss-seo') },
        { id: 'local', name: __('SEO Local', 'boss-seo') },
        { id: 'ecommerce', name: __('E-commerce', 'boss-seo') },
        { id: 'custom', name: __('Rapport personnalisé', 'boss-seo') }
      ];
      
      const formats = ['pdf', 'csv', 'html', 'sheets'];
      
      for (let i = 1; i <= 25; i++) {
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 90)); // Jusqu'à 90 jours dans le passé
        
        const reportType = reportTypes[Math.floor(Math.random() * reportTypes.length)];
        const format = formats[Math.floor(Math.random() * formats.length)];
        
        mockReports.push({
          id: i,
          title: i % 3 === 0 
            ? __('Rapport personnalisé', 'boss-seo') + ` #${i}` 
            : reportType.name,
          type: reportType.id,
          date: date.toISOString().split('T')[0],
          format: format,
          size: Math.floor(Math.random() * 5000) + 500, // 500KB - 5.5MB
          creator: 'admin',
          scheduled: i % 5 === 0,
          frequency: i % 5 === 0 
            ? ['weekly', 'monthly', 'quarterly'][Math.floor(Math.random() * 3)] 
            : null,
          recipients: i % 5 === 0 
            ? '<EMAIL>' + (i % 2 === 0 ? ', <EMAIL>' : '') 
            : null,
          url: `https://example.com/reports/report-${i}.${format}`
        });
      }
      
      // Trier par date (plus récent en premier)
      mockReports.sort((a, b) => new Date(b.date) - new Date(a.date));
      
      setReports(mockReports);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  // Réinitialiser la page lorsque les filtres changent
  useEffect(() => {
    setCurrentPage(1);
  }, [filterType, filterDate, searchQuery]);
  
  // Fonction pour filtrer les rapports
  const getFilteredReports = () => {
    return reports.filter(report => {
      // Filtrer par type
      const matchesType = filterType === 'all' || report.type === filterType;
      
      // Filtrer par date
      let matchesDate = true;
      const reportDate = new Date(report.date);
      const today = new Date();
      
      if (filterDate === 'today') {
        matchesDate = reportDate.toDateString() === today.toDateString();
      } else if (filterDate === 'week') {
        const weekAgo = new Date();
        weekAgo.setDate(today.getDate() - 7);
        matchesDate = reportDate >= weekAgo;
      } else if (filterDate === 'month') {
        const monthAgo = new Date();
        monthAgo.setMonth(today.getMonth() - 1);
        matchesDate = reportDate >= monthAgo;
      } else if (filterDate === 'quarter') {
        const quarterAgo = new Date();
        quarterAgo.setMonth(today.getMonth() - 3);
        matchesDate = reportDate >= quarterAgo;
      }
      
      // Filtrer par recherche
      const matchesSearch = searchQuery === '' || 
        report.title.toLowerCase().includes(searchQuery.toLowerCase());
      
      return matchesType && matchesDate && matchesSearch;
    });
  };
  
  // Obtenir les rapports filtrés
  const filteredReports = getFilteredReports();
  
  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredReports.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredReports.length / itemsPerPage);
  
  // Fonction pour changer de page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  
  // Fonction pour aller à la page précédente
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  // Fonction pour aller à la page suivante
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };
  
  // Fonction pour prévisualiser un rapport
  const handlePreview = (report) => {
    setSelectedReport(report);
    setShowPreview(true);
  };
  
  // Fonction pour formater la taille du fichier
  const formatFileSize = (bytes) => {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(2) + ' KB';
    } else {
      return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    }
  };
  
  // Fonction pour obtenir l'icône du format
  const getFormatIcon = (format) => {
    switch (format) {
      case 'pdf':
        return 'pdf';
      case 'csv':
        return 'media-spreadsheet';
      case 'html':
        return 'media-code';
      case 'sheets':
        return 'media-spreadsheet';
      default:
        return 'media-default';
    }
  };
  
  // Fonction pour obtenir le texte de la fréquence
  const getFrequencyText = (frequency) => {
    switch (frequency) {
      case 'daily':
        return __('Quotidien', 'boss-seo');
      case 'weekly':
        return __('Hebdomadaire', 'boss-seo');
      case 'monthly':
        return __('Mensuel', 'boss-seo');
      case 'quarterly':
        return __('Trimestriel', 'boss-seo');
      default:
        return frequency;
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          <Card className="boss-mb-6">
            <CardBody>
              <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4">
                <TextControl
                  placeholder={__('Rechercher un rapport...', 'boss-seo')}
                  value={searchQuery}
                  onChange={setSearchQuery}
                />
                
                <SelectControl
                  label=""
                  value={filterType}
                  options={[
                    { label: __('Tous les types', 'boss-seo'), value: 'all' },
                    { label: __('Performance SEO', 'boss-seo'), value: 'performance' },
                    { label: __('Analyse des mots-clés', 'boss-seo'), value: 'keywords' },
                    { label: __('Audit de contenu', 'boss-seo'), value: 'content' },
                    { label: __('Audit technique', 'boss-seo'), value: 'technical' },
                    { label: __('SEO Local', 'boss-seo'), value: 'local' },
                    { label: __('E-commerce', 'boss-seo'), value: 'ecommerce' },
                    { label: __('Rapports personnalisés', 'boss-seo'), value: 'custom' }
                  ]}
                  onChange={setFilterType}
                />
                
                <SelectControl
                  label=""
                  value={filterDate}
                  options={[
                    { label: __('Toutes les dates', 'boss-seo'), value: 'all' },
                    { label: __('Aujourd\'hui', 'boss-seo'), value: 'today' },
                    { label: __('7 derniers jours', 'boss-seo'), value: 'week' },
                    { label: __('30 derniers jours', 'boss-seo'), value: 'month' },
                    { label: __('3 derniers mois', 'boss-seo'), value: 'quarter' }
                  ]}
                  onChange={setFilterDate}
                />
              </div>
            </CardBody>
          </Card>
          
          <Card>
            <CardHeader className="boss-border-b boss-border-gray-200">
              <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                {__('Historique des rapports', 'boss-seo')}
              </h2>
            </CardHeader>
            <CardBody className="boss-p-0">
              <div className="boss-overflow-x-auto">
                <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
                  <thead className="boss-bg-gray-50">
                    <tr>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Titre', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Type', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Date', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Format', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Taille', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Planification', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-right boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Actions', 'boss-seo')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                    {currentItems.length === 0 ? (
                      <tr>
                        <td colSpan="7" className="boss-px-6 boss-py-4 boss-text-center boss-text-boss-gray">
                          {__('Aucun rapport trouvé.', 'boss-seo')}
                        </td>
                      </tr>
                    ) : (
                      currentItems.map(report => (
                        <tr key={report.id} className="boss-hover:boss-bg-gray-50">
                          <td className="boss-px-6 boss-py-4">
                            <div className="boss-font-medium boss-text-boss-dark">{report.title}</div>
                            <div className="boss-text-sm boss-text-boss-gray">{__('Par', 'boss-seo')} {report.creator}</div>
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <span className="boss-px-2 boss-py-1 boss-text-xs boss-rounded-full boss-bg-blue-100 boss-text-blue-800">
                              {report.type === 'performance' && __('Performance SEO', 'boss-seo')}
                              {report.type === 'keywords' && __('Analyse des mots-clés', 'boss-seo')}
                              {report.type === 'content' && __('Audit de contenu', 'boss-seo')}
                              {report.type === 'technical' && __('Audit technique', 'boss-seo')}
                              {report.type === 'local' && __('SEO Local', 'boss-seo')}
                              {report.type === 'ecommerce' && __('E-commerce', 'boss-seo')}
                              {report.type === 'custom' && __('Personnalisé', 'boss-seo')}
                            </span>
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <div className="boss-text-boss-gray">{report.date}</div>
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <div className="boss-flex boss-items-center">
                              <Dashicon icon={getFormatIcon(report.format)} className="boss-mr-1 boss-text-boss-gray" />
                              <span className="boss-uppercase">{report.format}</span>
                            </div>
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <div className="boss-text-boss-gray">{formatFileSize(report.size)}</div>
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            {report.scheduled ? (
                              <div className="boss-flex boss-items-center boss-text-green-600">
                                <Dashicon icon="calendar-alt" className="boss-mr-1" />
                                {getFrequencyText(report.frequency)}
                              </div>
                            ) : (
                              <div className="boss-text-boss-gray">-</div>
                            )}
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-right">
                            <div className="boss-flex boss-justify-end boss-space-x-2">
                              <Button
                                isSecondary
                                isSmall
                                onClick={() => handlePreview(report)}
                              >
                                <Dashicon icon="visibility" />
                              </Button>
                              <Button
                                isPrimary
                                isSmall
                                href={report.url}
                                target="_blank"
                              >
                                <Dashicon icon="download" />
                              </Button>
                              <Button
                                isSecondary
                                isSmall
                              >
                                <Dashicon icon="email" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardBody>
            {totalPages > 1 && (
              <CardFooter className="boss-border-t boss-border-gray-200">
                <div className="boss-flex boss-justify-between boss-items-center">
                  <div className="boss-text-boss-gray boss-text-sm">
                    {__('Affichage de', 'boss-seo')} {indexOfFirstItem + 1} {__('à', 'boss-seo')} {Math.min(indexOfLastItem, filteredReports.length)} {__('sur', 'boss-seo')} {filteredReports.length} {__('rapports', 'boss-seo')}
                  </div>
                  <div className="boss-flex boss-space-x-2">
                    <Button
                      isSecondary
                      isSmall
                      onClick={goToPreviousPage}
                      disabled={currentPage === 1}
                    >
                      <Dashicon icon="arrow-left-alt2" />
                    </Button>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      // Afficher les pages autour de la page courante
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }
                      
                      return (
                        <Button
                          key={pageNum}
                          isSecondary
                          isSmall
                          isPrimary={currentPage === pageNum}
                          onClick={() => paginate(pageNum)}
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                    <Button
                      isSecondary
                      isSmall
                      onClick={goToNextPage}
                      disabled={currentPage === totalPages}
                    >
                      <Dashicon icon="arrow-right-alt2" />
                    </Button>
                  </div>
                </div>
              </CardFooter>
            )}
          </Card>
          
          {/* Modal de prévisualisation */}
          {showPreview && selectedReport && (
            <Modal
              title={selectedReport.title}
              onRequestClose={() => setShowPreview(false)}
              className="boss-report-preview-modal"
            >
              <div className="boss-p-6">
                <div className="boss-mb-6">
                  <div className="boss-grid boss-grid-cols-2 boss-gap-4 boss-mb-6">
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Type de rapport', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {selectedReport.type === 'performance' && __('Performance SEO', 'boss-seo')}
                        {selectedReport.type === 'keywords' && __('Analyse des mots-clés', 'boss-seo')}
                        {selectedReport.type === 'content' && __('Audit de contenu', 'boss-seo')}
                        {selectedReport.type === 'technical' && __('Audit technique', 'boss-seo')}
                        {selectedReport.type === 'local' && __('SEO Local', 'boss-seo')}
                        {selectedReport.type === 'ecommerce' && __('E-commerce', 'boss-seo')}
                        {selectedReport.type === 'custom' && __('Personnalisé', 'boss-seo')}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Date de génération', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {selectedReport.date}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Format', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray boss-flex boss-items-center">
                        <Dashicon icon={getFormatIcon(selectedReport.format)} className="boss-mr-1" />
                        {selectedReport.format.toUpperCase()}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Taille', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {formatFileSize(selectedReport.size)}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Créé par', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {selectedReport.creator}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Planification', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {selectedReport.scheduled 
                          ? getFrequencyText(selectedReport.frequency)
                          : __('Aucune', 'boss-seo')}
                      </p>
                    </div>
                  </div>
                  
                  {selectedReport.scheduled && selectedReport.recipients && (
                    <div className="boss-mb-6">
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Destinataires', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {selectedReport.recipients}
                      </p>
                    </div>
                  )}
                  
                  <div className="boss-p-4 boss-bg-gray-50 boss-rounded-lg boss-mb-6">
                    <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                      {__('Aperçu du rapport', 'boss-seo')}
                    </h3>
                    
                    <p className="boss-text-sm boss-text-boss-gray boss-mb-4">
                      {__('Pour visualiser le rapport complet, veuillez le télécharger ou l\'ouvrir dans un nouvel onglet.', 'boss-seo')}
                    </p>
                    
                    <div className="boss-bg-white boss-border boss-border-gray-200 boss-rounded boss-p-4 boss-flex boss-justify-center boss-items-center boss-h-40">
                      <div className="boss-text-center">
                        <Dashicon icon={getFormatIcon(selectedReport.format)} className="boss-text-6xl boss-text-boss-gray boss-mb-2" />
                        <p className="boss-text-boss-gray">
                          {selectedReport.format.toUpperCase()} {__('Document', 'boss-seo')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="boss-flex boss-justify-between">
                  <Button
                    isSecondary
                    onClick={() => setShowPreview(false)}
                  >
                    {__('Fermer', 'boss-seo')}
                  </Button>
                  
                  <div className="boss-flex boss-space-x-2">
                    <Button
                      isSecondary
                      onClick={() => {
                        // Simuler l'envoi par email
                        alert(__('Email envoyé avec succès !', 'boss-seo'));
                      }}
                    >
                      <Dashicon icon="email" className="boss-mr-1" />
                      {__('Envoyer par email', 'boss-seo')}
                    </Button>
                    
                    <Button
                      isPrimary
                      href={selectedReport.url}
                      target="_blank"
                    >
                      <Dashicon icon="download" className="boss-mr-1" />
                      {__('Télécharger', 'boss-seo')}
                    </Button>
                  </div>
                </div>
              </div>
            </Modal>
          )}
        </div>
      )}
    </div>
  );
};

export default ReportsHistory;
