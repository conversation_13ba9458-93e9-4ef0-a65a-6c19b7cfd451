import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  TextControl,
  SelectControl,
  CheckboxControl,
  RadioControl,
  ToggleControl,
  DatePicker,
  Notice,
  Spinner
} from '@wordpress/components';

const ReportCreator = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [metricCategories, setMetricCategories] = useState([]);
  const [availableMetrics, setAvailableMetrics] = useState({});
  const [reportData, setReportData] = useState({
    title: '',
    description: '',
    dateRange: 'last30',
    customStartDate: new Date(),
    customEndDate: new Date(),
    metrics: [],
    format: 'pdf',
    schedule: 'none',
    recipients: '',
    saveTemplate: false,
    templateName: ''
  });
  
  // Charger les données
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour les catégories de métriques
      const mockMetricCategories = [
        { id: 'visibility', name: __('Visibilité', 'boss-seo') },
        { id: 'traffic', name: __('Trafic', 'boss-seo') },
        { id: 'keywords', name: __('Mots-clés', 'boss-seo') },
        { id: 'content', name: __('Contenu', 'boss-seo') },
        { id: 'technical', name: __('Technique', 'boss-seo') },
        { id: 'local', name: __('SEO Local', 'boss-seo') },
        { id: 'ecommerce', name: __('E-commerce', 'boss-seo') }
      ];
      
      // Données fictives pour les métriques disponibles
      const mockAvailableMetrics = {
        visibility: [
          { id: 'visibility_score', name: __('Score de visibilité', 'boss-seo') },
          { id: 'average_position', name: __('Position moyenne', 'boss-seo') },
          { id: 'serp_features', name: __('Présence SERP Features', 'boss-seo') },
          { id: 'indexed_pages', name: __('Pages indexées', 'boss-seo') }
        ],
        traffic: [
          { id: 'organic_traffic', name: __('Trafic organique', 'boss-seo') },
          { id: 'organic_ctr', name: __('CTR organique', 'boss-seo') },
          { id: 'bounce_rate', name: __('Taux de rebond', 'boss-seo') },
          { id: 'pages_per_session', name: __('Pages par session', 'boss-seo') }
        ],
        keywords: [
          { id: 'keyword_rankings', name: __('Classement des mots-clés', 'boss-seo') },
          { id: 'keyword_opportunities', name: __('Opportunités de mots-clés', 'boss-seo') },
          { id: 'keyword_difficulty', name: __('Difficulté des mots-clés', 'boss-seo') },
          { id: 'search_volume', name: __('Volume de recherche', 'boss-seo') }
        ],
        content: [
          { id: 'content_quality', name: __('Qualité du contenu', 'boss-seo') },
          { id: 'content_length', name: __('Longueur du contenu', 'boss-seo') },
          { id: 'readability', name: __('Lisibilité', 'boss-seo') },
          { id: 'content_gaps', name: __('Lacunes de contenu', 'boss-seo') }
        ],
        technical: [
          { id: 'page_speed', name: __('Vitesse de page', 'boss-seo') },
          { id: 'mobile_friendliness', name: __('Compatibilité mobile', 'boss-seo') },
          { id: 'crawl_errors', name: __('Erreurs d\'exploration', 'boss-seo') },
          { id: 'core_web_vitals', name: __('Core Web Vitals', 'boss-seo') }
        ],
        local: [
          { id: 'local_visibility', name: __('Visibilité locale', 'boss-seo') },
          { id: 'gmb_performance', name: __('Performance GMB', 'boss-seo') },
          { id: 'local_reviews', name: __('Avis locaux', 'boss-seo') },
          { id: 'local_citations', name: __('Citations locales', 'boss-seo') }
        ],
        ecommerce: [
          { id: 'product_visibility', name: __('Visibilité des produits', 'boss-seo') },
          { id: 'product_ctr', name: __('CTR des produits', 'boss-seo') },
          { id: 'conversion_rate', name: __('Taux de conversion', 'boss-seo') },
          { id: 'revenue', name: __('Revenus', 'boss-seo') }
        ]
      };
      
      setMetricCategories(mockMetricCategories);
      setAvailableMetrics(mockAvailableMetrics);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  // Fonction pour mettre à jour les données du rapport
  const updateReportData = (key, value) => {
    setReportData({
      ...reportData,
      [key]: value
    });
  };
  
  // Fonction pour gérer la sélection/désélection d'une métrique
  const handleMetricToggle = (metricId) => {
    if (reportData.metrics.includes(metricId)) {
      updateReportData('metrics', reportData.metrics.filter(id => id !== metricId));
    } else {
      updateReportData('metrics', [...reportData.metrics, metricId]);
    }
  };
  
  // Fonction pour générer le rapport
  const handleGenerateReport = () => {
    setIsGenerating(true);
    
    // Simuler la génération
    setTimeout(() => {
      setIsGenerating(false);
      setShowSuccess(true);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 2000);
  };
  
  // Vérifier si le formulaire est valide
  const isFormValid = () => {
    return (
      reportData.title.trim() !== '' &&
      reportData.metrics.length > 0 &&
      (reportData.dateRange !== 'custom' || (
        reportData.customStartDate && reportData.customEndDate
      ))
    );
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {__('Rapport généré avec succès ! Vous pouvez le consulter dans l\'historique des rapports.', 'boss-seo')}
            </Notice>
          )}
          
          <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
            {/* Panneau de gauche */}
            <div className="lg:boss-col-span-2">
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Informations du rapport', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <TextControl
                      label={__('Titre du rapport', 'boss-seo')}
                      value={reportData.title}
                      onChange={(value) => updateReportData('title', value)}
                    />
                    
                    <TextControl
                      label={__('Description', 'boss-seo')}
                      help={__('Une brève description du contenu du rapport', 'boss-seo')}
                      value={reportData.description}
                      onChange={(value) => updateReportData('description', value)}
                    />
                    
                    <div>
                      <RadioControl
                        label={__('Période', 'boss-seo')}
                        selected={reportData.dateRange}
                        options={[
                          { label: __('7 derniers jours', 'boss-seo'), value: 'last7' },
                          { label: __('30 derniers jours', 'boss-seo'), value: 'last30' },
                          { label: __('3 derniers mois', 'boss-seo'), value: 'last90' },
                          { label: __('6 derniers mois', 'boss-seo'), value: 'last180' },
                          { label: __('12 derniers mois', 'boss-seo'), value: 'last365' },
                          { label: __('Période personnalisée', 'boss-seo'), value: 'custom' }
                        ]}
                        onChange={(value) => updateReportData('dateRange', value)}
                      />
                      
                      {reportData.dateRange === 'custom' && (
                        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mt-4">
                          <div>
                            <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium boss-text-boss-dark">
                              {__('Date de début', 'boss-seo')}
                            </label>
                            <DatePicker
                              currentDate={reportData.customStartDate}
                              onChange={(date) => updateReportData('customStartDate', date)}
                            />
                          </div>
                          
                          <div>
                            <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium boss-text-boss-dark">
                              {__('Date de fin', 'boss-seo')}
                            </label>
                            <DatePicker
                              currentDate={reportData.customEndDate}
                              onChange={(date) => updateReportData('customEndDate', date)}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>
              
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Métriques', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-6">
                    {metricCategories.map(category => (
                      <div key={category.id}>
                        <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                          {category.name}
                        </h3>
                        
                        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-3">
                          {availableMetrics[category.id]?.map(metric => (
                            <CheckboxControl
                              key={metric.id}
                              label={metric.name}
                              checked={reportData.metrics.includes(metric.id)}
                              onChange={() => handleMetricToggle(metric.id)}
                            />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </div>
            
            {/* Panneau de droite */}
            <div>
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Options de rapport', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <SelectControl
                      label={__('Format', 'boss-seo')}
                      value={reportData.format}
                      options={[
                        { label: 'PDF', value: 'pdf' },
                        { label: 'CSV', value: 'csv' },
                        { label: 'HTML', value: 'html' },
                        { label: 'Google Sheets', value: 'sheets' }
                      ]}
                      onChange={(value) => updateReportData('format', value)}
                    />
                    
                    <SelectControl
                      label={__('Planification', 'boss-seo')}
                      value={reportData.schedule}
                      options={[
                        { label: __('Aucune (rapport unique)', 'boss-seo'), value: 'none' },
                        { label: __('Quotidienne', 'boss-seo'), value: 'daily' },
                        { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                        { label: __('Mensuelle', 'boss-seo'), value: 'monthly' },
                        { label: __('Trimestrielle', 'boss-seo'), value: 'quarterly' }
                      ]}
                      onChange={(value) => updateReportData('schedule', value)}
                    />
                    
                    {reportData.schedule !== 'none' && (
                      <TextControl
                        label={__('Destinataires', 'boss-seo')}
                        help={__('Adresses email séparées par des virgules', 'boss-seo')}
                        value={reportData.recipients}
                        onChange={(value) => updateReportData('recipients', value)}
                      />
                    )}
                    
                    <ToggleControl
                      label={__('Enregistrer comme modèle', 'boss-seo')}
                      checked={reportData.saveTemplate}
                      onChange={(value) => updateReportData('saveTemplate', value)}
                    />
                    
                    {reportData.saveTemplate && (
                      <TextControl
                        label={__('Nom du modèle', 'boss-seo')}
                        value={reportData.templateName}
                        onChange={(value) => updateReportData('templateName', value)}
                      />
                    )}
                  </div>
                </CardBody>
                <CardFooter className="boss-border-t boss-border-gray-200">
                  <Button
                    isPrimary
                    onClick={handleGenerateReport}
                    disabled={!isFormValid() || isGenerating}
                    isBusy={isGenerating}
                    className="boss-w-full"
                  >
                    {isGenerating ? __('Génération en cours...', 'boss-seo') : __('Générer le rapport', 'boss-seo')}
                  </Button>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Résumé', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Titre', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {reportData.title || __('Non défini', 'boss-seo')}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Période', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {reportData.dateRange === 'last7' && __('7 derniers jours', 'boss-seo')}
                        {reportData.dateRange === 'last30' && __('30 derniers jours', 'boss-seo')}
                        {reportData.dateRange === 'last90' && __('3 derniers mois', 'boss-seo')}
                        {reportData.dateRange === 'last180' && __('6 derniers mois', 'boss-seo')}
                        {reportData.dateRange === 'last365' && __('12 derniers mois', 'boss-seo')}
                        {reportData.dateRange === 'custom' && __('Période personnalisée', 'boss-seo')}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Métriques sélectionnées', 'boss-seo')}
                      </h3>
                      {reportData.metrics.length === 0 ? (
                        <p className="boss-text-boss-gray">
                          {__('Aucune métrique sélectionnée', 'boss-seo')}
                        </p>
                      ) : (
                        <div className="boss-flex boss-flex-wrap boss-gap-2">
                          {reportData.metrics.slice(0, 5).map(metricId => {
                            // Trouver la métrique dans les catégories
                            let metricName = metricId;
                            for (const categoryId in availableMetrics) {
                              const metric = availableMetrics[categoryId].find(m => m.id === metricId);
                              if (metric) {
                                metricName = metric.name;
                                break;
                              }
                            }
                            
                            return (
                              <span 
                                key={metricId} 
                                className="boss-px-2 boss-py-1 boss-text-xs boss-rounded-full boss-bg-blue-50 boss-text-blue-700"
                              >
                                {metricName}
                              </span>
                            );
                          })}
                          
                          {reportData.metrics.length > 5 && (
                            <span className="boss-px-2 boss-py-1 boss-text-xs boss-rounded-full boss-bg-gray-100 boss-text-boss-gray">
                              +{reportData.metrics.length - 5} {__('autres', 'boss-seo')}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Format', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {reportData.format.toUpperCase()}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Planification', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {reportData.schedule === 'none' && __('Aucune (rapport unique)', 'boss-seo')}
                        {reportData.schedule === 'daily' && __('Quotidienne', 'boss-seo')}
                        {reportData.schedule === 'weekly' && __('Hebdomadaire', 'boss-seo')}
                        {reportData.schedule === 'monthly' && __('Mensuelle', 'boss-seo')}
                        {reportData.schedule === 'quarterly' && __('Trimestrielle', 'boss-seo')}
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportCreator;
