import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { 
  <PERSON>, 
  Card<PERSON><PERSON>, 
  Card<PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ProgressBar,
  Modal
} from '@wordpress/components';

/**
 * Composant de tutoriels interactifs
 * 
 * @param {Object} props - Propriétés du composant
 * @param {Array} props.tutorials - Liste des tutoriels
 * @param {boolean} props.isLoading - Indique si les données sont en cours de chargement
 * @returns {React.ReactElement} Composant Tutorials
 */
const Tutorials = ({ 
  tutorials = [], 
  isLoading = false 
}) => {
  const [selectedTutorial, setSelectedTutorial] = useState(null);
  const [showTutorialModal, setShowTutorialModal] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedTutorials, setCompletedTutorials] = useState([]);
  const [inProgressTutorials, setInProgressTutorials] = useState({});
  
  // Charger les tutoriels complétés depuis le localStorage
  useEffect(() => {
    const loadCompletedTutorials = () => {
      const saved = localStorage.getItem('boss_completed_tutorials');
      if (saved) {
        try {
          setCompletedTutorials(JSON.parse(saved));
        } catch (e) {
          console.error('Erreur lors du chargement des tutoriels complétés', e);
          setCompletedTutorials([]);
        }
      }
      
      const savedProgress = localStorage.getItem('boss_tutorials_progress');
      if (savedProgress) {
        try {
          setInProgressTutorials(JSON.parse(savedProgress));
        } catch (e) {
          console.error('Erreur lors du chargement de la progression des tutoriels', e);
          setInProgressTutorials({});
        }
      }
    };
    
    loadCompletedTutorials();
  }, []);
  
  // Sauvegarder les tutoriels complétés dans le localStorage
  useEffect(() => {
    if (completedTutorials.length > 0) {
      localStorage.setItem('boss_completed_tutorials', JSON.stringify(completedTutorials));
    }
  }, [completedTutorials]);
  
  // Sauvegarder la progression des tutoriels dans le localStorage
  useEffect(() => {
    if (Object.keys(inProgressTutorials).length > 0) {
      localStorage.setItem('boss_tutorials_progress', JSON.stringify(inProgressTutorials));
    }
  }, [inProgressTutorials]);
  
  // Ouvrir un tutoriel
  const openTutorial = (tutorial) => {
    setSelectedTutorial(tutorial);
    setShowTutorialModal(true);
    
    // Reprendre à l'étape sauvegardée ou commencer au début
    const savedStep = inProgressTutorials[tutorial.id] || 0;
    setCurrentStep(savedStep);
  };
  
  // Fermer le tutoriel
  const closeTutorial = () => {
    // Sauvegarder la progression
    if (selectedTutorial) {
      setInProgressTutorials({
        ...inProgressTutorials,
        [selectedTutorial.id]: currentStep
      });
    }
    
    setShowTutorialModal(false);
  };
  
  // Passer à l'étape suivante
  const nextStep = () => {
    if (selectedTutorial && currentStep < selectedTutorial.steps.length - 1) {
      setCurrentStep(currentStep + 1);
      
      // Sauvegarder la progression
      setInProgressTutorials({
        ...inProgressTutorials,
        [selectedTutorial.id]: currentStep + 1
      });
    } else {
      // Tutoriel terminé
      completeTutorial();
    }
  };
  
  // Revenir à l'étape précédente
  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      
      // Sauvegarder la progression
      setInProgressTutorials({
        ...inProgressTutorials,
        [selectedTutorial.id]: currentStep - 1
      });
    }
  };
  
  // Marquer un tutoriel comme terminé
  const completeTutorial = () => {
    if (selectedTutorial && !completedTutorials.includes(selectedTutorial.id)) {
      setCompletedTutorials([...completedTutorials, selectedTutorial.id]);
      
      // Supprimer de la progression
      const newProgress = { ...inProgressTutorials };
      delete newProgress[selectedTutorial.id];
      setInProgressTutorials(newProgress);
    }
    
    setShowTutorialModal(false);
  };
  
  // Réinitialiser un tutoriel
  const resetTutorial = (tutorialId) => {
    // Supprimer de la liste des tutoriels complétés
    setCompletedTutorials(completedTutorials.filter(id => id !== tutorialId));
    
    // Supprimer de la progression
    const newProgress = { ...inProgressTutorials };
    delete newProgress[tutorialId];
    setInProgressTutorials(newProgress);
  };
  
  // Obtenir la progression d'un tutoriel
  const getTutorialProgress = (tutorialId) => {
    if (completedTutorials.includes(tutorialId)) {
      return 100;
    }
    
    if (inProgressTutorials[tutorialId] !== undefined) {
      const tutorial = tutorials.find(t => t.id === tutorialId);
      if (tutorial) {
        return Math.round((inProgressTutorials[tutorialId] / (tutorial.steps.length - 1)) * 100);
      }
    }
    
    return 0;
  };
  
  // Filtrer les tutoriels par catégorie
  const getTutorialsByCategory = () => {
    const categorized = {};
    
    tutorials.forEach(tutorial => {
      if (!categorized[tutorial.category]) {
        categorized[tutorial.category] = [];
      }
      
      categorized[tutorial.category].push(tutorial);
    });
    
    return categorized;
  };
  
  // Tutoriels par catégorie
  const tutorialsByCategory = getTutorialsByCategory();
  
  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {/* Tutoriels en cours */}
          {Object.keys(inProgressTutorials).length > 0 && (
            <Card className="boss-mb-6">
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Tutoriels en cours', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-4">
                  {Object.keys(inProgressTutorials).map(tutorialId => {
                    const tutorial = tutorials.find(t => t.id === tutorialId);
                    if (!tutorial) return null;
                    
                    const progress = getTutorialProgress(tutorialId);
                    
                    return (
                      <Card key={tutorialId} className="boss-transition-all boss-duration-300 boss-hover:boss-shadow-md">
                        <CardBody>
                          <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark boss-mb-2">
                            {tutorial.title}
                          </h3>
                          <p className="boss-text-sm boss-text-boss-gray boss-mb-4 boss-line-clamp-2">
                            {tutorial.description}
                          </p>
                          <div className="boss-mb-2">
                            <ProgressBar
                              percent={progress}
                              className="boss-mb-1"
                            />
                            <div className="boss-text-xs boss-text-boss-gray boss-text-right">
                              {progress}% {__('terminé', 'boss-seo')}
                            </div>
                          </div>
                        </CardBody>
                        <CardFooter className="boss-border-t boss-border-gray-200">
                          <Button
                            isPrimary
                            onClick={() => openTutorial(tutorial)}
                            className="boss-w-full"
                          >
                            {__('Continuer', 'boss-seo')}
                          </Button>
                        </CardFooter>
                      </Card>
                    );
                  })}
                </div>
              </CardBody>
            </Card>
          )}
          
          {/* Tutoriels par catégorie */}
          {Object.keys(tutorialsByCategory).map(category => (
            <Card key={category} className="boss-mb-6">
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {category}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-4">
                  {tutorialsByCategory[category].map(tutorial => {
                    const isCompleted = completedTutorials.includes(tutorial.id);
                    const progress = getTutorialProgress(tutorial.id);
                    
                    return (
                      <Card key={tutorial.id} className="boss-transition-all boss-duration-300 boss-hover:boss-shadow-md">
                        <CardBody>
                          <div className="boss-flex boss-justify-between boss-items-start boss-mb-2">
                            <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark">
                              {tutorial.title}
                            </h3>
                            {isCompleted && (
                              <span className="dashicons dashicons-yes-alt boss-text-green-600"></span>
                            )}
                          </div>
                          <p className="boss-text-sm boss-text-boss-gray boss-mb-4 boss-line-clamp-2">
                            {tutorial.description}
                          </p>
                          <div className="boss-flex boss-justify-between boss-items-center boss-mb-2">
                            <div className="boss-text-xs boss-text-boss-gray">
                              {tutorial.duration} {__('min', 'boss-seo')} • {tutorial.steps.length} {__('étapes', 'boss-seo')}
                            </div>
                            <div>
                              {tutorial.level === 'beginner' && (
                                <span className="boss-inline-block boss-px-2 boss-py-1 boss-text-xs boss-font-medium boss-rounded-full boss-bg-green-100 boss-text-green-800">
                                  {__('Débutant', 'boss-seo')}
                                </span>
                              )}
                              {tutorial.level === 'intermediate' && (
                                <span className="boss-inline-block boss-px-2 boss-py-1 boss-text-xs boss-font-medium boss-rounded-full boss-bg-blue-100 boss-text-blue-800">
                                  {__('Intermédiaire', 'boss-seo')}
                                </span>
                              )}
                              {tutorial.level === 'advanced' && (
                                <span className="boss-inline-block boss-px-2 boss-py-1 boss-text-xs boss-font-medium boss-rounded-full boss-bg-purple-100 boss-text-purple-800">
                                  {__('Avancé', 'boss-seo')}
                                </span>
                              )}
                            </div>
                          </div>
                          {progress > 0 && progress < 100 && (
                            <div className="boss-mb-2">
                              <ProgressBar
                                percent={progress}
                                className="boss-mb-1"
                              />
                              <div className="boss-text-xs boss-text-boss-gray boss-text-right">
                                {progress}% {__('terminé', 'boss-seo')}
                              </div>
                            </div>
                          )}
                        </CardBody>
                        <CardFooter className="boss-border-t boss-border-gray-200 boss-flex boss-justify-between">
                          {isCompleted ? (
                            <>
                              <Button
                                isSecondary
                                isSmall
                                onClick={() => resetTutorial(tutorial.id)}
                              >
                                {__('Réinitialiser', 'boss-seo')}
                              </Button>
                              <Button
                                isPrimary
                                onClick={() => openTutorial(tutorial)}
                              >
                                {__('Revoir', 'boss-seo')}
                              </Button>
                            </>
                          ) : (
                            <Button
                              isPrimary
                              onClick={() => openTutorial(tutorial)}
                              className="boss-w-full"
                            >
                              {progress > 0 ? __('Continuer', 'boss-seo') : __('Démarrer', 'boss-seo')}
                            </Button>
                          )}
                        </CardFooter>
                      </Card>
                    );
                  })}
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      )}
      
      {/* Modal de tutoriel */}
      {showTutorialModal && selectedTutorial && (
        <Modal
          title={selectedTutorial.title}
          onRequestClose={closeTutorial}
          className="boss-tutorial-modal"
        >
          <div className="boss-p-6">
            <div className="boss-mb-6">
              <div className="boss-mb-4">
                <ProgressBar
                  percent={Math.round((currentStep / (selectedTutorial.steps.length - 1)) * 100)}
                  className="boss-mb-1"
                />
                <div className="boss-flex boss-justify-between boss-text-xs boss-text-boss-gray">
                  <span>
                    {__('Étape', 'boss-seo')} {currentStep + 1} / {selectedTutorial.steps.length}
                  </span>
                  <span>
                    {Math.round((currentStep / (selectedTutorial.steps.length - 1)) * 100)}%
                  </span>
                </div>
              </div>
              
              <div className="boss-mb-6">
                <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark boss-mb-2">
                  {selectedTutorial.steps[currentStep].title}
                </h3>
                
                <div className="boss-prose boss-max-w-none boss-mb-4">
                  <div dangerouslySetInnerHTML={{ __html: selectedTutorial.steps[currentStep].content }} />
                </div>
                
                {selectedTutorial.steps[currentStep].image && (
                  <div className="boss-mb-4 boss-border boss-border-gray-200 boss-rounded-lg boss-overflow-hidden">
                    <img 
                      src={selectedTutorial.steps[currentStep].image} 
                      alt={selectedTutorial.steps[currentStep].title}
                      className="boss-w-full"
                    />
                  </div>
                )}
                
                {selectedTutorial.steps[currentStep].video && (
                  <div className="boss-mb-4 boss-border boss-border-gray-200 boss-rounded-lg boss-overflow-hidden">
                    <div className="boss-aspect-w-16 boss-aspect-h-9">
                      <iframe
                        src={selectedTutorial.steps[currentStep].video}
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        className="boss-w-full boss-h-full"
                      ></iframe>
                    </div>
                  </div>
                )}
                
                {selectedTutorial.steps[currentStep].tip && (
                  <div className="boss-p-4 boss-bg-blue-50 boss-rounded-lg boss-text-sm boss-text-blue-800 boss-mb-4">
                    <div className="boss-flex boss-items-start">
                      <div className="boss-flex-shrink-0 boss-mr-2">
                        <span className="dashicons dashicons-info"></span>
                      </div>
                      <div>
                        <strong>{__('Astuce :', 'boss-seo')}</strong> {selectedTutorial.steps[currentStep].tip}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="boss-flex boss-justify-between">
              <Button
                isSecondary
                onClick={previousStep}
                disabled={currentStep === 0}
              >
                {__('Précédent', 'boss-seo')}
              </Button>
              
              <div className="boss-flex boss-space-x-2">
                <Button
                  isLink
                  onClick={closeTutorial}
                >
                  {__('Sauvegarder et quitter', 'boss-seo')}
                </Button>
                
                <Button
                  isPrimary
                  onClick={nextStep}
                >
                  {currentStep < selectedTutorial.steps.length - 1 
                    ? __('Suivant', 'boss-seo') 
                    : __('Terminer', 'boss-seo')}
                </Button>
              </div>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default Tutorials;
