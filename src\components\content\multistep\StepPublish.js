/**
 * Composant pour l'étape 5 : Optimisation finale et publication
 */
import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Dashicon,
  Badge,
  SelectControl,
  ToggleControl,
  Notice
} from '@wordpress/components';

/**
 * Composant pour l'étape 5 : Optimisation finale et publication
 * 
 * @param {Object} props Propriétés du composant
 * @param {Object} props.data Données de toutes les étapes
 * @param {Function} props.onSaveDraft Fonction pour sauvegarder le brouillon
 * @param {Function} props.onPublish Fonction pour publier le contenu
 * @param {boolean} props.isLoading Indique si une opération est en cours
 */
const StepPublish = ({ data, onSaveDraft, onPublish, isLoading }) => {
  // États pour les options de publication
  const [publishOptions, setPublishOptions] = useState({
    status: 'draft',
    category: '',
    tags: [],
    featured: false,
    comments: true,
    pingbacks: false
  });
  
  // Fonction pour mettre à jour une option de publication
  const updatePublishOption = (option, value) => {
    setPublishOptions({
      ...publishOptions,
      [option]: value
    });
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du score
  const getSeoScoreClass = (score) => {
    if (score >= 80) return 'boss-bg-green-100 boss-text-green-800';
    if (score >= 50) return 'boss-bg-yellow-100 boss-text-yellow-800';
    return 'boss-bg-red-100 boss-text-red-800';
  };
  
  // Fonction pour obtenir les recommandations finales
  const getFinalRecommendations = () => {
    const recommendations = [];
    const { content, keywords, images } = data;
    
    // Vérifier le titre
    if (!content.title) {
      recommendations.push({
        type: 'error',
        text: __('Ajoutez un titre à votre contenu', 'boss-seo')
      });
    } else if (content.title.length < 20) {
      recommendations.push({
        type: 'warning',
        text: __('Votre titre est trop court (moins de 20 caractères)', 'boss-seo')
      });
    } else if (content.title.length > 70) {
      recommendations.push({
        type: 'warning',
        text: __('Votre titre est trop long (plus de 70 caractères)', 'boss-seo')
      });
    }
    
    // Vérifier la méta description
    if (!content.metaDescription) {
      recommendations.push({
        type: 'error',
        text: __('Ajoutez une méta description à votre contenu', 'boss-seo')
      });
    } else if (content.metaDescription.length < 120) {
      recommendations.push({
        type: 'warning',
        text: __('Votre méta description est trop courte (moins de 120 caractères)', 'boss-seo')
      });
    } else if (content.metaDescription.length > 160) {
      recommendations.push({
        type: 'warning',
        text: __('Votre méta description est trop longue (plus de 160 caractères)', 'boss-seo')
      });
    }
    
    // Vérifier le contenu
    if (!content.content) {
      recommendations.push({
        type: 'error',
        text: __('Ajoutez du contenu à votre page', 'boss-seo')
      });
    } else if (content.content.length < 300) {
      recommendations.push({
        type: 'warning',
        text: __('Votre contenu est trop court (moins de 300 caractères)', 'boss-seo')
      });
    }
    
    // Vérifier les mots-clés
    if (!keywords.main) {
      recommendations.push({
        type: 'error',
        text: __('Sélectionnez un mot-clé principal', 'boss-seo')
      });
    } else if (content.content && !content.content.toLowerCase().includes(keywords.main.toLowerCase())) {
      recommendations.push({
        type: 'error',
        text: __('Votre mot-clé principal n\'apparaît pas dans le contenu', 'boss-seo')
      });
    }
    
    // Vérifier les images
    if (!images.selected || images.selected.length === 0) {
      recommendations.push({
        type: 'warning',
        text: __('Ajoutez au moins une image à votre contenu', 'boss-seo')
      });
    }
    
    return recommendations;
  };
  
  const finalRecommendations = getFinalRecommendations();
  const canPublish = finalRecommendations.filter(rec => rec.type === 'error').length === 0;
  
  return (
    <div className="boss-space-y-6">
      <div className="boss-mb-6">
        <h2 className="boss-text-xl boss-font-semibold boss-text-boss-dark boss-mb-4">
          {__('Étape 5 : Optimisation finale et publication', 'boss-seo')}
        </h2>
        <p className="boss-text-boss-gray">
          {__('Vérifiez votre contenu, consultez les recommandations finales et publiez votre contenu optimisé pour le SEO.', 'boss-seo')}
        </p>
      </div>
      
      {!canPublish && (
        <Notice status="error" isDismissible={false} className="boss-mb-4">
          {__('Votre contenu présente des erreurs qui doivent être corrigées avant la publication. Consultez les recommandations ci-dessous.', 'boss-seo')}
        </Notice>
      )}
      
      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="clipboard" className="boss-mr-2" />
            {__('Résumé du contenu', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-mb-6">
            <h4 className="boss-font-medium boss-mb-2">{__('Titre', 'boss-seo')}</h4>
            <div className="boss-p-3 boss-bg-gray-50 boss-rounded boss-border boss-border-gray-200">
              {data.content.title || __('(Aucun titre défini)', 'boss-seo')}
            </div>
          </div>
          
          <div className="boss-mb-6">
            <h4 className="boss-font-medium boss-mb-2">{__('Méta description', 'boss-seo')}</h4>
            <div className="boss-p-3 boss-bg-gray-50 boss-rounded boss-border boss-border-gray-200">
              {data.content.metaDescription || __('(Aucune méta description définie)', 'boss-seo')}
            </div>
          </div>
          
          <div className="boss-mb-6">
            <h4 className="boss-font-medium boss-mb-2">{__('Mots-clés', 'boss-seo')}</h4>
            <div className="boss-flex boss-flex-wrap boss-gap-2">
              {data.keywords.main ? (
                <Badge className="boss-bg-boss-primary boss-text-white boss-px-3 boss-py-1 boss-rounded-full">
                  {data.keywords.main}
                </Badge>
              ) : (
                <div className="boss-text-boss-gray boss-italic">
                  {__('(Aucun mot-clé principal défini)', 'boss-seo')}
                </div>
              )}
              
              {data.keywords.secondary && data.keywords.secondary.map((keyword, index) => (
                <Badge key={index} className="boss-bg-boss-secondary boss-text-white boss-px-3 boss-py-1 boss-rounded-full">
                  {keyword}
                </Badge>
              ))}
            </div>
          </div>
          
          <div className="boss-mb-6">
            <h4 className="boss-font-medium boss-mb-2">{__('Images sélectionnées', 'boss-seo')}</h4>
            {data.images.selected && data.images.selected.length > 0 ? (
              <div className="boss-grid boss-grid-cols-2 md:boss-grid-cols-4 boss-gap-2">
                {data.images.selected.map((image, index) => (
                  <div key={index} className="boss-relative boss-rounded boss-overflow-hidden boss-border boss-border-gray-200">
                    <div className="boss-aspect-w-16 boss-aspect-h-9 boss-bg-gray-100">
                      <img 
                        src={image.thumbnail} 
                        alt={image.alt} 
                        className="boss-object-cover boss-w-full boss-h-full"
                      />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="boss-text-boss-gray boss-italic">
                {__('(Aucune image sélectionnée)', 'boss-seo')}
              </div>
            )}
          </div>
          
          <div className="boss-mb-6">
            <h4 className="boss-font-medium boss-mb-2">{__('Aperçu du contenu', 'boss-seo')}</h4>
            <div className="boss-p-3 boss-bg-gray-50 boss-rounded boss-border boss-border-gray-200 boss-max-h-60 boss-overflow-y-auto">
              {data.content.content ? (
                <div dangerouslySetInnerHTML={{ __html: data.content.content }} />
              ) : (
                <div className="boss-text-boss-gray boss-italic">
                  {__('(Aucun contenu défini)', 'boss-seo')}
                </div>
              )}
            </div>
          </div>
        </CardBody>
      </Card>
      
      <Card className="boss-mb-6">
        <CardHeader>
          <div className="boss-flex boss-justify-between boss-items-center">
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              <Dashicon icon="chart-bar" className="boss-mr-2" />
              {__('Score SEO', 'boss-seo')}
            </h3>
            
            <div className={`boss-px-3 boss-py-1 boss-rounded-full ${getSeoScoreClass(data.content.seoScore?.overall || 0)}`}>
              {data.content.seoScore?.overall || 0}/100
            </div>
          </div>
        </CardHeader>
        <CardBody>
          <div className="boss-space-y-4">
            {finalRecommendations.length > 0 ? (
              finalRecommendations.map((recommendation, index) => (
                <div 
                  key={index} 
                  className={`boss-p-3 boss-rounded boss-flex boss-items-start ${
                    recommendation.type === 'error' 
                      ? 'boss-bg-red-50 boss-text-red-700' 
                      : 'boss-bg-yellow-50 boss-text-yellow-700'
                  }`}
                >
                  <Dashicon 
                    icon={recommendation.type === 'error' ? 'dismiss' : 'warning'} 
                    className="boss-mr-2 boss-mt-0.5" 
                  />
                  <div>{recommendation.text}</div>
                </div>
              ))
            ) : (
              <div className="boss-p-3 boss-rounded boss-bg-green-50 boss-text-green-700 boss-flex boss-items-start">
                <Dashicon icon="yes-alt" className="boss-mr-2 boss-mt-0.5" />
                <div>{__('Votre contenu est prêt à être publié ! Aucune recommandation d\'amélioration.', 'boss-seo')}</div>
              </div>
            )}
          </div>
        </CardBody>
      </Card>
      
      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="admin-settings" className="boss-mr-2" />
            {__('Options de publication', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mb-4">
            <SelectControl
              label={__('Statut', 'boss-seo')}
              value={publishOptions.status}
              options={[
                { label: __('Brouillon', 'boss-seo'), value: 'draft' },
                { label: __('Publié', 'boss-seo'), value: 'publish' },
                { label: __('En attente de relecture', 'boss-seo'), value: 'pending' },
              ]}
              onChange={(value) => updatePublishOption('status', value)}
            />
            
            <SelectControl
              label={__('Catégorie', 'boss-seo')}
              value={publishOptions.category}
              options={[
                { label: __('Sélectionner...', 'boss-seo'), value: '' },
                { label: __('Non catégorisé', 'boss-seo'), value: 'uncategorized' },
                { label: __('SEO', 'boss-seo'), value: 'seo' },
                { label: __('Marketing', 'boss-seo'), value: 'marketing' },
                { label: __('Technologie', 'boss-seo'), value: 'technology' },
              ]}
              onChange={(value) => updatePublishOption('category', value)}
            />
          </div>
          
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
            <ToggleControl
              label={__('Article mis en avant', 'boss-seo')}
              checked={publishOptions.featured}
              onChange={(value) => updatePublishOption('featured', value)}
            />
            
            <ToggleControl
              label={__('Autoriser les commentaires', 'boss-seo')}
              checked={publishOptions.comments}
              onChange={(value) => updatePublishOption('comments', value)}
            />
          </div>
        </CardBody>
      </Card>
      
      <div className="boss-flex boss-justify-end boss-space-x-4">
        <Button
          isSecondary
          onClick={onSaveDraft}
          disabled={isLoading}
          isBusy={isLoading}
        >
          {__('Sauvegarder comme brouillon', 'boss-seo')}
        </Button>
        
        <Button
          isPrimary
          onClick={onPublish}
          disabled={!canPublish || isLoading}
          isBusy={isLoading}
        >
          {__('Publier', 'boss-seo')}
        </Button>
      </div>
    </div>
  );
};

export default StepPublish;
