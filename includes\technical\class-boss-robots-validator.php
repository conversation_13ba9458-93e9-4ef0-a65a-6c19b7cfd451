<?php
/**
 * Classe pour la validation du fichier robots.txt.
 *
 * Cette classe fournit des méthodes pour valider la syntaxe du fichier robots.txt.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Robots_Validator {

    /**
     * Valide la syntaxe du fichier robots.txt.
     *
     * @since    1.2.0
     * @param    string    $content    Le contenu du fichier robots.txt.
     * @return   array                 Un tableau contenant les erreurs de validation.
     */
    public static function validate( $content ) {
        $errors = array();
        $line_number = 0;
        $user_agent_defined = false;
        $disallow_after_user_agent = false;
        $allow_after_user_agent = false;
        $sitemap_valid = true;

        // Diviser le contenu en lignes
        $lines = explode( "\n", $content );

        foreach ( $lines as $line ) {
            $line_number++;
            $line = trim( $line );

            // Ignorer les lignes vides et les commentaires
            if ( empty( $line ) || strpos( $line, '#' ) === 0 ) {
                continue;
            }

            // Vérifier la directive User-agent
            if ( preg_match( '/^User-agent\s*:\s*(.+)$/i', $line, $matches ) ) {
                $user_agent = trim( $matches[1] );
                $user_agent_defined = true;
                $disallow_after_user_agent = false;
                $allow_after_user_agent = false;

                // Vérifier si l'agent utilisateur est vide
                if ( empty( $user_agent ) ) {
                    $errors[] = array(
                        'line' => $line_number,
                        'message' => __( 'L\'agent utilisateur ne peut pas être vide.', 'boss-seo' ),
                        'type' => 'error',
                    );
                }
            }
            // Vérifier la directive Disallow
            elseif ( preg_match( '/^Disallow\s*:\s*(.*)$/i', $line, $matches ) ) {
                if ( ! $user_agent_defined ) {
                    $errors[] = array(
                        'line' => $line_number,
                        'message' => __( 'La directive Disallow doit être précédée d\'une directive User-agent.', 'boss-seo' ),
                        'type' => 'error',
                    );
                } else {
                    $disallow_after_user_agent = true;
                }

                // Vérifier si le chemin contient des caractères invalides
                $path = trim( $matches[1] );
                if ( ! empty( $path ) && ! self::is_valid_path( $path ) ) {
                    $errors[] = array(
                        'line' => $line_number,
                        'message' => __( 'Le chemin contient des caractères invalides.', 'boss-seo' ),
                        'type' => 'warning',
                    );
                }
            }
            // Vérifier la directive Allow
            elseif ( preg_match( '/^Allow\s*:\s*(.*)$/i', $line, $matches ) ) {
                if ( ! $user_agent_defined ) {
                    $errors[] = array(
                        'line' => $line_number,
                        'message' => __( 'La directive Allow doit être précédée d\'une directive User-agent.', 'boss-seo' ),
                        'type' => 'error',
                    );
                } else {
                    $allow_after_user_agent = true;
                }

                // Vérifier si le chemin contient des caractères invalides
                $path = trim( $matches[1] );
                if ( ! empty( $path ) && ! self::is_valid_path( $path ) ) {
                    $errors[] = array(
                        'line' => $line_number,
                        'message' => __( 'Le chemin contient des caractères invalides.', 'boss-seo' ),
                        'type' => 'warning',
                    );
                }
            }
            // Vérifier la directive Sitemap
            elseif ( preg_match( '/^Sitemap\s*:\s*(.+)$/i', $line, $matches ) ) {
                $sitemap_url = trim( $matches[1] );

                // Vérifier si l'URL du sitemap est valide
                if ( ! filter_var( $sitemap_url, FILTER_VALIDATE_URL ) ) {
                    $errors[] = array(
                        'line' => $line_number,
                        'message' => __( 'L\'URL du sitemap n\'est pas valide.', 'boss-seo' ),
                        'type' => 'error',
                    );
                    $sitemap_valid = false;
                }
            }
            // Vérifier la directive Crawl-delay
            elseif ( preg_match( '/^Crawl-delay\s*:\s*(.+)$/i', $line, $matches ) ) {
                $crawl_delay = trim( $matches[1] );

                // Vérifier si le délai de crawl est un nombre
                if ( ! is_numeric( $crawl_delay ) ) {
                    $errors[] = array(
                        'line' => $line_number,
                        'message' => __( 'Le délai de crawl doit être un nombre.', 'boss-seo' ),
                        'type' => 'error',
                    );
                }
            }
            // Vérifier les directives inconnues
            else {
                // Vérifier si la ligne contient un deux-points (directive)
                if ( strpos( $line, ':' ) !== false ) {
                    $directive = substr( $line, 0, strpos( $line, ':' ) );
                    $directive = trim( $directive );

                    // Vérifier si la directive est connue
                    if ( ! in_array( strtolower( $directive ), array( 'user-agent', 'disallow', 'allow', 'sitemap', 'crawl-delay', 'host' ) ) ) {
                        $errors[] = array(
                            'line' => $line_number,
                            'message' => sprintf( __( 'Directive inconnue : %s', 'boss-seo' ), $directive ),
                            'type' => 'warning',
                        );
                    }
                } else {
                    $errors[] = array(
                        'line' => $line_number,
                        'message' => __( 'Ligne invalide. Les directives doivent être au format "Directive: Valeur".', 'boss-seo' ),
                        'type' => 'error',
                    );
                }
            }
        }

        // Vérifier si au moins une directive User-agent est définie
        if ( ! $user_agent_defined && ! empty( $content ) ) {
            $errors[] = array(
                'line' => 0,
                'message' => __( 'Aucune directive User-agent n\'est définie.', 'boss-seo' ),
                'type' => 'error',
            );
        }

        // Vérifier si au moins une directive Disallow ou Allow est définie après chaque User-agent
        if ( $user_agent_defined && ! $disallow_after_user_agent && ! $allow_after_user_agent ) {
            $errors[] = array(
                'line' => 0,
                'message' => __( 'Au moins une directive Disallow ou Allow doit être définie après chaque User-agent.', 'boss-seo' ),
                'type' => 'warning',
            );
        }

        return $errors;
    }

    /**
     * Vérifie si un chemin est valide.
     *
     * @since    1.2.0
     * @param    string    $path    Le chemin à vérifier.
     * @return   bool               True si le chemin est valide, false sinon.
     */
    private static function is_valid_path( $path ) {
        // Vérifier si le chemin contient des caractères invalides
        return ! preg_match( '/[^\x20-\x7E]/', $path );
    }

    /**
     * Génère des suggestions de règles prédéfinies pour le fichier robots.txt.
     *
     * @since    1.2.0
     * @param    string    $type    Le type de règles à générer (wordpress-core, media, sitemap, etc.).
     * @return   string             Les règles générées.
     */
    public static function generate_rules( $type ) {
        $rules = '';

        switch ( $type ) {
            case 'wordpress-core':
                $rules = "# WordPress Core\n";
                $rules .= "User-agent: *\n";
                $rules .= "Disallow: /wp-admin/\n";
                $rules .= "Allow: /wp-admin/admin-ajax.php\n";
                $rules .= "Disallow: /wp-includes/\n";
                $rules .= "Disallow: /wp-content/plugins/\n";
                $rules .= "Disallow: /wp-content/themes/\n";
                $rules .= "Disallow: /wp-login.php\n";
                $rules .= "Disallow: /xmlrpc.php\n";
                $rules .= "Disallow: /readme.html\n";
                break;

            case 'media':
                $rules = "# Médias\n";
                $rules .= "User-agent: *\n";
                $rules .= "Allow: /wp-content/uploads/\n";
                break;

            case 'sitemap':
                $site_url = get_site_url();
                $rules = "# Sitemap\n";
                $rules .= "Sitemap: {$site_url}/sitemap.xml\n";
                break;

            case 'ecommerce':
                $rules = "# E-commerce\n";
                $rules .= "User-agent: *\n";
                $rules .= "Disallow: /cart/\n";
                $rules .= "Disallow: /checkout/\n";
                $rules .= "Disallow: /my-account/\n";
                $rules .= "Disallow: /*?add-to-cart=*\n";
                break;

            case 'preview':
                $rules = "# Prévisualisations\n";
                $rules .= "User-agent: *\n";
                $rules .= "Disallow: /*?preview=true\n";
                $rules .= "Disallow: /*&preview=true\n";
                $rules .= "Disallow: /*?p=*\n";
                break;

            case 'noindex':
                $rules = "# Pages noindex\n";
                $rules .= "User-agent: *\n";
                $rules .= "Disallow: /tag/\n";
                $rules .= "Disallow: /author/\n";
                $rules .= "Disallow: /search/\n";
                $rules .= "Disallow: /*?s=*\n";
                break;

            case 'complete':
                $site_url = get_site_url();
                $rules = "# Règles complètes pour WordPress\n\n";
                
                // WordPress Core
                $rules .= "# WordPress Core\n";
                $rules .= "User-agent: *\n";
                $rules .= "Disallow: /wp-admin/\n";
                $rules .= "Allow: /wp-admin/admin-ajax.php\n";
                $rules .= "Disallow: /wp-includes/\n";
                $rules .= "Disallow: /wp-content/plugins/\n";
                $rules .= "Disallow: /wp-content/themes/\n";
                $rules .= "Disallow: /wp-login.php\n";
                $rules .= "Disallow: /xmlrpc.php\n";
                $rules .= "Disallow: /readme.html\n\n";
                
                // Prévisualisations
                $rules .= "# Prévisualisations\n";
                $rules .= "Disallow: /*?preview=true\n";
                $rules .= "Disallow: /*&preview=true\n";
                $rules .= "Disallow: /*?p=*\n\n";
                
                // Pages noindex
                $rules .= "# Pages noindex\n";
                $rules .= "Disallow: /tag/\n";
                $rules .= "Disallow: /author/\n";
                $rules .= "Disallow: /search/\n";
                $rules .= "Disallow: /*?s=*\n\n";
                
                // Médias
                $rules .= "# Médias\n";
                $rules .= "Allow: /wp-content/uploads/\n\n";
                
                // E-commerce (si WooCommerce est installé)
                if ( class_exists( 'WooCommerce' ) ) {
                    $rules .= "# E-commerce\n";
                    $rules .= "Disallow: /cart/\n";
                    $rules .= "Disallow: /checkout/\n";
                    $rules .= "Disallow: /my-account/\n";
                    $rules .= "Disallow: /*?add-to-cart=*\n\n";
                }
                
                // Sitemap
                $rules .= "# Sitemap\n";
                $rules .= "Sitemap: {$site_url}/sitemap.xml\n";
                break;
        }

        return $rules;
    }
}
