<?php
/**
 * Classe pour les informations d'entreprise.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour les informations d'entreprise.
 *
 * Cette classe gère les informations d'entreprise.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_Business_Info {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_local_business_info_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_business_info', array( $this, 'ajax_get_business_info' ) );
        add_action( 'wp_ajax_boss_seo_save_business_info', array( $this, 'ajax_save_business_info' ) );
        add_action( 'wp_ajax_boss_seo_get_business_types', array( $this, 'ajax_get_business_types' ) );
        add_action( 'wp_ajax_boss_seo_get_business_countries', array( $this, 'ajax_get_business_countries' ) );
        add_action( 'wp_ajax_boss_seo_get_business_currencies', array( $this, 'ajax_get_business_currencies' ) );
        add_action( 'wp_ajax_boss_seo_get_business_languages', array( $this, 'ajax_get_business_languages' ) );

        // Ajouter les actions pour les shortcodes
        add_shortcode( 'boss_business_info', array( $this, 'business_info_shortcode' ) );
        add_shortcode( 'boss_business_hours', array( $this, 'business_hours_shortcode' ) );
        add_shortcode( 'boss_business_map', array( $this, 'business_map_shortcode' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/local/business-info',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_business_info' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_business_info' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/business-types',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_business_types' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/business-countries',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_business_countries' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/business-currencies',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_business_currencies' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/business-languages',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_business_languages' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les informations d'entreprise.
     *
     * @since    1.2.0
     */
    public function ajax_get_business_info() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les informations d'entreprise
        $business_info = $this->get_business_info_data();

        wp_send_json_success( array(
            'message'       => __( 'Informations d\'entreprise récupérées avec succès.', 'boss-seo' ),
            'business_info' => $business_info,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour enregistrer les informations d'entreprise.
     *
     * @since    1.2.0
     */
    public function ajax_save_business_info() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['business_info'] ) || ! is_array( $_POST['business_info'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Les informations d\'entreprise sont requises.', 'boss-seo' ) ) );
        }

        $business_info = $_POST['business_info'];

        // Enregistrer les informations d'entreprise
        $result = $this->save_business_info_data( $business_info );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'       => __( 'Informations d\'entreprise enregistrées avec succès.', 'boss-seo' ),
            'business_info' => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les types d'entreprise.
     *
     * @since    1.2.0
     */
    public function ajax_get_business_types() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les types d'entreprise
        $business_types = $this->get_business_types_data();

        wp_send_json_success( array(
            'message'        => __( 'Types d\'entreprise récupérés avec succès.', 'boss-seo' ),
            'business_types' => $business_types,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les pays.
     *
     * @since    1.2.0
     */
    public function ajax_get_business_countries() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les pays
        $countries = $this->get_business_countries_data();

        wp_send_json_success( array(
            'message'   => __( 'Pays récupérés avec succès.', 'boss-seo' ),
            'countries' => $countries,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les devises.
     *
     * @since    1.2.0
     */
    public function ajax_get_business_currencies() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les devises
        $currencies = $this->get_business_currencies_data();

        wp_send_json_success( array(
            'message'    => __( 'Devises récupérées avec succès.', 'boss-seo' ),
            'currencies' => $currencies,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les langues.
     *
     * @since    1.2.0
     */
    public function ajax_get_business_languages() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les langues
        $languages = $this->get_business_languages_data();

        wp_send_json_success( array(
            'message'   => __( 'Langues récupérées avec succès.', 'boss-seo' ),
            'languages' => $languages,
        ) );
    }

    /**
     * Récupère les informations d'entreprise via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_business_info( $request ) {
        $business_info = $this->get_business_info_data();

        return rest_ensure_response( $business_info );
    }

    /**
     * Enregistre les informations d'entreprise via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_business_info( $request ) {
        $params = $request->get_params();

        if ( ! isset( $params['business_info'] ) || ! is_array( $params['business_info'] ) ) {
            return new WP_Error( 'missing_business_info', __( 'Les informations d\'entreprise sont requises.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $result = $this->save_business_info_data( $params['business_info'] );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message'       => __( 'Informations d\'entreprise enregistrées avec succès.', 'boss-seo' ),
            'business_info' => $result,
        ) );
    }

    /**
     * Récupère les types d'entreprise via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_business_types( $request ) {
        $business_types = $this->get_business_types_data();

        return rest_ensure_response( $business_types );
    }

    /**
     * Récupère les pays via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_business_countries( $request ) {
        $countries = $this->get_business_countries_data();

        return rest_ensure_response( $countries );
    }

    /**
     * Récupère les devises via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_business_currencies( $request ) {
        $currencies = $this->get_business_currencies_data();

        return rest_ensure_response( $currencies );
    }

    /**
     * Récupère les langues via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_business_languages( $request ) {
        $languages = $this->get_business_languages_data();

        return rest_ensure_response( $languages );
    }

    /**
     * Shortcode pour afficher les informations d'entreprise.
     *
     * @since    1.2.0
     * @param    array     $atts    Les attributs du shortcode.
     * @return   string             Le contenu du shortcode.
     */
    public function business_info_shortcode( $atts ) {
        $atts = shortcode_atts( array(
            'show_name'    => 'yes',
            'show_address' => 'yes',
            'show_phone'   => 'yes',
            'show_email'   => 'yes',
            'show_website' => 'yes',
            'show_logo'    => 'yes',
            'show_social'  => 'yes',
            'location_id'  => 0,
        ), $atts, 'boss_business_info' );

        // Récupérer les informations d'entreprise
        $business_info = $this->get_business_info_data();

        // Récupérer l'emplacement spécifique si demandé
        if ( $atts['location_id'] > 0 ) {
            $location = get_post( $atts['location_id'] );

            if ( $location && $location->post_type === 'boss_local_location' ) {
                $business_info['name'] = $location->post_title;
                $business_info['address'] = get_post_meta( $location->ID, '_boss_local_location_address', true );
                $business_info['city'] = get_post_meta( $location->ID, '_boss_local_location_city', true );
                $business_info['state'] = get_post_meta( $location->ID, '_boss_local_location_state', true );
                $business_info['postal_code'] = get_post_meta( $location->ID, '_boss_local_location_postal_code', true );
                $business_info['country'] = get_post_meta( $location->ID, '_boss_local_location_country', true );
                $business_info['phone'] = get_post_meta( $location->ID, '_boss_local_location_phone', true );
                $business_info['email'] = get_post_meta( $location->ID, '_boss_local_location_email', true );
                $business_info['website'] = get_post_meta( $location->ID, '_boss_local_location_website', true );
            }
        }

        // Générer le HTML
        $html = '<div class="boss-business-info">';

        // Nom de l'entreprise
        if ( $atts['show_name'] === 'yes' && ! empty( $business_info['name'] ) ) {
            $html .= '<div class="boss-business-name">' . esc_html( $business_info['name'] ) . '</div>';
        }

        // Logo
        if ( $atts['show_logo'] === 'yes' && ! empty( $business_info['logo'] ) ) {
            $html .= '<div class="boss-business-logo"><img src="' . esc_url( $business_info['logo'] ) . '" alt="' . esc_attr( $business_info['name'] ) . '"></div>';
        }

        // Adresse
        if ( $atts['show_address'] === 'yes' ) {
            $address = array();

            if ( ! empty( $business_info['address'] ) ) {
                $address[] = $business_info['address'];
            }

            if ( ! empty( $business_info['city'] ) ) {
                $city_line = $business_info['city'];

                if ( ! empty( $business_info['state'] ) ) {
                    $city_line .= ', ' . $business_info['state'];
                }

                if ( ! empty( $business_info['postal_code'] ) ) {
                    $city_line .= ' ' . $business_info['postal_code'];
                }

                $address[] = $city_line;
            }

            if ( ! empty( $business_info['country'] ) ) {
                $address[] = $business_info['country'];
            }

            if ( ! empty( $address ) ) {
                $html .= '<div class="boss-business-address">' . implode( '<br>', array_map( 'esc_html', $address ) ) . '</div>';
            }
        }

        // Téléphone
        if ( $atts['show_phone'] === 'yes' && ! empty( $business_info['phone'] ) ) {
            $html .= '<div class="boss-business-phone"><a href="tel:' . esc_attr( $business_info['phone'] ) . '">' . esc_html( $business_info['phone'] ) . '</a></div>';
        }

        // Email
        if ( $atts['show_email'] === 'yes' && ! empty( $business_info['email'] ) ) {
            $html .= '<div class="boss-business-email"><a href="mailto:' . esc_attr( $business_info['email'] ) . '">' . esc_html( $business_info['email'] ) . '</a></div>';
        }

        // Site web
        if ( $atts['show_website'] === 'yes' && ! empty( $business_info['website'] ) ) {
            $html .= '<div class="boss-business-website"><a href="' . esc_url( $business_info['website'] ) . '" target="_blank">' . esc_html( $business_info['website'] ) . '</a></div>';
        }

        // Réseaux sociaux
        if ( $atts['show_social'] === 'yes' && ! empty( $business_info['social_profiles'] ) ) {
            $html .= '<div class="boss-business-social">';

            foreach ( $business_info['social_profiles'] as $network => $url ) {
                if ( ! empty( $url ) ) {
                    $html .= '<a href="' . esc_url( $url ) . '" target="_blank" class="boss-social-' . esc_attr( $network ) . '">' . esc_html( ucfirst( $network ) ) . '</a>';
                }
            }

            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Shortcode pour afficher les horaires d'ouverture.
     *
     * @since    1.2.0
     * @param    array     $atts    Les attributs du shortcode.
     * @return   string             Le contenu du shortcode.
     */
    public function business_hours_shortcode( $atts ) {
        $atts = shortcode_atts( array(
            'show_title'    => 'yes',
            'title'         => __( 'Horaires d\'ouverture', 'boss-seo' ),
            'show_closed'   => 'yes',
            'location_id'   => 0,
            'days_format'   => 'full',
            'hours_format'  => '24h',
        ), $atts, 'boss_business_hours' );

        // Récupérer les informations d'entreprise
        $business_info = $this->get_business_info_data();
        $hours = $business_info['hours'];

        // Récupérer l'emplacement spécifique si demandé
        if ( $atts['location_id'] > 0 ) {
            $location_hours = get_post_meta( $atts['location_id'], '_boss_local_location_hours', true );

            if ( $location_hours ) {
                $hours = $location_hours;
            }
        }

        // Générer le HTML
        $html = '<div class="boss-business-hours">';

        // Titre
        if ( $atts['show_title'] === 'yes' && ! empty( $atts['title'] ) ) {
            $html .= '<h3 class="boss-business-hours-title">' . esc_html( $atts['title'] ) . '</h3>';
        }

        // Horaires
        if ( ! empty( $hours ) ) {
            $html .= '<table class="boss-business-hours-table">';

            $days = array(
                'monday'    => __( 'Lundi', 'boss-seo' ),
                'tuesday'   => __( 'Mardi', 'boss-seo' ),
                'wednesday' => __( 'Mercredi', 'boss-seo' ),
                'thursday'  => __( 'Jeudi', 'boss-seo' ),
                'friday'    => __( 'Vendredi', 'boss-seo' ),
                'saturday'  => __( 'Samedi', 'boss-seo' ),
                'sunday'    => __( 'Dimanche', 'boss-seo' ),
            );

            foreach ( $days as $day_key => $day_name ) {
                if ( isset( $hours[ $day_key ] ) ) {
                    $day_hours = $hours[ $day_key ];

                    // Afficher les jours fermés uniquement si demandé
                    if ( $atts['show_closed'] === 'no' && ( ! isset( $day_hours['open'] ) || ! $day_hours['open'] ) ) {
                        continue;
                    }

                    $html .= '<tr>';

                    // Jour
                    if ( $atts['days_format'] === 'short' ) {
                        $day_name = substr( $day_name, 0, 3 );
                    }

                    $html .= '<td class="boss-business-hours-day">' . esc_html( $day_name ) . '</td>';

                    // Horaires
                    $html .= '<td class="boss-business-hours-hours">';

                    if ( isset( $day_hours['open'] ) && $day_hours['open'] && isset( $day_hours['hours'] ) && ! empty( $day_hours['hours'] ) ) {
                        $periods = array();

                        foreach ( $day_hours['hours'] as $period ) {
                            if ( isset( $period['open'] ) && isset( $period['close'] ) ) {
                                $open = $period['open'];
                                $close = $period['close'];

                                // Convertir au format 12h si demandé
                                if ( $atts['hours_format'] === '12h' ) {
                                    $open = date( 'g:i A', strtotime( $open ) );
                                    $close = date( 'g:i A', strtotime( $close ) );
                                }

                                $periods[] = esc_html( $open ) . ' - ' . esc_html( $close );
                            }
                        }

                        $html .= implode( ', ', $periods );
                    } else {
                        $html .= '<span class="boss-business-hours-closed">' . esc_html__( 'Fermé', 'boss-seo' ) . '</span>';
                    }

                    $html .= '</td>';
                    $html .= '</tr>';
                }
            }

            $html .= '</table>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Shortcode pour afficher une carte Google Maps.
     *
     * @since    1.2.0
     * @param    array     $atts    Les attributs du shortcode.
     * @return   string             Le contenu du shortcode.
     */
    public function business_map_shortcode( $atts ) {
        $atts = shortcode_atts( array(
            'show_title'   => 'yes',
            'title'        => __( 'Nous trouver', 'boss-seo' ),
            'width'        => '100%',
            'height'       => '400px',
            'zoom'         => 15,
            'location_id'  => 0,
            'show_marker'  => 'yes',
            'marker_title' => '',
            'map_type'     => 'roadmap',
        ), $atts, 'boss_business_map' );

        // Récupérer les informations d'entreprise
        $business_info = $this->get_business_info_data();
        $latitude = $business_info['latitude'];
        $longitude = $business_info['longitude'];
        $marker_title = ! empty( $atts['marker_title'] ) ? $atts['marker_title'] : $business_info['name'];

        // Récupérer l'emplacement spécifique si demandé
        if ( $atts['location_id'] > 0 ) {
            $location_latitude = get_post_meta( $atts['location_id'], '_boss_local_location_latitude', true );
            $location_longitude = get_post_meta( $atts['location_id'], '_boss_local_location_longitude', true );

            if ( $location_latitude && $location_longitude ) {
                $latitude = $location_latitude;
                $longitude = $location_longitude;

                if ( empty( $atts['marker_title'] ) ) {
                    $location = get_post( $atts['location_id'] );

                    if ( $location ) {
                        $marker_title = $location->post_title;
                    }
                }
            }
        }

        // Vérifier si les coordonnées sont valides
        if ( empty( $latitude ) || empty( $longitude ) ) {
            return '<div class="boss-business-map-error">' . esc_html__( 'Coordonnées de localisation non disponibles.', 'boss-seo' ) . '</div>';
        }

        // Générer un ID unique pour la carte
        $map_id = 'boss-business-map-' . uniqid();

        // Générer le HTML
        $html = '<div class="boss-business-map">';

        // Titre
        if ( $atts['show_title'] === 'yes' && ! empty( $atts['title'] ) ) {
            $html .= '<h3 class="boss-business-map-title">' . esc_html( $atts['title'] ) . '</h3>';
        }

        // Carte
        $html .= '<div id="' . esc_attr( $map_id ) . '" class="boss-business-map-container" style="width: ' . esc_attr( $atts['width'] ) . '; height: ' . esc_attr( $atts['height'] ) . ';"></div>';

        // Script pour initialiser la carte
        $html .= '<script type="text/javascript">
            function initBossBusinessMap_' . esc_js( $map_id ) . '() {
                var mapOptions = {
                    center: new google.maps.LatLng(' . esc_js( $latitude ) . ', ' . esc_js( $longitude ) . '),
                    zoom: ' . esc_js( $atts['zoom'] ) . ',
                    mapTypeId: google.maps.MapTypeId.' . esc_js( strtoupper( $atts['map_type'] ) ) . '
                };

                var map = new google.maps.Map(document.getElementById("' . esc_js( $map_id ) . '"), mapOptions);

                ' . ( $atts['show_marker'] === 'yes' ? '
                var marker = new google.maps.Marker({
                    position: new google.maps.LatLng(' . esc_js( $latitude ) . ', ' . esc_js( $longitude ) . '),
                    map: map,
                    title: "' . esc_js( $marker_title ) . '"
                });
                ' : '' ) . '
            }

            if (typeof google !== "undefined" && typeof google.maps !== "undefined") {
                initBossBusinessMap_' . esc_js( $map_id ) . '();
            } else {
                document.addEventListener("boss_google_maps_loaded", function() {
                    initBossBusinessMap_' . esc_js( $map_id ) . '();
                });
            }
        </script>';

        $html .= '</div>';

        // Charger l'API Google Maps si nécessaire
        $this->enqueue_google_maps_api();

        return $html;
    }

    /**
     * Charge l'API Google Maps.
     *
     * @since    1.2.0
     */
    private function enqueue_google_maps_api() {
        static $api_loaded = false;

        if ( $api_loaded ) {
            return;
        }

        // Récupérer la clé API
        $business_info = $this->get_business_info_data();
        $api_key = '';

        // Vérifier si la clé API est définie dans les options du plugin
        $options = get_option( 'boss_local_settings', array() );

        if ( isset( $options['google_maps_api_key'] ) && ! empty( $options['google_maps_api_key'] ) ) {
            $api_key = $options['google_maps_api_key'];
        }

        if ( empty( $api_key ) ) {
            return;
        }

        // Enregistrer le script
        wp_enqueue_script( 'boss-google-maps', 'https://maps.googleapis.com/maps/api/js?key=' . $api_key . '&libraries=places', array(), null, true );

        // Ajouter un événement pour signaler que l'API est chargée
        wp_add_inline_script( 'boss-google-maps', 'document.dispatchEvent(new Event("boss_google_maps_loaded"));' );

        $api_loaded = true;
    }

    /**
     * Récupère les informations d'entreprise.
     *
     * @since    1.2.0
     * @return   array    Les informations d'entreprise.
     */
    private function get_business_info_data() {
        $default_info = array(
            'name'            => get_bloginfo( 'name' ),
            'description'     => get_bloginfo( 'description' ),
            'logo'            => '',
            'address'         => '',
            'city'            => '',
            'state'           => '',
            'postal_code'     => '',
            'country'         => 'FR',
            'phone'           => '',
            'email'           => get_bloginfo( 'admin_email' ),
            'website'         => get_bloginfo( 'url' ),
            'latitude'        => 0,
            'longitude'       => 0,
            'business_type'   => 'LocalBusiness',
            'currency'        => 'EUR',
            'language'        => 'fr',
            'hours'           => array(
                'monday'    => array( 'open' => true, 'hours' => array( array( 'open' => '09:00', 'close' => '18:00' ) ) ),
                'tuesday'   => array( 'open' => true, 'hours' => array( array( 'open' => '09:00', 'close' => '18:00' ) ) ),
                'wednesday' => array( 'open' => true, 'hours' => array( array( 'open' => '09:00', 'close' => '18:00' ) ) ),
                'thursday'  => array( 'open' => true, 'hours' => array( array( 'open' => '09:00', 'close' => '18:00' ) ) ),
                'friday'    => array( 'open' => true, 'hours' => array( array( 'open' => '09:00', 'close' => '18:00' ) ) ),
                'saturday'  => array( 'open' => false, 'hours' => array() ),
                'sunday'    => array( 'open' => false, 'hours' => array() ),
            ),
            'special_hours'   => array(),
            'social_profiles' => array(
                'facebook'  => '',
                'twitter'   => '',
                'instagram' => '',
                'linkedin'  => '',
                'youtube'   => '',
                'pinterest' => '',
            ),
            'payment_methods' => array(
                'cash'          => true,
                'credit_card'   => true,
                'debit_card'    => true,
                'bank_transfer' => false,
                'paypal'        => false,
                'apple_pay'     => false,
                'google_pay'    => false,
            ),
            'founded'         => '',
            'founder'         => '',
            'employees'       => '',
            'tax_id'          => '',
            'vat_id'          => '',
            'price_range'     => '€€',
        );

        $business_info = get_option( $this->option_prefix . 'data', $default_info );

        // Fusionner avec les valeurs par défaut pour s'assurer que toutes les clés existent
        $business_info = wp_parse_args( $business_info, $default_info );

        // Récupérer le logo du site
        if ( empty( $business_info['logo'] ) && has_custom_logo() ) {
            $custom_logo_id = get_theme_mod( 'custom_logo' );
            $logo_url = wp_get_attachment_image_url( $custom_logo_id, 'full' );

            if ( $logo_url ) {
                $business_info['logo'] = $logo_url;
            }
        }

        return $business_info;
    }

    /**
     * Enregistre les informations d'entreprise.
     *
     * @since    1.2.0
     * @param    array     $business_info    Les informations d'entreprise.
     * @return   array|WP_Error              Les informations d'entreprise ou une erreur.
     */
    private function save_business_info_data( $business_info ) {
        // Vérifier les données requises
        if ( ! isset( $business_info['name'] ) || empty( $business_info['name'] ) ) {
            return new WP_Error( 'missing_name', __( 'Le nom de l\'entreprise est requis.', 'boss-seo' ) );
        }

        // Récupérer les informations actuelles
        $current_info = $this->get_business_info_data();

        // Préparer les données à enregistrer
        $data = array();

        // Informations générales
        $data['name'] = sanitize_text_field( $business_info['name'] );
        $data['description'] = isset( $business_info['description'] ) ? wp_kses_post( $business_info['description'] ) : $current_info['description'];
        $data['logo'] = isset( $business_info['logo'] ) ? esc_url_raw( $business_info['logo'] ) : $current_info['logo'];

        // Adresse
        $data['address'] = isset( $business_info['address'] ) ? sanitize_text_field( $business_info['address'] ) : $current_info['address'];
        $data['city'] = isset( $business_info['city'] ) ? sanitize_text_field( $business_info['city'] ) : $current_info['city'];
        $data['state'] = isset( $business_info['state'] ) ? sanitize_text_field( $business_info['state'] ) : $current_info['state'];
        $data['postal_code'] = isset( $business_info['postal_code'] ) ? sanitize_text_field( $business_info['postal_code'] ) : $current_info['postal_code'];
        $data['country'] = isset( $business_info['country'] ) ? sanitize_text_field( $business_info['country'] ) : $current_info['country'];

        // Coordonnées
        $data['phone'] = isset( $business_info['phone'] ) ? sanitize_text_field( $business_info['phone'] ) : $current_info['phone'];
        $data['email'] = isset( $business_info['email'] ) ? sanitize_email( $business_info['email'] ) : $current_info['email'];
        $data['website'] = isset( $business_info['website'] ) ? esc_url_raw( $business_info['website'] ) : $current_info['website'];

        // Géolocalisation
        $data['latitude'] = isset( $business_info['latitude'] ) ? (float) $business_info['latitude'] : $current_info['latitude'];
        $data['longitude'] = isset( $business_info['longitude'] ) ? (float) $business_info['longitude'] : $current_info['longitude'];

        // Type d'entreprise
        $data['business_type'] = isset( $business_info['business_type'] ) ? sanitize_text_field( $business_info['business_type'] ) : $current_info['business_type'];

        // Paramètres régionaux
        $data['currency'] = isset( $business_info['currency'] ) ? sanitize_text_field( $business_info['currency'] ) : $current_info['currency'];
        $data['language'] = isset( $business_info['language'] ) ? sanitize_text_field( $business_info['language'] ) : $current_info['language'];

        // Horaires
        if ( isset( $business_info['hours'] ) && is_array( $business_info['hours'] ) ) {
            $data['hours'] = array();
            $days = array( 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday' );

            foreach ( $days as $day ) {
                if ( isset( $business_info['hours'][ $day ] ) && is_array( $business_info['hours'][ $day ] ) ) {
                    $data['hours'][ $day ] = array(
                        'open'  => isset( $business_info['hours'][ $day ]['open'] ) ? (bool) $business_info['hours'][ $day ]['open'] : false,
                        'hours' => array(),
                    );

                    if ( isset( $business_info['hours'][ $day ]['hours'] ) && is_array( $business_info['hours'][ $day ]['hours'] ) ) {
                        foreach ( $business_info['hours'][ $day ]['hours'] as $period ) {
                            if ( isset( $period['open'] ) && isset( $period['close'] ) ) {
                                $data['hours'][ $day ]['hours'][] = array(
                                    'open'  => sanitize_text_field( $period['open'] ),
                                    'close' => sanitize_text_field( $period['close'] ),
                                );
                            }
                        }
                    }
                } else {
                    $data['hours'][ $day ] = $current_info['hours'][ $day ];
                }
            }
        } else {
            $data['hours'] = $current_info['hours'];
        }

        // Horaires spéciaux
        if ( isset( $business_info['special_hours'] ) && is_array( $business_info['special_hours'] ) ) {
            $data['special_hours'] = array();

            foreach ( $business_info['special_hours'] as $special_hour ) {
                if ( isset( $special_hour['date'] ) && isset( $special_hour['open'] ) ) {
                    $special_hour_data = array(
                        'date'  => sanitize_text_field( $special_hour['date'] ),
                        'open'  => (bool) $special_hour['open'],
                        'hours' => array(),
                    );

                    if ( isset( $special_hour['hours'] ) && is_array( $special_hour['hours'] ) ) {
                        foreach ( $special_hour['hours'] as $period ) {
                            if ( isset( $period['open'] ) && isset( $period['close'] ) ) {
                                $special_hour_data['hours'][] = array(
                                    'open'  => sanitize_text_field( $period['open'] ),
                                    'close' => sanitize_text_field( $period['close'] ),
                                );
                            }
                        }
                    }

                    $data['special_hours'][] = $special_hour_data;
                }
            }
        } else {
            $data['special_hours'] = $current_info['special_hours'];
        }

        // Réseaux sociaux
        if ( isset( $business_info['social_profiles'] ) && is_array( $business_info['social_profiles'] ) ) {
            $data['social_profiles'] = array();
            $networks = array( 'facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest' );

            foreach ( $networks as $network ) {
                if ( isset( $business_info['social_profiles'][ $network ] ) ) {
                    $data['social_profiles'][ $network ] = esc_url_raw( $business_info['social_profiles'][ $network ] );
                } else {
                    $data['social_profiles'][ $network ] = $current_info['social_profiles'][ $network ];
                }
            }
        } else {
            $data['social_profiles'] = $current_info['social_profiles'];
        }

        // Méthodes de paiement
        if ( isset( $business_info['payment_methods'] ) && is_array( $business_info['payment_methods'] ) ) {
            $data['payment_methods'] = array();
            $methods = array( 'cash', 'credit_card', 'debit_card', 'bank_transfer', 'paypal', 'apple_pay', 'google_pay' );

            foreach ( $methods as $method ) {
                if ( isset( $business_info['payment_methods'][ $method ] ) ) {
                    $data['payment_methods'][ $method ] = (bool) $business_info['payment_methods'][ $method ];
                } else {
                    $data['payment_methods'][ $method ] = $current_info['payment_methods'][ $method ];
                }
            }
        } else {
            $data['payment_methods'] = $current_info['payment_methods'];
        }

        // Informations supplémentaires
        $data['founded'] = isset( $business_info['founded'] ) ? sanitize_text_field( $business_info['founded'] ) : $current_info['founded'];
        $data['founder'] = isset( $business_info['founder'] ) ? sanitize_text_field( $business_info['founder'] ) : $current_info['founder'];
        $data['employees'] = isset( $business_info['employees'] ) ? sanitize_text_field( $business_info['employees'] ) : $current_info['employees'];
        $data['tax_id'] = isset( $business_info['tax_id'] ) ? sanitize_text_field( $business_info['tax_id'] ) : $current_info['tax_id'];
        $data['vat_id'] = isset( $business_info['vat_id'] ) ? sanitize_text_field( $business_info['vat_id'] ) : $current_info['vat_id'];
        $data['price_range'] = isset( $business_info['price_range'] ) ? sanitize_text_field( $business_info['price_range'] ) : $current_info['price_range'];

        // Enregistrer les données
        update_option( $this->option_prefix . 'data', $data );

        return $data;
    }

    /**
     * Récupère les types d'entreprise.
     *
     * @since    1.2.0
     * @return   array    Les types d'entreprise.
     */
    private function get_business_types_data() {
        $types = array(
            'LocalBusiness'       => __( 'Entreprise locale', 'boss-seo' ),
            'Organization'        => __( 'Organisation', 'boss-seo' ),
            'Store'               => __( 'Magasin', 'boss-seo' ),
            'Restaurant'          => __( 'Restaurant', 'boss-seo' ),
            'FoodEstablishment'   => __( 'Établissement de restauration', 'boss-seo' ),
            'Bakery'              => __( 'Boulangerie', 'boss-seo' ),
            'BarOrPub'            => __( 'Bar ou pub', 'boss-seo' ),
            'Brewery'             => __( 'Brasserie', 'boss-seo' ),
            'CafeOrCoffeeShop'    => __( 'Café', 'boss-seo' ),
            'FastFoodRestaurant'  => __( 'Restaurant rapide', 'boss-seo' ),
            'IceCreamShop'        => __( 'Glacier', 'boss-seo' ),
            'Winery'              => __( 'Cave à vin', 'boss-seo' ),
            'Hotel'               => __( 'Hôtel', 'boss-seo' ),
            'BedAndBreakfast'     => __( 'Chambre d\'hôtes', 'boss-seo' ),
            'Hostel'              => __( 'Auberge', 'boss-seo' ),
            'Resort'              => __( 'Resort', 'boss-seo' ),
            'Campground'          => __( 'Camping', 'boss-seo' ),
            'LodgingBusiness'     => __( 'Hébergement', 'boss-seo' ),
            'HealthAndBeautyBusiness' => __( 'Santé et beauté', 'boss-seo' ),
            'BeautySalon'         => __( 'Salon de beauté', 'boss-seo' ),
            'DaySpa'              => __( 'Spa', 'boss-seo' ),
            'HairSalon'           => __( 'Salon de coiffure', 'boss-seo' ),
            'HealthClub'          => __( 'Club de santé', 'boss-seo' ),
            'NailSalon'           => __( 'Salon de manucure', 'boss-seo' ),
            'TattooParlor'        => __( 'Salon de tatouage', 'boss-seo' ),
            'EntertainmentBusiness' => __( 'Divertissement', 'boss-seo' ),
            'AmusementPark'       => __( 'Parc d\'attractions', 'boss-seo' ),
            'ArtGallery'          => __( 'Galerie d\'art', 'boss-seo' ),
            'Casino'              => __( 'Casino', 'boss-seo' ),
            'ComedyClub'          => __( 'Club de comédie', 'boss-seo' ),
            'MovieTheater'        => __( 'Cinéma', 'boss-seo' ),
            'NightClub'           => __( 'Boîte de nuit', 'boss-seo' ),
            'SportsActivityLocation' => __( 'Lieu d\'activité sportive', 'boss-seo' ),
            'BowlingAlley'        => __( 'Bowling', 'boss-seo' ),
            'ExerciseGym'         => __( 'Salle de sport', 'boss-seo' ),
            'GolfCourse'          => __( 'Terrain de golf', 'boss-seo' ),
            'PublicSwimmingPool'  => __( 'Piscine publique', 'boss-seo' ),
            'SkiResort'           => __( 'Station de ski', 'boss-seo' ),
            'SportsClub'          => __( 'Club de sport', 'boss-seo' ),
            'StadiumOrArena'      => __( 'Stade ou arène', 'boss-seo' ),
            'TennisComplex'       => __( 'Complexe de tennis', 'boss-seo' ),
            'ProfessionalService' => __( 'Service professionnel', 'boss-seo' ),
            'AccountingService'   => __( 'Service comptable', 'boss-seo' ),
            'Attorney'            => __( 'Avocat', 'boss-seo' ),
            'Dentist'             => __( 'Dentiste', 'boss-seo' ),
            'Physician'           => __( 'Médecin', 'boss-seo' ),
            'EmergencyService'    => __( 'Service d\'urgence', 'boss-seo' ),
            'EmploymentAgency'    => __( 'Agence d\'emploi', 'boss-seo' ),
            'FinancialService'    => __( 'Service financier', 'boss-seo' ),
            'InsuranceAgency'     => __( 'Agence d\'assurance', 'boss-seo' ),
            'LegalService'        => __( 'Service juridique', 'boss-seo' ),
            'Notary'              => __( 'Notaire', 'boss-seo' ),
            'RealEstateAgent'     => __( 'Agent immobilier', 'boss-seo' ),
            'TravelAgency'        => __( 'Agence de voyage', 'boss-seo' ),
            'HomeAndConstructionBusiness' => __( 'Maison et construction', 'boss-seo' ),
            'Electrician'         => __( 'Électricien', 'boss-seo' ),
            'GeneralContractor'   => __( 'Entrepreneur général', 'boss-seo' ),
            'HVACBusiness'        => __( 'Entreprise CVC', 'boss-seo' ),
            'HousePainter'        => __( 'Peintre en bâtiment', 'boss-seo' ),
            'Locksmith'           => __( 'Serrurier', 'boss-seo' ),
            'MovingCompany'       => __( 'Entreprise de déménagement', 'boss-seo' ),
            'Plumber'             => __( 'Plombier', 'boss-seo' ),
            'RoofingContractor'   => __( 'Couvreur', 'boss-seo' ),
            'ShoppingCenter'      => __( 'Centre commercial', 'boss-seo' ),
            'AutoPartsStore'      => __( 'Magasin de pièces auto', 'boss-seo' ),
            'BikeStore'           => __( 'Magasin de vélos', 'boss-seo' ),
            'BookStore'           => __( 'Librairie', 'boss-seo' ),
            'ClothingStore'       => __( 'Magasin de vêtements', 'boss-seo' ),
            'ComputerStore'       => __( 'Magasin d\'informatique', 'boss-seo' ),
            'ConvenienceStore'    => __( 'Épicerie', 'boss-seo' ),
            'DepartmentStore'     => __( 'Grand magasin', 'boss-seo' ),
            'ElectronicsStore'    => __( 'Magasin d\'électronique', 'boss-seo' ),
            'Florist'             => __( 'Fleuriste', 'boss-seo' ),
            'FurnitureStore'      => __( 'Magasin de meubles', 'boss-seo' ),
            'GardenStore'         => __( 'Jardinerie', 'boss-seo' ),
            'GroceryStore'        => __( 'Épicerie', 'boss-seo' ),
            'HardwareStore'       => __( 'Quincaillerie', 'boss-seo' ),
            'HobbyShop'           => __( 'Magasin de loisirs', 'boss-seo' ),
            'HomeGoodsStore'      => __( 'Magasin d\'articles ménagers', 'boss-seo' ),
            'JewelryStore'        => __( 'Bijouterie', 'boss-seo' ),
            'LiquorStore'         => __( 'Magasin d\'alcool', 'boss-seo' ),
            'MensClothingStore'   => __( 'Magasin de vêtements pour hommes', 'boss-seo' ),
            'MobilePhoneStore'    => __( 'Magasin de téléphonie mobile', 'boss-seo' ),
            'MovieRentalStore'    => __( 'Magasin de location de films', 'boss-seo' ),
            'MusicStore'          => __( 'Magasin de musique', 'boss-seo' ),
            'OfficeEquipmentStore' => __( 'Magasin d\'équipement de bureau', 'boss-seo' ),
            'OutletStore'         => __( 'Magasin d\'usine', 'boss-seo' ),
            'PawnShop'            => __( 'Prêteur sur gages', 'boss-seo' ),
            'PetStore'            => __( 'Animalerie', 'boss-seo' ),
            'ShoeStore'           => __( 'Magasin de chaussures', 'boss-seo' ),
            'SportingGoodsStore'  => __( 'Magasin d\'articles de sport', 'boss-seo' ),
            'TireShop'            => __( 'Magasin de pneus', 'boss-seo' ),
            'ToyStore'            => __( 'Magasin de jouets', 'boss-seo' ),
            'WholesaleStore'      => __( 'Magasin de gros', 'boss-seo' ),
            'AutomotiveBusiness'  => __( 'Entreprise automobile', 'boss-seo' ),
            'AutoBodyShop'        => __( 'Carrosserie', 'boss-seo' ),
            'AutoDealer'          => __( 'Concessionnaire automobile', 'boss-seo' ),
            'AutoRental'          => __( 'Location de voitures', 'boss-seo' ),
            'AutoRepair'          => __( 'Réparation automobile', 'boss-seo' ),
            'AutoWash'            => __( 'Lavage auto', 'boss-seo' ),
            'GasStation'          => __( 'Station-service', 'boss-seo' ),
            'MotorcycleDealer'    => __( 'Concessionnaire moto', 'boss-seo' ),
            'MotorcycleRepair'    => __( 'Réparation moto', 'boss-seo' ),
            'ChildCare'           => __( 'Garde d\'enfants', 'boss-seo' ),
            'DryCleaningOrLaundry' => __( 'Pressing ou laverie', 'boss-seo' ),
            'InternetCafe'        => __( 'Cybercafé', 'boss-seo' ),
            'Library'             => __( 'Bibliothèque', 'boss-seo' ),
            'PostOffice'          => __( 'Bureau de poste', 'boss-seo' ),
            'RadioStation'        => __( 'Station de radio', 'boss-seo' ),
            'TelevisionStation'   => __( 'Station de télévision', 'boss-seo' ),
            'SelfStorage'         => __( 'Self-stockage', 'boss-seo' ),
            'MedicalBusiness'     => __( 'Entreprise médicale', 'boss-seo' ),
            'MedicalClinic'       => __( 'Clinique médicale', 'boss-seo' ),
            'Optician'            => __( 'Opticien', 'boss-seo' ),
            'Pharmacy'            => __( 'Pharmacie', 'boss-seo' ),
            'VeterinaryCare'      => __( 'Soins vétérinaires', 'boss-seo' ),
        );

        return $types;
    }

    /**
     * Récupère les pays.
     *
     * @since    1.2.0
     * @return   array    Les pays.
     */
    private function get_business_countries_data() {
        $countries = array(
            'AF' => __( 'Afghanistan', 'boss-seo' ),
            'ZA' => __( 'Afrique du Sud', 'boss-seo' ),
            'AL' => __( 'Albanie', 'boss-seo' ),
            'DZ' => __( 'Algérie', 'boss-seo' ),
            'DE' => __( 'Allemagne', 'boss-seo' ),
            'AD' => __( 'Andorre', 'boss-seo' ),
            'AO' => __( 'Angola', 'boss-seo' ),
            'AI' => __( 'Anguilla', 'boss-seo' ),
            'AQ' => __( 'Antarctique', 'boss-seo' ),
            'AG' => __( 'Antigua-et-Barbuda', 'boss-seo' ),
            'SA' => __( 'Arabie saoudite', 'boss-seo' ),
            'AR' => __( 'Argentine', 'boss-seo' ),
            'AM' => __( 'Arménie', 'boss-seo' ),
            'AW' => __( 'Aruba', 'boss-seo' ),
            'AU' => __( 'Australie', 'boss-seo' ),
            'AT' => __( 'Autriche', 'boss-seo' ),
            'AZ' => __( 'Azerbaïdjan', 'boss-seo' ),
            'BS' => __( 'Bahamas', 'boss-seo' ),
            'BH' => __( 'Bahreïn', 'boss-seo' ),
            'BD' => __( 'Bangladesh', 'boss-seo' ),
            'BB' => __( 'Barbade', 'boss-seo' ),
            'BE' => __( 'Belgique', 'boss-seo' ),
            'BZ' => __( 'Belize', 'boss-seo' ),
            'BJ' => __( 'Bénin', 'boss-seo' ),
            'BM' => __( 'Bermudes', 'boss-seo' ),
            'BT' => __( 'Bhoutan', 'boss-seo' ),
            'BY' => __( 'Biélorussie', 'boss-seo' ),
            'BO' => __( 'Bolivie', 'boss-seo' ),
            'BA' => __( 'Bosnie-Herzégovine', 'boss-seo' ),
            'BW' => __( 'Botswana', 'boss-seo' ),
            'BR' => __( 'Brésil', 'boss-seo' ),
            'BN' => __( 'Brunéi Darussalam', 'boss-seo' ),
            'BG' => __( 'Bulgarie', 'boss-seo' ),
            'BF' => __( 'Burkina Faso', 'boss-seo' ),
            'BI' => __( 'Burundi', 'boss-seo' ),
            'KH' => __( 'Cambodge', 'boss-seo' ),
            'CM' => __( 'Cameroun', 'boss-seo' ),
            'CA' => __( 'Canada', 'boss-seo' ),
            'CV' => __( 'Cap-Vert', 'boss-seo' ),
            'CL' => __( 'Chili', 'boss-seo' ),
            'CN' => __( 'Chine', 'boss-seo' ),
            'CY' => __( 'Chypre', 'boss-seo' ),
            'CO' => __( 'Colombie', 'boss-seo' ),
            'KM' => __( 'Comores', 'boss-seo' ),
            'CG' => __( 'Congo-Brazzaville', 'boss-seo' ),
            'CD' => __( 'Congo-Kinshasa', 'boss-seo' ),
            'KP' => __( 'Corée du Nord', 'boss-seo' ),
            'KR' => __( 'Corée du Sud', 'boss-seo' ),
            'CR' => __( 'Costa Rica', 'boss-seo' ),
            'CI' => __( 'Côte d\'Ivoire', 'boss-seo' ),
            'HR' => __( 'Croatie', 'boss-seo' ),
            'CU' => __( 'Cuba', 'boss-seo' ),
            'CW' => __( 'Curaçao', 'boss-seo' ),
            'DK' => __( 'Danemark', 'boss-seo' ),
            'DJ' => __( 'Djibouti', 'boss-seo' ),
            'DM' => __( 'Dominique', 'boss-seo' ),
            'EG' => __( 'Égypte', 'boss-seo' ),
            'AE' => __( 'Émirats arabes unis', 'boss-seo' ),
            'EC' => __( 'Équateur', 'boss-seo' ),
            'ER' => __( 'Érythrée', 'boss-seo' ),
            'ES' => __( 'Espagne', 'boss-seo' ),
            'EE' => __( 'Estonie', 'boss-seo' ),
            'US' => __( 'États-Unis', 'boss-seo' ),
            'ET' => __( 'Éthiopie', 'boss-seo' ),
            'FJ' => __( 'Fidji', 'boss-seo' ),
            'FI' => __( 'Finlande', 'boss-seo' ),
            'FR' => __( 'France', 'boss-seo' ),
            'GA' => __( 'Gabon', 'boss-seo' ),
            'GM' => __( 'Gambie', 'boss-seo' ),
            'GE' => __( 'Géorgie', 'boss-seo' ),
            'GS' => __( 'Géorgie du Sud et les îles Sandwich du Sud', 'boss-seo' ),
            'GH' => __( 'Ghana', 'boss-seo' ),
            'GI' => __( 'Gibraltar', 'boss-seo' ),
            'GR' => __( 'Grèce', 'boss-seo' ),
            'GD' => __( 'Grenade', 'boss-seo' ),
            'GL' => __( 'Groenland', 'boss-seo' ),
            'GP' => __( 'Guadeloupe', 'boss-seo' ),
            'GU' => __( 'Guam', 'boss-seo' ),
            'GT' => __( 'Guatemala', 'boss-seo' ),
            'GG' => __( 'Guernesey', 'boss-seo' ),
            'GN' => __( 'Guinée', 'boss-seo' ),
            'GQ' => __( 'Guinée équatoriale', 'boss-seo' ),
            'GW' => __( 'Guinée-Bissau', 'boss-seo' ),
            'GY' => __( 'Guyana', 'boss-seo' ),
            'GF' => __( 'Guyane française', 'boss-seo' ),
            'HT' => __( 'Haïti', 'boss-seo' ),
            'HN' => __( 'Honduras', 'boss-seo' ),
            'HK' => __( 'Hong Kong', 'boss-seo' ),
            'HU' => __( 'Hongrie', 'boss-seo' ),
            'BV' => __( 'Île Bouvet', 'boss-seo' ),
            'CX' => __( 'Île Christmas', 'boss-seo' ),
            'IM' => __( 'Île de Man', 'boss-seo' ),
            'NF' => __( 'Île Norfolk', 'boss-seo' ),
            'AX' => __( 'Îles Åland', 'boss-seo' ),
            'KY' => __( 'Îles Caïmans', 'boss-seo' ),
            'CC' => __( 'Îles Cocos', 'boss-seo' ),
            'CK' => __( 'Îles Cook', 'boss-seo' ),
            'FK' => __( 'Îles Falkland', 'boss-seo' ),
            'FO' => __( 'Îles Féroé', 'boss-seo' ),
            'HM' => __( 'Îles Heard et MacDonald', 'boss-seo' ),
            'MP' => __( 'Îles Mariannes du Nord', 'boss-seo' ),
            'MH' => __( 'Îles Marshall', 'boss-seo' ),
            'UM' => __( 'Îles mineures éloignées des États-Unis', 'boss-seo' ),
            'PN' => __( 'Îles Pitcairn', 'boss-seo' ),
            'SB' => __( 'Îles Salomon', 'boss-seo' ),
            'TC' => __( 'Îles Turks et Caïques', 'boss-seo' ),
            'VG' => __( 'Îles Vierges britanniques', 'boss-seo' ),
            'VI' => __( 'Îles Vierges des États-Unis', 'boss-seo' ),
            'IN' => __( 'Inde', 'boss-seo' ),
            'ID' => __( 'Indonésie', 'boss-seo' ),
            'IQ' => __( 'Irak', 'boss-seo' ),
            'IR' => __( 'Iran', 'boss-seo' ),
            'IE' => __( 'Irlande', 'boss-seo' ),
            'IS' => __( 'Islande', 'boss-seo' ),
            'IL' => __( 'Israël', 'boss-seo' ),
            'IT' => __( 'Italie', 'boss-seo' ),
            'JM' => __( 'Jamaïque', 'boss-seo' ),
            'JP' => __( 'Japon', 'boss-seo' ),
            'JE' => __( 'Jersey', 'boss-seo' ),
            'JO' => __( 'Jordanie', 'boss-seo' ),
            'KZ' => __( 'Kazakhstan', 'boss-seo' ),
            'KE' => __( 'Kenya', 'boss-seo' ),
            'KG' => __( 'Kirghizistan', 'boss-seo' ),
            'KI' => __( 'Kiribati', 'boss-seo' ),
            'KW' => __( 'Koweït', 'boss-seo' ),
            'LA' => __( 'Laos', 'boss-seo' ),
            'LS' => __( 'Lesotho', 'boss-seo' ),
            'LV' => __( 'Lettonie', 'boss-seo' ),
            'LB' => __( 'Liban', 'boss-seo' ),
            'LR' => __( 'Libéria', 'boss-seo' ),
            'LY' => __( 'Libye', 'boss-seo' ),
            'LI' => __( 'Liechtenstein', 'boss-seo' ),
            'LT' => __( 'Lituanie', 'boss-seo' ),
            'LU' => __( 'Luxembourg', 'boss-seo' ),
            'MO' => __( 'Macao', 'boss-seo' ),
            'MK' => __( 'Macédoine du Nord', 'boss-seo' ),
            'MG' => __( 'Madagascar', 'boss-seo' ),
            'MY' => __( 'Malaisie', 'boss-seo' ),
            'MW' => __( 'Malawi', 'boss-seo' ),
            'MV' => __( 'Maldives', 'boss-seo' ),
            'ML' => __( 'Mali', 'boss-seo' ),
            'MT' => __( 'Malte', 'boss-seo' ),
            'MA' => __( 'Maroc', 'boss-seo' ),
            'MQ' => __( 'Martinique', 'boss-seo' ),
            'MU' => __( 'Maurice', 'boss-seo' ),
            'MR' => __( 'Mauritanie', 'boss-seo' ),
            'YT' => __( 'Mayotte', 'boss-seo' ),
            'MX' => __( 'Mexique', 'boss-seo' ),
            'FM' => __( 'Micronésie', 'boss-seo' ),
            'MD' => __( 'Moldavie', 'boss-seo' ),
            'MC' => __( 'Monaco', 'boss-seo' ),
            'MN' => __( 'Mongolie', 'boss-seo' ),
            'ME' => __( 'Monténégro', 'boss-seo' ),
            'MS' => __( 'Montserrat', 'boss-seo' ),
            'MZ' => __( 'Mozambique', 'boss-seo' ),
            'MM' => __( 'Myanmar', 'boss-seo' ),
            'NA' => __( 'Namibie', 'boss-seo' ),
            'NR' => __( 'Nauru', 'boss-seo' ),
            'NP' => __( 'Népal', 'boss-seo' ),
            'NI' => __( 'Nicaragua', 'boss-seo' ),
            'NE' => __( 'Niger', 'boss-seo' ),
            'NG' => __( 'Nigéria', 'boss-seo' ),
            'NU' => __( 'Niue', 'boss-seo' ),
            'NO' => __( 'Norvège', 'boss-seo' ),
            'NC' => __( 'Nouvelle-Calédonie', 'boss-seo' ),
            'NZ' => __( 'Nouvelle-Zélande', 'boss-seo' ),
            'OM' => __( 'Oman', 'boss-seo' ),
            'UG' => __( 'Ouganda', 'boss-seo' ),
            'UZ' => __( 'Ouzbékistan', 'boss-seo' ),
            'PK' => __( 'Pakistan', 'boss-seo' ),
            'PW' => __( 'Palaos', 'boss-seo' ),
            'PA' => __( 'Panama', 'boss-seo' ),
            'PG' => __( 'Papouasie-Nouvelle-Guinée', 'boss-seo' ),
            'PY' => __( 'Paraguay', 'boss-seo' ),
            'NL' => __( 'Pays-Bas', 'boss-seo' ),
            'PE' => __( 'Pérou', 'boss-seo' ),
            'PH' => __( 'Philippines', 'boss-seo' ),
            'PL' => __( 'Pologne', 'boss-seo' ),
            'PF' => __( 'Polynésie française', 'boss-seo' ),
            'PR' => __( 'Porto Rico', 'boss-seo' ),
            'PT' => __( 'Portugal', 'boss-seo' ),
            'QA' => __( 'Qatar', 'boss-seo' ),
            'RE' => __( 'La Réunion', 'boss-seo' ),
            'RO' => __( 'Roumanie', 'boss-seo' ),
            'GB' => __( 'Royaume-Uni', 'boss-seo' ),
            'RU' => __( 'Russie', 'boss-seo' ),
            'RW' => __( 'Rwanda', 'boss-seo' ),
            'EH' => __( 'Sahara occidental', 'boss-seo' ),
            'BL' => __( 'Saint-Barthélemy', 'boss-seo' ),
            'KN' => __( 'Saint-Christophe-et-Niévès', 'boss-seo' ),
            'SM' => __( 'Saint-Marin', 'boss-seo' ),
            'MF' => __( 'Saint-Martin', 'boss-seo' ),
            'SX' => __( 'Saint-Martin (partie néerlandaise)', 'boss-seo' ),
            'PM' => __( 'Saint-Pierre-et-Miquelon', 'boss-seo' ),
            'VA' => __( 'Saint-Siège (État de la Cité du Vatican)', 'boss-seo' ),
            'VC' => __( 'Saint-Vincent-et-les Grenadines', 'boss-seo' ),
            'SH' => __( 'Sainte-Hélène', 'boss-seo' ),
            'LC' => __( 'Sainte-Lucie', 'boss-seo' ),
            'SV' => __( 'Salvador', 'boss-seo' ),
            'WS' => __( 'Samoa', 'boss-seo' ),
            'AS' => __( 'Samoa américaines', 'boss-seo' ),
            'ST' => __( 'Sao Tomé-et-Principe', 'boss-seo' ),
            'SN' => __( 'Sénégal', 'boss-seo' ),
            'RS' => __( 'Serbie', 'boss-seo' ),
            'SC' => __( 'Seychelles', 'boss-seo' ),
            'SL' => __( 'Sierra Leone', 'boss-seo' ),
            'SG' => __( 'Singapour', 'boss-seo' ),
            'SK' => __( 'Slovaquie', 'boss-seo' ),
            'SI' => __( 'Slovénie', 'boss-seo' ),
            'SO' => __( 'Somalie', 'boss-seo' ),
            'SD' => __( 'Soudan', 'boss-seo' ),
            'SS' => __( 'Soudan du Sud', 'boss-seo' ),
            'LK' => __( 'Sri Lanka', 'boss-seo' ),
            'SE' => __( 'Suède', 'boss-seo' ),
            'CH' => __( 'Suisse', 'boss-seo' ),
            'SR' => __( 'Suriname', 'boss-seo' ),
            'SJ' => __( 'Svalbard et Jan Mayen', 'boss-seo' ),
            'SZ' => __( 'Eswatini', 'boss-seo' ),
            'SY' => __( 'Syrie', 'boss-seo' ),
            'TJ' => __( 'Tadjikistan', 'boss-seo' ),
            'TW' => __( 'Taïwan', 'boss-seo' ),
            'TZ' => __( 'Tanzanie', 'boss-seo' ),
            'TD' => __( 'Tchad', 'boss-seo' ),
            'CZ' => __( 'Tchéquie', 'boss-seo' ),
            'TF' => __( 'Terres australes et antarctiques françaises', 'boss-seo' ),
            'IO' => __( 'Territoire britannique de l\'océan Indien', 'boss-seo' ),
            'PS' => __( 'Territoires palestiniens', 'boss-seo' ),
            'TH' => __( 'Thaïlande', 'boss-seo' ),
            'TL' => __( 'Timor oriental', 'boss-seo' ),
            'TG' => __( 'Togo', 'boss-seo' ),
            'TK' => __( 'Tokelau', 'boss-seo' ),
            'TO' => __( 'Tonga', 'boss-seo' ),
            'TT' => __( 'Trinité-et-Tobago', 'boss-seo' ),
            'TN' => __( 'Tunisie', 'boss-seo' ),
            'TM' => __( 'Turkménistan', 'boss-seo' ),
            'TR' => __( 'Turquie', 'boss-seo' ),
            'TV' => __( 'Tuvalu', 'boss-seo' ),
            'UA' => __( 'Ukraine', 'boss-seo' ),
            'UY' => __( 'Uruguay', 'boss-seo' ),
            'VU' => __( 'Vanuatu', 'boss-seo' ),
            'VE' => __( 'Venezuela', 'boss-seo' ),
            'VN' => __( 'Viêt Nam', 'boss-seo' ),
            'WF' => __( 'Wallis-et-Futuna', 'boss-seo' ),
            'YE' => __( 'Yémen', 'boss-seo' ),
            'ZM' => __( 'Zambie', 'boss-seo' ),
            'ZW' => __( 'Zimbabwe', 'boss-seo' ),
        );

        return $countries;
    }

    /**
     * Récupère les devises.
     *
     * @since    1.2.0
     * @return   array    Les devises.
     */
    private function get_business_currencies_data() {
        $currencies = array(
            'AED' => __( 'Dirham des Émirats arabes unis', 'boss-seo' ),
            'AFN' => __( 'Afghani afghan', 'boss-seo' ),
            'ALL' => __( 'Lek albanais', 'boss-seo' ),
            'AMD' => __( 'Dram arménien', 'boss-seo' ),
            'ANG' => __( 'Florin antillais', 'boss-seo' ),
            'AOA' => __( 'Kwanza angolais', 'boss-seo' ),
            'ARS' => __( 'Peso argentin', 'boss-seo' ),
            'AUD' => __( 'Dollar australien', 'boss-seo' ),
            'AWG' => __( 'Florin arubais', 'boss-seo' ),
            'AZN' => __( 'Manat azerbaïdjanais', 'boss-seo' ),
            'BAM' => __( 'Mark convertible', 'boss-seo' ),
            'BBD' => __( 'Dollar barbadien', 'boss-seo' ),
            'BDT' => __( 'Taka bangladeshi', 'boss-seo' ),
            'BGN' => __( 'Lev bulgare', 'boss-seo' ),
            'BHD' => __( 'Dinar bahreïni', 'boss-seo' ),
            'BIF' => __( 'Franc burundais', 'boss-seo' ),
            'BMD' => __( 'Dollar bermudien', 'boss-seo' ),
            'BND' => __( 'Dollar brunéien', 'boss-seo' ),
            'BOB' => __( 'Boliviano bolivien', 'boss-seo' ),
            'BRL' => __( 'Real brésilien', 'boss-seo' ),
            'BSD' => __( 'Dollar bahaméen', 'boss-seo' ),
            'BTN' => __( 'Ngultrum bhoutanais', 'boss-seo' ),
            'BWP' => __( 'Pula botswanais', 'boss-seo' ),
            'BYN' => __( 'Rouble biélorusse', 'boss-seo' ),
            'BZD' => __( 'Dollar bélizien', 'boss-seo' ),
            'CAD' => __( 'Dollar canadien', 'boss-seo' ),
            'CDF' => __( 'Franc congolais', 'boss-seo' ),
            'CHF' => __( 'Franc suisse', 'boss-seo' ),
            'CLP' => __( 'Peso chilien', 'boss-seo' ),
            'CNY' => __( 'Yuan renminbi chinois', 'boss-seo' ),
            'COP' => __( 'Peso colombien', 'boss-seo' ),
            'CRC' => __( 'Colón costaricain', 'boss-seo' ),
            'CUC' => __( 'Peso cubain convertible', 'boss-seo' ),
            'CUP' => __( 'Peso cubain', 'boss-seo' ),
            'CVE' => __( 'Escudo cap-verdien', 'boss-seo' ),
            'CZK' => __( 'Couronne tchèque', 'boss-seo' ),
            'DJF' => __( 'Franc djiboutien', 'boss-seo' ),
            'DKK' => __( 'Couronne danoise', 'boss-seo' ),
            'DOP' => __( 'Peso dominicain', 'boss-seo' ),
            'DZD' => __( 'Dinar algérien', 'boss-seo' ),
            'EGP' => __( 'Livre égyptienne', 'boss-seo' ),
            'ERN' => __( 'Nakfa érythréen', 'boss-seo' ),
            'ETB' => __( 'Birr éthiopien', 'boss-seo' ),
            'EUR' => __( 'Euro', 'boss-seo' ),
            'FJD' => __( 'Dollar fidjien', 'boss-seo' ),
            'FKP' => __( 'Livre des îles Falkland', 'boss-seo' ),
            'GBP' => __( 'Livre sterling', 'boss-seo' ),
            'GEL' => __( 'Lari géorgien', 'boss-seo' ),
            'GHS' => __( 'Cedi ghanéen', 'boss-seo' ),
            'GIP' => __( 'Livre de Gibraltar', 'boss-seo' ),
            'GMD' => __( 'Dalasi gambien', 'boss-seo' ),
            'GNF' => __( 'Franc guinéen', 'boss-seo' ),
            'GTQ' => __( 'Quetzal guatémaltèque', 'boss-seo' ),
            'GYD' => __( 'Dollar guyanien', 'boss-seo' ),
            'HKD' => __( 'Dollar de Hong Kong', 'boss-seo' ),
            'HNL' => __( 'Lempira hondurien', 'boss-seo' ),
            'HRK' => __( 'Kuna croate', 'boss-seo' ),
            'HTG' => __( 'Gourde haïtienne', 'boss-seo' ),
            'HUF' => __( 'Forint hongrois', 'boss-seo' ),
            'IDR' => __( 'Roupie indonésienne', 'boss-seo' ),
            'ILS' => __( 'Shekel israélien', 'boss-seo' ),
            'INR' => __( 'Roupie indienne', 'boss-seo' ),
            'IQD' => __( 'Dinar iraquien', 'boss-seo' ),
            'IRR' => __( 'Rial iranien', 'boss-seo' ),
            'ISK' => __( 'Couronne islandaise', 'boss-seo' ),
            'JMD' => __( 'Dollar jamaïcain', 'boss-seo' ),
            'JOD' => __( 'Dinar jordanien', 'boss-seo' ),
            'JPY' => __( 'Yen japonais', 'boss-seo' ),
            'KES' => __( 'Shilling kényan', 'boss-seo' ),
            'KGS' => __( 'Som kirghize', 'boss-seo' ),
            'KHR' => __( 'Riel cambodgien', 'boss-seo' ),
            'KMF' => __( 'Franc comorien', 'boss-seo' ),
            'KPW' => __( 'Won nord-coréen', 'boss-seo' ),
            'KRW' => __( 'Won sud-coréen', 'boss-seo' ),
            'KWD' => __( 'Dinar koweïtien', 'boss-seo' ),
            'KYD' => __( 'Dollar des îles Caïmans', 'boss-seo' ),
            'KZT' => __( 'Tenge kazakh', 'boss-seo' ),
            'LAK' => __( 'Kip laotien', 'boss-seo' ),
            'LBP' => __( 'Livre libanaise', 'boss-seo' ),
            'LKR' => __( 'Roupie srilankaise', 'boss-seo' ),
            'LRD' => __( 'Dollar libérien', 'boss-seo' ),
            'LSL' => __( 'Loti lesothan', 'boss-seo' ),
            'LYD' => __( 'Dinar libyen', 'boss-seo' ),
            'MAD' => __( 'Dirham marocain', 'boss-seo' ),
            'MDL' => __( 'Leu moldave', 'boss-seo' ),
            'MGA' => __( 'Ariary malgache', 'boss-seo' ),
            'MKD' => __( 'Denar macédonien', 'boss-seo' ),
            'MMK' => __( 'Kyat birman', 'boss-seo' ),
            'MNT' => __( 'Tugrik mongol', 'boss-seo' ),
            'MOP' => __( 'Pataca macanais', 'boss-seo' ),
            'MRU' => __( 'Ouguiya mauritanien', 'boss-seo' ),
            'MUR' => __( 'Roupie mauricienne', 'boss-seo' ),
            'MVR' => __( 'Rufiyaa maldivien', 'boss-seo' ),
            'MWK' => __( 'Kwacha malawite', 'boss-seo' ),
            'MXN' => __( 'Peso mexicain', 'boss-seo' ),
            'MYR' => __( 'Ringgit malaisien', 'boss-seo' ),
            'MZN' => __( 'Metical mozambicain', 'boss-seo' ),
            'NAD' => __( 'Dollar namibien', 'boss-seo' ),
            'NGN' => __( 'Naira nigérian', 'boss-seo' ),
            'NIO' => __( 'Córdoba oro nicaraguayen', 'boss-seo' ),
            'NOK' => __( 'Couronne norvégienne', 'boss-seo' ),
            'NPR' => __( 'Roupie népalaise', 'boss-seo' ),
            'NZD' => __( 'Dollar néo-zélandais', 'boss-seo' ),
            'OMR' => __( 'Rial omanais', 'boss-seo' ),
            'PAB' => __( 'Balboa panaméen', 'boss-seo' ),
            'PEN' => __( 'Sol péruvien', 'boss-seo' ),
            'PGK' => __( 'Kina papou-néo-guinéen', 'boss-seo' ),
            'PHP' => __( 'Peso philippin', 'boss-seo' ),
            'PKR' => __( 'Roupie pakistanaise', 'boss-seo' ),
            'PLN' => __( 'Zloty polonais', 'boss-seo' ),
            'PYG' => __( 'Guaraní paraguayen', 'boss-seo' ),
            'QAR' => __( 'Riyal qatari', 'boss-seo' ),
            'RON' => __( 'Leu roumain', 'boss-seo' ),
            'RSD' => __( 'Dinar serbe', 'boss-seo' ),
            'RUB' => __( 'Rouble russe', 'boss-seo' ),
            'RWF' => __( 'Franc rwandais', 'boss-seo' ),
            'SAR' => __( 'Riyal saoudien', 'boss-seo' ),
            'SBD' => __( 'Dollar des îles Salomon', 'boss-seo' ),
            'SCR' => __( 'Roupie seychelloise', 'boss-seo' ),
            'SDG' => __( 'Livre soudanaise', 'boss-seo' ),
            'SEK' => __( 'Couronne suédoise', 'boss-seo' ),
            'SGD' => __( 'Dollar de Singapour', 'boss-seo' ),
            'SHP' => __( 'Livre de Sainte-Hélène', 'boss-seo' ),
            'SLL' => __( 'Leone sierra-léonais', 'boss-seo' ),
            'SOS' => __( 'Shilling somalien', 'boss-seo' ),
            'SRD' => __( 'Dollar surinamais', 'boss-seo' ),
            'SSP' => __( 'Livre sud-soudanaise', 'boss-seo' ),
            'STN' => __( 'Dobra santoméen', 'boss-seo' ),
            'SVC' => __( 'Colón salvadorien', 'boss-seo' ),
            'SYP' => __( 'Livre syrienne', 'boss-seo' ),
            'SZL' => __( 'Lilangeni swazi', 'boss-seo' ),
            'THB' => __( 'Baht thaïlandais', 'boss-seo' ),
            'TJS' => __( 'Somoni tadjik', 'boss-seo' ),
            'TMT' => __( 'Manat turkmène', 'boss-seo' ),
            'TND' => __( 'Dinar tunisien', 'boss-seo' ),
            'TOP' => __( 'Paʻanga tongan', 'boss-seo' ),
            'TRY' => __( 'Livre turque', 'boss-seo' ),
            'TTD' => __( 'Dollar de Trinité-et-Tobago', 'boss-seo' ),
            'TWD' => __( 'Dollar taïwanais', 'boss-seo' ),
            'TZS' => __( 'Shilling tanzanien', 'boss-seo' ),
            'UAH' => __( 'Hryvnia ukrainienne', 'boss-seo' ),
            'UGX' => __( 'Shilling ougandais', 'boss-seo' ),
            'USD' => __( 'Dollar américain', 'boss-seo' ),
            'UYU' => __( 'Peso uruguayen', 'boss-seo' ),
            'UZS' => __( 'Sum ouzbek', 'boss-seo' ),
            'VES' => __( 'Bolívar vénézuélien', 'boss-seo' ),
            'VND' => __( 'Dong vietnamien', 'boss-seo' ),
            'VUV' => __( 'Vatu vanuatuan', 'boss-seo' ),
            'WST' => __( 'Tala samoan', 'boss-seo' ),
            'XAF' => __( 'Franc CFA (BEAC)', 'boss-seo' ),
            'XCD' => __( 'Dollar des Caraïbes orientales', 'boss-seo' ),
            'XOF' => __( 'Franc CFA (BCEAO)', 'boss-seo' ),
            'XPF' => __( 'Franc CFP', 'boss-seo' ),
            'YER' => __( 'Rial yéménite', 'boss-seo' ),
            'ZAR' => __( 'Rand sud-africain', 'boss-seo' ),
            'ZMW' => __( 'Kwacha zambien', 'boss-seo' ),
            'ZWL' => __( 'Dollar zimbabwéen', 'boss-seo' ),
        );

        return $currencies;
    }

    /**
     * Récupère les langues.
     *
     * @since    1.2.0
     * @return   array    Les langues.
     */
    private function get_business_languages_data() {
        $languages = array(
            'af' => __( 'Afrikaans', 'boss-seo' ),
            'sq' => __( 'Albanais', 'boss-seo' ),
            'am' => __( 'Amharique', 'boss-seo' ),
            'ar' => __( 'Arabe', 'boss-seo' ),
            'hy' => __( 'Arménien', 'boss-seo' ),
            'az' => __( 'Azerbaïdjanais', 'boss-seo' ),
            'eu' => __( 'Basque', 'boss-seo' ),
            'be' => __( 'Biélorusse', 'boss-seo' ),
            'bn' => __( 'Bengali', 'boss-seo' ),
            'bs' => __( 'Bosniaque', 'boss-seo' ),
            'bg' => __( 'Bulgare', 'boss-seo' ),
            'ca' => __( 'Catalan', 'boss-seo' ),
            'ceb' => __( 'Cebuano', 'boss-seo' ),
            'ny' => __( 'Chichewa', 'boss-seo' ),
            'zh-CN' => __( 'Chinois (simplifié)', 'boss-seo' ),
            'zh-TW' => __( 'Chinois (traditionnel)', 'boss-seo' ),
            'co' => __( 'Corse', 'boss-seo' ),
            'hr' => __( 'Croate', 'boss-seo' ),
            'cs' => __( 'Tchèque', 'boss-seo' ),
            'da' => __( 'Danois', 'boss-seo' ),
            'nl' => __( 'Néerlandais', 'boss-seo' ),
            'en' => __( 'Anglais', 'boss-seo' ),
            'eo' => __( 'Espéranto', 'boss-seo' ),
            'et' => __( 'Estonien', 'boss-seo' ),
            'tl' => __( 'Filipino', 'boss-seo' ),
            'fi' => __( 'Finnois', 'boss-seo' ),
            'fr' => __( 'Français', 'boss-seo' ),
            'fy' => __( 'Frison', 'boss-seo' ),
            'gl' => __( 'Galicien', 'boss-seo' ),
            'ka' => __( 'Géorgien', 'boss-seo' ),
            'de' => __( 'Allemand', 'boss-seo' ),
            'el' => __( 'Grec', 'boss-seo' ),
            'gu' => __( 'Gujarati', 'boss-seo' ),
            'ht' => __( 'Créole haïtien', 'boss-seo' ),
            'ha' => __( 'Haoussa', 'boss-seo' ),
            'haw' => __( 'Hawaïen', 'boss-seo' ),
            'iw' => __( 'Hébreu', 'boss-seo' ),
            'hi' => __( 'Hindi', 'boss-seo' ),
            'hmn' => __( 'Hmong', 'boss-seo' ),
            'hu' => __( 'Hongrois', 'boss-seo' ),
            'is' => __( 'Islandais', 'boss-seo' ),
            'ig' => __( 'Igbo', 'boss-seo' ),
            'id' => __( 'Indonésien', 'boss-seo' ),
            'ga' => __( 'Irlandais', 'boss-seo' ),
            'it' => __( 'Italien', 'boss-seo' ),
            'ja' => __( 'Japonais', 'boss-seo' ),
            'jw' => __( 'Javanais', 'boss-seo' ),
            'kn' => __( 'Kannada', 'boss-seo' ),
            'kk' => __( 'Kazakh', 'boss-seo' ),
            'km' => __( 'Khmer', 'boss-seo' ),
            'rw' => __( 'Kinyarwanda', 'boss-seo' ),
            'ko' => __( 'Coréen', 'boss-seo' ),
            'ku' => __( 'Kurde', 'boss-seo' ),
            'ky' => __( 'Kirghize', 'boss-seo' ),
            'lo' => __( 'Lao', 'boss-seo' ),
            'la' => __( 'Latin', 'boss-seo' ),
            'lv' => __( 'Letton', 'boss-seo' ),
            'lt' => __( 'Lituanien', 'boss-seo' ),
            'lb' => __( 'Luxembourgeois', 'boss-seo' ),
            'mk' => __( 'Macédonien', 'boss-seo' ),
            'mg' => __( 'Malgache', 'boss-seo' ),
            'ms' => __( 'Malais', 'boss-seo' ),
            'ml' => __( 'Malayalam', 'boss-seo' ),
            'mt' => __( 'Maltais', 'boss-seo' ),
            'mi' => __( 'Maori', 'boss-seo' ),
            'mr' => __( 'Marathi', 'boss-seo' ),
            'mn' => __( 'Mongol', 'boss-seo' ),
            'my' => __( 'Birman', 'boss-seo' ),
            'ne' => __( 'Népalais', 'boss-seo' ),
            'no' => __( 'Norvégien', 'boss-seo' ),
            'or' => __( 'Odia', 'boss-seo' ),
            'ps' => __( 'Pachtô', 'boss-seo' ),
            'fa' => __( 'Persan', 'boss-seo' ),
            'pl' => __( 'Polonais', 'boss-seo' ),
            'pt' => __( 'Portugais', 'boss-seo' ),
            'pa' => __( 'Pendjabi', 'boss-seo' ),
            'ro' => __( 'Roumain', 'boss-seo' ),
            'ru' => __( 'Russe', 'boss-seo' ),
            'sm' => __( 'Samoan', 'boss-seo' ),
            'gd' => __( 'Gaélique écossais', 'boss-seo' ),
            'sr' => __( 'Serbe', 'boss-seo' ),
            'st' => __( 'Sesotho', 'boss-seo' ),
            'sn' => __( 'Shona', 'boss-seo' ),
            'sd' => __( 'Sindhi', 'boss-seo' ),
            'si' => __( 'Cingalais', 'boss-seo' ),
            'sk' => __( 'Slovaque', 'boss-seo' ),
            'sl' => __( 'Slovène', 'boss-seo' ),
            'so' => __( 'Somali', 'boss-seo' ),
            'es' => __( 'Espagnol', 'boss-seo' ),
            'su' => __( 'Soundanais', 'boss-seo' ),
            'sw' => __( 'Swahili', 'boss-seo' ),
            'sv' => __( 'Suédois', 'boss-seo' ),
            'tg' => __( 'Tadjik', 'boss-seo' ),
            'ta' => __( 'Tamoul', 'boss-seo' ),
            'tt' => __( 'Tatar', 'boss-seo' ),
            'te' => __( 'Télougou', 'boss-seo' ),
            'th' => __( 'Thaï', 'boss-seo' ),
            'tr' => __( 'Turc', 'boss-seo' ),
            'tk' => __( 'Turkmène', 'boss-seo' ),
            'uk' => __( 'Ukrainien', 'boss-seo' ),
            'ur' => __( 'Ourdou', 'boss-seo' ),
            'ug' => __( 'Ouïghour', 'boss-seo' ),
            'uz' => __( 'Ouzbek', 'boss-seo' ),
            'vi' => __( 'Vietnamien', 'boss-seo' ),
            'cy' => __( 'Gallois', 'boss-seo' ),
            'xh' => __( 'Xhosa', 'boss-seo' ),
            'yi' => __( 'Yiddish', 'boss-seo' ),
            'yo' => __( 'Yoruba', 'boss-seo' ),
            'zu' => __( 'Zoulou', 'boss-seo' ),
        );

        return $languages;
    }
}
