import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  CheckboxControl,
  SelectControl,
  TextControl,
  Dashicon,
  Modal,
  Spinner
} from '@wordpress/components';

const MediaList = ({ 
  media, 
  onOptimize, 
  onGenerateAlt, 
  onBulkAction,
  isProcessing
}) => {
  // États
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedItems, setSelectedItems] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  
  // Réinitialiser la page lorsque les filtres changent
  useEffect(() => {
    setCurrentPage(1);
  }, [filterType, filterStatus, searchQuery]);
  
  // Effet pour gérer la sélection de tous les éléments
  useEffect(() => {
    if (selectAll) {
      const ids = filteredMedia.map(item => item.id);
      setSelectedItems(ids);
    } else {
      setSelectedItems([]);
    }
  }, [selectAll]);
  
  // Fonction pour gérer la sélection d'un élément
  const handleItemSelection = (id) => {
    if (selectedItems.includes(id)) {
      setSelectedItems(selectedItems.filter(itemId => itemId !== id));
    } else {
      setSelectedItems([...selectedItems, id]);
    }
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColorClass = (status) => {
    switch (status) {
      case 'optimized':
        return 'boss-text-green-600';
      case 'unoptimized':
        return 'boss-text-yellow-600';
      case 'error':
        return 'boss-text-red-600';
      default:
        return 'boss-text-boss-gray';
    }
  };
  
  // Fonction pour obtenir le texte du statut
  const getStatusText = (status) => {
    switch (status) {
      case 'optimized':
        return __('Optimisé', 'boss-seo');
      case 'unoptimized':
        return __('Non optimisé', 'boss-seo');
      case 'error':
        return __('Erreur', 'boss-seo');
      default:
        return status;
    }
  };
  
  // Fonction pour obtenir l'icône du statut
  const getStatusIcon = (status) => {
    switch (status) {
      case 'optimized':
        return 'yes-alt';
      case 'unoptimized':
        return 'warning';
      case 'error':
        return 'dismiss';
      default:
        return 'info';
    }
  };
  
  // Fonction pour prévisualiser une image
  const handlePreview = (image) => {
    setPreviewImage(image);
    setShowImagePreview(true);
  };
  
  // Filtrer les médias
  const filteredMedia = media.filter(item => {
    // Filtrer par type
    const matchesType = filterType === 'all' || item.type === filterType;
    
    // Filtrer par statut
    const matchesStatus = filterStatus === 'all' || item.status === filterStatus;
    
    // Filtrer par recherche
    const matchesSearch = searchQuery === '' || 
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (item.alt && item.alt.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesType && matchesStatus && matchesSearch;
  });
  
  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredMedia.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredMedia.length / itemsPerPage);
  
  // Fonction pour changer de page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  
  // Fonction pour aller à la page précédente
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  // Fonction pour aller à la page suivante
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };
  
  // Fonction pour formater la taille du fichier
  const formatFileSize = (bytes) => {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(2) + ' KB';
    } else {
      return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    }
  };

  return (
    <>
      <Card className="boss-mb-6" id="media-list">
        <CardHeader className="boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Liste des médias', 'boss-seo')}
            </h2>
            <div className="boss-flex boss-space-x-2">
              <Button
                isPrimary
                onClick={() => onBulkAction('optimize')}
                disabled={selectedItems.length === 0 || isProcessing}
                isBusy={isProcessing}
              >
                {__('Optimiser la sélection', 'boss-seo')}
              </Button>
              <Button
                isSecondary
                onClick={() => onBulkAction('generate-alt')}
                disabled={selectedItems.length === 0 || isProcessing}
                isBusy={isProcessing}
              >
                {__('Générer Alt Text', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardBody className="boss-p-4">
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4 boss-mb-6">
            <TextControl
              placeholder={__('Rechercher des médias...', 'boss-seo')}
              value={searchQuery}
              onChange={setSearchQuery}
            />
            
            <SelectControl
              label=""
              value={filterType}
              options={[
                { label: __('Tous les types', 'boss-seo'), value: 'all' },
                { label: 'JPEG', value: 'jpeg' },
                { label: 'PNG', value: 'png' },
                { label: 'GIF', value: 'gif' },
                { label: 'WebP', value: 'webp' },
                { label: 'SVG', value: 'svg' }
              ]}
              onChange={setFilterType}
            />
            
            <SelectControl
              label=""
              value={filterStatus}
              options={[
                { label: __('Tous les statuts', 'boss-seo'), value: 'all' },
                { label: __('Optimisés', 'boss-seo'), value: 'optimized' },
                { label: __('Non optimisés', 'boss-seo'), value: 'unoptimized' },
                { label: __('Erreurs', 'boss-seo'), value: 'error' }
              ]}
              onChange={setFilterStatus}
            />
          </div>
          
          <div className="boss-overflow-x-auto">
            <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
              <thead className="boss-bg-gray-50">
                <tr>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-w-10">
                    <CheckboxControl
                      checked={selectAll}
                      onChange={setSelectAll}
                      label=""
                    />
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Image', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Nom', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Type', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Dimensions', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Taille', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Alt Text', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Statut', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-right boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Actions', 'boss-seo')}
                  </th>
                </tr>
              </thead>
              <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                {currentItems.length === 0 ? (
                  <tr>
                    <td colSpan="9" className="boss-px-6 boss-py-4 boss-text-center boss-text-boss-gray">
                      {__('Aucun média trouvé.', 'boss-seo')}
                    </td>
                  </tr>
                ) : (
                  currentItems.map(item => (
                    <tr key={item.id} className="boss-hover:boss-bg-gray-50">
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <CheckboxControl
                          checked={selectedItems.includes(item.id)}
                          onChange={() => handleItemSelection(item.id)}
                          label=""
                        />
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div 
                          className="boss-w-12 boss-h-12 boss-bg-gray-100 boss-rounded boss-flex boss-items-center boss-justify-center boss-cursor-pointer"
                          onClick={() => handlePreview(item)}
                        >
                          {item.thumbnail ? (
                            <img 
                              src={item.thumbnail} 
                              alt={item.alt || item.name} 
                              className="boss-max-w-full boss-max-h-full boss-rounded"
                            />
                          ) : (
                            <Dashicon icon="format-image" className="boss-text-gray-400" />
                          )}
                        </div>
                      </td>
                      <td className="boss-px-6 boss-py-4">
                        <div className="boss-font-medium boss-text-boss-dark">{item.name}</div>
                        <div className="boss-text-sm boss-text-boss-gray">{item.date}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray boss-uppercase">{item.type}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{item.dimensions.width} × {item.dimensions.height}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{formatFileSize(item.size)}</div>
                        {item.originalSize && (
                          <div className="boss-text-xs boss-text-green-600">
                            {__('Économisé:', 'boss-seo')} {formatFileSize(item.originalSize - item.size)}
                          </div>
                        )}
                      </td>
                      <td className="boss-px-6 boss-py-4">
                        <div className="boss-text-boss-gray boss-truncate boss-max-w-xs">
                          {item.alt || (
                            <span className="boss-text-yellow-600 boss-italic">
                              {__('Manquant', 'boss-seo')}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className={`boss-flex boss-items-center ${getStatusColorClass(item.status)}`}>
                          <Dashicon icon={getStatusIcon(item.status)} className="boss-mr-1" />
                          {getStatusText(item.status)}
                        </div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-right">
                        <div className="boss-flex boss-justify-end boss-space-x-2">
                          <Button
                            isSecondary
                            isSmall
                            onClick={() => handlePreview(item)}
                          >
                            <Dashicon icon="visibility" />
                          </Button>
                          <Button
                            isPrimary
                            isSmall
                            onClick={() => onOptimize(item.id)}
                            disabled={item.status === 'optimized' || isProcessing}
                          >
                            <Dashicon icon="image-crop" />
                          </Button>
                          <Button
                            isSecondary
                            isSmall
                            onClick={() => onGenerateAlt(item.id)}
                            disabled={isProcessing}
                          >
                            <Dashicon icon="edit" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardBody>
        {totalPages > 1 && (
          <CardFooter className="boss-border-t boss-border-gray-200">
            <div className="boss-flex boss-justify-between boss-items-center">
              <div className="boss-text-boss-gray boss-text-sm">
                {__('Affichage de', 'boss-seo')} {indexOfFirstItem + 1} {__('à', 'boss-seo')} {Math.min(indexOfLastItem, filteredMedia.length)} {__('sur', 'boss-seo')} {filteredMedia.length} {__('médias', 'boss-seo')}
              </div>
              <div className="boss-flex boss-space-x-2">
                <Button
                  isSecondary
                  isSmall
                  onClick={goToPreviousPage}
                  disabled={currentPage === 1}
                >
                  <Dashicon icon="arrow-left-alt2" />
                </Button>
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Afficher les pages autour de la page courante
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <Button
                      key={pageNum}
                      isSecondary
                      isSmall
                      isPrimary={currentPage === pageNum}
                      onClick={() => paginate(pageNum)}
                    >
                      {pageNum}
                    </Button>
                  );
                })}
                <Button
                  isSecondary
                  isSmall
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  <Dashicon icon="arrow-right-alt2" />
                </Button>
              </div>
            </div>
          </CardFooter>
        )}
      </Card>
      
      {/* Modal de prévisualisation d'image */}
      {showImagePreview && previewImage && (
        <Modal
          title={previewImage.name}
          onRequestClose={() => setShowImagePreview(false)}
          className="boss-image-preview-modal"
        >
          <div className="boss-p-6">
            <div className="boss-flex boss-justify-center boss-mb-6">
              <img 
                src={previewImage.url} 
                alt={previewImage.alt || previewImage.name} 
                className="boss-max-w-full boss-max-h-96 boss-rounded"
              />
            </div>
            
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6 boss-mb-6">
              <div>
                <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                  {__('Informations', 'boss-seo')}
                </h3>
                
                <div className="boss-space-y-2">
                  <div className="boss-flex boss-justify-between">
                    <span className="boss-text-sm boss-text-boss-gray">{__('Dimensions:', 'boss-seo')}</span>
                    <span className="boss-text-sm boss-font-medium boss-text-boss-dark">
                      {previewImage.dimensions.width} × {previewImage.dimensions.height}
                    </span>
                  </div>
                  
                  <div className="boss-flex boss-justify-between">
                    <span className="boss-text-sm boss-text-boss-gray">{__('Type:', 'boss-seo')}</span>
                    <span className="boss-text-sm boss-font-medium boss-text-boss-dark boss-uppercase">
                      {previewImage.type}
                    </span>
                  </div>
                  
                  <div className="boss-flex boss-justify-between">
                    <span className="boss-text-sm boss-text-boss-gray">{__('Taille:', 'boss-seo')}</span>
                    <span className="boss-text-sm boss-font-medium boss-text-boss-dark">
                      {formatFileSize(previewImage.size)}
                    </span>
                  </div>
                  
                  <div className="boss-flex boss-justify-between">
                    <span className="boss-text-sm boss-text-boss-gray">{__('Date:', 'boss-seo')}</span>
                    <span className="boss-text-sm boss-font-medium boss-text-boss-dark">
                      {previewImage.date}
                    </span>
                  </div>
                  
                  <div className="boss-flex boss-justify-between">
                    <span className="boss-text-sm boss-text-boss-gray">{__('Statut:', 'boss-seo')}</span>
                    <span className={`boss-text-sm boss-font-medium ${getStatusColorClass(previewImage.status)}`}>
                      {getStatusText(previewImage.status)}
                    </span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                  {__('Texte alternatif', 'boss-seo')}
                </h3>
                
                <div className="boss-bg-gray-50 boss-p-3 boss-rounded-lg boss-mb-4">
                  {previewImage.alt || (
                    <span className="boss-text-yellow-600 boss-italic">
                      {__('Aucun texte alternatif défini', 'boss-seo')}
                    </span>
                  )}
                </div>
                
                <div className="boss-flex boss-justify-end">
                  <Button
                    isSecondary
                    onClick={() => onGenerateAlt(previewImage.id)}
                    disabled={isProcessing}
                  >
                    {__('Générer Alt Text', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="boss-flex boss-justify-between">
              <Button
                isSecondary
                onClick={() => setShowImagePreview(false)}
              >
                {__('Fermer', 'boss-seo')}
              </Button>
              
              <Button
                isPrimary
                onClick={() => {
                  onOptimize(previewImage.id);
                  setShowImagePreview(false);
                }}
                disabled={previewImage.status === 'optimized' || isProcessing}
              >
                {__('Optimiser cette image', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default MediaList;
