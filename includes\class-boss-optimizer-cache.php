<?php
/**
 * Classe de gestion du cache pour le module Boss Optimizer.
 *
 * Cette classe gère la mise en cache des résultats d'analyse SEO et des recommandations
 * pour améliorer les performances du module.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe de gestion du cache pour le module Boss Optimizer.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_Cache {

    /**
     * Préfixe pour les clés de cache.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $cache_prefix    Préfixe pour les clés de cache.
     */
    private $cache_prefix = 'boss_optimizer_';

    /**
     * Durée de vie par défaut du cache en secondes.
     *
     * @since    1.1.0
     * @access   private
     * @var      int    $default_expiration    Durée de vie par défaut du cache.
     */
    private $default_expiration = 3600; // 1 heure

    /**
     * Instance unique de la classe (Singleton).
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_Optimizer_Cache    $instance    Instance unique de la classe.
     */
    private static $instance = null;

    /**
     * Constructeur privé pour empêcher l'instanciation directe.
     *
     * @since    1.1.0
     */
    private function __construct() {
        // Constructeur privé pour le pattern Singleton
    }

    /**
     * Récupère l'instance unique de la classe.
     *
     * @since    1.1.0
     * @return   Boss_Optimizer_Cache    Instance unique de la classe.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Génère une clé de cache unique.
     *
     * @since    1.1.0
     * @param    string    $key    Clé de base.
     * @param    array     $args   Arguments supplémentaires pour la clé.
     * @return   string            Clé de cache unique.
     */
    private function generate_cache_key( $key, $args = array() ) {
        $cache_key = $this->cache_prefix . $key;
        
        if ( ! empty( $args ) ) {
            $cache_key .= '_' . md5( serialize( $args ) );
        }
        
        return $cache_key;
    }

    /**
     * Met en cache une valeur.
     *
     * @since    1.1.0
     * @param    string    $key         Clé de cache.
     * @param    mixed     $value       Valeur à mettre en cache.
     * @param    int       $expiration  Durée de vie en secondes (optionnel).
     * @param    array     $args        Arguments supplémentaires pour la clé.
     * @return   bool                   True si la mise en cache a réussi, false sinon.
     */
    public function set( $key, $value, $expiration = null, $args = array() ) {
        if ( null === $expiration ) {
            $expiration = $this->default_expiration;
        }
        
        $cache_key = $this->generate_cache_key( $key, $args );
        
        return wp_cache_set( $cache_key, $value, 'boss_optimizer', $expiration );
    }

    /**
     * Récupère une valeur du cache.
     *
     * @since    1.1.0
     * @param    string    $key     Clé de cache.
     * @param    array     $args    Arguments supplémentaires pour la clé.
     * @return   mixed              Valeur du cache ou false si non trouvée.
     */
    public function get( $key, $args = array() ) {
        $cache_key = $this->generate_cache_key( $key, $args );
        
        return wp_cache_get( $cache_key, 'boss_optimizer' );
    }

    /**
     * Supprime une valeur du cache.
     *
     * @since    1.1.0
     * @param    string    $key     Clé de cache.
     * @param    array     $args    Arguments supplémentaires pour la clé.
     * @return   bool               True si la suppression a réussi, false sinon.
     */
    public function delete( $key, $args = array() ) {
        $cache_key = $this->generate_cache_key( $key, $args );
        
        return wp_cache_delete( $cache_key, 'boss_optimizer' );
    }

    /**
     * Vide tout le cache du module.
     *
     * @since    1.1.0
     * @return   bool    True si le vidage a réussi, false sinon.
     */
    public function flush() {
        return wp_cache_flush_group( 'boss_optimizer' );
    }

    /**
     * Met en cache le score SEO d'un contenu.
     *
     * @since    1.1.0
     * @param    int    $post_id    ID du contenu.
     * @param    int    $score      Score SEO.
     * @return   bool               True si la mise en cache a réussi.
     */
    public function set_seo_score( $post_id, $score ) {
        return $this->set( 'seo_score', $score, 7200, array( 'post_id' => $post_id ) ); // 2 heures
    }

    /**
     * Récupère le score SEO d'un contenu depuis le cache.
     *
     * @since    1.1.0
     * @param    int    $post_id    ID du contenu.
     * @return   mixed              Score SEO ou false si non trouvé.
     */
    public function get_seo_score( $post_id ) {
        return $this->get( 'seo_score', array( 'post_id' => $post_id ) );
    }

    /**
     * Met en cache les recommandations d'un contenu.
     *
     * @since    1.1.0
     * @param    int      $post_id           ID du contenu.
     * @param    array    $recommendations   Recommandations.
     * @return   bool                        True si la mise en cache a réussi.
     */
    public function set_recommendations( $post_id, $recommendations ) {
        return $this->set( 'recommendations', $recommendations, 7200, array( 'post_id' => $post_id ) ); // 2 heures
    }

    /**
     * Récupère les recommandations d'un contenu depuis le cache.
     *
     * @since    1.1.0
     * @param    int    $post_id    ID du contenu.
     * @return   mixed              Recommandations ou false si non trouvées.
     */
    public function get_recommendations( $post_id ) {
        return $this->get( 'recommendations', array( 'post_id' => $post_id ) );
    }

    /**
     * Met en cache les résultats d'analyse d'un contenu.
     *
     * @since    1.1.0
     * @param    int      $post_id    ID du contenu.
     * @param    array    $analysis   Résultats d'analyse.
     * @return   bool                 True si la mise en cache a réussi.
     */
    public function set_analysis_results( $post_id, $analysis ) {
        return $this->set( 'analysis_results', $analysis, 3600, array( 'post_id' => $post_id ) ); // 1 heure
    }

    /**
     * Récupère les résultats d'analyse d'un contenu depuis le cache.
     *
     * @since    1.1.0
     * @param    int    $post_id    ID du contenu.
     * @return   mixed              Résultats d'analyse ou false si non trouvés.
     */
    public function get_analysis_results( $post_id ) {
        return $this->get( 'analysis_results', array( 'post_id' => $post_id ) );
    }

    /**
     * Met en cache les résultats d'une requête de contenus.
     *
     * @since    1.1.0
     * @param    array    $filters    Filtres appliqués.
     * @param    string   $search     Terme de recherche.
     * @param    int      $page       Page courante.
     * @param    int      $per_page   Éléments par page.
     * @param    array    $results    Résultats de la requête.
     * @return   bool                 True si la mise en cache a réussi.
     */
    public function set_contents_query( $filters, $search, $page, $per_page, $results ) {
        $args = array(
            'filters' => $filters,
            'search' => $search,
            'page' => $page,
            'per_page' => $per_page
        );
        
        return $this->set( 'contents_query', $results, 600, $args ); // 10 minutes
    }

    /**
     * Récupère les résultats d'une requête de contenus depuis le cache.
     *
     * @since    1.1.0
     * @param    array     $filters    Filtres appliqués.
     * @param    string    $search     Terme de recherche.
     * @param    int       $page       Page courante.
     * @param    int       $per_page   Éléments par page.
     * @return   mixed                 Résultats de la requête ou false si non trouvés.
     */
    public function get_contents_query( $filters, $search, $page, $per_page ) {
        $args = array(
            'filters' => $filters,
            'search' => $search,
            'page' => $page,
            'per_page' => $per_page
        );
        
        return $this->get( 'contents_query', $args );
    }

    /**
     * Invalide le cache d'un contenu spécifique.
     *
     * @since    1.1.0
     * @param    int    $post_id    ID du contenu.
     * @return   bool               True si l'invalidation a réussi.
     */
    public function invalidate_post_cache( $post_id ) {
        $success = true;
        
        $success &= $this->delete( 'seo_score', array( 'post_id' => $post_id ) );
        $success &= $this->delete( 'recommendations', array( 'post_id' => $post_id ) );
        $success &= $this->delete( 'analysis_results', array( 'post_id' => $post_id ) );
        
        // Invalider aussi le cache des requêtes de contenus
        $this->invalidate_contents_queries_cache();
        
        return $success;
    }

    /**
     * Invalide le cache des requêtes de contenus.
     *
     * @since    1.1.0
     * @return   bool    True si l'invalidation a réussi.
     */
    public function invalidate_contents_queries_cache() {
        // Pour simplifier, on vide tout le cache des requêtes
        // Dans une implémentation plus avancée, on pourrait être plus sélectif
        return $this->flush();
    }

    /**
     * Met en cache le résultat d'un test de clé API IA.
     *
     * @since    1.1.0
     * @param    string    $provider    Fournisseur IA.
     * @param    string    $api_key     Clé API (hashée).
     * @param    bool      $is_valid    Validité de la clé.
     * @return   bool                   True si la mise en cache a réussi.
     */
    public function set_api_key_test( $provider, $api_key, $is_valid ) {
        $key_hash = md5( $api_key );
        return $this->set( 'api_key_test', $is_valid, 1800, array( 'provider' => $provider, 'key' => $key_hash ) ); // 30 minutes
    }

    /**
     * Récupère le résultat d'un test de clé API IA depuis le cache.
     *
     * @since    1.1.0
     * @param    string    $provider    Fournisseur IA.
     * @param    string    $api_key     Clé API.
     * @return   mixed                  Résultat du test ou false si non trouvé.
     */
    public function get_api_key_test( $provider, $api_key ) {
        $key_hash = md5( $api_key );
        return $this->get( 'api_key_test', array( 'provider' => $provider, 'key' => $key_hash ) );
    }
}
