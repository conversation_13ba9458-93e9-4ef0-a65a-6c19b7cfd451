import { __ } from '@wordpress/i18n';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Dashicon } from '@wordpress/components';

/**
 * Composant pour afficher les problèmes techniques détectés
 */
const TechnicalIssues = ({ issues }) => {
  // Grouper les problèmes par catégorie
  const groupedIssues = issues.reduce((acc, issue) => {
    if (!acc[issue.category]) {
      acc[issue.category] = [];
    }
    acc[issue.category].push(issue);
    return acc;
  }, {});

  // Compter les problèmes par sévérité
  const criticalCount = issues.filter(issue => issue.severity === 'critical').length;
  const warningCount = issues.filter(issue => issue.severity === 'warning').length;
  const infoCount = issues.filter(issue => issue.severity === 'info').length;

  return (
    <Card className="boss-card boss-h-full">
      <CardHeader>
        <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Problèmes techniques détectés', 'boss-seo')}
        </h3>
      </CardHeader>
      <CardBody>
        {/* Résumé des problèmes */}
        <div className="boss-flex boss-justify-between boss-mb-6">
          <div className="boss-text-center boss-px-4">
            <div className="boss-flex boss-items-center boss-justify-center boss-w-10 boss-h-10 boss-rounded-full boss-bg-red-100 boss-mx-auto boss-mb-2">
              <Dashicon icon="warning" className="boss-text-boss-error" />
            </div>
            <div className="boss-text-xl boss-font-bold boss-text-boss-error">{criticalCount}</div>
            <div className="boss-text-xs boss-text-boss-gray">{__('Critiques', 'boss-seo')}</div>
          </div>
          <div className="boss-text-center boss-px-4">
            <div className="boss-flex boss-items-center boss-justify-center boss-w-10 boss-h-10 boss-rounded-full boss-bg-yellow-100 boss-mx-auto boss-mb-2">
              <Dashicon icon="flag" className="boss-text-boss-warning" />
            </div>
            <div className="boss-text-xl boss-font-bold boss-text-boss-warning">{warningCount}</div>
            <div className="boss-text-xs boss-text-boss-gray">{__('Avertissements', 'boss-seo')}</div>
          </div>
          <div className="boss-text-center boss-px-4">
            <div className="boss-flex boss-items-center boss-justify-center boss-w-10 boss-h-10 boss-rounded-full boss-bg-blue-100 boss-mx-auto boss-mb-2">
              <Dashicon icon="info" className="boss-text-blue-600" />
            </div>
            <div className="boss-text-xl boss-font-bold boss-text-blue-600">{infoCount}</div>
            <div className="boss-text-xs boss-text-boss-gray">{__('Informations', 'boss-seo')}</div>
          </div>
        </div>

        {/* Liste des problèmes par catégorie */}
        <div className="boss-space-y-4">
          {Object.entries(groupedIssues).map(([category, categoryIssues]) => (
            <div key={category} className="boss-border boss-border-gray-200 boss-rounded-lg boss-overflow-hidden">
              <div className="boss-bg-gray-50 boss-px-4 boss-py-2 boss-border-b boss-border-gray-200">
                <h4 className="boss-font-medium boss-text-boss-dark">{category}</h4>
              </div>
              <ul className="boss-divide-y boss-divide-gray-200">
                {categoryIssues.slice(0, 3).map((issue) => (
                  <li key={issue.id} className="boss-px-4 boss-py-3 boss-flex boss-items-start">
                    <div className={`boss-flex-shrink-0 boss-mr-3 ${
                      issue.severity === 'critical' ? 'boss-text-boss-error' :
                      issue.severity === 'warning' ? 'boss-text-boss-warning' :
                      'boss-text-blue-600'
                    }`}>
                      <Dashicon icon={
                        issue.severity === 'critical' ? 'warning' :
                        issue.severity === 'warning' ? 'flag' :
                        'info'
                      } />
                    </div>
                    <div className="boss-flex-1">
                      <p className="boss-text-sm boss-font-medium boss-text-boss-dark">{issue.title}</p>
                      <p className="boss-text-xs boss-text-boss-gray boss-mt-1">{issue.description}</p>
                    </div>
                    <Button
                      isSmall
                      isSecondary
                      className="boss-ml-2 boss-flex-shrink-0"
                    >
                      {__('Corriger', 'boss-seo')}
                    </Button>
                  </li>
                ))}
                {categoryIssues.length > 3 && (
                  <li className="boss-px-4 boss-py-2 boss-text-center boss-text-sm boss-text-boss-primary boss-font-medium">
                    <Button
                      isLink
                      className="boss-text-boss-primary"
                    >
                      {__('Voir les', 'boss-seo')} {categoryIssues.length - 3} {__('autres problèmes', 'boss-seo')}
                    </Button>
                  </li>
                )}
              </ul>
            </div>
          ))}
        </div>
      </CardBody>
      <CardFooter>
        <Button
          isPrimary
          className="boss-w-full boss-justify-center"
        >
          {__('Analyser à nouveau', 'boss-seo')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TechnicalIssues;
