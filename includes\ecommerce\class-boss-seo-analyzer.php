<?php
/**
 * Classe pour l'analyse SEO des produits.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour l'analyse SEO des produits.
 *
 * Cette classe gère l'analyse SEO des produits.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Seo_Analyzer {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_seo_analyzer_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_analyze_product', array( $this, 'ajax_analyze_product' ) );
        add_action( 'wp_ajax_boss_seo_get_analysis_history', array( $this, 'ajax_get_analysis_history' ) );

        // Ajouter les actions pour l'analyse SEO
        add_action( 'woocommerce_process_product_meta', array( $this, 'analyze_product' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/analyzer/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_analysis' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'analyze' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/analyzer/(?P<id>\d+)/history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_analysis_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour analyser un produit.
     *
     * @since    1.2.0
     */
    public function ajax_analyze_product() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );

        // Analyser le produit
        $analysis = $this->analyze_product( $product_id );

        if ( is_wp_error( $analysis ) ) {
            wp_send_json_error( array( 'message' => $analysis->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Produit analysé avec succès.', 'boss-seo' ),
            'analysis' => $analysis,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer l'historique d'analyse d'un produit.
     *
     * @since    1.2.0
     */
    public function ajax_get_analysis_history() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );

        // Récupérer l'historique d'analyse
        $history = $this->get_product_analysis_history( $product_id );

        wp_send_json_success( array(
            'message' => __( 'Historique d\'analyse récupéré avec succès.', 'boss-seo' ),
            'history' => $history,
        ) );
    }

    /**
     * Récupère l'analyse d'un produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_analysis( $request ) {
        $product_id = $request['id'];

        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Récupérer l'analyse
        $analysis = get_post_meta( $product_id, '_boss_seo_analysis', true );

        if ( empty( $analysis ) ) {
            return new WP_Error( 'no_analysis', __( 'Aucune analyse trouvée.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        return rest_ensure_response( $analysis );
    }

    /**
     * Analyse un produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function analyze( $request ) {
        $product_id = $request['id'];

        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Analyser le produit
        $analysis = $this->analyze_product( $product_id );

        if ( is_wp_error( $analysis ) ) {
            return $analysis;
        }

        return rest_ensure_response( array(
            'message'  => __( 'Produit analysé avec succès.', 'boss-seo' ),
            'analysis' => $analysis,
        ) );
    }

    /**
     * Récupère l'historique d'analyse d'un produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_analysis_history( $request ) {
        $product_id = $request['id'];

        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Récupérer l'historique d'analyse
        $history = $this->get_product_analysis_history( $product_id );

        return rest_ensure_response( $history );
    }

    /**
     * Affiche la métabox pour l'analyse SEO.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post.
     */
    public function render_meta_box( $post ) {
        // Récupérer les données du produit
        $product_id = $post->ID;
        $analysis = get_post_meta( $product_id, '_boss_seo_analysis', true );

        // Afficher le formulaire
        wp_nonce_field( 'boss_seo_analyzer_meta_box', 'boss_seo_analyzer_meta_box_nonce' );
        ?>
        <div class="boss-seo-analyzer-meta-box">
            <div class="boss-seo-analyzer-actions">
                <button type="button" class="button button-primary" id="boss-seo-analyze-product" data-product-id="<?php echo esc_attr( $product_id ); ?>"><?php esc_html_e( 'Analyser le produit', 'boss-seo' ); ?></button>
                <button type="button" class="button button-secondary" id="boss-seo-view-analysis-history" data-product-id="<?php echo esc_attr( $product_id ); ?>"><?php esc_html_e( 'Voir l\'historique d\'analyse', 'boss-seo' ); ?></button>
            </div>

            <div class="boss-seo-analyzer-result" <?php echo empty( $analysis ) ? 'style="display: none;"' : ''; ?>>
                <h4><?php esc_html_e( 'Résultat de l\'analyse :', 'boss-seo' ); ?></h4>
                <div id="boss-seo-analyzer-result">
                    <?php if ( ! empty( $analysis ) ) : ?>
                        <?php echo $this->render_analysis_result( $analysis ); ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="boss-seo-analyzer-history" style="display: none;">
                <h4><?php esc_html_e( 'Historique d\'analyse :', 'boss-seo' ); ?></h4>
                <div id="boss-seo-analyzer-history"></div>
            </div>
        </div>
        <?php
    }

    /**
     * Enregistre les métadonnées du produit.
     *
     * @since    1.2.0
     * @param    int    $post_id    L'ID du produit.
     */
    public function save_meta( $post_id ) {
        // Vérifier le nonce
        if ( ! isset( $_POST['boss_seo_analyzer_meta_box_nonce'] ) || ! wp_verify_nonce( $_POST['boss_seo_analyzer_meta_box_nonce'], 'boss_seo_analyzer_meta_box' ) ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Rien à enregistrer pour l'analyse SEO
    }

    /**
     * Analyse un produit.
     *
     * @since    1.2.0
     * @param    int       $product_id    L'ID du produit.
     * @return   array|WP_Error           Les résultats de l'analyse ou une erreur.
     */
    public function analyze_product( $product_id ) {
        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ) );
        }

        // Récupérer les données du produit
        $title = $product->get_name();
        $description = $product->get_description();
        $short_description = $product->get_short_description();
        $sku = $product->get_sku();
        $price = $product->get_price();
        $regular_price = $product->get_regular_price();
        $sale_price = $product->get_sale_price();
        $categories = $product->get_category_ids();
        $tags = $product->get_tag_ids();
        $attributes = $product->get_attributes();
        $images = $product->get_gallery_image_ids();
        $featured_image = $product->get_image_id();
        $stock_status = $product->get_stock_status();
        $stock_quantity = $product->get_stock_quantity();
        $rating_count = $product->get_rating_count();
        $average_rating = $product->get_average_rating();
        $review_count = $product->get_review_count();
        $permalink = get_permalink( $product_id );

        // Analyser le titre
        $title_analysis = $this->analyze_title( $title );

        // Analyser la description
        $description_analysis = $this->analyze_description( $description, $short_description );

        // Analyser les images
        $images_analysis = $this->analyze_images( $featured_image, $images );

        // Analyser les catégories et les tags
        $categories_tags_analysis = $this->analyze_categories_tags( $categories, $tags );

        // Analyser les attributs
        $attributes_analysis = $this->analyze_attributes( $attributes );

        // Analyser le prix
        $price_analysis = $this->analyze_price( $price, $regular_price, $sale_price );

        // Analyser le stock
        $stock_analysis = $this->analyze_stock( $stock_status, $stock_quantity );

        // Analyser les avis
        $reviews_analysis = $this->analyze_reviews( $rating_count, $average_rating, $review_count );

        // Analyser le SKU
        $sku_analysis = $this->analyze_sku( $sku );

        // Analyser le permalien
        $permalink_analysis = $this->analyze_permalink( $permalink );

        // Calculer le score global
        $score = $this->calculate_score( array(
            $title_analysis,
            $description_analysis,
            $images_analysis,
            $categories_tags_analysis,
            $attributes_analysis,
            $price_analysis,
            $stock_analysis,
            $reviews_analysis,
            $sku_analysis,
            $permalink_analysis,
        ) );

        // Préparer les résultats
        $analysis = array(
            'score' => $score,
            'date' => current_time( 'mysql' ),
            'title' => $title_analysis,
            'description' => $description_analysis,
            'images' => $images_analysis,
            'categories_tags' => $categories_tags_analysis,
            'attributes' => $attributes_analysis,
            'price' => $price_analysis,
            'stock' => $stock_analysis,
            'reviews' => $reviews_analysis,
            'sku' => $sku_analysis,
            'permalink' => $permalink_analysis,
        );

        // Enregistrer l'analyse
        $this->save_analysis( $product_id, $analysis );

        return $analysis;
    }

    /**
     * Analyse le titre du produit.
     *
     * @since    1.2.0
     * @param    string    $title    Le titre du produit.
     * @return   array               Les résultats de l'analyse.
     */
    private function analyze_title( $title ) {
        $analysis = array(
            'title' => __( 'Titre du produit', 'boss-seo' ),
            'score' => 0,
            'max_score' => 10,
            'status' => 'error',
            'description' => '',
            'recommendations' => array(),
        );

        // Vérifier si le titre existe
        if ( empty( $title ) ) {
            $analysis['description'] = __( 'Le titre du produit est vide.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Ajoutez un titre à votre produit.', 'boss-seo' );
            return $analysis;
        }

        // Vérifier la longueur du titre
        $title_length = mb_strlen( $title );

        if ( $title_length < 20 ) {
            $analysis['score'] = 3;
            $analysis['status'] = 'error';
            $analysis['description'] = __( 'Le titre du produit est trop court.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Le titre du produit devrait contenir au moins 20 caractères.', 'boss-seo' );
        } elseif ( $title_length < 40 ) {
            $analysis['score'] = 5;
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'Le titre du produit est un peu court.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez d\'allonger le titre du produit à au moins 40 caractères.', 'boss-seo' );
        } elseif ( $title_length < 60 ) {
            $analysis['score'] = 8;
            $analysis['status'] = 'good';
            $analysis['description'] = __( 'Le titre du produit a une bonne longueur.', 'boss-seo' );
        } elseif ( $title_length < 70 ) {
            $analysis['score'] = 10;
            $analysis['status'] = 'excellent';
            $analysis['description'] = __( 'Le titre du produit a une excellente longueur.', 'boss-seo' );
        } else {
            $analysis['score'] = 7;
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'Le titre du produit est un peu long.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez de raccourcir le titre du produit à moins de 70 caractères.', 'boss-seo' );
        }

        // Vérifier si le titre contient des mots-clés
        $keywords = $this->extract_keywords( $title );

        if ( count( $keywords ) < 2 ) {
            $analysis['score'] = max( 0, $analysis['score'] - 2 );
            $analysis['status'] = 'warning';
            $analysis['recommendations'][] = __( 'Essayez d\'ajouter plus de mots-clés pertinents dans le titre du produit.', 'boss-seo' );
        }

        return $analysis;
    }

    /**
     * Analyse la description du produit.
     *
     * @since    1.2.0
     * @param    string    $description         La description du produit.
     * @param    string    $short_description   La description courte du produit.
     * @return   array                          Les résultats de l'analyse.
     */
    private function analyze_description( $description, $short_description ) {
        $analysis = array(
            'title' => __( 'Description du produit', 'boss-seo' ),
            'score' => 0,
            'max_score' => 10,
            'status' => 'error',
            'description' => '',
            'recommendations' => array(),
        );

        // Vérifier si la description existe
        if ( empty( $description ) && empty( $short_description ) ) {
            $analysis['description'] = __( 'La description du produit est vide.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Ajoutez une description à votre produit.', 'boss-seo' );
            return $analysis;
        }

        // Vérifier la longueur de la description
        $description_length = mb_strlen( $description );
        $short_description_length = mb_strlen( $short_description );
        $total_description_length = $description_length + $short_description_length;

        if ( $total_description_length < 100 ) {
            $analysis['score'] = 3;
            $analysis['status'] = 'error';
            $analysis['description'] = __( 'La description du produit est trop courte.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'La description du produit devrait contenir au moins 100 caractères.', 'boss-seo' );
        } elseif ( $total_description_length < 300 ) {
            $analysis['score'] = 5;
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'La description du produit est un peu courte.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez d\'allonger la description du produit à au moins 300 caractères.', 'boss-seo' );
        } elseif ( $total_description_length < 500 ) {
            $analysis['score'] = 8;
            $analysis['status'] = 'good';
            $analysis['description'] = __( 'La description du produit a une bonne longueur.', 'boss-seo' );
        } else {
            $analysis['score'] = 10;
            $analysis['status'] = 'excellent';
            $analysis['description'] = __( 'La description du produit a une excellente longueur.', 'boss-seo' );
        }

        // Vérifier si la description contient des mots-clés
        $keywords = $this->extract_keywords( $description . ' ' . $short_description );

        if ( count( $keywords ) < 5 ) {
            $analysis['score'] = max( 0, $analysis['score'] - 2 );
            $analysis['status'] = 'warning';
            $analysis['recommendations'][] = __( 'Essayez d\'ajouter plus de mots-clés pertinents dans la description du produit.', 'boss-seo' );
        }

        // Vérifier si la description contient des listes
        if ( strpos( $description, '<ul>' ) === false && strpos( $description, '<ol>' ) === false ) {
            $analysis['recommendations'][] = __( 'Essayez d\'ajouter des listes à puces ou numérotées dans la description du produit.', 'boss-seo' );
        }

        // Vérifier si la description contient des titres
        if ( strpos( $description, '<h' ) === false ) {
            $analysis['recommendations'][] = __( 'Essayez d\'ajouter des titres dans la description du produit.', 'boss-seo' );
        }

        return $analysis;
    }

    /**
     * Analyse les images du produit.
     *
     * @since    1.2.0
     * @param    int      $featured_image    L'ID de l'image mise en avant.
     * @param    array    $images            Les IDs des images de la galerie.
     * @return   array                       Les résultats de l'analyse.
     */
    private function analyze_images( $featured_image, $images ) {
        $analysis = array(
            'title' => __( 'Images du produit', 'boss-seo' ),
            'score' => 0,
            'max_score' => 10,
            'status' => 'error',
            'description' => '',
            'recommendations' => array(),
        );

        // Vérifier si l'image mise en avant existe
        if ( empty( $featured_image ) ) {
            $analysis['description'] = __( 'L\'image mise en avant du produit est manquante.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Ajoutez une image mise en avant à votre produit.', 'boss-seo' );
            return $analysis;
        }

        // Vérifier le nombre d'images
        $image_count = count( $images ) + 1; // +1 pour l'image mise en avant

        if ( $image_count < 2 ) {
            $analysis['score'] = 3;
            $analysis['status'] = 'error';
            $analysis['description'] = __( 'Le produit n\'a qu\'une seule image.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Ajoutez plus d\'images à votre produit.', 'boss-seo' );
        } elseif ( $image_count < 3 ) {
            $analysis['score'] = 5;
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'Le produit n\'a que quelques images.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez d\'ajouter plus d\'images à votre produit.', 'boss-seo' );
        } elseif ( $image_count < 5 ) {
            $analysis['score'] = 8;
            $analysis['status'] = 'good';
            $analysis['description'] = __( 'Le produit a un bon nombre d\'images.', 'boss-seo' );
        } else {
            $analysis['score'] = 10;
            $analysis['status'] = 'excellent';
            $analysis['description'] = __( 'Le produit a un excellent nombre d\'images.', 'boss-seo' );
        }

        // Vérifier la qualité des images
        $featured_image_data = wp_get_attachment_metadata( $featured_image );

        if ( isset( $featured_image_data['width'] ) && isset( $featured_image_data['height'] ) ) {
            $width = $featured_image_data['width'];
            $height = $featured_image_data['height'];

            if ( $width < 800 || $height < 800 ) {
                $analysis['score'] = max( 0, $analysis['score'] - 2 );
                $analysis['status'] = 'warning';
                $analysis['recommendations'][] = __( 'L\'image mise en avant du produit est de faible résolution. Essayez d\'utiliser une image d\'au moins 800x800 pixels.', 'boss-seo' );
            }
        }

        // Vérifier si les images ont des attributs alt
        $featured_image_alt = get_post_meta( $featured_image, '_wp_attachment_image_alt', true );

        if ( empty( $featured_image_alt ) ) {
            $analysis['score'] = max( 0, $analysis['score'] - 1 );
            $analysis['recommendations'][] = __( 'Ajoutez un attribut alt à l\'image mise en avant du produit.', 'boss-seo' );
        }

        foreach ( $images as $image_id ) {
            $image_alt = get_post_meta( $image_id, '_wp_attachment_image_alt', true );

            if ( empty( $image_alt ) ) {
                $analysis['score'] = max( 0, $analysis['score'] - 1 );
                $analysis['recommendations'][] = __( 'Ajoutez des attributs alt à toutes les images du produit.', 'boss-seo' );
                break;
            }
        }

        return $analysis;
    }

    /**
     * Analyse les catégories et les tags du produit.
     *
     * @since    1.2.0
     * @param    array    $categories    Les IDs des catégories du produit.
     * @param    array    $tags          Les IDs des tags du produit.
     * @return   array                   Les résultats de l'analyse.
     */
    private function analyze_categories_tags( $categories, $tags ) {
        $analysis = array(
            'title' => __( 'Catégories et tags du produit', 'boss-seo' ),
            'score' => 0,
            'max_score' => 10,
            'status' => 'error',
            'description' => '',
            'recommendations' => array(),
        );

        // Vérifier si le produit a des catégories
        $category_count = count( $categories );

        if ( $category_count === 0 ) {
            $analysis['description'] = __( 'Le produit n\'a pas de catégorie.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Ajoutez au moins une catégorie à votre produit.', 'boss-seo' );
            return $analysis;
        }

        // Vérifier le nombre de catégories
        if ( $category_count < 2 ) {
            $analysis['score'] = 5;
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'Le produit n\'a qu\'une seule catégorie.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez d\'ajouter plus de catégories à votre produit.', 'boss-seo' );
        } elseif ( $category_count < 4 ) {
            $analysis['score'] = 8;
            $analysis['status'] = 'good';
            $analysis['description'] = __( 'Le produit a un bon nombre de catégories.', 'boss-seo' );
        } else {
            $analysis['score'] = 10;
            $analysis['status'] = 'excellent';
            $analysis['description'] = __( 'Le produit a un excellent nombre de catégories.', 'boss-seo' );
        }

        // Vérifier si le produit a des tags
        $tag_count = count( $tags );

        if ( $tag_count === 0 ) {
            $analysis['score'] = max( 0, $analysis['score'] - 2 );
            $analysis['status'] = 'warning';
            $analysis['recommendations'][] = __( 'Ajoutez des tags à votre produit.', 'boss-seo' );
        } elseif ( $tag_count < 3 ) {
            $analysis['score'] = max( 0, $analysis['score'] - 1 );
            $analysis['recommendations'][] = __( 'Essayez d\'ajouter plus de tags à votre produit.', 'boss-seo' );
        }

        return $analysis;
    }

    /**
     * Analyse les attributs du produit.
     *
     * @since    1.2.0
     * @param    array    $attributes    Les attributs du produit.
     * @return   array                   Les résultats de l'analyse.
     */
    private function analyze_attributes( $attributes ) {
        $analysis = array(
            'title' => __( 'Attributs du produit', 'boss-seo' ),
            'score' => 0,
            'max_score' => 10,
            'status' => 'error',
            'description' => '',
            'recommendations' => array(),
        );

        // Vérifier si le produit a des attributs
        $attribute_count = count( $attributes );

        if ( $attribute_count === 0 ) {
            $analysis['description'] = __( 'Le produit n\'a pas d\'attribut.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Ajoutez des attributs à votre produit.', 'boss-seo' );
            return $analysis;
        }

        // Vérifier le nombre d'attributs
        if ( $attribute_count < 2 ) {
            $analysis['score'] = 5;
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'Le produit n\'a qu\'un seul attribut.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez d\'ajouter plus d\'attributs à votre produit.', 'boss-seo' );
        } elseif ( $attribute_count < 4 ) {
            $analysis['score'] = 8;
            $analysis['status'] = 'good';
            $analysis['description'] = __( 'Le produit a un bon nombre d\'attributs.', 'boss-seo' );
        } else {
            $analysis['score'] = 10;
            $analysis['status'] = 'excellent';
            $analysis['description'] = __( 'Le produit a un excellent nombre d\'attributs.', 'boss-seo' );
        }

        return $analysis;
    }

    /**
     * Analyse le prix du produit.
     *
     * @since    1.2.0
     * @param    float    $price           Le prix du produit.
     * @param    float    $regular_price   Le prix régulier du produit.
     * @param    float    $sale_price      Le prix de vente du produit.
     * @return   array                     Les résultats de l'analyse.
     */
    private function analyze_price( $price, $regular_price, $sale_price ) {
        $analysis = array(
            'title' => __( 'Prix du produit', 'boss-seo' ),
            'score' => 0,
            'max_score' => 10,
            'status' => 'error',
            'description' => '',
            'recommendations' => array(),
        );

        // Vérifier si le prix existe
        if ( empty( $price ) ) {
            $analysis['description'] = __( 'Le prix du produit est vide.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Ajoutez un prix à votre produit.', 'boss-seo' );
            return $analysis;
        }

        // Vérifier si le produit est en promotion
        if ( ! empty( $sale_price ) && ! empty( $regular_price ) ) {
            $discount = ( $regular_price - $sale_price ) / $regular_price * 100;

            if ( $discount < 5 ) {
                $analysis['score'] = 5;
                $analysis['status'] = 'warning';
                $analysis['description'] = __( 'La réduction du produit est très faible.', 'boss-seo' );
                $analysis['recommendations'][] = __( 'Essayez d\'augmenter la réduction du produit.', 'boss-seo' );
            } elseif ( $discount < 10 ) {
                $analysis['score'] = 8;
                $analysis['status'] = 'good';
                $analysis['description'] = __( 'La réduction du produit est correcte.', 'boss-seo' );
            } else {
                $analysis['score'] = 10;
                $analysis['status'] = 'excellent';
                $analysis['description'] = __( 'La réduction du produit est excellente.', 'boss-seo' );
            }
        } else {
            $analysis['score'] = 5;
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'Le produit n\'est pas en promotion.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez de mettre votre produit en promotion.', 'boss-seo' );
        }

        return $analysis;
    }

    /**
     * Analyse le stock du produit.
     *
     * @since    1.2.0
     * @param    string    $stock_status      Le statut du stock du produit.
     * @param    int       $stock_quantity    La quantité en stock du produit.
     * @return   array                        Les résultats de l'analyse.
     */
    private function analyze_stock( $stock_status, $stock_quantity ) {
        $analysis = array(
            'title' => __( 'Stock du produit', 'boss-seo' ),
            'score' => 0,
            'max_score' => 10,
            'status' => 'error',
            'description' => '',
            'recommendations' => array(),
        );

        // Vérifier si le produit est en stock
        if ( $stock_status === 'outofstock' ) {
            $analysis['description'] = __( 'Le produit est en rupture de stock.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Mettez à jour le stock de votre produit.', 'boss-seo' );
            return $analysis;
        }

        // Vérifier la quantité en stock
        if ( $stock_quantity !== null ) {
            if ( $stock_quantity < 5 ) {
                $analysis['score'] = 5;
                $analysis['status'] = 'warning';
                $analysis['description'] = __( 'Le produit a un stock très limité.', 'boss-seo' );
                $analysis['recommendations'][] = __( 'Augmentez le stock de votre produit.', 'boss-seo' );
            } elseif ( $stock_quantity < 10 ) {
                $analysis['score'] = 8;
                $analysis['status'] = 'good';
                $analysis['description'] = __( 'Le produit a un stock correct.', 'boss-seo' );
            } else {
                $analysis['score'] = 10;
                $analysis['status'] = 'excellent';
                $analysis['description'] = __( 'Le produit a un excellent stock.', 'boss-seo' );
            }
        } else {
            $analysis['score'] = 10;
            $analysis['status'] = 'excellent';
            $analysis['description'] = __( 'Le produit est en stock.', 'boss-seo' );
        }

        return $analysis;
    }

    /**
     * Analyse les avis du produit.
     *
     * @since    1.2.0
     * @param    int       $rating_count      Le nombre de notes du produit.
     * @param    float     $average_rating    La note moyenne du produit.
     * @param    int       $review_count      Le nombre d'avis du produit.
     * @return   array                        Les résultats de l'analyse.
     */
    private function analyze_reviews( $rating_count, $average_rating, $review_count ) {
        $analysis = array(
            'title' => __( 'Avis du produit', 'boss-seo' ),
            'score' => 0,
            'max_score' => 10,
            'status' => 'error',
            'description' => '',
            'recommendations' => array(),
        );

        // Vérifier si le produit a des avis
        if ( $review_count === 0 ) {
            $analysis['description'] = __( 'Le produit n\'a pas d\'avis.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Encouragez vos clients à laisser des avis sur votre produit.', 'boss-seo' );
            return $analysis;
        }

        // Vérifier le nombre d'avis
        if ( $review_count < 5 ) {
            $analysis['score'] = 5;
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'Le produit n\'a que quelques avis.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez d\'obtenir plus d\'avis pour votre produit.', 'boss-seo' );
        } elseif ( $review_count < 10 ) {
            $analysis['score'] = 8;
            $analysis['status'] = 'good';
            $analysis['description'] = __( 'Le produit a un bon nombre d\'avis.', 'boss-seo' );
        } else {
            $analysis['score'] = 10;
            $analysis['status'] = 'excellent';
            $analysis['description'] = __( 'Le produit a un excellent nombre d\'avis.', 'boss-seo' );
        }

        // Vérifier la note moyenne
        if ( $average_rating < 3 ) {
            $analysis['score'] = max( 0, $analysis['score'] - 3 );
            $analysis['status'] = 'error';
            $analysis['description'] = __( 'La note moyenne du produit est très basse.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Améliorez votre produit pour obtenir de meilleures notes.', 'boss-seo' );
        } elseif ( $average_rating < 4 ) {
            $analysis['score'] = max( 0, $analysis['score'] - 2 );
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'La note moyenne du produit est moyenne.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez d\'améliorer votre produit pour obtenir de meilleures notes.', 'boss-seo' );
        }

        return $analysis;
    }

    /**
     * Analyse le SKU du produit.
     *
     * @since    1.2.0
     * @param    string    $sku    Le SKU du produit.
     * @return   array             Les résultats de l'analyse.
     */
    private function analyze_sku( $sku ) {
        $analysis = array(
            'title' => __( 'SKU du produit', 'boss-seo' ),
            'score' => 0,
            'max_score' => 10,
            'status' => 'error',
            'description' => '',
            'recommendations' => array(),
        );

        // Vérifier si le SKU existe
        if ( empty( $sku ) ) {
            $analysis['description'] = __( 'Le SKU du produit est vide.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Ajoutez un SKU à votre produit.', 'boss-seo' );
            return $analysis;
        }

        // Vérifier la longueur du SKU
        $sku_length = mb_strlen( $sku );

        if ( $sku_length < 5 ) {
            $analysis['score'] = 5;
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'Le SKU du produit est un peu court.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez d\'utiliser un SKU plus descriptif.', 'boss-seo' );
        } elseif ( $sku_length < 10 ) {
            $analysis['score'] = 8;
            $analysis['status'] = 'good';
            $analysis['description'] = __( 'Le SKU du produit a une bonne longueur.', 'boss-seo' );
        } else {
            $analysis['score'] = 10;
            $analysis['status'] = 'excellent';
            $analysis['description'] = __( 'Le SKU du produit a une excellente longueur.', 'boss-seo' );
        }

        // Vérifier si le SKU contient des caractères spéciaux
        if ( preg_match( '/[^a-zA-Z0-9\-_]/', $sku ) ) {
            $analysis['score'] = max( 0, $analysis['score'] - 2 );
            $analysis['status'] = 'warning';
            $analysis['recommendations'][] = __( 'Évitez d\'utiliser des caractères spéciaux dans le SKU du produit.', 'boss-seo' );
        }

        return $analysis;
    }

    /**
     * Analyse le permalien du produit.
     *
     * @since    1.2.0
     * @param    string    $permalink    Le permalien du produit.
     * @return   array                   Les résultats de l'analyse.
     */
    private function analyze_permalink( $permalink ) {
        $analysis = array(
            'title' => __( 'Permalien du produit', 'boss-seo' ),
            'score' => 0,
            'max_score' => 10,
            'status' => 'error',
            'description' => '',
            'recommendations' => array(),
        );

        // Vérifier si le permalien existe
        if ( empty( $permalink ) ) {
            $analysis['description'] = __( 'Le permalien du produit est vide.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Ajoutez un permalien à votre produit.', 'boss-seo' );
            return $analysis;
        }

        // Vérifier la longueur du permalien
        $permalink_length = mb_strlen( $permalink );

        if ( $permalink_length < 30 ) {
            $analysis['score'] = 5;
            $analysis['status'] = 'warning';
            $analysis['description'] = __( 'Le permalien du produit est un peu court.', 'boss-seo' );
            $analysis['recommendations'][] = __( 'Essayez d\'utiliser un permalien plus descriptif.', 'boss-seo' );
        } elseif ( $permalink_length < 60 ) {
            $analysis['score'] = 8;
            $analysis['status'] = 'good';
            $analysis['description'] = __( 'Le permalien du produit a une bonne longueur.', 'boss-seo' );
        } else {
            $analysis['score'] = 10;
            $analysis['status'] = 'excellent';
            $analysis['description'] = __( 'Le permalien du produit a une excellente longueur.', 'boss-seo' );
        }

        // Vérifier si le permalien contient des mots-clés
        $keywords = $this->extract_keywords( $permalink );

        if ( count( $keywords ) < 2 ) {
            $analysis['score'] = max( 0, $analysis['score'] - 2 );
            $analysis['status'] = 'warning';
            $analysis['recommendations'][] = __( 'Essayez d\'ajouter plus de mots-clés pertinents dans le permalien du produit.', 'boss-seo' );
        }

        return $analysis;
    }

    /**
     * Calcule le score global de l'analyse.
     *
     * @since    1.2.0
     * @param    array    $analyses    Les analyses individuelles.
     * @return   int                   Le score global.
     */
    private function calculate_score( $analyses ) {
        $total_score = 0;
        $total_max_score = 0;

        foreach ( $analyses as $analysis ) {
            $total_score += $analysis['score'];
            $total_max_score += $analysis['max_score'];
        }

        if ( $total_max_score === 0 ) {
            return 0;
        }

        return round( $total_score / $total_max_score * 100 );
    }

    /**
     * Extrait les mots-clés d'un texte.
     *
     * @since    1.2.0
     * @param    string    $text    Le texte à analyser.
     * @return   array              Les mots-clés extraits.
     */
    private function extract_keywords( $text ) {
        // Supprimer les balises HTML
        $text = strip_tags( $text );

        // Convertir en minuscules
        $text = mb_strtolower( $text );

        // Supprimer les caractères spéciaux
        $text = preg_replace( '/[^\p{L}\p{N}\s]/u', ' ', $text );

        // Supprimer les espaces multiples
        $text = preg_replace( '/\s+/', ' ', $text );

        // Diviser en mots
        $words = explode( ' ', $text );

        // Supprimer les mots vides
        $stop_words = array( 'le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'de', 'du', 'au', 'aux', 'a', 'à', 'en', 'par', 'pour', 'sur', 'dans', 'avec', 'sans', 'ce', 'cette', 'ces', 'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses', 'notre', 'nos', 'votre', 'vos', 'leur', 'leurs', 'qui', 'que', 'quoi', 'dont', 'où', 'comment', 'pourquoi', 'quand', 'quel', 'quelle', 'quels', 'quelles', 'est', 'sont', 'sera', 'seront', 'était', 'étaient', 'fut', 'furent', 'être', 'avoir', 'faire', 'dire', 'voir', 'aller', 'venir', 'prendre', 'mettre', 'donner', 'trouver', 'passer', 'rester', 'partir', 'arriver', 'entrer', 'sortir', 'monter', 'descendre', 'tomber', 'lever', 'baisser', 'ouvrir', 'fermer', 'commencer', 'finir', 'arrêter', 'continuer', 'suivre', 'attendre', 'demander', 'répondre', 'parler', 'écouter', 'lire', 'écrire', 'penser', 'croire', 'savoir', 'connaître', 'comprendre', 'apprendre', 'oublier', 'se souvenir', 'aimer', 'détester', 'vouloir', 'pouvoir', 'devoir', 'falloir', 'the', 'of', 'and', 'a', 'to', 'in', 'is', 'you', 'that', 'it', 'he', 'was', 'for', 'on', 'are', 'as', 'with', 'his', 'they', 'I', 'at', 'be', 'this', 'have', 'from', 'or', 'one', 'had', 'by', 'word', 'but', 'not', 'what', 'all', 'were', 'we', 'when', 'your', 'can', 'said', 'there', 'use', 'an', 'each', 'which', 'she', 'do', 'how', 'their', 'if', 'will', 'up', 'other', 'about', 'out', 'many', 'then', 'them', 'these', 'so', 'some', 'her', 'would', 'make', 'like', 'him', 'into', 'time', 'has', 'look', 'two', 'more', 'write', 'go', 'see', 'number', 'no', 'way', 'could', 'people', 'my', 'than', 'first', 'water', 'been', 'call', 'who', 'oil', 'its', 'now', 'find', 'long', 'down', 'day', 'did', 'get', 'come', 'made', 'may', 'part' );
        $keywords = array_diff( $words, $stop_words );

        // Supprimer les mots trop courts
        $keywords = array_filter( $keywords, function( $word ) {
            return mb_strlen( $word ) > 2;
        } );

        // Compter les occurrences
        $keyword_counts = array_count_values( $keywords );

        // Trier par occurrence
        arsort( $keyword_counts );

        // Limiter le nombre de mots-clés
        $keyword_counts = array_slice( $keyword_counts, 0, 10 );

        return array_keys( $keyword_counts );
    }

    /**
     * Enregistre l'analyse d'un produit.
     *
     * @since    1.2.0
     * @param    int       $product_id    L'ID du produit.
     * @param    array     $analysis      Les résultats de l'analyse.
     */
    private function save_analysis( $product_id, $analysis ) {
        // Enregistrer l'analyse actuelle
        update_post_meta( $product_id, '_boss_seo_analysis', $analysis );

        // Récupérer l'historique des analyses
        $history = get_post_meta( $product_id, '_boss_seo_analysis_history', true );

        if ( empty( $history ) || ! is_array( $history ) ) {
            $history = array();
        }

        // Ajouter l'analyse à l'historique
        $history[] = $analysis;

        // Limiter l'historique à 10 analyses
        if ( count( $history ) > 10 ) {
            $history = array_slice( $history, -10 );
        }

        // Enregistrer l'historique
        update_post_meta( $product_id, '_boss_seo_analysis_history', $history );
    }

    /**
     * Récupère l'historique des analyses d'un produit.
     *
     * @since    1.2.0
     * @param    int       $product_id    L'ID du produit.
     * @return   array                    L'historique des analyses.
     */
    private function get_product_analysis_history( $product_id ) {
        $history = get_post_meta( $product_id, '_boss_seo_analysis_history', true );

        if ( empty( $history ) || ! is_array( $history ) ) {
            return array();
        }

        return $history;
    }

    /**
     * Affiche le résultat de l'analyse.
     *
     * @since    1.2.0
     * @param    array     $analysis    Les résultats de l'analyse.
     * @return   string                 Le HTML du résultat de l'analyse.
     */
    private function render_analysis_result( $analysis ) {
        $output = '<div class="boss-seo-analysis-result">';

        // Afficher le score global
        $score = $analysis['score'];
        $score_class = '';

        if ( $score < 50 ) {
            $score_class = 'error';
        } elseif ( $score < 70 ) {
            $score_class = 'warning';
        } elseif ( $score < 90 ) {
            $score_class = 'good';
        } else {
            $score_class = 'excellent';
        }

        $output .= '<div class="boss-seo-analysis-score boss-seo-analysis-score-' . esc_attr( $score_class ) . '">';
        $output .= '<span class="boss-seo-analysis-score-value">' . esc_html( $score ) . '</span>';
        $output .= '<span class="boss-seo-analysis-score-label">' . esc_html__( 'Score SEO', 'boss-seo' ) . '</span>';
        $output .= '</div>';

        // Afficher la date de l'analyse
        $output .= '<div class="boss-seo-analysis-date">';
        $output .= '<span class="boss-seo-analysis-date-label">' . esc_html__( 'Date de l\'analyse :', 'boss-seo' ) . '</span>';
        $output .= '<span class="boss-seo-analysis-date-value">' . esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $analysis['date'] ) ) ) . '</span>';
        $output .= '</div>';

        // Afficher les résultats détaillés
        $output .= '<div class="boss-seo-analysis-details">';

        // Afficher le titre
        $output .= '<div class="boss-seo-analysis-detail boss-seo-analysis-detail-' . esc_attr( $analysis['title']['status'] ) . '">';
        $output .= '<h4 class="boss-seo-analysis-detail-title">' . esc_html( $analysis['title']['title'] ) . '</h4>';
        $output .= '<div class="boss-seo-analysis-detail-score">';
        $output .= '<span class="boss-seo-analysis-detail-score-value">' . esc_html( $analysis['title']['score'] ) . '</span>';
        $output .= '<span class="boss-seo-analysis-detail-score-max">/' . esc_html( $analysis['title']['max_score'] ) . '</span>';
        $output .= '</div>';
        $output .= '<div class="boss-seo-analysis-detail-description">' . esc_html( $analysis['title']['description'] ) . '</div>';

        if ( ! empty( $analysis['title']['recommendations'] ) ) {
            $output .= '<div class="boss-seo-analysis-detail-recommendations">';
            $output .= '<h5>' . esc_html__( 'Recommandations :', 'boss-seo' ) . '</h5>';
            $output .= '<ul>';

            foreach ( $analysis['title']['recommendations'] as $recommendation ) {
                $output .= '<li>' . esc_html( $recommendation ) . '</li>';
            }

            $output .= '</ul>';
            $output .= '</div>';
        }

        $output .= '</div>';

        // Afficher la description
        $output .= '<div class="boss-seo-analysis-detail boss-seo-analysis-detail-' . esc_attr( $analysis['description']['status'] ) . '">';
        $output .= '<h4 class="boss-seo-analysis-detail-title">' . esc_html( $analysis['description']['title'] ) . '</h4>';
        $output .= '<div class="boss-seo-analysis-detail-score">';
        $output .= '<span class="boss-seo-analysis-detail-score-value">' . esc_html( $analysis['description']['score'] ) . '</span>';
        $output .= '<span class="boss-seo-analysis-detail-score-max">/' . esc_html( $analysis['description']['max_score'] ) . '</span>';
        $output .= '</div>';
        $output .= '<div class="boss-seo-analysis-detail-description">' . esc_html( $analysis['description']['description'] ) . '</div>';

        if ( ! empty( $analysis['description']['recommendations'] ) ) {
            $output .= '<div class="boss-seo-analysis-detail-recommendations">';
            $output .= '<h5>' . esc_html__( 'Recommandations :', 'boss-seo' ) . '</h5>';
            $output .= '<ul>';

            foreach ( $analysis['description']['recommendations'] as $recommendation ) {
                $output .= '<li>' . esc_html( $recommendation ) . '</li>';
            }

            $output .= '</ul>';
            $output .= '</div>';
        }

        $output .= '</div>';

        // Afficher les autres résultats
        $other_analyses = array( 'images', 'categories_tags', 'attributes', 'price', 'stock', 'reviews', 'sku', 'permalink' );

        foreach ( $other_analyses as $key ) {
            if ( isset( $analysis[$key] ) ) {
                $output .= '<div class="boss-seo-analysis-detail boss-seo-analysis-detail-' . esc_attr( $analysis[$key]['status'] ) . '">';
                $output .= '<h4 class="boss-seo-analysis-detail-title">' . esc_html( $analysis[$key]['title'] ) . '</h4>';
                $output .= '<div class="boss-seo-analysis-detail-score">';
                $output .= '<span class="boss-seo-analysis-detail-score-value">' . esc_html( $analysis[$key]['score'] ) . '</span>';
                $output .= '<span class="boss-seo-analysis-detail-score-max">/' . esc_html( $analysis[$key]['max_score'] ) . '</span>';
                $output .= '</div>';
                $output .= '<div class="boss-seo-analysis-detail-description">' . esc_html( $analysis[$key]['description'] ) . '</div>';

                if ( ! empty( $analysis[$key]['recommendations'] ) ) {
                    $output .= '<div class="boss-seo-analysis-detail-recommendations">';
                    $output .= '<h5>' . esc_html__( 'Recommandations :', 'boss-seo' ) . '</h5>';
                    $output .= '<ul>';

                    foreach ( $analysis[$key]['recommendations'] as $recommendation ) {
                        $output .= '<li>' . esc_html( $recommendation ) . '</li>';
                    }

                    $output .= '</ul>';
                    $output .= '</div>';
                }

                $output .= '</div>';
            }
        }

        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }
}
