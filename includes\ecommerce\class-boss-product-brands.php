<?php
/**
 * Classe pour la gestion des marques de produits.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour la gestion des marques de produits.
 *
 * Cette classe gère les marques de produits.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Product_Brands {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_product_brands_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_add_brand', array( $this, 'ajax_add_brand' ) );
        add_action( 'wp_ajax_boss_seo_edit_brand', array( $this, 'ajax_edit_brand' ) );
        add_action( 'wp_ajax_boss_seo_delete_brand', array( $this, 'ajax_delete_brand' ) );

        // Ajouter les actions pour les marques de produits
        add_action( 'init', array( $this, 'register_brand_taxonomy' ) );
        add_action( 'woocommerce_product_options_general_product_data', array( $this, 'add_brand_field' ) );
        add_action( 'woocommerce_process_product_meta', array( $this, 'save_brand_field' ) );

        // Ajouter les filtres pour les marques de produits
        add_filter( 'woocommerce_product_tabs', array( $this, 'add_brand_tab' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/brands',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_brands' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'add_brand' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/brands/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_brand' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'PUT',
                    'callback'            => array( $this, 'update_brand' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_brand' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour ajouter une marque.
     *
     * @since    1.2.0
     */
    public function ajax_add_brand() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['brand_name'] ) || empty( $_POST['brand_name'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Le nom de la marque est requis.', 'boss-seo' ) ) );
        }

        $brand_name = sanitize_text_field( $_POST['brand_name'] );
        $brand_description = isset( $_POST['brand_description'] ) ? sanitize_textarea_field( $_POST['brand_description'] ) : '';
        $brand_logo = isset( $_POST['brand_logo'] ) ? absint( $_POST['brand_logo'] ) : 0;

        // Ajouter la marque
        $result = $this->add_brand_term( $brand_name, $brand_description, $brand_logo );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Marque ajoutée avec succès.', 'boss-seo' ),
            'brand'   => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour modifier une marque.
     *
     * @since    1.2.0
     */
    public function ajax_edit_brand() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['brand_id'] ) || empty( $_POST['brand_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de la marque est requis.', 'boss-seo' ) ) );
        }

        if ( ! isset( $_POST['brand_name'] ) || empty( $_POST['brand_name'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Le nom de la marque est requis.', 'boss-seo' ) ) );
        }

        $brand_id = absint( $_POST['brand_id'] );
        $brand_name = sanitize_text_field( $_POST['brand_name'] );
        $brand_description = isset( $_POST['brand_description'] ) ? sanitize_textarea_field( $_POST['brand_description'] ) : '';
        $brand_logo = isset( $_POST['brand_logo'] ) ? absint( $_POST['brand_logo'] ) : 0;

        // Modifier la marque
        $result = $this->update_brand_term( $brand_id, $brand_name, $brand_description, $brand_logo );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Marque modifiée avec succès.', 'boss-seo' ),
            'brand'   => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour supprimer une marque.
     *
     * @since    1.2.0
     */
    public function ajax_delete_brand() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['brand_id'] ) || empty( $_POST['brand_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de la marque est requis.', 'boss-seo' ) ) );
        }

        $brand_id = absint( $_POST['brand_id'] );

        // Supprimer la marque
        $result = $this->delete_brand_term( $brand_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Marque supprimée avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les marques via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_brands( $request ) {
        $brands = $this->get_all_brands();

        return rest_ensure_response( $brands );
    }

    /**
     * Récupère une marque via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_brand( $request ) {
        $brand_id = $request['id'];

        $brand = $this->get_brand_by_id( $brand_id );

        if ( is_wp_error( $brand ) ) {
            return $brand;
        }

        return rest_ensure_response( $brand );
    }

    /**
     * Ajoute une marque via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function add_brand( $request ) {
        $params = $request->get_params();

        if ( ! isset( $params['name'] ) || empty( $params['name'] ) ) {
            return new WP_Error( 'missing_name', __( 'Le nom de la marque est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $brand_name = sanitize_text_field( $params['name'] );
        $brand_description = isset( $params['description'] ) ? sanitize_textarea_field( $params['description'] ) : '';
        $brand_logo = isset( $params['logo'] ) ? absint( $params['logo'] ) : 0;

        $result = $this->add_brand_term( $brand_name, $brand_description, $brand_logo );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Marque ajoutée avec succès.', 'boss-seo' ),
            'brand'   => $result,
        ) );
    }

    /**
     * Met à jour une marque via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_brand( $request ) {
        $brand_id = $request['id'];
        $params = $request->get_params();

        if ( ! isset( $params['name'] ) || empty( $params['name'] ) ) {
            return new WP_Error( 'missing_name', __( 'Le nom de la marque est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $brand_name = sanitize_text_field( $params['name'] );
        $brand_description = isset( $params['description'] ) ? sanitize_textarea_field( $params['description'] ) : '';
        $brand_logo = isset( $params['logo'] ) ? absint( $params['logo'] ) : 0;

        $result = $this->update_brand_term( $brand_id, $brand_name, $brand_description, $brand_logo );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Marque mise à jour avec succès.', 'boss-seo' ),
            'brand'   => $result,
        ) );
    }

    /**
     * Supprime une marque via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_brand( $request ) {
        $brand_id = $request['id'];

        $result = $this->delete_brand_term( $brand_id );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Marque supprimée avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Enregistre la taxonomie des marques.
     *
     * @since    1.2.0
     */
    public function register_brand_taxonomy() {
        $labels = array(
            'name'                       => _x( 'Marques', 'Taxonomy General Name', 'boss-seo' ),
            'singular_name'              => _x( 'Marque', 'Taxonomy Singular Name', 'boss-seo' ),
            'menu_name'                  => __( 'Marques', 'boss-seo' ),
            'all_items'                  => __( 'Toutes les marques', 'boss-seo' ),
            'parent_item'                => __( 'Marque parente', 'boss-seo' ),
            'parent_item_colon'          => __( 'Marque parente :', 'boss-seo' ),
            'new_item_name'              => __( 'Nouvelle marque', 'boss-seo' ),
            'add_new_item'               => __( 'Ajouter une nouvelle marque', 'boss-seo' ),
            'edit_item'                  => __( 'Modifier la marque', 'boss-seo' ),
            'update_item'                => __( 'Mettre à jour la marque', 'boss-seo' ),
            'view_item'                  => __( 'Voir la marque', 'boss-seo' ),
            'separate_items_with_commas' => __( 'Séparer les marques avec des virgules', 'boss-seo' ),
            'add_or_remove_items'        => __( 'Ajouter ou supprimer des marques', 'boss-seo' ),
            'choose_from_most_used'      => __( 'Choisir parmi les plus utilisées', 'boss-seo' ),
            'popular_items'              => __( 'Marques populaires', 'boss-seo' ),
            'search_items'               => __( 'Rechercher des marques', 'boss-seo' ),
            'not_found'                  => __( 'Non trouvée', 'boss-seo' ),
            'no_terms'                   => __( 'Aucune marque', 'boss-seo' ),
            'items_list'                 => __( 'Liste des marques', 'boss-seo' ),
            'items_list_navigation'      => __( 'Navigation de la liste des marques', 'boss-seo' ),
        );

        $args = array(
            'labels'                     => $labels,
            'hierarchical'               => true,
            'public'                     => true,
            'show_ui'                    => true,
            'show_admin_column'          => true,
            'show_in_nav_menus'          => true,
            'show_tagcloud'              => true,
            'show_in_rest'               => true,
            'rest_base'                  => 'product_brands',
            'rest_controller_class'      => 'WP_REST_Terms_Controller',
        );

        register_taxonomy( 'product_brand', array( 'product' ), $args );
    }

    /**
     * Ajoute le champ de marque dans l'éditeur de produit.
     *
     * @since    1.2.0
     */
    public function add_brand_field() {
        global $post;

        // Récupérer les marques
        $brands = $this->get_all_brands();

        // Récupérer la marque du produit
        $product_brand = get_post_meta( $post->ID, '_boss_product_brand', true );

        // Afficher le champ
        echo '<div class="options_group">';

        woocommerce_wp_select( array(
            'id'          => '_boss_product_brand',
            'label'       => __( 'Marque du produit', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Sélectionnez la marque du produit.', 'boss-seo' ),
            'options'     => $this->get_brands_options( $brands ),
            'value'       => $product_brand,
        ) );

        echo '</div>';
    }

    /**
     * Enregistre le champ de marque.
     *
     * @since    1.2.0
     * @param    int    $post_id    L'ID du produit.
     */
    public function save_brand_field( $post_id ) {
        $brand = isset( $_POST['_boss_product_brand'] ) ? sanitize_text_field( $_POST['_boss_product_brand'] ) : '';

        update_post_meta( $post_id, '_boss_product_brand', $brand );
    }

    /**
     * Ajoute un onglet de marque dans la page du produit.
     *
     * @since    1.2.0
     * @param    array    $tabs    Les onglets existants.
     * @return   array             Les onglets modifiés.
     */
    public function add_brand_tab( $tabs ) {
        global $post;

        // Récupérer la marque du produit
        $product_brand = get_post_meta( $post->ID, '_boss_product_brand', true );

        if ( ! empty( $product_brand ) ) {
            $brand = $this->get_brand_by_id( $product_brand );

            if ( ! is_wp_error( $brand ) ) {
                $tabs['brand'] = array(
                    'title'    => __( 'Marque', 'boss-seo' ),
                    'priority' => 50,
                    'callback' => array( $this, 'brand_tab_content' ),
                );
            }
        }

        return $tabs;
    }

    /**
     * Affiche le contenu de l'onglet de marque.
     *
     * @since    1.2.0
     */
    public function brand_tab_content() {
        global $post;

        // Récupérer la marque du produit
        $product_brand = get_post_meta( $post->ID, '_boss_product_brand', true );

        if ( ! empty( $product_brand ) ) {
            $brand = $this->get_brand_by_id( $product_brand );

            if ( ! is_wp_error( $brand ) ) {
                echo '<h2>' . esc_html( $brand['name'] ) . '</h2>';

                if ( ! empty( $brand['description'] ) ) {
                    echo '<div class="brand-description">' . wp_kses_post( $brand['description'] ) . '</div>';
                }

                if ( ! empty( $brand['logo'] ) ) {
                    echo '<div class="brand-logo">';
                    echo wp_get_attachment_image( $brand['logo'], 'medium' );
                    echo '</div>';
                }
            }
        }
    }

    /**
     * Récupère toutes les marques.
     *
     * @since    1.2.0
     * @return   array    Les marques.
     */
    private function get_all_brands() {
        $terms = get_terms( array(
            'taxonomy'   => 'product_brand',
            'hide_empty' => false,
        ) );

        if ( is_wp_error( $terms ) ) {
            return array();
        }

        $brands = array();

        foreach ( $terms as $term ) {
            $logo_id = get_term_meta( $term->term_id, 'logo', true );

            $brands[] = array(
                'id'          => $term->term_id,
                'name'        => $term->name,
                'slug'        => $term->slug,
                'description' => $term->description,
                'count'       => $term->count,
                'logo'        => $logo_id,
                'logo_url'    => $logo_id ? wp_get_attachment_url( $logo_id ) : '',
            );
        }

        return $brands;
    }

    /**
     * Récupère une marque par son ID.
     *
     * @since    1.2.0
     * @param    int       $brand_id    L'ID de la marque.
     * @return   array|WP_Error         La marque ou une erreur.
     */
    private function get_brand_by_id( $brand_id ) {
        $term = get_term( $brand_id, 'product_brand' );

        if ( is_wp_error( $term ) || ! $term ) {
            return new WP_Error( 'brand_not_found', __( 'Marque non trouvée.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        $logo_id = get_term_meta( $term->term_id, 'logo', true );

        return array(
            'id'          => $term->term_id,
            'name'        => $term->name,
            'slug'        => $term->slug,
            'description' => $term->description,
            'count'       => $term->count,
            'logo'        => $logo_id,
            'logo_url'    => $logo_id ? wp_get_attachment_url( $logo_id ) : '',
        );
    }

    /**
     * Ajoute une marque.
     *
     * @since    1.2.0
     * @param    string    $name         Le nom de la marque.
     * @param    string    $description  La description de la marque.
     * @param    int       $logo         L'ID du logo de la marque.
     * @return   array|WP_Error          La marque ajoutée ou une erreur.
     */
    private function add_brand_term( $name, $description = '', $logo = 0 ) {
        $term = wp_insert_term( $name, 'product_brand', array(
            'description' => $description,
        ) );

        if ( is_wp_error( $term ) ) {
            return $term;
        }

        $term_id = $term['term_id'];

        if ( $logo ) {
            update_term_meta( $term_id, 'logo', $logo );
        }

        return $this->get_brand_by_id( $term_id );
    }

    /**
     * Met à jour une marque.
     *
     * @since    1.2.0
     * @param    int       $brand_id     L'ID de la marque.
     * @param    string    $name         Le nom de la marque.
     * @param    string    $description  La description de la marque.
     * @param    int       $logo         L'ID du logo de la marque.
     * @return   array|WP_Error          La marque mise à jour ou une erreur.
     */
    private function update_brand_term( $brand_id, $name, $description = '', $logo = 0 ) {
        $term = get_term( $brand_id, 'product_brand' );

        if ( is_wp_error( $term ) || ! $term ) {
            return new WP_Error( 'brand_not_found', __( 'Marque non trouvée.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        $result = wp_update_term( $brand_id, 'product_brand', array(
            'name'        => $name,
            'description' => $description,
        ) );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        if ( $logo ) {
            update_term_meta( $brand_id, 'logo', $logo );
        } else {
            delete_term_meta( $brand_id, 'logo' );
        }

        return $this->get_brand_by_id( $brand_id );
    }

    /**
     * Supprime une marque.
     *
     * @since    1.2.0
     * @param    int       $brand_id    L'ID de la marque.
     * @return   bool|WP_Error          True si la marque a été supprimée, une erreur sinon.
     */
    private function delete_brand_term( $brand_id ) {
        $term = get_term( $brand_id, 'product_brand' );

        if ( is_wp_error( $term ) || ! $term ) {
            return new WP_Error( 'brand_not_found', __( 'Marque non trouvée.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        $result = wp_delete_term( $brand_id, 'product_brand' );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        if ( ! $result ) {
            return new WP_Error( 'delete_failed', __( 'La suppression de la marque a échoué.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return true;
    }

    /**
     * Récupère les options de marques pour un select.
     *
     * @since    1.2.0
     * @param    array    $brands    Les marques.
     * @return   array               Les options de marques.
     */
    private function get_brands_options( $brands ) {
        $options = array(
            '' => __( 'Sélectionnez une marque', 'boss-seo' ),
        );

        foreach ( $brands as $brand ) {
            $options[$brand['id']] = $brand['name'];
        }

        return $options;
    }
}
