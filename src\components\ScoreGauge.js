import { __ } from '@wordpress/i18n';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Dashicon } from '@wordpress/components';
import { useState } from '@wordpress/element';

/**
 * Composant de jauge pour afficher le score SEO
 */
const ScoreGauge = ({ score, maxScore = 100, size = 200, strokeWidth = 15 }) => {
  // Calcul des propriétés de la jauge
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const progress = (score / maxScore) * 100;
  const strokeDashoffset = circumference - (progress / 100) * circumference;
  
  // Déterminer la couleur en fonction du score
  const getColor = () => {
    if (progress >= 80) return 'boss-success';
    if (progress >= 50) return 'boss-warning';
    return 'boss-error';
  };
  
  // Texte d'évaluation en fonction du score
  const getEvaluation = () => {
    if (progress >= 80) return __('Excellent', 'boss-seo');
    if (progress >= 60) return __('Bon', 'boss-seo');
    if (progress >= 40) return __('Moyen', 'boss-seo');
    return __('À améliorer', 'boss-seo');
  };

  return (
    <Card className="boss-card boss-h-full">
      <CardHeader>
        <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Score SEO global', 'boss-seo')}
        </h3>
      </CardHeader>
      <CardBody>
        <div className="boss-flex boss-flex-col boss-items-center boss-justify-center boss-p-4">
          <div className="boss-relative boss-w-full boss-flex boss-justify-center">
            <svg
              className="boss-transform boss-rotate-[-90deg]"
              width={size}
              height={size}
            >
              {/* Cercle de fond */}
              <circle
                cx={size / 2}
                cy={size / 2}
                r={radius}
                strokeWidth={strokeWidth}
                stroke="#e5e7eb"
                fill="transparent"
              />
              {/* Cercle de progression */}
              <circle
                cx={size / 2}
                cy={size / 2}
                r={radius}
                strokeWidth={strokeWidth}
                stroke={`var(--boss-${getColor()})`}
                fill="transparent"
                strokeDasharray={circumference}
                strokeDashoffset={strokeDashoffset}
                strokeLinecap="round"
                className="boss-transition-all boss-duration-1000 boss-ease-out"
              />
            </svg>
            <div className="boss-absolute boss-inset-0 boss-flex boss-flex-col boss-items-center boss-justify-center">
              <span className="boss-text-4xl boss-font-bold boss-text-boss-dark">{score}</span>
              <span className="boss-text-sm boss-text-boss-gray">{__('sur', 'boss-seo')} {maxScore}</span>
              <span className={`boss-text-${getColor()} boss-font-medium boss-mt-2`}>
                {getEvaluation()}
              </span>
            </div>
          </div>
          
          <div className="boss-mt-6 boss-text-center">
            <p className="boss-text-boss-gray boss-mb-4">
              {__('Votre site est bien optimisé, mais quelques améliorations sont encore possibles.', 'boss-seo')}
            </p>
            <Button
              isPrimary
              className="boss-bg-boss-primary boss-hover:boss-bg-boss-primary-dark"
            >
              {__('Voir les recommandations', 'boss-seo')}
            </Button>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default ScoreGauge;
