import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  ToggleControl,
  Tooltip,
  Modal,
  SelectControl
} from '@wordpress/components';

const SchemaOverview = ({ 
  schemas, 
  schemaTypes, 
  onCreateSchema, 
  onEditSchema, 
  onDeleteSchema, 
  onToggleSchema 
}) => {
  // États
  const [showTypeSelector, setShowTypeSelector] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [schemaToDelete, setSchemaToDelete] = useState(null);
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  // Filtrer les schémas
  const filteredSchemas = schemas.filter(schema => {
    if (filterType !== 'all' && schema.type !== filterType) return false;
    if (filterStatus === 'active' && !schema.active) return false;
    if (filterStatus === 'inactive' && schema.active) return false;
    return true;
  });

  // Calculer les statistiques
  const stats = {
    total: schemas.length,
    active: schemas.filter(s => s.active).length,
    validated: schemas.filter(s => s.validated).length,
    byType: {}
  };

  // Calculer les statistiques par type
  schemaTypes.forEach(type => {
    stats.byType[type.id] = schemas.filter(s => s.type === type.id).length;
  });

  // Fonction pour obtenir les détails d'un type de schéma
  const getSchemaTypeDetails = (typeId) => {
    return schemaTypes.find(type => type.id === typeId) || {
      name: typeId,
      icon: 'admin-generic',
      color: 'boss-gray'
    };
  };

  // Fonction pour confirmer la suppression
  const confirmDelete = (schema) => {
    setSchemaToDelete(schema);
    setShowDeleteConfirm(true);
  };

  // Fonction pour effectuer la suppression
  const handleDelete = () => {
    onDeleteSchema(schemaToDelete.id);
    setShowDeleteConfirm(false);
    setSchemaToDelete(null);
  };

  return (
    <div>
      {/* Statistiques */}
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-4 boss-gap-6 boss-mb-6">
        <Card className="boss-card">
          <CardBody>
            <div className="boss-flex boss-justify-between boss-items-start">
              <div>
                <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                  {__('Total des schémas', 'boss-seo')}
                </h3>
                <div className="boss-flex boss-items-baseline">
                  <span className="boss-text-2xl boss-font-bold boss-mr-2">{stats.total}</span>
                </div>
              </div>
              <div className="boss-bg-boss-primary/10 boss-p-2 boss-rounded-lg">
                <Dashicon icon="admin-generic" className="boss-text-boss-primary boss-text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="boss-card">
          <CardBody>
            <div className="boss-flex boss-justify-between boss-items-start">
              <div>
                <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                  {__('Schémas actifs', 'boss-seo')}
                </h3>
                <div className="boss-flex boss-items-baseline">
                  <span className="boss-text-2xl boss-font-bold boss-mr-2">{stats.active}</span>
                  <span className="boss-text-boss-gray boss-text-sm">/ {stats.total}</span>
                </div>
              </div>
              <div className="boss-bg-boss-success/10 boss-p-2 boss-rounded-lg">
                <Dashicon icon="yes-alt" className="boss-text-boss-success boss-text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="boss-card">
          <CardBody>
            <div className="boss-flex boss-justify-between boss-items-start">
              <div>
                <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                  {__('Schémas validés', 'boss-seo')}
                </h3>
                <div className="boss-flex boss-items-baseline">
                  <span className="boss-text-2xl boss-font-bold boss-mr-2">{stats.validated}</span>
                  <span className="boss-text-boss-gray boss-text-sm">/ {stats.total}</span>
                </div>
              </div>
              <div className="boss-bg-boss-warning/10 boss-p-2 boss-rounded-lg">
                <Dashicon icon="shield" className="boss-text-boss-warning boss-text-xl" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="boss-card">
          <CardBody className="boss-flex boss-items-center boss-justify-center boss-h-full">
            <Button
              isPrimary
              className="boss-w-full boss-flex boss-items-center boss-justify-center"
              onClick={() => setShowTypeSelector(true)}
            >
              <Dashicon icon="plus" className="boss-mr-2" />
              {__('Ajouter un schéma', 'boss-seo')}
            </Button>
          </CardBody>
        </Card>
      </div>

      {/* Filtres et liste des schémas */}
      <Card className="boss-mb-6">
        <CardHeader className="boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Schémas structurés', 'boss-seo')}
            </h2>
            <div className="boss-flex boss-space-x-4">
              <SelectControl
                label=""
                value={filterType}
                options={[
                  { label: __('Tous les types', 'boss-seo'), value: 'all' },
                  ...schemaTypes.map(type => ({ 
                    label: type.name, 
                    value: type.id 
                  }))
                ]}
                onChange={setFilterType}
                className="boss-w-40"
              />
              <SelectControl
                label=""
                value={filterStatus}
                options={[
                  { label: __('Tous les statuts', 'boss-seo'), value: 'all' },
                  { label: __('Actifs', 'boss-seo'), value: 'active' },
                  { label: __('Inactifs', 'boss-seo'), value: 'inactive' }
                ]}
                onChange={setFilterStatus}
                className="boss-w-40"
              />
            </div>
          </div>
        </CardHeader>
        <CardBody className="boss-p-0">
          <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
            <thead className="boss-bg-gray-50">
              <tr>
                <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                  {__('Nom', 'boss-seo')}
                </th>
                <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                  {__('Type', 'boss-seo')}
                </th>
                <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                  {__('Utilisation', 'boss-seo')}
                </th>
                <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                  {__('Dernière mise à jour', 'boss-seo')}
                </th>
                <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                  {__('Statut', 'boss-seo')}
                </th>
                <th className="boss-px-6 boss-py-3 boss-text-right boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                  {__('Actions', 'boss-seo')}
                </th>
              </tr>
            </thead>
            <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
              {filteredSchemas.length === 0 ? (
                <tr>
                  <td colSpan="6" className="boss-px-6 boss-py-4 boss-text-center boss-text-boss-gray">
                    {__('Aucun schéma trouvé. Créez votre premier schéma structuré !', 'boss-seo')}
                  </td>
                </tr>
              ) : (
                filteredSchemas.map(schema => {
                  const typeDetails = getSchemaTypeDetails(schema.type);
                  return (
                    <tr key={schema.id} className="boss-hover:boss-bg-gray-50">
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-font-medium boss-text-boss-dark">{schema.name}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-flex boss-items-center">
                          <div className={`boss-bg-${typeDetails.color}/10 boss-p-1 boss-rounded boss-mr-2`}>
                            <Dashicon icon={typeDetails.icon} className={`boss-text-${typeDetails.color}`} />
                          </div>
                          <span>{typeDetails.name}</span>
                        </div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{schema.usage} {__('pages', 'boss-seo')}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{schema.lastUpdated}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-flex boss-items-center">
                          <ToggleControl
                            checked={schema.active}
                            onChange={() => onToggleSchema(schema.id)}
                          />
                          {schema.validated ? (
                            <Tooltip text={__('Schéma validé', 'boss-seo')}>
                              <div className="boss-ml-2 boss-text-boss-success">
                                <Dashicon icon="yes-alt" />
                              </div>
                            </Tooltip>
                          ) : (
                            <Tooltip text={__('Schéma non validé', 'boss-seo')}>
                              <div className="boss-ml-2 boss-text-boss-warning">
                                <Dashicon icon="warning" />
                              </div>
                            </Tooltip>
                          )}
                        </div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-right boss-text-sm boss-font-medium">
                        <div className="boss-flex boss-justify-end boss-space-x-2">
                          <Button
                            isSecondary
                            isSmall
                            onClick={() => onEditSchema(schema)}
                            icon="edit"
                          />
                          <Button
                            isSecondary
                            isSmall
                            onClick={() => confirmDelete(schema)}
                            icon="trash"
                          />
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </CardBody>
      </Card>

      {/* Modal de sélection de type de schéma */}
      {showTypeSelector && (
        <Modal
          title={__('Sélectionner un type de schéma', 'boss-seo')}
          onRequestClose={() => setShowTypeSelector(false)}
          className="boss-schema-type-selector-modal"
        >
          <div className="boss-p-4">
            <p className="boss-mb-4 boss-text-boss-gray">
              {__('Choisissez le type de schéma structuré que vous souhaitez créer :', 'boss-seo')}
            </p>
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-max-h-96 boss-overflow-y-auto">
              {schemaTypes.map(type => (
                <div
                  key={type.id}
                  className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-cursor-pointer boss-hover:boss-bg-gray-50 boss-transition-colors"
                  onClick={() => {
                    onCreateSchema(type.id);
                    setShowTypeSelector(false);
                  }}
                >
                  <div className="boss-flex boss-items-start">
                    <div className={`boss-bg-${type.color}/10 boss-p-3 boss-rounded-lg boss-mr-4`}>
                      <Dashicon icon={type.icon} className={`boss-text-${type.color} boss-text-2xl`} />
                    </div>
                    <div>
                      <h3 className="boss-text-lg boss-font-semibold boss-mb-1">{type.name}</h3>
                      <p className="boss-text-boss-gray boss-text-sm">{type.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Modal>
      )}

      {/* Modal de confirmation de suppression */}
      {showDeleteConfirm && (
        <Modal
          title={__('Confirmer la suppression', 'boss-seo')}
          onRequestClose={() => setShowDeleteConfirm(false)}
        >
          <div className="boss-p-4">
            <p className="boss-mb-4">
              {__('Êtes-vous sûr de vouloir supprimer le schéma', 'boss-seo')} <strong>{schemaToDelete.name}</strong> ?
              {schemaToDelete.usage > 0 && (
                <span className="boss-block boss-mt-2 boss-text-boss-error">
                  {__('Ce schéma est utilisé sur', 'boss-seo')} {schemaToDelete.usage} {__('pages.', 'boss-seo')}
                </span>
              )}
            </p>
            <div className="boss-flex boss-justify-end boss-space-x-3">
              <Button
                isSecondary
                onClick={() => setShowDeleteConfirm(false)}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                isDanger
                onClick={handleDelete}
              >
                {__('Supprimer', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default SchemaOverview;
