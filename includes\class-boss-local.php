<?php
/**
 * Classe principale pour le module SEO local.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe principale pour le module SEO local.
 *
 * Cette classe gère toutes les fonctionnalités du module SEO local.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Local {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_local_';

    /**
     * L'instance du tableau de bord local.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Dashboard    $dashboard    L'instance du tableau de bord local.
     */
    protected $dashboard;

    /**
     * L'instance de la gestion des emplacements.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Locations    $locations    L'instance de la gestion des emplacements.
     */
    protected $locations;

    /**
     * L'instance des informations d'entreprise.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Business_Info    $business_info    L'instance des informations d'entreprise.
     */
    protected $business_info;

    /**
     * L'instance du générateur de pages locales.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Page_Generator    $page_generator    L'instance du générateur de pages locales.
     */
    protected $page_generator;

    /**
     * L'instance des schémas structurés.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Schema    $schema    L'instance des schémas structurés.
     */
    protected $schema;

    /**
     * L'instance du suivi des classements locaux.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Rankings    $rankings    L'instance du suivi des classements locaux.
     */
    protected $rankings;

    /**
     * L'instance de l'analyse SEO locale.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Analyzer    $analyzer    L'instance de l'analyse SEO locale.
     */
    protected $analyzer;

    /**
     * L'instance de l'intégration Google My Business.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_GMB_Integration    $gmb_integration    L'instance de l'intégration Google My Business.
     */
    protected $gmb_integration;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->load_dependencies();
        $this->init_components();
    }

    /**
     * Charge les dépendances du module.
     *
     * @since    1.2.0
     * @access   private
     */
    private function load_dependencies() {
        // Charger les classes du module
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-dashboard.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-locations.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-business-info.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-page-generator.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-schema.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-schema-manager.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-rankings.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-analyzer.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-gmb-integration.php';

        // Charger la classe pour les variables d'environnement
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-seo-env.php';
    }

    /**
     * Initialise les composants du module.
     *
     * @since    1.2.0
     * @access   private
     */
    private function init_components() {
        $this->dashboard = new Boss_Local_Dashboard( $this->plugin_name, $this->version );
        $this->locations = new Boss_Local_Locations( $this->plugin_name, $this->version );
        $this->business_info = new Boss_Local_Business_Info( $this->plugin_name, $this->version );
        $this->page_generator = new Boss_Local_Page_Generator( $this->plugin_name, $this->version );
        $this->schema = new Boss_Local_Schema( $this->plugin_name, $this->version );
        $this->rankings = new Boss_Local_Rankings( $this->plugin_name, $this->version );
        $this->analyzer = new Boss_Local_Analyzer( $this->plugin_name, $this->version );
        $this->gmb_integration = new Boss_Local_GMB_Integration( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Enregistrer les hooks des composants
        $this->dashboard->register_hooks();
        $this->locations->register_hooks();
        $this->business_info->register_hooks();
        $this->page_generator->register_hooks();
        $this->schema->register_hooks();
        $this->rankings->register_hooks();
        $this->analyzer->register_hooks();
        $this->gmb_integration->register_hooks();

        // Enregistrer les types de post personnalisés
        add_action( 'init', array( $this, 'register_post_types' ) );
        add_action( 'init', array( $this, 'register_taxonomies' ) );

        // Enregistrer les scripts et styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_public_scripts' ) );

        // Ajouter les menus d'administration
        add_action( 'admin_menu', array( $this, 'add_admin_menus' ) );

        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_local_settings', array( $this, 'ajax_get_local_settings' ) );
        add_action( 'wp_ajax_boss_seo_save_local_settings', array( $this, 'ajax_save_local_settings' ) );

        // Charger les variables d'environnement
        Boss_SEO_Env::load();
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Enregistrer les routes REST API des composants
        $this->dashboard->register_rest_routes();
        $this->locations->register_rest_routes();
        $this->business_info->register_rest_routes();
        $this->page_generator->register_rest_routes();
        $this->schema->register_rest_routes();
        $this->rankings->register_rest_routes();
        $this->analyzer->register_rest_routes();
        $this->gmb_integration->register_rest_routes();

        // Enregistrer les routes REST API du module
        register_rest_route(
            'boss-seo/v1',
            '/local/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_local_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_local_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Enregistre les types de post personnalisés.
     *
     * @since    1.2.0
     */
    public function register_post_types() {
        // Type de post pour les emplacements
        $labels = array(
            'name'                  => _x( 'Emplacements', 'Post type general name', 'boss-seo' ),
            'singular_name'         => _x( 'Emplacement', 'Post type singular name', 'boss-seo' ),
            'menu_name'             => _x( 'Emplacements', 'Admin Menu text', 'boss-seo' ),
            'name_admin_bar'        => _x( 'Emplacement', 'Add New on Toolbar', 'boss-seo' ),
            'add_new'               => __( 'Ajouter', 'boss-seo' ),
            'add_new_item'          => __( 'Ajouter un emplacement', 'boss-seo' ),
            'new_item'              => __( 'Nouvel emplacement', 'boss-seo' ),
            'edit_item'             => __( 'Modifier l\'emplacement', 'boss-seo' ),
            'view_item'             => __( 'Voir l\'emplacement', 'boss-seo' ),
            'all_items'             => __( 'Tous les emplacements', 'boss-seo' ),
            'search_items'          => __( 'Rechercher des emplacements', 'boss-seo' ),
            'parent_item_colon'     => __( 'Emplacement parent :', 'boss-seo' ),
            'not_found'             => __( 'Aucun emplacement trouvé.', 'boss-seo' ),
            'not_found_in_trash'    => __( 'Aucun emplacement trouvé dans la corbeille.', 'boss-seo' ),
            'featured_image'        => _x( 'Image de l\'emplacement', 'Overrides the "Featured Image" phrase', 'boss-seo' ),
            'set_featured_image'    => _x( 'Définir l\'image de l\'emplacement', 'Overrides the "Set featured image" phrase', 'boss-seo' ),
            'remove_featured_image' => _x( 'Supprimer l\'image de l\'emplacement', 'Overrides the "Remove featured image" phrase', 'boss-seo' ),
            'use_featured_image'    => _x( 'Utiliser comme image de l\'emplacement', 'Overrides the "Use as featured image" phrase', 'boss-seo' ),
            'archives'              => _x( 'Archives des emplacements', 'The post type archive label used in nav menus', 'boss-seo' ),
            'insert_into_item'      => _x( 'Insérer dans l\'emplacement', 'Overrides the "Insert into post" phrase', 'boss-seo' ),
            'uploaded_to_this_item' => _x( 'Téléversé sur cet emplacement', 'Overrides the "Uploaded to this post" phrase', 'boss-seo' ),
            'filter_items_list'     => _x( 'Filtrer la liste des emplacements', 'Screen reader text for the filter links heading on the post type listing screen', 'boss-seo' ),
            'items_list_navigation' => _x( 'Navigation de la liste des emplacements', 'Screen reader text for the pagination heading on the post type listing screen', 'boss-seo' ),
            'items_list'            => _x( 'Liste des emplacements', 'Screen reader text for the items list heading on the post type listing screen', 'boss-seo' ),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => false,
            'query_var'          => true,
            'rewrite'            => array( 'slug' => 'emplacement' ),
            'capability_type'    => 'post',
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => null,
            'supports'           => array( 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'       => true,
        );

        register_post_type( 'boss_local_location', $args );

        // Type de post pour les pages locales
        $labels = array(
            'name'                  => _x( 'Pages locales', 'Post type general name', 'boss-seo' ),
            'singular_name'         => _x( 'Page locale', 'Post type singular name', 'boss-seo' ),
            'menu_name'             => _x( 'Pages locales', 'Admin Menu text', 'boss-seo' ),
            'name_admin_bar'        => _x( 'Page locale', 'Add New on Toolbar', 'boss-seo' ),
            'add_new'               => __( 'Ajouter', 'boss-seo' ),
            'add_new_item'          => __( 'Ajouter une page locale', 'boss-seo' ),
            'new_item'              => __( 'Nouvelle page locale', 'boss-seo' ),
            'edit_item'             => __( 'Modifier la page locale', 'boss-seo' ),
            'view_item'             => __( 'Voir la page locale', 'boss-seo' ),
            'all_items'             => __( 'Toutes les pages locales', 'boss-seo' ),
            'search_items'          => __( 'Rechercher des pages locales', 'boss-seo' ),
            'parent_item_colon'     => __( 'Page locale parente :', 'boss-seo' ),
            'not_found'             => __( 'Aucune page locale trouvée.', 'boss-seo' ),
            'not_found_in_trash'    => __( 'Aucune page locale trouvée dans la corbeille.', 'boss-seo' ),
            'featured_image'        => _x( 'Image de la page locale', 'Overrides the "Featured Image" phrase', 'boss-seo' ),
            'set_featured_image'    => _x( 'Définir l\'image de la page locale', 'Overrides the "Set featured image" phrase', 'boss-seo' ),
            'remove_featured_image' => _x( 'Supprimer l\'image de la page locale', 'Overrides the "Remove featured image" phrase', 'boss-seo' ),
            'use_featured_image'    => _x( 'Utiliser comme image de la page locale', 'Overrides the "Use as featured image" phrase', 'boss-seo' ),
            'archives'              => _x( 'Archives des pages locales', 'The post type archive label used in nav menus', 'boss-seo' ),
            'insert_into_item'      => _x( 'Insérer dans la page locale', 'Overrides the "Insert into post" phrase', 'boss-seo' ),
            'uploaded_to_this_item' => _x( 'Téléversé sur cette page locale', 'Overrides the "Uploaded to this post" phrase', 'boss-seo' ),
            'filter_items_list'     => _x( 'Filtrer la liste des pages locales', 'Screen reader text for the filter links heading on the post type listing screen', 'boss-seo' ),
            'items_list_navigation' => _x( 'Navigation de la liste des pages locales', 'Screen reader text for the pagination heading on the post type listing screen', 'boss-seo' ),
            'items_list'            => _x( 'Liste des pages locales', 'Screen reader text for the items list heading on the post type listing screen', 'boss-seo' ),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => false,
            'query_var'          => true,
            'rewrite'            => array( 'slug' => 'page-locale' ),
            'capability_type'    => 'post',
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => null,
            'supports'           => array( 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'       => true,
        );

        register_post_type( 'boss_local_page', $args );
    }

    /**
     * Enregistre les taxonomies personnalisées.
     *
     * @since    1.2.0
     */
    public function register_taxonomies() {
        // Taxonomie pour les types d'emplacement
        $labels = array(
            'name'                       => _x( 'Types d\'emplacement', 'Taxonomy general name', 'boss-seo' ),
            'singular_name'              => _x( 'Type d\'emplacement', 'Taxonomy singular name', 'boss-seo' ),
            'search_items'               => __( 'Rechercher des types d\'emplacement', 'boss-seo' ),
            'popular_items'              => __( 'Types d\'emplacement populaires', 'boss-seo' ),
            'all_items'                  => __( 'Tous les types d\'emplacement', 'boss-seo' ),
            'parent_item'                => __( 'Type d\'emplacement parent', 'boss-seo' ),
            'parent_item_colon'          => __( 'Type d\'emplacement parent :', 'boss-seo' ),
            'edit_item'                  => __( 'Modifier le type d\'emplacement', 'boss-seo' ),
            'update_item'                => __( 'Mettre à jour le type d\'emplacement', 'boss-seo' ),
            'add_new_item'               => __( 'Ajouter un type d\'emplacement', 'boss-seo' ),
            'new_item_name'              => __( 'Nouveau nom de type d\'emplacement', 'boss-seo' ),
            'separate_items_with_commas' => __( 'Séparer les types d\'emplacement par des virgules', 'boss-seo' ),
            'add_or_remove_items'        => __( 'Ajouter ou supprimer des types d\'emplacement', 'boss-seo' ),
            'choose_from_most_used'      => __( 'Choisir parmi les types d\'emplacement les plus utilisés', 'boss-seo' ),
            'not_found'                  => __( 'Aucun type d\'emplacement trouvé.', 'boss-seo' ),
            'menu_name'                  => __( 'Types d\'emplacement', 'boss-seo' ),
        );

        $args = array(
            'hierarchical'          => true,
            'labels'                => $labels,
            'show_ui'               => true,
            'show_admin_column'     => true,
            'query_var'             => true,
            'rewrite'               => array( 'slug' => 'type-emplacement' ),
            'show_in_rest'          => true,
        );

        register_taxonomy( 'boss_local_location_type', array( 'boss_local_location' ), $args );

        // Taxonomie pour les modèles de page
        $labels = array(
            'name'                       => _x( 'Modèles de page', 'Taxonomy general name', 'boss-seo' ),
            'singular_name'              => _x( 'Modèle de page', 'Taxonomy singular name', 'boss-seo' ),
            'search_items'               => __( 'Rechercher des modèles de page', 'boss-seo' ),
            'popular_items'              => __( 'Modèles de page populaires', 'boss-seo' ),
            'all_items'                  => __( 'Tous les modèles de page', 'boss-seo' ),
            'parent_item'                => __( 'Modèle de page parent', 'boss-seo' ),
            'parent_item_colon'          => __( 'Modèle de page parent :', 'boss-seo' ),
            'edit_item'                  => __( 'Modifier le modèle de page', 'boss-seo' ),
            'update_item'                => __( 'Mettre à jour le modèle de page', 'boss-seo' ),
            'add_new_item'               => __( 'Ajouter un modèle de page', 'boss-seo' ),
            'new_item_name'              => __( 'Nouveau nom de modèle de page', 'boss-seo' ),
            'separate_items_with_commas' => __( 'Séparer les modèles de page par des virgules', 'boss-seo' ),
            'add_or_remove_items'        => __( 'Ajouter ou supprimer des modèles de page', 'boss-seo' ),
            'choose_from_most_used'      => __( 'Choisir parmi les modèles de page les plus utilisés', 'boss-seo' ),
            'not_found'                  => __( 'Aucun modèle de page trouvé.', 'boss-seo' ),
            'menu_name'                  => __( 'Modèles de page', 'boss-seo' ),
        );

        $args = array(
            'hierarchical'          => false,
            'labels'                => $labels,
            'show_ui'               => true,
            'show_admin_column'     => true,
            'query_var'             => true,
            'rewrite'               => array( 'slug' => 'modele-page' ),
            'show_in_rest'          => true,
        );

        register_taxonomy( 'boss_local_page_template', array( 'boss_local_page' ), $args );
    }

    /**
     * Enregistre les scripts et styles pour l'administration.
     *
     * @since    1.2.0
     * @param    string    $hook_suffix    Le suffixe du hook.
     */
    public function enqueue_admin_scripts( $hook_suffix ) {
        // Vérifier si nous sommes sur une page du plugin
        if ( strpos( $hook_suffix, 'boss-seo' ) === false ) {
            return;
        }

        // Enregistrer les styles
        wp_enqueue_style( 'boss-local-admin', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/css/boss-local-admin.css', array(), $this->version, 'all' );

        // Enregistrer les scripts
        wp_enqueue_script( 'boss-local-admin', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/js/boss-local-admin.js', array( 'jquery' ), $this->version, false );

        // Localiser les scripts
        wp_localize_script( 'boss-local-admin', 'boss_local_params', array(
            'ajax_url'   => admin_url( 'admin-ajax.php' ),
            'nonce'      => wp_create_nonce( 'boss_seo_local_nonce' ),
            'rest_url'   => rest_url( 'boss-seo/v1' ),
            'rest_nonce' => wp_create_nonce( 'wp_rest' ),
        ) );
    }

    /**
     * Enregistre les scripts et styles pour le front-end.
     *
     * @since    1.2.0
     */
    public function enqueue_public_scripts() {
        // Enregistrer les styles
        wp_enqueue_style( 'boss-local-public', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/css/boss-local-public.css', array(), $this->version, 'all' );

        // Enregistrer les scripts
        wp_enqueue_script( 'boss-local-public', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/js/boss-local-public.js', array( 'jquery' ), $this->version, false );

        // Localiser les scripts
        wp_localize_script( 'boss-local-public', 'boss_local_params', array(
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'nonce'    => wp_create_nonce( 'boss_seo_local_nonce' ),
        ) );
    }

    /**
     * Ajoute les menus d'administration.
     *
     * @since    1.2.0
     */
    public function add_admin_menus() {
        // Nous n'ajoutons plus de sous-menus ici car ils sont gérés par l'interface à onglets
        // dans la page "SEO local & e-commerce"
    }

    /**
     * Affiche la page d'administration principale.
     *
     * @since    1.2.0
     */
    public function render_admin_page() {
        include plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-local-admin-display.php';
    }

    /**
     * Affiche la page des paramètres.
     *
     * @since    1.2.0
     */
    public function render_settings_page() {
        include plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-local-settings-display.php';
    }

    /**
     * Gère les requêtes AJAX pour récupérer les paramètres.
     *
     * @since    1.2.0
     */
    public function ajax_get_local_settings() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $settings = $this->get_local_settings_data();

        wp_send_json_success( array(
            'message'  => __( 'Paramètres récupérés avec succès.', 'boss-seo' ),
            'settings' => $settings,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour enregistrer les paramètres.
     *
     * @since    1.2.0
     */
    public function ajax_save_local_settings() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['settings'] ) || ! is_array( $_POST['settings'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Les paramètres sont requis.', 'boss-seo' ) ) );
        }

        $settings = $_POST['settings'];

        // Enregistrer les paramètres
        $result = $this->save_local_settings_data( $settings );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Paramètres enregistrés avec succès.', 'boss-seo' ),
            'settings' => $result,
        ) );
    }

    /**
     * Récupère les paramètres via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_local_settings( $request ) {
        $settings = $this->get_local_settings_data();

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_local_settings( $request ) {
        $params = $request->get_params();

        // Enregistrer les paramètres
        $result = $this->save_local_settings_data( $params );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( $result );
    }

    /**
     * Récupère les paramètres du module.
     *
     * @since    1.2.0
     * @return   array    Les paramètres.
     */
    private function get_local_settings_data() {
        $settings = array(
            'enabled'                => get_option( $this->option_prefix . 'enabled', true ),
            'business_name'          => get_option( $this->option_prefix . 'business_name', get_bloginfo( 'name' ) ),
            'business_description'   => get_option( $this->option_prefix . 'business_description', get_bloginfo( 'description' ) ),
            'business_logo'          => get_option( $this->option_prefix . 'business_logo', '' ),
            'business_type'          => get_option( $this->option_prefix . 'business_type', 'LocalBusiness' ),
            'business_country'       => get_option( $this->option_prefix . 'business_country', '' ),
            'business_region'        => get_option( $this->option_prefix . 'business_region', '' ),
            'business_city'          => get_option( $this->option_prefix . 'business_city', '' ),
            'business_address'       => get_option( $this->option_prefix . 'business_address', '' ),
            'business_postal_code'   => get_option( $this->option_prefix . 'business_postal_code', '' ),
            'business_phone'         => get_option( $this->option_prefix . 'business_phone', '' ),
            'business_email'         => get_option( $this->option_prefix . 'business_email', '' ),
            'business_website'       => get_option( $this->option_prefix . 'business_website', home_url() ),
            'business_hours'         => get_option( $this->option_prefix . 'business_hours', array() ),
            'business_social_media'  => get_option( $this->option_prefix . 'business_social_media', array() ),
            'business_payment_types' => get_option( $this->option_prefix . 'business_payment_types', array() ),
            'business_price_range'   => get_option( $this->option_prefix . 'business_price_range', '' ),
            'google_maps_api_key'    => get_option( $this->option_prefix . 'google_maps_api_key', '' ),
            'google_places_api_key'  => get_option( $this->option_prefix . 'google_places_api_key', '' ),
            'enable_schema'          => get_option( $this->option_prefix . 'enable_schema', true ),
            'enable_locations'       => get_option( $this->option_prefix . 'enable_locations', true ),
            'enable_pages'           => get_option( $this->option_prefix . 'enable_pages', true ),
            'enable_rankings'        => get_option( $this->option_prefix . 'enable_rankings', true ),
            'enable_analyzer'        => get_option( $this->option_prefix . 'enable_analyzer', true ),
        );

        return $settings;
    }

    /**
     * Enregistre les paramètres du module.
     *
     * @since    1.2.0
     * @param    array     $settings    Les paramètres.
     * @return   array|WP_Error         Les paramètres enregistrés ou une erreur.
     */
    private function save_local_settings_data( $settings ) {
        // Paramètres généraux
        update_option( $this->option_prefix . 'enabled', isset( $settings['enabled'] ) ? (bool) $settings['enabled'] : true );

        // Informations de l'entreprise
        update_option( $this->option_prefix . 'business_name', isset( $settings['business_name'] ) ? sanitize_text_field( $settings['business_name'] ) : get_bloginfo( 'name' ) );
        update_option( $this->option_prefix . 'business_description', isset( $settings['business_description'] ) ? sanitize_textarea_field( $settings['business_description'] ) : get_bloginfo( 'description' ) );
        update_option( $this->option_prefix . 'business_logo', isset( $settings['business_logo'] ) ? absint( $settings['business_logo'] ) : '' );
        update_option( $this->option_prefix . 'business_type', isset( $settings['business_type'] ) ? sanitize_text_field( $settings['business_type'] ) : 'LocalBusiness' );
        update_option( $this->option_prefix . 'business_country', isset( $settings['business_country'] ) ? sanitize_text_field( $settings['business_country'] ) : '' );
        update_option( $this->option_prefix . 'business_region', isset( $settings['business_region'] ) ? sanitize_text_field( $settings['business_region'] ) : '' );
        update_option( $this->option_prefix . 'business_city', isset( $settings['business_city'] ) ? sanitize_text_field( $settings['business_city'] ) : '' );
        update_option( $this->option_prefix . 'business_address', isset( $settings['business_address'] ) ? sanitize_text_field( $settings['business_address'] ) : '' );
        update_option( $this->option_prefix . 'business_postal_code', isset( $settings['business_postal_code'] ) ? sanitize_text_field( $settings['business_postal_code'] ) : '' );
        update_option( $this->option_prefix . 'business_phone', isset( $settings['business_phone'] ) ? sanitize_text_field( $settings['business_phone'] ) : '' );
        update_option( $this->option_prefix . 'business_email', isset( $settings['business_email'] ) ? sanitize_email( $settings['business_email'] ) : '' );
        update_option( $this->option_prefix . 'business_website', isset( $settings['business_website'] ) ? esc_url_raw( $settings['business_website'] ) : home_url() );
        update_option( $this->option_prefix . 'business_hours', isset( $settings['business_hours'] ) && is_array( $settings['business_hours'] ) ? $settings['business_hours'] : array() );
        update_option( $this->option_prefix . 'business_social_media', isset( $settings['business_social_media'] ) && is_array( $settings['business_social_media'] ) ? array_map( 'esc_url_raw', $settings['business_social_media'] ) : array() );
        update_option( $this->option_prefix . 'business_payment_types', isset( $settings['business_payment_types'] ) && is_array( $settings['business_payment_types'] ) ? array_map( 'sanitize_text_field', $settings['business_payment_types'] ) : array() );
        update_option( $this->option_prefix . 'business_price_range', isset( $settings['business_price_range'] ) ? sanitize_text_field( $settings['business_price_range'] ) : '' );

        // Clés API
        update_option( $this->option_prefix . 'google_maps_api_key', isset( $settings['google_maps_api_key'] ) ? sanitize_text_field( $settings['google_maps_api_key'] ) : '' );
        update_option( $this->option_prefix . 'google_places_api_key', isset( $settings['google_places_api_key'] ) ? sanitize_text_field( $settings['google_places_api_key'] ) : '' );

        // Fonctionnalités
        update_option( $this->option_prefix . 'enable_schema', isset( $settings['enable_schema'] ) ? (bool) $settings['enable_schema'] : true );
        update_option( $this->option_prefix . 'enable_locations', isset( $settings['enable_locations'] ) ? (bool) $settings['enable_locations'] : true );
        update_option( $this->option_prefix . 'enable_pages', isset( $settings['enable_pages'] ) ? (bool) $settings['enable_pages'] : true );
        update_option( $this->option_prefix . 'enable_rankings', isset( $settings['enable_rankings'] ) ? (bool) $settings['enable_rankings'] : true );
        update_option( $this->option_prefix . 'enable_analyzer', isset( $settings['enable_analyzer'] ) ? (bool) $settings['enable_analyzer'] : true );

        return $this->get_local_settings_data();
    }
}
