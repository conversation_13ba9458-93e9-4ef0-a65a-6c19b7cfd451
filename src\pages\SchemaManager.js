import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  TabPanel,
  <PERSON><PERSON>,
  Spin<PERSON>,
  Notice
} from '@wordpress/components';
import { motion } from 'framer-motion';

// Composants
import SchemaOverview from '../components/schema/SchemaOverview';
import SchemaBuilder from '../components/schema/SchemaBuilder';
import SchemaAIGenerator from '../components/schema/SchemaAIGenerator';
import SchemaRules from '../components/schema/SchemaRules';

// Services
import SchemaService from '../services/SchemaService';

const SchemaManager = () => {
  // États
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedSchema, setSelectedSchema] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [schemas, setSchemas] = useState([]);
  const [schemaTypes, setSchemaTypes] = useState([]);
  const [error, setError] = useState(null);

  // Effet pour charger les données initiales
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Charger les types de schémas
        const typesResponse = await SchemaService.getSchemaTypes();

        // Transformer les types de schémas pour l'interface
        const formattedTypes = Object.entries(typesResponse).map(([id, data]) => ({
          id,
          name: data.label,
          description: data.description,
          icon: getIconForSchemaType(id),
          color: getColorForSchemaType(id)
        }));

        setSchemaTypes(formattedTypes);

        // Charger les schémas
        const schemasResponse = await SchemaService.getSchemas();

        // Transformer les schémas pour l'interface
        const formattedSchemas = schemasResponse.map(schema => ({
          id: schema.id,
          name: schema.title,
          type: schema.type,
          active: schema.active,
          validated: true, // Supposons que tous les schémas sont validés par défaut
          usage: 0, // Cette information sera calculée à partir des règles
          lastUpdated: schema.date_modified ? new Date(schema.date_modified).toLocaleDateString() : '',
          properties: schema.properties,
          rules: {
            postTypes: [],
            taxonomies: []
          }
        }));

        setSchemas(formattedSchemas);
      } catch (err) {
        console.error('Erreur lors du chargement des données:', err);
        setError(__('Erreur lors du chargement des données. Veuillez rafraîchir la page.', 'boss-seo'));

        // Utiliser des données fictives en cas d'erreur pour le développement
        setSchemaTypes([
          {
            id: 'Article',
            name: __('Article', 'boss-seo'),
            icon: 'text-page',
            description: __('Contenu éditorial, actualités, articles de blog', 'boss-seo'),
            color: 'boss-primary'
          },
          {
            id: 'Product',
            name: __('Produit', 'boss-seo'),
            icon: 'cart',
            description: __('Produits, services, offres commerciales', 'boss-seo'),
            color: 'boss-success'
          },
          {
            id: 'LocalBusiness',
            name: __('Entreprise locale', 'boss-seo'),
            icon: 'store',
            description: __('Commerces, restaurants, services locaux', 'boss-seo'),
            color: 'boss-warning'
          }
        ]);

        setSchemas([
          {
            id: 1,
            name: __('Article de blog', 'boss-seo'),
            type: 'Article',
            active: true,
            validated: true,
            usage: 0,
            lastUpdated: new Date().toLocaleDateString(),
            properties: {},
            rules: {
              postTypes: ['post'],
              taxonomies: []
            }
          }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Fonction pour obtenir l'icône en fonction du type de schéma
  const getIconForSchemaType = (type) => {
    const iconMap = {
      'Article': 'text-page',
      'Product': 'cart',
      'LocalBusiness': 'store',
      'Organization': 'building',
      'Person': 'admin-users',
      'Event': 'calendar',
      'Recipe': 'food',
      'FAQ': 'info',
      'HowTo': 'list-view',
      'WebPage': 'admin-site',
      'Custom': 'edit'
    };

    return iconMap[type] || 'admin-generic';
  };

  // Fonction pour obtenir la couleur en fonction du type de schéma
  const getColorForSchemaType = (type) => {
    const colorMap = {
      'Article': 'boss-primary',
      'Product': 'boss-success',
      'LocalBusiness': 'boss-warning',
      'Organization': 'boss-warning',
      'Person': 'boss-success',
      'Event': 'boss-error',
      'Recipe': 'boss-dark',
      'FAQ': 'boss-secondary',
      'HowTo': 'boss-primary',
      'WebPage': 'boss-secondary',
      'Custom': 'boss-dark'
    };

    return colorMap[type] || 'boss-gray';
  };

  // Fonction pour créer un nouveau schéma
  const handleCreateSchema = (type) => {
    setSelectedSchema({
      id: null,
      name: '',
      type: type,
      active: true,
      validated: false,
      usage: 0,
      lastUpdated: new Date().toISOString().split('T')[0],
      properties: {},
      rules: {
        postTypes: [],
        taxonomies: []
      }
    });
    setActiveTab('builder');
  };

  // Fonction pour éditer un schéma existant
  const handleEditSchema = async (schema) => {
    setIsLoading(true);

    try {
      // Récupérer les détails complets du schéma
      const schemaDetails = await SchemaService.getSchema(schema.id);

      // Transformer les données pour l'interface
      const formattedSchema = {
        id: schemaDetails.id,
        name: schemaDetails.title,
        type: schemaDetails.type,
        active: schemaDetails.active,
        validated: true,
        usage: 0,
        lastUpdated: schemaDetails.date_modified ? new Date(schemaDetails.date_modified).toLocaleDateString() : '',
        properties: schemaDetails.properties,
        rules: {
          postTypes: [],
          taxonomies: []
        }
      };

      setSelectedSchema(formattedSchema);
      setActiveTab('builder');
    } catch (err) {
      console.error('Erreur lors de la récupération des détails du schéma:', err);
      setError(__('Erreur lors de la récupération des détails du schéma.', 'boss-seo'));

      // Fallback en cas d'erreur
      setSelectedSchema(schema);
      setActiveTab('builder');
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour sauvegarder un schéma
  const handleSaveSchema = async (schema) => {
    setIsLoading(true);
    setError(null);

    try {
      // Préparer les données pour l'API
      const schemaData = {
        title: schema.name,
        type: schema.type,
        properties: schema.properties,
        active: schema.active
      };

      let response;

      if (schema.id) {
        // Mise à jour d'un schéma existant
        response = await SchemaService.updateSchema(schema.id, schemaData);
      } else {
        // Création d'un nouveau schéma
        response = await SchemaService.createSchema(schemaData);
      }

      // Mettre à jour la liste des schémas
      const updatedSchemas = await SchemaService.getSchemas();

      // Transformer les schémas pour l'interface
      const formattedSchemas = updatedSchemas.map(s => ({
        id: s.id,
        name: s.title,
        type: s.type,
        active: s.active,
        validated: true,
        usage: 0,
        lastUpdated: s.date_modified ? new Date(s.date_modified).toLocaleDateString() : '',
        properties: s.properties,
        rules: {
          postTypes: [],
          taxonomies: []
        }
      }));

      setSchemas(formattedSchemas);
      setActiveTab('overview');
      setSelectedSchema(null);
    } catch (err) {
      console.error('Erreur lors de la sauvegarde du schéma:', err);
      setError(__('Erreur lors de la sauvegarde du schéma.', 'boss-seo'));
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour supprimer un schéma
  const handleDeleteSchema = async (schemaId) => {
    setIsLoading(true);
    setError(null);

    try {
      // Supprimer le schéma
      await SchemaService.deleteSchema(schemaId);

      // Mettre à jour la liste des schémas
      setSchemas(schemas.filter(s => s.id !== schemaId));
    } catch (err) {
      console.error('Erreur lors de la suppression du schéma:', err);
      setError(__('Erreur lors de la suppression du schéma.', 'boss-seo'));
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour activer/désactiver un schéma
  const handleToggleSchema = async (schemaId) => {
    setIsLoading(true);
    setError(null);

    try {
      // Trouver le schéma
      const schema = schemas.find(s => s.id === schemaId);

      if (!schema) {
        throw new Error('Schéma non trouvé');
      }

      // Inverser l'état actif
      const newActiveState = !schema.active;

      // Mettre à jour le schéma
      await SchemaService.toggleSchema(schemaId, newActiveState);

      // Mettre à jour l'état local
      setSchemas(schemas.map(s => {
        if (s.id === schemaId) {
          return { ...s, active: newActiveState };
        }
        return s;
      }));
    } catch (err) {
      console.error('Erreur lors de l\'activation/désactivation du schéma:', err);
      setError(__('Erreur lors de l\'activation/désactivation du schéma.', 'boss-seo'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Schémas structurés', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Gérez les données structurées de votre site pour améliorer votre visibilité dans les résultats de recherche', 'boss-seo')}
          </p>
        </div>

        {error && (
          <Notice status="error" isDismissible={false} className="boss-mb-6">
            {error}
          </Notice>
        )}

        {isLoading ? (
          <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
            <Spinner />
          </div>
        ) : (
          <TabPanel
            className="boss-mb-6"
            activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
            tabs={[
              {
                name: 'overview',
                title: __('Vue d\'ensemble', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'builder',
                title: __('Générateur de schéma', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'ai-generator',
                title: __('Générateur IA', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'rules',
                title: __('Règles d\'application', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              }
            ]}
            onSelect={(tabName) => setActiveTab(tabName)}
          >
            {(tab) => {
              if (tab.name === 'overview') {
                return (
                  <SchemaOverview
                    schemas={schemas}
                    schemaTypes={schemaTypes}
                    onCreateSchema={handleCreateSchema}
                    onEditSchema={handleEditSchema}
                    onDeleteSchema={handleDeleteSchema}
                    onToggleSchema={handleToggleSchema}
                  />
                );
              } else if (tab.name === 'builder') {
                return (
                  <SchemaBuilder
                    schema={selectedSchema}
                    schemaTypes={schemaTypes}
                    onSave={handleSaveSchema}
                    onCancel={() => {
                      setSelectedSchema(null);
                      setActiveTab('overview');
                    }}
                  />
                );
              } else if (tab.name === 'ai-generator') {
                return (
                  <SchemaAIGenerator
                    schemaTypes={schemaTypes}
                    onGenerate={(schema) => {
                      setSelectedSchema(schema);
                      setActiveTab('builder');
                    }}
                  />
                );
              } else if (tab.name === 'rules') {
                return (
                  <SchemaRules
                    schema={selectedSchema}
                    schemas={schemas}
                    onSave={handleSaveSchema}
                  />
                );
              }
            }}
          </TabPanel>
        )}
      </div>
    </div>
  );
};

export default SchemaManager;
