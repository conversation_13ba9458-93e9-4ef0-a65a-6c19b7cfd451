/**
 * Service pour l'optimisation des médias
 *
 * Gère les communications avec l'API pour les fonctionnalités d'optimisation des médias
 */

import apiFetch from '@wordpress/api-fetch';

class MediaOptimizationService {
  /**
   * Récupère la liste des médias avec statistiques
   *
   * @param {number} page - Numéro de page
   * @param {number} perPage - Nombre d'éléments par page
   * @param {Object} filters - Filtres à appliquer
   * @returns {Promise} Promesse contenant la liste des médias et les statistiques
   */
  async getMediaList(page = 1, perPage = 20, filters = {}) {
    try {
      let path = `/boss-seo/v1/media/list?page=${page}&per_page=${perPage}`;

      // Ajouter les filtres à l'URL
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          path += `&${key}=${encodeURIComponent(filters[key])}`;
        }
      });

      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de la liste des médias:', error);
      throw error;
    }
  }

  /**
   * Récupère la liste des médias
   *
   * @param {number} page - Numéro de page
   * @param {number} perPage - Nombre d'éléments par page
   * @param {Object} filters - Filtres à appliquer
   * @returns {Promise} Promesse contenant la liste des médias
   */
  async getMedia(page = 1, perPage = 20, filters = {}) {
    try {
      let path = `/boss-seo/v1/media?page=${page}&per_page=${perPage}`;

      // Ajouter les filtres à l'URL
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          path += `&${key}=${encodeURIComponent(filters[key])}`;
        }
      });

      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des médias:', error);
      throw error;
    }
  }

  /**
   * Récupère les statistiques des médias
   *
   * @returns {Promise} Promesse contenant les statistiques des médias
   */
  async getMediaStats() {
    try {
      const path = '/boss-seo/v1/media/stats';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques des médias:', error);
      throw error;
    }
  }

  /**
   * Optimise une image
   *
   * @param {number} id - ID de l'image
   * @param {Object} options - Options d'optimisation
   * @returns {Promise} Promesse contenant le résultat de l'optimisation
   */
  async optimizeImage(id, options = {}) {
    try {
      const path = '/boss-seo/v1/media/optimize';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          id,
          options
        }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'optimisation de l\'image:', error);
      throw error;
    }
  }

  /**
   * Optimise plusieurs images
   *
   * @param {Array} ids - Liste des IDs des images
   * @param {Object} options - Options d'optimisation
   * @returns {Promise} Promesse contenant le résultat de l'optimisation
   */
  async optimizeImages(ids, options = {}) {
    try {
      const path = '/boss-seo/v1/media/optimize-bulk';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          ids,
          options
        }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'optimisation des images:', error);
      throw error;
    }
  }

  /**
   * Génère un texte alternatif pour une image
   *
   * @param {number} id - ID de l'image
   * @returns {Promise} Promesse contenant le texte alternatif généré
   */
  async generateAltText(id) {
    try {
      const path = '/boss-seo/v1/media/generate-alt';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { id }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la génération du texte alternatif:', error);
      throw error;
    }
  }

  /**
   * Génère des textes alternatifs pour plusieurs images
   *
   * @param {Array} ids - Liste des IDs des images
   * @returns {Promise} Promesse contenant les textes alternatifs générés
   */
  async generateAltTexts(ids) {
    try {
      const path = '/boss-seo/v1/media/generate-alt-bulk';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { ids }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la génération des textes alternatifs:', error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres d'optimisation des médias
   *
   * @returns {Promise} Promesse contenant les paramètres d'optimisation
   */
  async getMediaSettings() {
    try {
      const path = '/boss-seo/v1/media/settings';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres d\'optimisation des médias:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres d'optimisation des médias
   *
   * @param {Object} settings - Nouveaux paramètres d'optimisation
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async saveMediaSettings(settings) {
    try {
      const path = '/boss-seo/v1/media/settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { settings }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres d\'optimisation des médias:', error);
      throw error;
    }
  }

  /**
   * Analyse les problèmes des médias
   *
   * @returns {Promise} Promesse contenant les problèmes détectés
   */
  async analyzeMediaIssues() {
    try {
      const path = '/boss-seo/v1/media/analyze';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'analyse des problèmes des médias:', error);
      throw error;
    }
  }
}

export default new MediaOptimizationService();
