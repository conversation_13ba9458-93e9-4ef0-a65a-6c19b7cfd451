<?php
/**
 * Classe pour le suivi des positions locales.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour le suivi des positions locales.
 *
 * Cette classe gère toutes les fonctionnalités liées au suivi des positions locales.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_Rankings {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_local_rankings_';

    /**
     * Le nom de la table personnalisée pour les mots-clés.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $keywords_table    Le nom de la table des mots-clés.
     */
    protected $keywords_table;

    /**
     * Le nom de la table personnalisée pour les positions.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $positions_table    Le nom de la table des positions.
     */
    protected $positions_table;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        global $wpdb;

        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->keywords_table = $wpdb->prefix . 'boss_local_keywords';
        $this->positions_table = $wpdb->prefix . 'boss_local_positions';
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Créer les tables personnalisées lors de l'activation du plugin
        register_activation_hook( $this->plugin_name, array( $this, 'create_tables' ) );

        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_add_keyword', array( $this, 'ajax_add_keyword' ) );
        add_action( 'wp_ajax_boss_seo_delete_keyword', array( $this, 'ajax_delete_keyword' ) );
        add_action( 'wp_ajax_boss_seo_get_keywords', array( $this, 'ajax_get_keywords' ) );
        add_action( 'wp_ajax_boss_seo_get_positions', array( $this, 'ajax_get_positions' ) );
        add_action( 'wp_ajax_boss_seo_update_positions', array( $this, 'ajax_update_positions' ) );
        add_action( 'wp_ajax_boss_seo_export_positions', array( $this, 'ajax_export_positions' ) );

        // Ajouter les actions planifiées
        add_action( 'boss_seo_update_positions_cron', array( $this, 'update_positions_cron' ) );

        // Planifier la mise à jour des positions
        if ( ! wp_next_scheduled( 'boss_seo_update_positions_cron' ) ) {
            wp_schedule_event( time(), 'daily', 'boss_seo_update_positions_cron' );
        }
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/keywords',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_keywords' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'add_keyword' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/keywords/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_keyword' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'PUT',
                    'callback'            => array( $this, 'update_keyword' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_keyword' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/positions',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_positions' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'update_positions' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/positions/export',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'export_positions' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Crée les tables personnalisées pour les mots-clés et les positions.
     *
     * @since    1.2.0
     */
    public function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Table des mots-clés
        $sql = "CREATE TABLE $this->keywords_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            location_id mediumint(9) NOT NULL,
            keyword varchar(255) NOT NULL,
            search_volume int(11) DEFAULT 0,
            difficulty int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY location_id (location_id)
        ) $charset_collate;";

        // Table des positions
        $sql .= "CREATE TABLE $this->positions_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            keyword_id mediumint(9) NOT NULL,
            position int(11) DEFAULT 0,
            previous_position int(11) DEFAULT 0,
            date date DEFAULT CURRENT_DATE,
            url varchar(255) DEFAULT '',
            PRIMARY KEY  (id),
            KEY keyword_id (keyword_id),
            KEY date (date)
        ) $charset_collate;";

        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );
    }

    /**
     * Récupère les mots-clés via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_keywords( $request ) {
        $location_id = isset( $request['location_id'] ) ? absint( $request['location_id'] ) : 0;
        $search = isset( $request['search'] ) ? sanitize_text_field( $request['search'] ) : '';

        $keywords = $this->get_all_keywords( $location_id, $search );

        return rest_ensure_response( $keywords );
    }

    /**
     * Récupère un mot-clé spécifique via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_keyword( $request ) {
        $keyword_id = $request['id'];

        $keyword = $this->get_keyword_by_id( $keyword_id );

        if ( ! $keyword ) {
            return new WP_Error( 'keyword_not_found', __( 'Mot-clé non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        return rest_ensure_response( $keyword );
    }

    /**
     * Ajoute un mot-clé via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function add_keyword( $request ) {
        $params = $request->get_params();

        // Vérifier les paramètres requis
        if ( ! isset( $params['location_id'] ) || empty( $params['location_id'] ) ) {
            return new WP_Error( 'missing_location_id', __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        if ( ! isset( $params['keyword'] ) || empty( $params['keyword'] ) ) {
            return new WP_Error( 'missing_keyword', __( 'Le mot-clé est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $location_id = absint( $params['location_id'] );
        $keyword = sanitize_text_field( $params['keyword'] );
        $search_volume = isset( $params['search_volume'] ) ? absint( $params['search_volume'] ) : 0;
        $difficulty = isset( $params['difficulty'] ) ? absint( $params['difficulty'] ) : 0;

        // Vérifier si l'emplacement existe
        $location = get_post( $location_id );
        if ( ! $location || $location->post_type !== 'boss_location' ) {
            return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Vérifier si le mot-clé existe déjà pour cet emplacement
        if ( $this->keyword_exists( $location_id, $keyword ) ) {
            return new WP_Error( 'keyword_exists', __( 'Ce mot-clé existe déjà pour cet emplacement.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Ajouter le mot-clé
        $keyword_id = $this->add_keyword_to_db( $location_id, $keyword, $search_volume, $difficulty );

        if ( ! $keyword_id ) {
            return new WP_Error( 'keyword_add_failed', __( 'L\'ajout du mot-clé a échoué.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        // Récupérer le mot-clé ajouté
        $keyword_data = $this->get_keyword_by_id( $keyword_id );

        return rest_ensure_response( $keyword_data );
    }

    /**
     * Met à jour un mot-clé via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_keyword( $request ) {
        $keyword_id = $request['id'];
        $params = $request->get_params();

        // Vérifier si le mot-clé existe
        $keyword = $this->get_keyword_by_id( $keyword_id );
        if ( ! $keyword ) {
            return new WP_Error( 'keyword_not_found', __( 'Mot-clé non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Mettre à jour le mot-clé
        $keyword_text = isset( $params['keyword'] ) ? sanitize_text_field( $params['keyword'] ) : $keyword['keyword'];
        $search_volume = isset( $params['search_volume'] ) ? absint( $params['search_volume'] ) : $keyword['search_volume'];
        $difficulty = isset( $params['difficulty'] ) ? absint( $params['difficulty'] ) : $keyword['difficulty'];

        // Vérifier si le nouveau mot-clé existe déjà pour cet emplacement
        if ( $keyword_text !== $keyword['keyword'] && $this->keyword_exists( $keyword['location_id'], $keyword_text ) ) {
            return new WP_Error( 'keyword_exists', __( 'Ce mot-clé existe déjà pour cet emplacement.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $result = $this->update_keyword_in_db( $keyword_id, $keyword_text, $search_volume, $difficulty );

        if ( ! $result ) {
            return new WP_Error( 'keyword_update_failed', __( 'La mise à jour du mot-clé a échoué.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        // Récupérer le mot-clé mis à jour
        $keyword_data = $this->get_keyword_by_id( $keyword_id );

        return rest_ensure_response( $keyword_data );
    }

    /**
     * Supprime un mot-clé via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_keyword( $request ) {
        $keyword_id = $request['id'];

        // Vérifier si le mot-clé existe
        $keyword = $this->get_keyword_by_id( $keyword_id );
        if ( ! $keyword ) {
            return new WP_Error( 'keyword_not_found', __( 'Mot-clé non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Supprimer le mot-clé
        $result = $this->delete_keyword_from_db( $keyword_id );

        if ( ! $result ) {
            return new WP_Error( 'keyword_delete_failed', __( 'La suppression du mot-clé a échoué.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array(
            'deleted'  => true,
            'previous' => $keyword,
        ) );
    }

    /**
     * Récupère les positions via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_positions( $request ) {
        $location_id = isset( $request['location_id'] ) ? absint( $request['location_id'] ) : 0;
        $keyword_id = isset( $request['keyword_id'] ) ? absint( $request['keyword_id'] ) : 0;
        $start_date = isset( $request['start_date'] ) ? sanitize_text_field( $request['start_date'] ) : '';
        $end_date = isset( $request['end_date'] ) ? sanitize_text_field( $request['end_date'] ) : '';

        $positions = $this->get_all_positions( $location_id, $keyword_id, $start_date, $end_date );

        return rest_ensure_response( $positions );
    }

    /**
     * Met à jour les positions via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_positions( $request ) {
        $params = $request->get_params();

        // Vérifier les paramètres requis
        if ( ! isset( $params['location_id'] ) || empty( $params['location_id'] ) ) {
            return new WP_Error( 'missing_location_id', __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $location_id = absint( $params['location_id'] );
        $keyword_id = isset( $params['keyword_id'] ) ? absint( $params['keyword_id'] ) : 0;

        // Mettre à jour les positions
        $result = $this->update_positions_for_location( $location_id, $keyword_id );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( $result );
    }

    /**
     * Exporte les positions via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function export_positions( $request ) {
        $location_id = isset( $request['location_id'] ) ? absint( $request['location_id'] ) : 0;
        $keyword_id = isset( $request['keyword_id'] ) ? absint( $request['keyword_id'] ) : 0;
        $start_date = isset( $request['start_date'] ) ? sanitize_text_field( $request['start_date'] ) : '';
        $end_date = isset( $request['end_date'] ) ? sanitize_text_field( $request['end_date'] ) : '';
        $format = isset( $request['format'] ) ? sanitize_text_field( $request['format'] ) : 'csv';

        $positions = $this->get_all_positions( $location_id, $keyword_id, $start_date, $end_date );

        if ( $format === 'csv' ) {
            $csv = $this->generate_csv_file( $positions );

            return rest_ensure_response( array(
                'file_content' => $csv,
                'file_name'    => 'boss-seo-positions-' . date( 'Y-m-d' ) . '.csv',
                'file_type'    => 'text/csv',
            ) );
        } elseif ( $format === 'json' ) {
            return rest_ensure_response( $positions );
        } else {
            return new WP_Error( 'invalid_format', __( 'Format non pris en charge.', 'boss-seo' ), array( 'status' => 400 ) );
        }
    }

    /**
     * Gère les requêtes AJAX pour ajouter un mot-clé.
     *
     * @since    1.2.0
     */
    public function ajax_add_keyword() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ) ) );
        }

        if ( ! isset( $_POST['keyword'] ) || empty( $_POST['keyword'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Le mot-clé est requis.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );
        $keyword = sanitize_text_field( $_POST['keyword'] );
        $search_volume = isset( $_POST['search_volume'] ) ? absint( $_POST['search_volume'] ) : 0;
        $difficulty = isset( $_POST['difficulty'] ) ? absint( $_POST['difficulty'] ) : 0;

        // Vérifier si l'emplacement existe
        $location = get_post( $location_id );
        if ( ! $location || $location->post_type !== 'boss_location' ) {
            wp_send_json_error( array( 'message' => __( 'Emplacement non trouvé.', 'boss-seo' ) ) );
        }

        // Vérifier si le mot-clé existe déjà pour cet emplacement
        if ( $this->keyword_exists( $location_id, $keyword ) ) {
            wp_send_json_error( array( 'message' => __( 'Ce mot-clé existe déjà pour cet emplacement.', 'boss-seo' ) ) );
        }

        // Ajouter le mot-clé
        $keyword_id = $this->add_keyword_to_db( $location_id, $keyword, $search_volume, $difficulty );

        if ( ! $keyword_id ) {
            wp_send_json_error( array( 'message' => __( 'L\'ajout du mot-clé a échoué.', 'boss-seo' ) ) );
        }

        // Récupérer le mot-clé ajouté
        $keyword_data = $this->get_keyword_by_id( $keyword_id );

        wp_send_json_success( array(
            'message' => __( 'Mot-clé ajouté avec succès.', 'boss-seo' ),
            'keyword' => $keyword_data,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour supprimer un mot-clé.
     *
     * @since    1.2.0
     */
    public function ajax_delete_keyword() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['keyword_id'] ) || empty( $_POST['keyword_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du mot-clé est requis.', 'boss-seo' ) ) );
        }

        $keyword_id = absint( $_POST['keyword_id'] );

        // Vérifier si le mot-clé existe
        $keyword = $this->get_keyword_by_id( $keyword_id );
        if ( ! $keyword ) {
            wp_send_json_error( array( 'message' => __( 'Mot-clé non trouvé.', 'boss-seo' ) ) );
        }

        // Supprimer le mot-clé
        $result = $this->delete_keyword_from_db( $keyword_id );

        if ( ! $result ) {
            wp_send_json_error( array( 'message' => __( 'La suppression du mot-clé a échoué.', 'boss-seo' ) ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Mot-clé supprimé avec succès.', 'boss-seo' ),
            'keyword_id' => $keyword_id,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les mots-clés.
     *
     * @since    1.2.0
     */
    public function ajax_get_keywords() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        $location_id = isset( $_POST['location_id'] ) ? absint( $_POST['location_id'] ) : 0;
        $search = isset( $_POST['search'] ) ? sanitize_text_field( $_POST['search'] ) : '';

        $keywords = $this->get_all_keywords( $location_id, $search );

        wp_send_json_success( $keywords );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les positions.
     *
     * @since    1.2.0
     */
    public function ajax_get_positions() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        $location_id = isset( $_POST['location_id'] ) ? absint( $_POST['location_id'] ) : 0;
        $keyword_id = isset( $_POST['keyword_id'] ) ? absint( $_POST['keyword_id'] ) : 0;
        $start_date = isset( $_POST['start_date'] ) ? sanitize_text_field( $_POST['start_date'] ) : '';
        $end_date = isset( $_POST['end_date'] ) ? sanitize_text_field( $_POST['end_date'] ) : '';

        $positions = $this->get_all_positions( $location_id, $keyword_id, $start_date, $end_date );

        wp_send_json_success( $positions );
    }

    /**
     * Gère les requêtes AJAX pour mettre à jour les positions.
     *
     * @since    1.2.0
     */
    public function ajax_update_positions() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );
        $keyword_id = isset( $_POST['keyword_id'] ) ? absint( $_POST['keyword_id'] ) : 0;

        // Mettre à jour les positions
        $result = $this->update_positions_for_location( $location_id, $keyword_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Positions mises à jour avec succès.', 'boss-seo' ),
            'positions' => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour exporter les positions.
     *
     * @since    1.2.0
     */
    public function ajax_export_positions() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        $location_id = isset( $_POST['location_id'] ) ? absint( $_POST['location_id'] ) : 0;
        $keyword_id = isset( $_POST['keyword_id'] ) ? absint( $_POST['keyword_id'] ) : 0;
        $start_date = isset( $_POST['start_date'] ) ? sanitize_text_field( $_POST['start_date'] ) : '';
        $end_date = isset( $_POST['end_date'] ) ? sanitize_text_field( $_POST['end_date'] ) : '';
        $format = isset( $_POST['format'] ) ? sanitize_text_field( $_POST['format'] ) : 'csv';

        $positions = $this->get_all_positions( $location_id, $keyword_id, $start_date, $end_date );

        if ( $format === 'csv' ) {
            $csv = $this->generate_csv_file( $positions );

            wp_send_json_success( array(
                'file_content' => $csv,
                'file_name'    => 'boss-seo-positions-' . date( 'Y-m-d' ) . '.csv',
                'file_type'    => 'text/csv',
            ) );
        } elseif ( $format === 'json' ) {
            wp_send_json_success( $positions );
        } else {
            wp_send_json_error( array( 'message' => __( 'Format non pris en charge.', 'boss-seo' ) ) );
        }
    }

    /**
     * Met à jour les positions via une tâche planifiée.
     *
     * @since    1.2.0
     */
    public function update_positions_cron() {
        global $wpdb;

        // Récupérer tous les mots-clés
        $keywords = $wpdb->get_results( "SELECT * FROM $this->keywords_table", ARRAY_A );

        if ( ! $keywords ) {
            return;
        }

        foreach ( $keywords as $keyword ) {
            $this->update_position_for_keyword( $keyword['id'] );
        }
    }

    /**
     * Récupère tous les mots-clés.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @param    string    $search         Le terme de recherche.
     * @return   array                     Les mots-clés.
     */
    private function get_all_keywords( $location_id = 0, $search = '' ) {
        global $wpdb;

        $where = array();
        $where_values = array();

        if ( $location_id ) {
            $where[] = 'location_id = %d';
            $where_values[] = $location_id;
        }

        if ( $search ) {
            $where[] = 'keyword LIKE %s';
            $where_values[] = '%' . $wpdb->esc_like( $search ) . '%';
        }

        $where_clause = '';
        if ( ! empty( $where ) ) {
            $where_clause = 'WHERE ' . implode( ' AND ', $where );
        }

        $query = $wpdb->prepare(
            "SELECT * FROM $this->keywords_table $where_clause ORDER BY keyword ASC",
            $where_values
        );

        $keywords = $wpdb->get_results( $query, ARRAY_A );

        if ( ! $keywords ) {
            return array();
        }

        // Récupérer les dernières positions pour chaque mot-clé
        foreach ( $keywords as &$keyword ) {
            $position = $this->get_latest_position( $keyword['id'] );
            $keyword['position'] = $position ? $position['position'] : 0;
            $keyword['previous_position'] = $position ? $position['previous_position'] : 0;
            $keyword['date'] = $position ? $position['date'] : '';
            $keyword['url'] = $position ? $position['url'] : '';

            // Récupérer le nom de l'emplacement
            $location = get_post( $keyword['location_id'] );
            $keyword['location_name'] = $location ? $location->post_title : '';
        }

        return $keywords;
    }

    /**
     * Récupère un mot-clé par son ID.
     *
     * @since    1.2.0
     * @param    int       $keyword_id    L'ID du mot-clé.
     * @return   array|false              Le mot-clé ou false si non trouvé.
     */
    private function get_keyword_by_id( $keyword_id ) {
        global $wpdb;

        $keyword = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $this->keywords_table WHERE id = %d",
                $keyword_id
            ),
            ARRAY_A
        );

        if ( ! $keyword ) {
            return false;
        }

        // Récupérer la dernière position
        $position = $this->get_latest_position( $keyword_id );
        $keyword['position'] = $position ? $position['position'] : 0;
        $keyword['previous_position'] = $position ? $position['previous_position'] : 0;
        $keyword['date'] = $position ? $position['date'] : '';
        $keyword['url'] = $position ? $position['url'] : '';

        // Récupérer le nom de l'emplacement
        $location = get_post( $keyword['location_id'] );
        $keyword['location_name'] = $location ? $location->post_title : '';

        return $keyword;
    }

    /**
     * Vérifie si un mot-clé existe déjà pour un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @param    string    $keyword        Le mot-clé.
     * @return   bool                      True si le mot-clé existe, false sinon.
     */
    private function keyword_exists( $location_id, $keyword ) {
        global $wpdb;

        $exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $this->keywords_table WHERE location_id = %d AND keyword = %s",
                $location_id,
                $keyword
            )
        );

        return $exists > 0;
    }

    /**
     * Ajoute un mot-clé à la base de données.
     *
     * @since    1.2.0
     * @param    int       $location_id     L'ID de l'emplacement.
     * @param    string    $keyword         Le mot-clé.
     * @param    int       $search_volume   Le volume de recherche.
     * @param    int       $difficulty      La difficulté.
     * @return   int|false                  L'ID du mot-clé ajouté ou false si échec.
     */
    private function add_keyword_to_db( $location_id, $keyword, $search_volume = 0, $difficulty = 0 ) {
        global $wpdb;

        $result = $wpdb->insert(
            $this->keywords_table,
            array(
                'location_id'   => $location_id,
                'keyword'       => $keyword,
                'search_volume' => $search_volume,
                'difficulty'    => $difficulty,
                'created_at'    => current_time( 'mysql' ),
            ),
            array(
                '%d',
                '%s',
                '%d',
                '%d',
                '%s',
            )
        );

        if ( ! $result ) {
            return false;
        }

        return $wpdb->insert_id;
    }

    /**
     * Met à jour un mot-clé dans la base de données.
     *
     * @since    1.2.0
     * @param    int       $keyword_id      L'ID du mot-clé.
     * @param    string    $keyword         Le mot-clé.
     * @param    int       $search_volume   Le volume de recherche.
     * @param    int       $difficulty      La difficulté.
     * @return   bool                       True si succès, false si échec.
     */
    private function update_keyword_in_db( $keyword_id, $keyword, $search_volume = 0, $difficulty = 0 ) {
        global $wpdb;

        $result = $wpdb->update(
            $this->keywords_table,
            array(
                'keyword'       => $keyword,
                'search_volume' => $search_volume,
                'difficulty'    => $difficulty,
            ),
            array(
                'id' => $keyword_id,
            ),
            array(
                '%s',
                '%d',
                '%d',
            ),
            array(
                '%d',
            )
        );

        return $result !== false;
    }

    /**
     * Supprime un mot-clé de la base de données.
     *
     * @since    1.2.0
     * @param    int       $keyword_id    L'ID du mot-clé.
     * @return   bool                     True si succès, false si échec.
     */
    private function delete_keyword_from_db( $keyword_id ) {
        global $wpdb;

        // Supprimer les positions associées
        $wpdb->delete(
            $this->positions_table,
            array(
                'keyword_id' => $keyword_id,
            ),
            array(
                '%d',
            )
        );

        // Supprimer le mot-clé
        $result = $wpdb->delete(
            $this->keywords_table,
            array(
                'id' => $keyword_id,
            ),
            array(
                '%d',
            )
        );

        return $result !== false;
    }

    /**
     * Récupère toutes les positions.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @param    int       $keyword_id     L'ID du mot-clé.
     * @param    string    $start_date     La date de début.
     * @param    string    $end_date       La date de fin.
     * @return   array                     Les positions.
     */
    private function get_all_positions( $location_id = 0, $keyword_id = 0, $start_date = '', $end_date = '' ) {
        global $wpdb;

        $where = array();
        $where_values = array();

        if ( $keyword_id ) {
            $where[] = 'p.keyword_id = %d';
            $where_values[] = $keyword_id;
        } elseif ( $location_id ) {
            $where[] = 'k.location_id = %d';
            $where_values[] = $location_id;
        }

        if ( $start_date ) {
            $where[] = 'p.date >= %s';
            $where_values[] = $start_date;
        }

        if ( $end_date ) {
            $where[] = 'p.date <= %s';
            $where_values[] = $end_date;
        }

        $where_clause = '';
        if ( ! empty( $where ) ) {
            $where_clause = 'WHERE ' . implode( ' AND ', $where );
        }

        $query = $wpdb->prepare(
            "SELECT p.*, k.keyword, k.location_id FROM $this->positions_table p
            JOIN $this->keywords_table k ON p.keyword_id = k.id
            $where_clause
            ORDER BY p.date DESC, k.keyword ASC",
            $where_values
        );

        $positions = $wpdb->get_results( $query, ARRAY_A );

        if ( ! $positions ) {
            return array();
        }

        // Récupérer les noms des emplacements
        foreach ( $positions as &$position ) {
            $location = get_post( $position['location_id'] );
            $position['location_name'] = $location ? $location->post_title : '';
        }

        return $positions;
    }

    /**
     * Récupère la dernière position pour un mot-clé.
     *
     * @since    1.2.0
     * @param    int       $keyword_id    L'ID du mot-clé.
     * @return   array|false              La position ou false si non trouvée.
     */
    private function get_latest_position( $keyword_id ) {
        global $wpdb;

        $position = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $this->positions_table WHERE keyword_id = %d ORDER BY date DESC LIMIT 1",
                $keyword_id
            ),
            ARRAY_A
        );

        return $position;
    }

    /**
     * Met à jour les positions pour un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @param    int       $keyword_id     L'ID du mot-clé (optionnel).
     * @return   array|WP_Error            Les positions mises à jour ou une erreur.
     */
    private function update_positions_for_location( $location_id, $keyword_id = 0 ) {
        global $wpdb;

        // Vérifier si l'emplacement existe
        $location = get_post( $location_id );
        if ( ! $location || $location->post_type !== 'boss_location' ) {
            return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ) );
        }

        // Récupérer les mots-clés
        $where = array( 'location_id = %d' );
        $where_values = array( $location_id );

        if ( $keyword_id ) {
            $where[] = 'id = %d';
            $where_values[] = $keyword_id;
        }

        $where_clause = 'WHERE ' . implode( ' AND ', $where );

        $query = $wpdb->prepare(
            "SELECT * FROM $this->keywords_table $where_clause",
            $where_values
        );

        $keywords = $wpdb->get_results( $query, ARRAY_A );

        if ( ! $keywords ) {
            return new WP_Error( 'no_keywords', __( 'Aucun mot-clé trouvé pour cet emplacement.', 'boss-seo' ) );
        }

        $updated_positions = array();

        foreach ( $keywords as $keyword ) {
            $position = $this->update_position_for_keyword( $keyword['id'] );
            if ( $position ) {
                $updated_positions[] = array_merge( $position, array(
                    'keyword' => $keyword['keyword'],
                    'location_id' => $keyword['location_id'],
                    'location_name' => $location->post_title,
                ) );
            }
        }

        return $updated_positions;
    }

    /**
     * Met à jour la position pour un mot-clé.
     *
     * @since    1.2.0
     * @param    int       $keyword_id    L'ID du mot-clé.
     * @return   array|false              La position mise à jour ou false si échec.
     */
    private function update_position_for_keyword( $keyword_id ) {
        global $wpdb;

        // Récupérer le mot-clé
        $keyword = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $this->keywords_table WHERE id = %d",
                $keyword_id
            ),
            ARRAY_A
        );

        if ( ! $keyword ) {
            return false;
        }

        // Récupérer la dernière position
        $last_position = $this->get_latest_position( $keyword_id );
        $previous_position = $last_position ? $last_position['position'] : 0;

        // Simuler une recherche et récupérer la position
        $position = $this->simulate_search( $keyword['keyword'], $keyword['location_id'] );
        $url = $this->get_location_url( $keyword['location_id'] );

        // Enregistrer la nouvelle position
        $result = $wpdb->insert(
            $this->positions_table,
            array(
                'keyword_id'        => $keyword_id,
                'position'          => $position,
                'previous_position' => $previous_position,
                'date'              => current_time( 'mysql', false ),
                'url'               => $url,
            ),
            array(
                '%d',
                '%d',
                '%d',
                '%s',
                '%s',
            )
        );

        if ( ! $result ) {
            return false;
        }

        $position_id = $wpdb->insert_id;

        return array(
            'id'                => $position_id,
            'keyword_id'        => $keyword_id,
            'position'          => $position,
            'previous_position' => $previous_position,
            'date'              => current_time( 'mysql', false ),
            'url'               => $url,
        );
    }

    /**
     * Simule une recherche pour un mot-clé et récupère la position.
     *
     * @since    1.2.0
     * @param    string    $keyword       Le mot-clé.
     * @param    int       $location_id   L'ID de l'emplacement.
     * @return   int                      La position.
     */
    private function simulate_search( $keyword, $location_id ) {
        // Dans une version réelle, cette méthode ferait une requête à l'API Google Search Console
        // ou utiliserait un service tiers pour récupérer la position réelle.
        // Pour cette version de démonstration, nous générons une position aléatoire.

        // Récupérer la dernière position
        $last_position = $this->get_latest_position( $location_id );
        $previous_position = $last_position ? $last_position['position'] : 0;

        // Générer une position aléatoire proche de la précédente
        if ( $previous_position > 0 ) {
            $min = max( 1, $previous_position - 5 );
            $max = $previous_position + 5;
            $position = rand( $min, $max );
        } else {
            $position = rand( 1, 50 );
        }

        return $position;
    }

    /**
     * Récupère l'URL d'un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   string                    L'URL de l'emplacement.
     */
    private function get_location_url( $location_id ) {
        // Vérifier si une page locale existe pour cet emplacement
        $args = array(
            'post_type'      => 'page',
            'post_status'    => 'publish',
            'posts_per_page' => 1,
            'meta_query'     => array(
                array(
                    'key'     => 'boss_local_page_location_id',
                    'value'   => $location_id,
                    'compare' => '=',
                ),
            ),
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            $query->the_post();
            $url = get_permalink();
            wp_reset_postdata();
            return $url;
        }

        // Sinon, utiliser l'URL de l'emplacement
        return get_permalink( $location_id );
    }

    /**
     * Génère un fichier CSV à partir des positions.
     *
     * @since    1.2.0
     * @param    array     $positions    Les positions.
     * @return   string                  Le contenu du fichier CSV.
     */
    private function generate_csv_file( $positions ) {
        if ( empty( $positions ) ) {
            return '';
        }

        $headers = array(
            __( 'ID', 'boss-seo' ),
            __( 'Mot-clé', 'boss-seo' ),
            __( 'Emplacement', 'boss-seo' ),
            __( 'Position', 'boss-seo' ),
            __( 'Position précédente', 'boss-seo' ),
            __( 'Évolution', 'boss-seo' ),
            __( 'Date', 'boss-seo' ),
            __( 'URL', 'boss-seo' ),
        );

        $csv = implode( ',', $headers ) . "\n";

        foreach ( $positions as $position ) {
            $evolution = $position['previous_position'] > 0 ? $position['previous_position'] - $position['position'] : 0;

            $row = array(
                $position['id'],
                '"' . str_replace( '"', '""', $position['keyword'] ) . '"',
                '"' . str_replace( '"', '""', $position['location_name'] ) . '"',
                $position['position'],
                $position['previous_position'],
                $evolution,
                $position['date'],
                '"' . str_replace( '"', '""', $position['url'] ) . '"',
            );

            $csv .= implode( ',', $row ) . "\n";
        }

        return $csv;
    }
}