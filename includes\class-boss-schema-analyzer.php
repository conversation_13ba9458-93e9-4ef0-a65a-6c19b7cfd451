<?php
/**
 * Analyseur de Schema Markup pour Boss SEO
 *
 * @package Boss_SEO
 * @subpackage Technical_Analysis
 * @since 1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour analyser les Schema Markup d'un site
 */
class Boss_Schema_Analyzer {

    /**
     * Analyse le Schema Markup d'une URL
     *
     * @param string $url URL à analyser
     * @return array Résultats de l'analyse
     */
    public function analyze_url( $url ) {
        $html = $this->fetch_page_content( $url );
        
        if ( ! $html ) {
            return array(
                'success' => false,
                'message' => __( 'Impossible de récupérer le contenu de la page', 'boss-seo' )
            );
        }

        $schemas = $this->extract_schemas( $html );
        $issues = $this->validate_schemas( $schemas );
        $recommendations = $this->generate_recommendations( $schemas, $issues );

        return array(
            'success' => true,
            'url' => $url,
            'schemas_found' => count( $schemas ),
            'schemas' => $schemas,
            'issues' => $issues,
            'recommendations' => $recommendations,
            'score' => $this->calculate_schema_score( $schemas, $issues )
        );
    }

    /**
     * Récupère le contenu HTML d'une page
     *
     * @param string $url URL à analyser
     * @return string|false Contenu HTML ou false
     */
    private function fetch_page_content( $url ) {
        $response = wp_remote_get( $url, array(
            'timeout' => 30,
            'user-agent' => 'Boss SEO Schema Analyzer/1.0'
        ) );

        if ( is_wp_error( $response ) ) {
            return false;
        }

        $status_code = wp_remote_retrieve_response_code( $response );
        if ( $status_code !== 200 ) {
            return false;
        }

        return wp_remote_retrieve_body( $response );
    }

    /**
     * Extrait tous les schemas JSON-LD d'une page
     *
     * @param string $html Contenu HTML
     * @return array Schemas trouvés
     */
    private function extract_schemas( $html ) {
        $schemas = array();

        // Extraire les JSON-LD
        preg_match_all( '/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/is', $html, $matches );

        foreach ( $matches[1] as $index => $json_content ) {
            $decoded = json_decode( trim( $json_content ), true );
            
            if ( json_last_error() === JSON_ERROR_NONE && ! empty( $decoded ) ) {
                // Gérer les graphes multiples
                if ( isset( $decoded['@graph'] ) && is_array( $decoded['@graph'] ) ) {
                    foreach ( $decoded['@graph'] as $graph_item ) {
                        if ( isset( $graph_item['@type'] ) ) {
                            $schemas[] = array(
                                'type' => 'json-ld',
                                'schema_type' => $graph_item['@type'],
                                'data' => $graph_item,
                                'position' => $index + 1
                            );
                        }
                    }
                } elseif ( isset( $decoded['@type'] ) ) {
                    $schemas[] = array(
                        'type' => 'json-ld',
                        'schema_type' => $decoded['@type'],
                        'data' => $decoded,
                        'position' => $index + 1
                    );
                }
            }
        }

        // Extraire les microdata (optionnel)
        $microdata_schemas = $this->extract_microdata( $html );
        $schemas = array_merge( $schemas, $microdata_schemas );

        return $schemas;
    }

    /**
     * Extrait les microdata d'une page
     *
     * @param string $html Contenu HTML
     * @return array Schemas microdata
     */
    private function extract_microdata( $html ) {
        $schemas = array();
        
        // Rechercher les éléments avec itemscope et itemtype
        preg_match_all( '/itemscope[^>]*itemtype=["\']([^"\']+)["\'][^>]*>/i', $html, $matches );
        
        foreach ( $matches[1] as $index => $itemtype ) {
            $schema_type = basename( $itemtype );
            $schemas[] = array(
                'type' => 'microdata',
                'schema_type' => $schema_type,
                'data' => array( '@type' => $schema_type ),
                'position' => $index + 1
            );
        }

        return $schemas;
    }

    /**
     * Valide les schemas trouvés
     *
     * @param array $schemas Schemas à valider
     * @return array Issues trouvées
     */
    private function validate_schemas( $schemas ) {
        $issues = array();

        // Vérifier la présence de schemas essentiels
        $required_schemas = array( 'Organization', 'WebSite', 'WebPage' );
        $found_types = array_column( $schemas, 'schema_type' );

        foreach ( $required_schemas as $required_type ) {
            if ( ! in_array( $required_type, $found_types ) ) {
                $issues[] = array(
                    'type' => 'missing_schema',
                    'severity' => 'warning',
                    'schema_type' => $required_type,
                    'message' => sprintf( __( 'Schema %s manquant', 'boss-seo' ), $required_type ),
                    'description' => $this->get_schema_description( $required_type )
                );
            }
        }

        // Valider chaque schema individuellement
        foreach ( $schemas as $schema ) {
            $schema_issues = $this->validate_individual_schema( $schema );
            $issues = array_merge( $issues, $schema_issues );
        }

        // Vérifier les doublons
        $type_counts = array_count_values( $found_types );
        foreach ( $type_counts as $type => $count ) {
            if ( $count > 1 && in_array( $type, array( 'Organization', 'WebSite' ) ) ) {
                $issues[] = array(
                    'type' => 'duplicate_schema',
                    'severity' => 'error',
                    'schema_type' => $type,
                    'message' => sprintf( __( 'Schema %s dupliqué (%d fois)', 'boss-seo' ), $type, $count ),
                    'description' => __( 'Les schemas Organization et WebSite ne doivent apparaître qu\'une seule fois par page.', 'boss-seo' )
                );
            }
        }

        return $issues;
    }

    /**
     * Valide un schema individuel
     *
     * @param array $schema Schema à valider
     * @return array Issues trouvées
     */
    private function validate_individual_schema( $schema ) {
        $issues = array();
        $data = $schema['data'];
        $type = $schema['schema_type'];

        // Propriétés requises par type de schema
        $required_properties = $this->get_required_properties( $type );

        foreach ( $required_properties as $property ) {
            if ( ! isset( $data[ $property ] ) || empty( $data[ $property ] ) ) {
                $issues[] = array(
                    'type' => 'missing_property',
                    'severity' => 'error',
                    'schema_type' => $type,
                    'property' => $property,
                    'message' => sprintf( __( 'Propriété "%s" manquante dans le schema %s', 'boss-seo' ), $property, $type ),
                    'description' => $this->get_property_description( $type, $property )
                );
            }
        }

        // Vérifications spécifiques par type
        switch ( $type ) {
            case 'Organization':
                if ( isset( $data['logo'] ) && ! $this->is_valid_url( $data['logo'] ) ) {
                    $issues[] = array(
                        'type' => 'invalid_property',
                        'severity' => 'warning',
                        'schema_type' => $type,
                        'property' => 'logo',
                        'message' => __( 'URL du logo invalide', 'boss-seo' ),
                        'description' => __( 'L\'URL du logo doit être une URL complète et valide.', 'boss-seo' )
                    );
                }
                break;

            case 'Article':
                if ( isset( $data['datePublished'] ) && ! $this->is_valid_date( $data['datePublished'] ) ) {
                    $issues[] = array(
                        'type' => 'invalid_property',
                        'severity' => 'error',
                        'schema_type' => $type,
                        'property' => 'datePublished',
                        'message' => __( 'Format de date invalide', 'boss-seo' ),
                        'description' => __( 'La date doit être au format ISO 8601.', 'boss-seo' )
                    );
                }
                break;
        }

        return $issues;
    }

    /**
     * Retourne les propriétés requises pour un type de schema
     *
     * @param string $type Type de schema
     * @return array Propriétés requises
     */
    private function get_required_properties( $type ) {
        $properties = array(
            'Organization' => array( 'name', 'url' ),
            'WebSite' => array( 'name', 'url' ),
            'WebPage' => array( 'name', 'url' ),
            'Article' => array( 'headline', 'author', 'datePublished' ),
            'Person' => array( 'name' ),
            'Product' => array( 'name', 'description' ),
            'BreadcrumbList' => array( 'itemListElement' ),
            'LocalBusiness' => array( 'name', 'address' )
        );

        return isset( $properties[ $type ] ) ? $properties[ $type ] : array();
    }

    /**
     * Génère des recommandations basées sur l'analyse
     *
     * @param array $schemas Schemas trouvés
     * @param array $issues Issues trouvées
     * @return array Recommandations
     */
    private function generate_recommendations( $schemas, $issues ) {
        $recommendations = array();

        // Recommandations basées sur les issues
        $critical_issues = array_filter( $issues, function( $issue ) {
            return $issue['severity'] === 'error';
        } );

        if ( ! empty( $critical_issues ) ) {
            $recommendations[] = array(
                'priority' => 'high',
                'title' => __( 'Corriger les erreurs critiques', 'boss-seo' ),
                'description' => sprintf( __( '%d erreurs critiques détectées dans vos schemas.', 'boss-seo' ), count( $critical_issues ) ),
                'action' => __( 'Corrigez les propriétés manquantes et les formats invalides.', 'boss-seo' )
            );
        }

        // Recommandations pour schemas manquants
        $missing_schemas = array_filter( $issues, function( $issue ) {
            return $issue['type'] === 'missing_schema';
        } );

        if ( ! empty( $missing_schemas ) ) {
            $recommendations[] = array(
                'priority' => 'medium',
                'title' => __( 'Ajouter les schemas manquants', 'boss-seo' ),
                'description' => sprintf( __( '%d schemas recommandés sont manquants.', 'boss-seo' ), count( $missing_schemas ) ),
                'action' => __( 'Ajoutez les schemas Organization, WebSite et WebPage pour améliorer votre SEO.', 'boss-seo' )
            );
        }

        // Recommandations d'amélioration
        if ( count( $schemas ) > 0 && count( $critical_issues ) === 0 ) {
            $recommendations[] = array(
                'priority' => 'low',
                'title' => __( 'Optimiser les schemas existants', 'boss-seo' ),
                'description' => __( 'Vos schemas sont valides, mais peuvent être enrichis.', 'boss-seo' ),
                'action' => __( 'Ajoutez des propriétés optionnelles comme sameAs, logo, ou contactPoint pour enrichir vos données.', 'boss-seo' )
            );
        }

        return $recommendations;
    }

    /**
     * Calcule un score pour les schemas
     *
     * @param array $schemas Schemas trouvés
     * @param array $issues Issues trouvées
     * @return int Score sur 100
     */
    private function calculate_schema_score( $schemas, $issues ) {
        $base_score = 100;

        // Pénalités pour les erreurs
        $errors = array_filter( $issues, function( $issue ) {
            return $issue['severity'] === 'error';
        } );

        $warnings = array_filter( $issues, function( $issue ) {
            return $issue['severity'] === 'warning';
        } );

        $base_score -= count( $errors ) * 15; // -15 points par erreur
        $base_score -= count( $warnings ) * 5; // -5 points par avertissement

        // Bonus pour les schemas présents
        $schema_types = array_unique( array_column( $schemas, 'schema_type' ) );
        $important_schemas = array( 'Organization', 'WebSite', 'WebPage', 'Article', 'BreadcrumbList' );
        
        foreach ( $important_schemas as $important_schema ) {
            if ( in_array( $important_schema, $schema_types ) ) {
                $base_score += 5; // +5 points par schema important
            }
        }

        return max( 0, min( 100, $base_score ) );
    }

    /**
     * Vérifie si une URL est valide
     *
     * @param string $url URL à vérifier
     * @return bool True si valide
     */
    private function is_valid_url( $url ) {
        return filter_var( $url, FILTER_VALIDATE_URL ) !== false;
    }

    /**
     * Vérifie si une date est au format ISO 8601
     *
     * @param string $date Date à vérifier
     * @return bool True si valide
     */
    private function is_valid_date( $date ) {
        $datetime = DateTime::createFromFormat( 'c', $date );
        return $datetime !== false;
    }

    /**
     * Retourne la description d'un type de schema
     *
     * @param string $type Type de schema
     * @return string Description
     */
    private function get_schema_description( $type ) {
        $descriptions = array(
            'Organization' => __( 'Décrit votre organisation/entreprise avec ses informations de base.', 'boss-seo' ),
            'WebSite' => __( 'Décrit votre site web avec ses informations générales.', 'boss-seo' ),
            'WebPage' => __( 'Décrit la page web actuelle.', 'boss-seo' ),
            'Article' => __( 'Décrit un article de blog ou une actualité.', 'boss-seo' ),
            'BreadcrumbList' => __( 'Décrit la navigation en fil d\'Ariane.', 'boss-seo' )
        );

        return isset( $descriptions[ $type ] ) ? $descriptions[ $type ] : '';
    }

    /**
     * Retourne la description d'une propriété
     *
     * @param string $schema_type Type de schema
     * @param string $property Propriété
     * @return string Description
     */
    private function get_property_description( $schema_type, $property ) {
        $descriptions = array(
            'Organization' => array(
                'name' => __( 'Le nom de votre organisation.', 'boss-seo' ),
                'url' => __( 'L\'URL de votre site web.', 'boss-seo' ),
                'logo' => __( 'L\'URL de votre logo.', 'boss-seo' )
            ),
            'Article' => array(
                'headline' => __( 'Le titre de l\'article.', 'boss-seo' ),
                'author' => __( 'L\'auteur de l\'article.', 'boss-seo' ),
                'datePublished' => __( 'La date de publication.', 'boss-seo' )
            )
        );

        return isset( $descriptions[ $schema_type ][ $property ] ) ? $descriptions[ $schema_type ][ $property ] : '';
    }
}
