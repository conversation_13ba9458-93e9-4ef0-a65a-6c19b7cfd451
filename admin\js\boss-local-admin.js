/**
 * Scripts pour l'administration du module SEO Local.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin/js
 */

(function( $ ) {
    'use strict';

    /**
     * Toutes les fonctions JavaScript personnalisées pour l'administration du module SEO Local
     * doivent être définies dans cet objet.
     */
    var BossLocalAdmin = {

        /**
         * Initialise l'objet.
         */
        init: function() {
            // Initialiser les événements
            this.initEvents();
        },

        /**
         * Initialise les événements.
         */
        initEvents: function() {
            // Événements pour les emplacements
            this.initLocationEvents();

            // Événements pour les informations d'entreprise
            this.initBusinessInfoEvents();

            // Événements pour le générateur de pages
            this.initPageGeneratorEvents();

            // Événements pour les schémas
            this.initSchemaEvents();

            // Événements pour le suivi des positions
            this.initRankingsEvents();

            // Événements pour l'analyse
            this.initAnalysisEvents();
        },

        /**
         * Initialise les événements pour les emplacements.
         */
        initLocationEvents: function() {
            // Événements pour le formulaire d'ajout d'emplacement
            $('#boss-local-add-location-form').on('submit', this.handleAddLocation);

            // Événements pour le formulaire de modification d'emplacement
            $('#boss-local-edit-location-form').on('submit', this.handleEditLocation);

            // Événements pour la suppression d'emplacement
            $('.boss-local-delete-location').on('click', this.handleDeleteLocation);
        },

        /**
         * Initialise les événements pour les informations d'entreprise.
         */
        initBusinessInfoEvents: function() {
            // Événements pour le formulaire d'informations d'entreprise
            $('#boss-local-business-info-form').on('submit', this.handleSaveBusinessInfo);
        },

        /**
         * Initialise les événements pour le générateur de pages.
         */
        initPageGeneratorEvents: function() {
            // Événements pour le formulaire de génération de pages
            $('#boss-local-page-generator-form').on('submit', this.handleGeneratePages);
        },

        /**
         * Initialise les événements pour les schémas.
         */
        initSchemaEvents: function() {
            // Événements pour le formulaire d'ajout de schéma
            $('#boss-local-add-schema-form').on('submit', this.handleAddSchema);

            // Événements pour le formulaire de modification de schéma
            $('#boss-local-edit-schema-form').on('submit', this.handleEditSchema);

            // Événements pour la suppression de schéma
            $('.boss-local-delete-schema').on('click', this.handleDeleteSchema);
        },

        /**
         * Initialise les événements pour le suivi des positions.
         */
        initRankingsEvents: function() {
            // Événements pour le formulaire d'ajout de mot-clé
            $('#boss-local-add-keyword-form').on('submit', this.handleAddKeyword);

            // Événements pour le formulaire de modification de mot-clé
            $('#boss-local-edit-keyword-form').on('submit', this.handleEditKeyword);

            // Événements pour la suppression de mot-clé
            $('.boss-local-delete-keyword').on('click', this.handleDeleteKeyword);
        },

        /**
         * Initialise les événements pour l'analyse.
         */
        initAnalysisEvents: function() {
            // Événements pour le formulaire d'analyse
            $('#boss-local-analysis-form').on('submit', this.handleRunAnalysis);
        },

        /**
         * Gère l'ajout d'un emplacement.
         *
         * @param {Event} e L'événement.
         */
        handleAddLocation: function(e) {
            e.preventDefault();

            // Récupérer les données du formulaire
            var formData = $(this).serialize();

            // Ajouter le nonce
            formData += '&action=boss_seo_add_location&nonce=' + boss_local_params.nonce;

            // Envoyer la requête AJAX
            $.ajax({
                url: boss_local_params.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Afficher un message de succès
                        alert(response.data.message);

                        // Rediriger vers la liste des emplacements
                        window.location.href = boss_local_params.locations_url;
                    } else {
                        // Afficher un message d'erreur
                        alert(response.data.message);
                    }
                },
                error: function() {
                    // Afficher un message d'erreur
                    alert(boss_local_params.error_message);
                }
            });
        },

        /**
         * Gère la modification d'un emplacement.
         *
         * @param {Event} e L'événement.
         */
        handleEditLocation: function(e) {
            e.preventDefault();

            // Récupérer les données du formulaire
            var formData = $(this).serialize();

            // Ajouter le nonce
            formData += '&action=boss_seo_edit_location&nonce=' + boss_local_params.nonce;

            // Envoyer la requête AJAX
            $.ajax({
                url: boss_local_params.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Afficher un message de succès
                        alert(response.data.message);
                    } else {
                        // Afficher un message d'erreur
                        alert(response.data.message);
                    }
                },
                error: function() {
                    // Afficher un message d'erreur
                    alert(boss_local_params.error_message);
                }
            });
        },

        /**
         * Gère la suppression d'un emplacement.
         *
         * @param {Event} e L'événement.
         */
        handleDeleteLocation: function(e) {
            e.preventDefault();

            // Demander confirmation
            if (!confirm(boss_local_params.confirm_delete)) {
                return;
            }

            // Récupérer l'ID de l'emplacement
            var locationId = $(this).data('location-id');

            // Envoyer la requête AJAX
            $.ajax({
                url: boss_local_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'boss_seo_delete_location',
                    nonce: boss_local_params.nonce,
                    location_id: locationId
                },
                success: function(response) {
                    if (response.success) {
                        // Afficher un message de succès
                        alert(response.data.message);

                        // Rediriger vers la liste des emplacements
                        window.location.href = boss_local_params.locations_url;
                    } else {
                        // Afficher un message d'erreur
                        alert(response.data.message);
                    }
                },
                error: function() {
                    // Afficher un message d'erreur
                    alert(boss_local_params.error_message);
                }
            });
        }
    };

    // Initialiser l'objet lorsque le document est prêt
    $(document).ready(function() {
        BossLocalAdmin.init();
    });

})( jQuery );
