import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  CardFooter,
  Button,
  TextControl,
  TextareaControl,
  SelectControl,
  RadioControl,
  ToggleControl,
  Notice
} from '@wordpress/components';

/**
 * Composant de feedback et support
 * 
 * @param {Object} props - Propriétés du composant
 * @returns {React.ReactElement} Composant Feedback
 */
const Feedback = () => {
  // États pour le formulaire de signalement de bugs
  const [bugReport, setBugReport] = useState({
    title: '',
    description: '',
    module: '',
    severity: 'medium',
    steps: '',
    expectedBehavior: '',
    actualBehavior: '',
    browserInfo: true,
    wpInfo: true,
    pluginInfo: true,
    email: ''
  });
  
  // États pour le formulaire de suggestion
  const [featureSuggestion, setFeatureSuggestion] = useState({
    title: '',
    description: '',
    module: '',
    priority: 'medium',
    useCase: '',
    email: ''
  });
  
  // États pour le formulaire de satisfaction
  const [satisfaction, setSatisfaction] = useState({
    rating: 0,
    feedback: '',
    wouldRecommend: null,
    favoriteFeature: '',
    improvementSuggestion: '',
    email: ''
  });
  
  // État pour le formulaire actif
  const [activeForm, setActiveForm] = useState('bug');
  
  // État pour le message de succès
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  
  // Mettre à jour les champs du formulaire de bug
  const updateBugReport = (field, value) => {
    setBugReport({
      ...bugReport,
      [field]: value
    });
  };
  
  // Mettre à jour les champs du formulaire de suggestion
  const updateFeatureSuggestion = (field, value) => {
    setFeatureSuggestion({
      ...featureSuggestion,
      [field]: value
    });
  };
  
  // Mettre à jour les champs du formulaire de satisfaction
  const updateSatisfaction = (field, value) => {
    setSatisfaction({
      ...satisfaction,
      [field]: value
    });
  };
  
  // Soumettre le formulaire de bug
  const submitBugReport = () => {
    // Simuler l'envoi du formulaire
    setTimeout(() => {
      setSuccessMessage(__('Votre signalement de bug a été envoyé avec succès. Nous vous contacterons dès que possible.', 'boss-seo'));
      setShowSuccess(true);
      
      // Réinitialiser le formulaire
      setBugReport({
        title: '',
        description: '',
        module: '',
        severity: 'medium',
        steps: '',
        expectedBehavior: '',
        actualBehavior: '',
        browserInfo: true,
        wpInfo: true,
        pluginInfo: true,
        email: ''
      });
      
      // Masquer le message après 5 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 5000);
    }, 1000);
  };
  
  // Soumettre le formulaire de suggestion
  const submitFeatureSuggestion = () => {
    // Simuler l'envoi du formulaire
    setTimeout(() => {
      setSuccessMessage(__('Votre suggestion de fonctionnalité a été envoyée avec succès. Merci pour votre contribution !', 'boss-seo'));
      setShowSuccess(true);
      
      // Réinitialiser le formulaire
      setFeatureSuggestion({
        title: '',
        description: '',
        module: '',
        priority: 'medium',
        useCase: '',
        email: ''
      });
      
      // Masquer le message après 5 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 5000);
    }, 1000);
  };
  
  // Soumettre le formulaire de satisfaction
  const submitSatisfaction = () => {
    // Simuler l'envoi du formulaire
    setTimeout(() => {
      setSuccessMessage(__('Votre évaluation a été envoyée avec succès. Merci pour votre feedback !', 'boss-seo'));
      setShowSuccess(true);
      
      // Réinitialiser le formulaire
      setSatisfaction({
        rating: 0,
        feedback: '',
        wouldRecommend: null,
        favoriteFeature: '',
        improvementSuggestion: '',
        email: ''
      });
      
      // Masquer le message après 5 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 5000);
    }, 1000);
  };
  
  // Vérifier si le formulaire de bug est valide
  const isBugReportValid = () => {
    return (
      bugReport.title.trim() !== '' &&
      bugReport.description.trim() !== '' &&
      bugReport.module !== ''
    );
  };
  
  // Vérifier si le formulaire de suggestion est valide
  const isFeatureSuggestionValid = () => {
    return (
      featureSuggestion.title.trim() !== '' &&
      featureSuggestion.description.trim() !== '' &&
      featureSuggestion.module !== ''
    );
  };
  
  // Vérifier si le formulaire de satisfaction est valide
  const isSatisfactionValid = () => {
    return satisfaction.rating > 0;
  };
  
  // Options de modules
  const moduleOptions = [
    { label: __('-- Sélectionner un module --', 'boss-seo'), value: '' },
    { label: __('Dashboard', 'boss-seo'), value: 'dashboard' },
    { label: __('Optimisation de contenu', 'boss-seo'), value: 'content' },
    { label: __('Analyse technique', 'boss-seo'), value: 'technical' },
    { label: __('Schémas structurés', 'boss-seo'), value: 'schemas' },
    { label: __('Analytics', 'boss-seo'), value: 'analytics' },
    { label: __('SEO Local', 'boss-seo'), value: 'local' },
    { label: __('E-commerce', 'boss-seo'), value: 'ecommerce' },
    { label: __('Gestion technique', 'boss-seo'), value: 'technical-management' },
    { label: __('Rapports', 'boss-seo'), value: 'reports' },
    { label: __('Paramètres', 'boss-seo'), value: 'settings' },
    { label: __('Autre', 'boss-seo'), value: 'other' }
  ];

  return (
    <div>
      {showSuccess && (
        <Notice status="success" isDismissible={false} className="boss-mb-6">
          {successMessage}
        </Notice>
      )}
      
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4 boss-mb-6">
        <Card 
          className={`boss-cursor-pointer boss-transition-all boss-duration-300 ${activeForm === 'bug' ? 'boss-ring-2 boss-ring-blue-500 boss-shadow-md' : 'boss-hover:boss-shadow-md'}`}
          onClick={() => setActiveForm('bug')}
        >
          <CardBody className="boss-text-center boss-p-6">
            <div className="boss-text-4xl boss-mb-4 boss-text-red-500">
              <span className="dashicons dashicons-bug"></span>
            </div>
            <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark boss-mb-2">
              {__('Signaler un bug', 'boss-seo')}
            </h3>
            <p className="boss-text-sm boss-text-boss-gray">
              {__('Signalez un problème ou un comportement inattendu', 'boss-seo')}
            </p>
          </CardBody>
        </Card>
        
        <Card 
          className={`boss-cursor-pointer boss-transition-all boss-duration-300 ${activeForm === 'feature' ? 'boss-ring-2 boss-ring-blue-500 boss-shadow-md' : 'boss-hover:boss-shadow-md'}`}
          onClick={() => setActiveForm('feature')}
        >
          <CardBody className="boss-text-center boss-p-6">
            <div className="boss-text-4xl boss-mb-4 boss-text-green-500">
              <span className="dashicons dashicons-lightbulb"></span>
            </div>
            <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark boss-mb-2">
              {__('Suggérer une fonctionnalité', 'boss-seo')}
            </h3>
            <p className="boss-text-sm boss-text-boss-gray">
              {__('Proposez une nouvelle fonctionnalité ou amélioration', 'boss-seo')}
            </p>
          </CardBody>
        </Card>
        
        <Card 
          className={`boss-cursor-pointer boss-transition-all boss-duration-300 ${activeForm === 'satisfaction' ? 'boss-ring-2 boss-ring-blue-500 boss-shadow-md' : 'boss-hover:boss-shadow-md'}`}
          onClick={() => setActiveForm('satisfaction')}
        >
          <CardBody className="boss-text-center boss-p-6">
            <div className="boss-text-4xl boss-mb-4 boss-text-blue-500">
              <span className="dashicons dashicons-thumbs-up"></span>
            </div>
            <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark boss-mb-2">
              {__('Évaluation de satisfaction', 'boss-seo')}
            </h3>
            <p className="boss-text-sm boss-text-boss-gray">
              {__('Partagez votre expérience avec Boss SEO', 'boss-seo')}
            </p>
          </CardBody>
        </Card>
      </div>
      
      {/* Formulaire de signalement de bug */}
      {activeForm === 'bug' && (
        <Card>
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Signaler un bug', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <div className="boss-space-y-4">
              <TextControl
                label={__('Titre', 'boss-seo')}
                help={__('Titre court et descriptif du problème', 'boss-seo')}
                value={bugReport.title}
                onChange={(value) => updateBugReport('title', value)}
                required
              />
              
              <SelectControl
                label={__('Module concerné', 'boss-seo')}
                value={bugReport.module}
                options={moduleOptions}
                onChange={(value) => updateBugReport('module', value)}
                required
              />
              
              <RadioControl
                label={__('Sévérité', 'boss-seo')}
                selected={bugReport.severity}
                options={[
                  { label: __('Critique (bloquant)', 'boss-seo'), value: 'critical' },
                  { label: __('Élevée', 'boss-seo'), value: 'high' },
                  { label: __('Moyenne', 'boss-seo'), value: 'medium' },
                  { label: __('Faible', 'boss-seo'), value: 'low' }
                ]}
                onChange={(value) => updateBugReport('severity', value)}
              />
              
              <TextareaControl
                label={__('Description du problème', 'boss-seo')}
                help={__('Décrivez le problème en détail', 'boss-seo')}
                value={bugReport.description}
                onChange={(value) => updateBugReport('description', value)}
                rows={4}
                required
              />
              
              <TextareaControl
                label={__('Étapes pour reproduire', 'boss-seo')}
                help={__('Décrivez les étapes pour reproduire le problème', 'boss-seo')}
                value={bugReport.steps}
                onChange={(value) => updateBugReport('steps', value)}
                rows={3}
              />
              
              <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                <TextareaControl
                  label={__('Comportement attendu', 'boss-seo')}
                  value={bugReport.expectedBehavior}
                  onChange={(value) => updateBugReport('expectedBehavior', value)}
                  rows={3}
                />
                
                <TextareaControl
                  label={__('Comportement actuel', 'boss-seo')}
                  value={bugReport.actualBehavior}
                  onChange={(value) => updateBugReport('actualBehavior', value)}
                  rows={3}
                />
              </div>
              
              <div className="boss-p-4 boss-bg-gray-50 boss-rounded-lg">
                <h3 className="boss-text-md boss-font-medium boss-text-boss-dark boss-mb-3">
                  {__('Informations système', 'boss-seo')}
                </h3>
                
                <div className="boss-space-y-3">
                  <ToggleControl
                    label={__('Inclure les informations du navigateur', 'boss-seo')}
                    checked={bugReport.browserInfo}
                    onChange={(value) => updateBugReport('browserInfo', value)}
                  />
                  
                  <ToggleControl
                    label={__('Inclure les informations WordPress', 'boss-seo')}
                    checked={bugReport.wpInfo}
                    onChange={(value) => updateBugReport('wpInfo', value)}
                  />
                  
                  <ToggleControl
                    label={__('Inclure les informations du plugin', 'boss-seo')}
                    checked={bugReport.pluginInfo}
                    onChange={(value) => updateBugReport('pluginInfo', value)}
                  />
                </div>
              </div>
              
              <TextControl
                label={__('Email de contact (optionnel)', 'boss-seo')}
                help={__('Pour vous contacter si nous avons besoin de plus d\'informations', 'boss-seo')}
                type="email"
                value={bugReport.email}
                onChange={(value) => updateBugReport('email', value)}
              />
            </div>
          </CardBody>
          <CardFooter className="boss-border-t boss-border-gray-200">
            <div className="boss-flex boss-justify-end">
              <Button
                isPrimary
                onClick={submitBugReport}
                disabled={!isBugReportValid()}
              >
                {__('Envoyer le signalement', 'boss-seo')}
              </Button>
            </div>
          </CardFooter>
        </Card>
      )}
      
      {/* Formulaire de suggestion de fonctionnalité */}
      {activeForm === 'feature' && (
        <Card>
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Suggérer une fonctionnalité', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <div className="boss-space-y-4">
              <TextControl
                label={__('Titre', 'boss-seo')}
                help={__('Titre court et descriptif de la fonctionnalité', 'boss-seo')}
                value={featureSuggestion.title}
                onChange={(value) => updateFeatureSuggestion('title', value)}
                required
              />
              
              <SelectControl
                label={__('Module concerné', 'boss-seo')}
                value={featureSuggestion.module}
                options={moduleOptions}
                onChange={(value) => updateFeatureSuggestion('module', value)}
                required
              />
              
              <RadioControl
                label={__('Priorité', 'boss-seo')}
                selected={featureSuggestion.priority}
                options={[
                  { label: __('Élevée', 'boss-seo'), value: 'high' },
                  { label: __('Moyenne', 'boss-seo'), value: 'medium' },
                  { label: __('Faible', 'boss-seo'), value: 'low' }
                ]}
                onChange={(value) => updateFeatureSuggestion('priority', value)}
              />
              
              <TextareaControl
                label={__('Description de la fonctionnalité', 'boss-seo')}
                help={__('Décrivez la fonctionnalité en détail', 'boss-seo')}
                value={featureSuggestion.description}
                onChange={(value) => updateFeatureSuggestion('description', value)}
                rows={4}
                required
              />
              
              <TextareaControl
                label={__('Cas d\'utilisation', 'boss-seo')}
                help={__('Décrivez comment cette fonctionnalité serait utilisée', 'boss-seo')}
                value={featureSuggestion.useCase}
                onChange={(value) => updateFeatureSuggestion('useCase', value)}
                rows={3}
              />
              
              <TextControl
                label={__('Email de contact (optionnel)', 'boss-seo')}
                help={__('Pour vous contacter si nous avons besoin de plus d\'informations', 'boss-seo')}
                type="email"
                value={featureSuggestion.email}
                onChange={(value) => updateFeatureSuggestion('email', value)}
              />
            </div>
          </CardBody>
          <CardFooter className="boss-border-t boss-border-gray-200">
            <div className="boss-flex boss-justify-end">
              <Button
                isPrimary
                onClick={submitFeatureSuggestion}
                disabled={!isFeatureSuggestionValid()}
              >
                {__('Envoyer la suggestion', 'boss-seo')}
              </Button>
            </div>
          </CardFooter>
        </Card>
      )}
      
      {/* Formulaire d'évaluation de satisfaction */}
      {activeForm === 'satisfaction' && (
        <Card>
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Évaluation de satisfaction', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <div className="boss-space-y-4">
              <div>
                <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium boss-text-boss-dark">
                  {__('Votre évaluation globale', 'boss-seo')}
                </label>
                <div className="boss-flex boss-space-x-2 boss-text-2xl">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      className={`focus:boss-outline-none ${
                        satisfaction.rating >= star
                          ? 'boss-text-yellow-400'
                          : 'boss-text-gray-300 boss-hover:boss-text-gray-400'
                      }`}
                      onClick={() => updateSatisfaction('rating', star)}
                      aria-label={__('Évaluer', 'boss-seo') + ' ' + star}
                    >
                      <span className="dashicons dashicons-star-filled"></span>
                    </button>
                  ))}
                </div>
              </div>
              
              <TextareaControl
                label={__('Votre feedback', 'boss-seo')}
                help={__('Partagez votre expérience avec Boss SEO', 'boss-seo')}
                value={satisfaction.feedback}
                onChange={(value) => updateSatisfaction('feedback', value)}
                rows={4}
              />
              
              <RadioControl
                label={__('Recommanderiez-vous Boss SEO à d\'autres ?', 'boss-seo')}
                selected={satisfaction.wouldRecommend}
                options={[
                  { label: __('Oui, certainement', 'boss-seo'), value: 'yes' },
                  { label: __('Probablement', 'boss-seo'), value: 'maybe' },
                  { label: __('Non', 'boss-seo'), value: 'no' }
                ]}
                onChange={(value) => updateSatisfaction('wouldRecommend', value)}
              />
              
              <TextControl
                label={__('Fonctionnalité préférée', 'boss-seo')}
                value={satisfaction.favoriteFeature}
                onChange={(value) => updateSatisfaction('favoriteFeature', value)}
              />
              
              <TextareaControl
                label={__('Suggestions d\'amélioration', 'boss-seo')}
                value={satisfaction.improvementSuggestion}
                onChange={(value) => updateSatisfaction('improvementSuggestion', value)}
                rows={3}
              />
              
              <TextControl
                label={__('Email de contact (optionnel)', 'boss-seo')}
                type="email"
                value={satisfaction.email}
                onChange={(value) => updateSatisfaction('email', value)}
              />
            </div>
          </CardBody>
          <CardFooter className="boss-border-t boss-border-gray-200">
            <div className="boss-flex boss-justify-end">
              <Button
                isPrimary
                onClick={submitSatisfaction}
                disabled={!isSatisfactionValid()}
              >
                {__('Envoyer l\'évaluation', 'boss-seo')}
              </Button>
            </div>
          </CardFooter>
        </Card>
      )}
      
      {/* Section de support */}
      <Card className="boss-mt-6">
        <CardHeader className="boss-border-b boss-border-gray-200">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Besoin d\'aide supplémentaire ?', 'boss-seo')}
          </h2>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4">
            <div className="boss-text-center boss-p-4">
              <div className="boss-text-4xl boss-mb-4 boss-text-blue-500">
                <span className="dashicons dashicons-book"></span>
              </div>
              <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark boss-mb-2">
                {__('Documentation', 'boss-seo')}
              </h3>
              <p className="boss-text-sm boss-text-boss-gray boss-mb-4">
                {__('Consultez notre documentation complète', 'boss-seo')}
              </p>
              <Button
                isSecondary
                href="https://docs.example.com/boss-seo"
                target="_blank"
              >
                {__('Voir la documentation', 'boss-seo')}
              </Button>
            </div>
            
            <div className="boss-text-center boss-p-4">
              <div className="boss-text-4xl boss-mb-4 boss-text-purple-500">
                <span className="dashicons dashicons-admin-comments"></span>
              </div>
              <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark boss-mb-2">
                {__('Forum de support', 'boss-seo')}
              </h3>
              <p className="boss-text-sm boss-text-boss-gray boss-mb-4">
                {__('Posez vos questions à notre communauté', 'boss-seo')}
              </p>
              <Button
                isSecondary
                href="https://forum.example.com/boss-seo"
                target="_blank"
              >
                {__('Visiter le forum', 'boss-seo')}
              </Button>
            </div>
            
            <div className="boss-text-center boss-p-4">
              <div className="boss-text-4xl boss-mb-4 boss-text-green-500">
                <span className="dashicons dashicons-email"></span>
              </div>
              <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark boss-mb-2">
                {__('Support premium', 'boss-seo')}
              </h3>
              <p className="boss-text-sm boss-text-boss-gray boss-mb-4">
                {__('Contactez notre équipe de support', 'boss-seo')}
              </p>
              <Button
                isPrimary
                href="mailto:<EMAIL>"
              >
                {__('Contacter le support', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default Feedback;
