<?php
/**
 * La classe de gestion des meta boxes du module Boss Optimizer.
 *
 * Cette classe gère l'affichage des meta boxes SEO dans l'éditeur WordPress.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * La classe de gestion des meta boxes du module Boss Optimizer.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_Metabox {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Types de contenu pris en charge.
     *
     * @since    1.1.0
     * @access   protected
     * @var      array    $supported_post_types    Types de contenu pris en charge.
     */
    protected $supported_post_types;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string                   $plugin_name          Le nom du plugin.
     * @param    string                   $version              La version du plugin.
     * @param    Boss_Optimizer_Settings  $settings             Instance de la classe de paramètres.
     */
    public function __construct( $plugin_name, $version, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;

        // Types de contenu pris en charge par défaut
        $this->supported_post_types = array( 'post', 'page' );

        // Ajouter les types de contenu personnalisés configurés dans les paramètres
        $custom_post_types = $this->settings->get( 'optimizer', 'custom_post_types', array() );
        if ( ! empty( $custom_post_types ) && is_array( $custom_post_types ) ) {
            $this->supported_post_types = array_merge( $this->supported_post_types, $custom_post_types );
        }
    }

    /**
     * Enregistre les hooks nécessaires pour les meta boxes.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Ajouter les meta boxes
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );

        // Enregistrer les données des meta boxes
        add_action( 'save_post', array( $this, 'save_meta_boxes' ), 10, 2 );

        // Ajouter les scripts et styles nécessaires
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
    }

    /**
     * Ajoute les meta boxes dans l'éditeur WordPress.
     *
     * @since    1.1.0
     */
    public function add_meta_boxes() {
        foreach ( $this->supported_post_types as $post_type ) {
            add_meta_box(
                'boss_seo_metabox',
                __( 'Boss SEO - Optimisation', 'boss-seo' ),
                array( $this, 'render_meta_box' ),
                $post_type,
                'normal',
                'high'
            );
        }
    }

    /**
     * Affiche le contenu de la meta box.
     *
     * @since    1.1.0
     * @param    WP_Post    $post    L'objet post actuel.
     */
    public function render_meta_box( $post ) {
        // Récupérer les métadonnées SEO
        $meta_description = get_post_meta( $post->ID, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post->ID, '_boss_seo_focus_keyword', true );
        $secondary_keywords = get_post_meta( $post->ID, '_boss_seo_secondary_keywords', true );
        $seo_title = get_post_meta( $post->ID, '_boss_seo_title', true );
        $canonical_url = get_post_meta( $post->ID, '_boss_seo_canonical_url', true );
        $robots_index = get_post_meta( $post->ID, '_boss_seo_robots_index', true ) ?: 'index';
        $robots_follow = get_post_meta( $post->ID, '_boss_seo_robots_follow', true ) ?: 'follow';
        $og_title = get_post_meta( $post->ID, '_boss_seo_og_title', true );
        $og_description = get_post_meta( $post->ID, '_boss_seo_og_description', true );
        $og_image = get_post_meta( $post->ID, '_boss_seo_og_image', true );
        $twitter_title = get_post_meta( $post->ID, '_boss_seo_twitter_title', true );
        $twitter_description = get_post_meta( $post->ID, '_boss_seo_twitter_description', true );
        $twitter_image = get_post_meta( $post->ID, '_boss_seo_twitter_image', true );
        $twitter_card_type = get_post_meta( $post->ID, '_boss_seo_twitter_card_type', true ) ?: 'summary_large_image';
        $seo_score = get_post_meta( $post->ID, '_boss_seo_score', true );
        $recommendations = get_post_meta( $post->ID, '_boss_seo_recommendations', true );
        $analysis_date = get_post_meta( $post->ID, '_boss_seo_analysis_date', true );

        // Ajouter un nonce pour la sécurité
        wp_nonce_field( 'boss_seo_meta_box', 'boss_seo_meta_box_nonce' );

        // Obtenir des suggestions de mots-clés basées sur le contenu
        $keyword_suggestions = $this->get_keyword_suggestions( $post );

        // Afficher le formulaire
        ?>
        <div class="boss-seo-metabox">
            <div class="boss-seo-metabox-section">
                <h4><span class="dashicons dashicons-tag"></span><?php _e( 'Mots-clés', 'boss-seo' ); ?></h4>

                <div class="boss-seo-keywords-container">
                    <div class="boss-seo-keywords-input-container">
                        <input type="text" id="boss_seo_keywords_input" class="boss-seo-keywords-input" placeholder="<?php _e( 'Ajouter un mot-clé et appuyer sur Entrée', 'boss-seo' ); ?>">
                    </div>

                    <div class="boss-seo-keywords-tags">
                        <!-- Les mots-clés seront ajoutés ici par JavaScript -->
                    </div>

                    <?php if ( ! empty( $keyword_suggestions ) ) : ?>
                    <div class="boss-seo-keywords-suggestions">
                        <div class="boss-seo-keywords-suggestions-title"><?php _e( 'Suggestions de mots-clés', 'boss-seo' ); ?></div>
                        <div class="boss-seo-keywords-suggestions-list">
                            <?php foreach ( $keyword_suggestions as $suggestion ) : ?>
                            <div class="boss-seo-keyword-suggestion">
                                <span class="dashicons dashicons-plus-alt2"></span>
                                <?php echo esc_html( $suggestion ); ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Champs cachés pour stocker les valeurs -->
                <input type="hidden" name="boss_seo_focus_keyword" id="boss_seo_focus_keyword" value="<?php echo esc_attr( $focus_keyword ); ?>">
                <input type="hidden" name="boss_seo_secondary_keywords" id="boss_seo_secondary_keywords" value="<?php echo esc_attr( $secondary_keywords ); ?>">

                <p class="description"><?php _e( 'Définissez un mot-clé principal (étoile) et des mots-clés secondaires pour optimiser votre contenu.', 'boss-seo' ); ?></p>
            </div>

            <div class="boss-seo-metabox-section">
                <h4><span class="dashicons dashicons-admin-appearance"></span><?php _e( 'Métadonnées de base', 'boss-seo' ); ?></h4>

                <div class="boss-seo-metadata-field">
                    <label for="boss_seo_title"><?php _e( 'Titre SEO', 'boss-seo' ); ?></label>
                    <input type="text" name="boss_seo_title" id="boss_seo_title" value="<?php echo esc_attr( $seo_title ); ?>" class="widefat" placeholder="<?php _e( 'Titre optimisé pour les moteurs de recherche', 'boss-seo' ); ?>">
                    <p class="description"><?php _e( 'Laissez vide pour utiliser le titre de l\'article. Idéalement entre 50 et 60 caractères.', 'boss-seo' ); ?></p>
                </div>

                <div class="boss-seo-metadata-field">
                    <label for="boss_seo_meta_description"><?php _e( 'Meta description', 'boss-seo' ); ?></label>
                    <textarea name="boss_seo_meta_description" id="boss_seo_meta_description" class="widefat" rows="3" placeholder="<?php _e( 'Entrez votre méta-description', 'boss-seo' ); ?>"><?php echo esc_textarea( $meta_description ); ?></textarea>
                    <p class="description"><?php _e( 'La méta-description apparaît dans les résultats de recherche. Idéalement entre 120 et 160 caractères.', 'boss-seo' ); ?></p>
                    <div id="boss_seo_meta_description_counter" class="boss-seo-counter">
                        <span class="boss-seo-counter-current"><?php echo mb_strlen( $meta_description ); ?></span>
                        <span class="boss-seo-counter-separator">/</span>
                        <span class="boss-seo-counter-max">160</span>
                    </div>
                </div>

                <div class="boss-seo-metadata-field">
                    <label for="boss_seo_canonical_url"><?php _e( 'URL canonique', 'boss-seo' ); ?></label>
                    <input type="url" name="boss_seo_canonical_url" id="boss_seo_canonical_url" value="<?php echo esc_url( $canonical_url ); ?>" class="widefat" placeholder="<?php _e( 'https://example.com/page-canonique', 'boss-seo' ); ?>">
                    <p class="description"><?php _e( 'Laissez vide pour utiliser l\'URL actuelle. Utilisez cette option uniquement si cette page est un duplicata d\'une autre page.', 'boss-seo' ); ?></p>
                </div>

                <div class="boss-seo-metadata-grid">
                    <div class="boss-seo-metadata-field">
                        <label for="boss_seo_robots_index"><?php _e( 'Indexation', 'boss-seo' ); ?></label>
                        <select name="boss_seo_robots_index" id="boss_seo_robots_index">
                            <option value="index" <?php selected( $robots_index, 'index' ); ?>><?php _e( 'Index', 'boss-seo' ); ?></option>
                            <option value="noindex" <?php selected( $robots_index, 'noindex' ); ?>><?php _e( 'Noindex', 'boss-seo' ); ?></option>
                        </select>
                    </div>

                    <div class="boss-seo-metadata-field">
                        <label for="boss_seo_robots_follow"><?php _e( 'Suivi des liens', 'boss-seo' ); ?></label>
                        <select name="boss_seo_robots_follow" id="boss_seo_robots_follow">
                            <option value="follow" <?php selected( $robots_follow, 'follow' ); ?>><?php _e( 'Follow', 'boss-seo' ); ?></option>
                            <option value="nofollow" <?php selected( $robots_follow, 'nofollow' ); ?>><?php _e( 'Nofollow', 'boss-seo' ); ?></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="boss-seo-metabox-section">
                <h4><span class="dashicons dashicons-share"></span><?php _e( 'Réseaux sociaux', 'boss-seo' ); ?></h4>

                <div class="boss-seo-metadata-field">
                    <label for="boss_seo_og_title"><?php _e( 'Titre Facebook', 'boss-seo' ); ?></label>
                    <input type="text" name="boss_seo_og_title" id="boss_seo_og_title" value="<?php echo esc_attr( $og_title ); ?>" class="widefat" placeholder="<?php _e( 'Titre pour le partage sur Facebook', 'boss-seo' ); ?>">
                </div>

                <div class="boss-seo-metadata-field">
                    <label for="boss_seo_og_description"><?php _e( 'Description Facebook', 'boss-seo' ); ?></label>
                    <textarea name="boss_seo_og_description" id="boss_seo_og_description" class="widefat" rows="2" placeholder="<?php _e( 'Description pour le partage sur Facebook', 'boss-seo' ); ?>"><?php echo esc_textarea( $og_description ); ?></textarea>
                </div>

                <div class="boss-seo-metadata-field">
                    <label for="boss_seo_og_image"><?php _e( 'Image Facebook', 'boss-seo' ); ?></label>
                    <input type="url" name="boss_seo_og_image" id="boss_seo_og_image" value="<?php echo esc_url( $og_image ); ?>" class="widefat" placeholder="<?php _e( 'URL de l\'image pour Facebook', 'boss-seo' ); ?>">
                </div>

                <div class="boss-seo-metadata-field">
                    <label for="boss_seo_twitter_title"><?php _e( 'Titre Twitter', 'boss-seo' ); ?></label>
                    <input type="text" name="boss_seo_twitter_title" id="boss_seo_twitter_title" value="<?php echo esc_attr( $twitter_title ); ?>" class="widefat" placeholder="<?php _e( 'Titre pour le partage sur Twitter', 'boss-seo' ); ?>">
                </div>

                <div class="boss-seo-metadata-field">
                    <label for="boss_seo_twitter_description"><?php _e( 'Description Twitter', 'boss-seo' ); ?></label>
                    <textarea name="boss_seo_twitter_description" id="boss_seo_twitter_description" class="widefat" rows="2" placeholder="<?php _e( 'Description pour le partage sur Twitter', 'boss-seo' ); ?>"><?php echo esc_textarea( $twitter_description ); ?></textarea>
                </div>

                <div class="boss-seo-metadata-grid">
                    <div class="boss-seo-metadata-field">
                        <label for="boss_seo_twitter_image"><?php _e( 'Image Twitter', 'boss-seo' ); ?></label>
                        <input type="url" name="boss_seo_twitter_image" id="boss_seo_twitter_image" value="<?php echo esc_url( $twitter_image ); ?>" class="widefat" placeholder="<?php _e( 'URL de l\'image pour Twitter', 'boss-seo' ); ?>">
                    </div>

                    <div class="boss-seo-metadata-field">
                        <label for="boss_seo_twitter_card_type"><?php _e( 'Type de carte Twitter', 'boss-seo' ); ?></label>
                        <select name="boss_seo_twitter_card_type" id="boss_seo_twitter_card_type">
                            <option value="summary_large_image" <?php selected( $twitter_card_type, 'summary_large_image' ); ?>><?php _e( 'Grande image', 'boss-seo' ); ?></option>
                            <option value="summary" <?php selected( $twitter_card_type, 'summary' ); ?>><?php _e( 'Résumé', 'boss-seo' ); ?></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="boss-seo-metabox-section">
                <h4><span class="dashicons dashicons-chart-bar"></span><?php _e( 'Score SEO', 'boss-seo' ); ?></h4>
                <div class="boss-seo-score-container">
                    <?php if ( ! empty( $seo_score ) ) : ?>
                        <div class="boss-seo-score-indicator boss-seo-score-<?php echo $this->get_score_class( $seo_score ); ?>">
                            <span class="boss-seo-score-value"><?php echo intval( $seo_score ); ?></span>
                        </div>
                    <?php else : ?>
                        <div class="boss-seo-score-indicator boss-seo-score-none">
                            <span class="boss-seo-score-value">?</span>
                        </div>
                    <?php endif; ?>
                    <div class="boss-seo-score-details">
                        <p><?php _e( 'Dernière analyse:', 'boss-seo' ); ?> <strong><?php echo ! empty( $analysis_date ) ? date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $analysis_date ) ) : __( 'Jamais', 'boss-seo' ); ?></strong></p>
                        <button type="button" class="button" id="boss_seo_analyze_button"><?php _e( 'Analyser maintenant', 'boss-seo' ); ?></button>
                    </div>
                </div>
            </div>

            <?php if ( ! empty( $recommendations ) && is_array( $recommendations ) ) : ?>
                <div class="boss-seo-metabox-section">
                    <h4><span class="dashicons dashicons-lightbulb"></span><?php _e( 'Recommandations', 'boss-seo' ); ?></h4>
                    <div class="boss-seo-recommendations">
                        <?php foreach ( $recommendations as $recommendation ) : ?>
                            <div class="boss-seo-recommendation boss-seo-recommendation-<?php echo esc_attr( $recommendation['type'] ); ?>">
                                <span class="boss-seo-recommendation-icon dashicons dashicons-<?php echo $this->get_recommendation_icon( $recommendation['type'] ); ?>"></span>
                                <span class="boss-seo-recommendation-text"><?php echo esc_html( $recommendation['text'] ); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <div class="boss-seo-metabox-section">
                <button type="button" class="button button-primary" id="boss_seo_optimize_button"><?php _e( 'Optimiser avec l\'IA', 'boss-seo' ); ?></button>
                <p class="description"><?php _e( 'Utilise l\'intelligence artificielle pour optimiser votre contenu pour le SEO.', 'boss-seo' ); ?></p>
            </div>
        </div>
        <?php
    }

    /**
     * Obtient des suggestions de mots-clés basées sur le contenu du post.
     *
     * @since    1.1.0
     * @param    WP_Post    $post    L'objet post actuel.
     * @return   array               Tableau de suggestions de mots-clés.
     */
    private function get_keyword_suggestions( $post ) {
        // Si le post est vide ou n'a pas de contenu, retourner un tableau vide
        if ( empty( $post ) || empty( $post->post_content ) ) {
            return array();
        }

        // Extraire le contenu du post
        $content = wp_strip_all_tags( $post->post_content );
        $title = $post->post_title;

        // Si le contenu est trop court, retourner un tableau vide
        if ( strlen( $content ) < 100 ) {
            return array();
        }

        // Analyser le contenu pour extraire les mots-clés potentiels
        // Dans une implémentation réelle, vous utiliseriez un service d'IA ou un algorithme plus sophistiqué
        // Ici, nous utilisons une approche simplifiée pour l'exemple

        // Liste des mots à ignorer (stop words)
        $stop_words = array(
            'le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'mais', 'donc', 'car', 'pour', 'par', 'avec', 'sans',
            'dans', 'sur', 'sous', 'avant', 'après', 'pendant', 'depuis', 'jusqu', 'vers', 'chez', 'entre', 'parmi',
            'ce', 'cette', 'ces', 'mon', 'ton', 'son', 'notre', 'votre', 'leur', 'mes', 'tes', 'ses', 'nos', 'vos', 'leurs',
            'qui', 'que', 'quoi', 'dont', 'où', 'comment', 'pourquoi', 'quand', 'est', 'sont', 'sera', 'seront', 'été',
            'avoir', 'être', 'faire', 'dire', 'aller', 'voir', 'savoir', 'pouvoir', 'vouloir', 'falloir', 'devoir',
            'très', 'peu', 'plus', 'moins', 'autant', 'aussi', 'bien', 'mal', 'vite', 'lentement', 'souvent', 'toujours',
            'jamais', 'parfois', 'alors', 'ensuite', 'enfin', 'puis', 'soudain', 'tout', 'tous', 'toute', 'toutes',
            'aucun', 'aucune', 'chaque', 'plusieurs', 'certain', 'certaine', 'certains', 'certaines'
        );

        // Nettoyer le contenu
        $content = strtolower( $content );
        $content = preg_replace( '/[^\p{L}\p{N}\s]/u', ' ', $content ); // Supprimer les caractères spéciaux
        $content = preg_replace( '/\s+/', ' ', $content ); // Supprimer les espaces multiples

        // Extraire les mots
        $words = explode( ' ', $content );
        $words = array_filter( $words, function( $word ) use ( $stop_words ) {
            return strlen( $word ) > 3 && ! in_array( $word, $stop_words );
        } );

        // Compter les occurrences de chaque mot
        $word_counts = array_count_values( $words );

        // Trier par nombre d'occurrences
        arsort( $word_counts );

        // Extraire les mots-clés les plus fréquents
        $keywords = array_slice( array_keys( $word_counts ), 0, 10 );

        // Extraire les expressions de 2-3 mots
        $phrases = array();
        $content_words = explode( ' ', $content );

        for ( $i = 0; $i < count( $content_words ) - 1; $i++ ) {
            if ( strlen( $content_words[$i] ) > 3 && ! in_array( $content_words[$i], $stop_words ) ) {
                // Expressions de 2 mots
                if ( isset( $content_words[$i + 1] ) && strlen( $content_words[$i + 1] ) > 3 && ! in_array( $content_words[$i + 1], $stop_words ) ) {
                    $phrase = $content_words[$i] . ' ' . $content_words[$i + 1];
                    if ( ! isset( $phrases[$phrase] ) ) {
                        $phrases[$phrase] = 0;
                    }
                    $phrases[$phrase]++;
                }

                // Expressions de 3 mots
                if ( isset( $content_words[$i + 1] ) && isset( $content_words[$i + 2] ) &&
                     strlen( $content_words[$i + 1] ) > 3 && ! in_array( $content_words[$i + 1], $stop_words ) &&
                     strlen( $content_words[$i + 2] ) > 3 && ! in_array( $content_words[$i + 2], $stop_words ) ) {
                    $phrase = $content_words[$i] . ' ' . $content_words[$i + 1] . ' ' . $content_words[$i + 2];
                    if ( ! isset( $phrases[$phrase] ) ) {
                        $phrases[$phrase] = 0;
                    }
                    $phrases[$phrase]++;
                }
            }
        }

        // Filtrer les expressions qui apparaissent au moins 2 fois
        $phrases = array_filter( $phrases, function( $count ) {
            return $count >= 2;
        } );

        // Trier par nombre d'occurrences
        arsort( $phrases );

        // Extraire les expressions les plus fréquentes
        $top_phrases = array_slice( array_keys( $phrases ), 0, 5 );

        // Combiner les mots-clés et les expressions
        $suggestions = array_merge( $keywords, $top_phrases );

        // Limiter à 10 suggestions
        $suggestions = array_slice( $suggestions, 0, 10 );

        return $suggestions;
    }

    /**
     * Enregistre les données des meta boxes.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du post.
     * @param    WP_Post   $post       L'objet post.
     */
    public function save_meta_boxes( $post_id, $post ) {
        // Vérifier le nonce
        if ( ! isset( $_POST['boss_seo_meta_box_nonce'] ) || ! wp_verify_nonce( $_POST['boss_seo_meta_box_nonce'], 'boss_seo_meta_box' ) ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Vérifier si c'est une sauvegarde automatique
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }

        // Vérifier si c'est un type de post pris en charge
        if ( ! in_array( $post->post_type, $this->supported_post_types ) ) {
            return;
        }

        // Enregistrer les métadonnées de base
        if ( isset( $_POST['boss_seo_focus_keyword'] ) ) {
            update_post_meta( $post_id, '_boss_seo_focus_keyword', sanitize_text_field( $_POST['boss_seo_focus_keyword'] ) );
        }

        if ( isset( $_POST['boss_seo_secondary_keywords'] ) ) {
            update_post_meta( $post_id, '_boss_seo_secondary_keywords', sanitize_text_field( $_POST['boss_seo_secondary_keywords'] ) );
        }

        if ( isset( $_POST['boss_seo_meta_description'] ) ) {
            update_post_meta( $post_id, '_boss_seo_meta_description', sanitize_textarea_field( $_POST['boss_seo_meta_description'] ) );
        } else {
            // Si la méta-description n'est pas définie, utiliser un extrait du contenu
            $excerpt = wp_trim_words( $post->post_content, 30 );
            update_post_meta( $post_id, '_boss_seo_meta_description', $excerpt );
        }

        if ( isset( $_POST['boss_seo_title'] ) ) {
            update_post_meta( $post_id, '_boss_seo_title', sanitize_text_field( $_POST['boss_seo_title'] ) );
        } else {
            // Si le titre SEO n'est pas défini, utiliser le titre du post
            update_post_meta( $post_id, '_boss_seo_title', $post->post_title );
        }

        if ( isset( $_POST['boss_seo_canonical_url'] ) ) {
            update_post_meta( $post_id, '_boss_seo_canonical_url', esc_url_raw( $_POST['boss_seo_canonical_url'] ) );
        }

        // Enregistrer les métadonnées robots
        if ( isset( $_POST['boss_seo_robots_index'] ) ) {
            update_post_meta( $post_id, '_boss_seo_robots_index', sanitize_text_field( $_POST['boss_seo_robots_index'] ) );
        }

        if ( isset( $_POST['boss_seo_robots_follow'] ) ) {
            update_post_meta( $post_id, '_boss_seo_robots_follow', sanitize_text_field( $_POST['boss_seo_robots_follow'] ) );
        }

        // Enregistrer les métadonnées Open Graph (Facebook)
        if ( isset( $_POST['boss_seo_og_title'] ) ) {
            update_post_meta( $post_id, '_boss_seo_og_title', sanitize_text_field( $_POST['boss_seo_og_title'] ) );
        }

        if ( isset( $_POST['boss_seo_og_description'] ) ) {
            update_post_meta( $post_id, '_boss_seo_og_description', sanitize_textarea_field( $_POST['boss_seo_og_description'] ) );
        }

        if ( isset( $_POST['boss_seo_og_image'] ) ) {
            update_post_meta( $post_id, '_boss_seo_og_image', esc_url_raw( $_POST['boss_seo_og_image'] ) );
        }

        // Enregistrer les métadonnées Twitter
        if ( isset( $_POST['boss_seo_twitter_title'] ) ) {
            update_post_meta( $post_id, '_boss_seo_twitter_title', sanitize_text_field( $_POST['boss_seo_twitter_title'] ) );
        }

        if ( isset( $_POST['boss_seo_twitter_description'] ) ) {
            update_post_meta( $post_id, '_boss_seo_twitter_description', sanitize_textarea_field( $_POST['boss_seo_twitter_description'] ) );
        }

        if ( isset( $_POST['boss_seo_twitter_image'] ) ) {
            update_post_meta( $post_id, '_boss_seo_twitter_image', esc_url_raw( $_POST['boss_seo_twitter_image'] ) );
        }

        if ( isset( $_POST['boss_seo_twitter_card_type'] ) ) {
            update_post_meta( $post_id, '_boss_seo_twitter_card_type', sanitize_text_field( $_POST['boss_seo_twitter_card_type'] ) );
        }
    }

    /**
     * Enregistre les scripts et styles nécessaires.
     *
     * @since    1.1.0
     * @param    string    $hook    Le hook de la page actuelle.
     */
    public function enqueue_scripts( $hook ) {
        // N'enregistrer les scripts que sur les pages d'édition
        if ( ! in_array( $hook, array( 'post.php', 'post-new.php' ) ) ) {
            return;
        }

        // Enregistrer les styles
        wp_enqueue_style( 'boss-seo-metabox', plugin_dir_url( dirname( __FILE__ ) ) . 'admin/css/boss-seo-metabox.css', array(), $this->version, 'all' );

        // Enregistrer les scripts
        wp_enqueue_script( 'boss-seo-metabox', plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/boss-seo-metabox.js', array( 'jquery' ), $this->version, false );

        // Localiser le script
        wp_localize_script( 'boss-seo-metabox', 'bossSeoMetabox', array(
            'ajaxUrl' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'boss_seo_metabox_nonce' ),
            'analyzing' => __( 'Analyse en cours...', 'boss-seo' ),
            'optimizing' => __( 'Optimisation en cours...', 'boss-seo' ),
            'analyzeSuccess' => __( 'Analyse terminée avec succès !', 'boss-seo' ),
            'optimizeSuccess' => __( 'Optimisation terminée avec succès !', 'boss-seo' ),
            'confirmAnalyze' => __( 'Êtes-vous sûr de vouloir analyser ce contenu ?', 'boss-seo' ),
            'confirmOptimize' => __( 'Êtes-vous sûr de vouloir optimiser ce contenu ? Cette action peut modifier le contenu existant.', 'boss-seo' ),
            'error' => __( 'Une erreur est survenue.', 'boss-seo' )
        ) );
    }

    /**
     * Retourne la classe CSS en fonction du score SEO.
     *
     * @since    1.1.0
     * @param    int       $score    Le score SEO.
     * @return   string              La classe CSS.
     */
    private function get_score_class( $score ) {
        if ( $score >= 80 ) {
            return 'good';
        } elseif ( $score >= 60 ) {
            return 'ok';
        } elseif ( $score >= 40 ) {
            return 'poor';
        } else {
            return 'bad';
        }
    }

    /**
     * Retourne l'icône en fonction du type de recommandation.
     *
     * @since    1.1.0
     * @param    string    $type    Le type de recommandation.
     * @return   string             L'icône.
     */
    private function get_recommendation_icon( $type ) {
        switch ( $type ) {
            case 'critical':
                return 'warning';
            case 'warning':
                return 'flag';
            case 'info':
                return 'info';
            default:
                return 'info';
        }
    }
}
