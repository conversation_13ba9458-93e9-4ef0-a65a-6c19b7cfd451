<?php
/**
 * La classe qui gère l'intégration IA pour les schémas structurés.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 */

/**
 * La classe qui gère l'intégration IA pour les schémas structurés.
 *
 * Cette classe gère la génération de schémas structurés à l'aide de l'IA.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 * <AUTHOR> SEO Team
 */
class Boss_Structured_Schemas_AI {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Instance du service d'IA.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_AI_Service    $ai_service    Service d'IA.
     */
    protected $ai_service;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string                   $plugin_name    Le nom du plugin.
     * @param    string                   $version        La version du plugin.
     * @param    Boss_Optimizer_Settings  $settings       Instance de la classe de paramètres (peut être null).
     */
    public function __construct( $plugin_name, $version, $settings = null ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;

        // Initialiser le service d'IA
        if ( class_exists( 'Boss_Optimizer_AI_Service' ) && $settings !== null ) {
            $this->ai_service = new Boss_Optimizer_AI_Service( $settings );
        }
    }

    /**
     * Génère un schéma structuré à l'aide de l'IA.
     *
     * @since    1.2.0
     * @param    int       $post_id       L'ID du post.
     * @param    string    $schema_type   Le type de schéma à générer.
     * @return   array|WP_Error           Le schéma généré ou une erreur.
     */
    public function generate_schema( $post_id, $schema_type ) {
        // Vérifier que le service d'IA est disponible
        if ( ! $this->ai_service ) {
            // Générer un schéma de base sans IA
            return $this->generate_fallback_schema( $post_id, $schema_type );
        }

        // Vérifier que l'IA est configurée
        if ( $this->settings && ! $this->settings->is_ai_configured() ) {
            // Générer un schéma de base sans IA
            return $this->generate_fallback_schema( $post_id, $schema_type );
        }

        // Récupérer le post
        $post = get_post( $post_id );

        if ( ! $post ) {
            return new WP_Error( 'post_not_found', __( 'Post non trouvé.', 'boss-seo' ) );
        }

        try {
            // Préparer les données du post
            $post_data = $this->prepare_post_data( $post );

            // Générer le prompt pour l'IA
            $prompt = $this->generate_prompt( $post_data, $schema_type );

            // Appeler l'IA
            $response = $this->ai_service->generate_content( $prompt );

            if ( is_wp_error( $response ) ) {
                // En cas d'erreur IA, utiliser le fallback
                return $this->generate_fallback_schema( $post_id, $schema_type );
            }

            // Traiter la réponse de l'IA
            $schema = $this->process_ai_response( $response, $schema_type );

            if ( is_wp_error( $schema ) ) {
                // En cas d'erreur de traitement, utiliser le fallback
                return $this->generate_fallback_schema( $post_id, $schema_type );
            }

            return $schema;

        } catch ( Exception $e ) {
            // En cas d'exception, utiliser le fallback
            return $this->generate_fallback_schema( $post_id, $schema_type );
        }
    }

    /**
     * Prépare les données du post pour l'IA.
     *
     * @since    1.2.0
     * @param    WP_Post   $post    Le post.
     * @return   array              Les données du post.
     */
    private function prepare_post_data( $post ) {
        $post_data = array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'content' => wp_strip_all_tags( $post->post_content ),
            'excerpt' => get_the_excerpt( $post ),
            'permalink' => get_permalink( $post ),
            'date' => get_the_date( 'c', $post ),
            'modified' => get_the_modified_date( 'c', $post ),
            'author' => array(
                'id' => $post->post_author,
                'name' => get_the_author_meta( 'display_name', $post->post_author ),
                'url' => get_author_posts_url( $post->post_author )
            ),
            'featured_image' => get_the_post_thumbnail_url( $post, 'full' ),
            'categories' => array(),
            'tags' => array(),
            'meta' => array()
        );

        // Récupérer les catégories
        $categories = get_the_category( $post->ID );

        if ( $categories ) {
            foreach ( $categories as $category ) {
                $post_data['categories'][] = array(
                    'id' => $category->term_id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'url' => get_category_link( $category->term_id )
                );
            }
        }

        // Récupérer les tags
        $tags = get_the_tags( $post->ID );

        if ( $tags ) {
            foreach ( $tags as $tag ) {
                $post_data['tags'][] = array(
                    'id' => $tag->term_id,
                    'name' => $tag->name,
                    'slug' => $tag->slug,
                    'url' => get_tag_link( $tag->term_id )
                );
            }
        }

        // Récupérer les métadonnées
        $meta_keys = array(
            '_boss_seo_focus_keyword',
            '_boss_seo_secondary_keywords',
            '_boss_seo_meta_description',
            '_boss_seo_title'
        );

        foreach ( $meta_keys as $meta_key ) {
            $meta_value = get_post_meta( $post->ID, $meta_key, true );

            if ( $meta_value ) {
                $key = str_replace( '_boss_seo_', '', $meta_key );
                $post_data['meta'][ $key ] = $meta_value;
            }
        }

        return $post_data;
    }

    /**
     * Génère un prompt pour l'IA.
     *
     * @since    1.2.0
     * @param    array     $post_data     Les données du post.
     * @param    string    $schema_type   Le type de schéma à générer.
     * @return   string                   Le prompt pour l'IA.
     */
    private function generate_prompt( $post_data, $schema_type ) {
        $prompt = sprintf(
            "Génère un schéma structuré de type '%s' au format JSON-LD pour le contenu suivant :\n\n",
            $schema_type
        );

        $prompt .= sprintf( "Titre : %s\n", $post_data['title'] );
        $prompt .= sprintf( "Extrait : %s\n", $post_data['excerpt'] );
        $prompt .= sprintf( "URL : %s\n", $post_data['permalink'] );
        $prompt .= sprintf( "Date de publication : %s\n", $post_data['date'] );
        $prompt .= sprintf( "Auteur : %s\n", $post_data['author']['name'] );

        if ( ! empty( $post_data['categories'] ) ) {
            $categories = array_column( $post_data['categories'], 'name' );
            $prompt .= sprintf( "Catégories : %s\n", implode( ', ', $categories ) );
        }

        if ( ! empty( $post_data['tags'] ) ) {
            $tags = array_column( $post_data['tags'], 'name' );
            $prompt .= sprintf( "Tags : %s\n", implode( ', ', $tags ) );
        }

        if ( ! empty( $post_data['meta']['focus_keyword'] ) ) {
            $prompt .= sprintf( "Mot-clé principal : %s\n", $post_data['meta']['focus_keyword'] );
        }

        if ( ! empty( $post_data['meta']['secondary_keywords'] ) ) {
            $prompt .= sprintf( "Mots-clés secondaires : %s\n", $post_data['meta']['secondary_keywords'] );
        }

        $prompt .= sprintf( "\nContenu :\n%s\n\n", $post_data['content'] );

        $prompt .= "Génère un schéma structuré complet et valide selon les spécifications de schema.org. ";
        $prompt .= "Inclus uniquement les propriétés pertinentes pour ce contenu. ";
        $prompt .= "Retourne uniquement le JSON-LD sans aucun texte explicatif. ";
        $prompt .= "N'inclus pas les balises <script> ou autres balises HTML. ";
        $prompt .= "Assure-toi que le JSON est valide et correctement formaté.";

        return $prompt;
    }

    /**
     * Traite la réponse de l'IA.
     *
     * @since    1.2.0
     * @param    string    $response      La réponse de l'IA.
     * @param    string    $schema_type   Le type de schéma.
     * @return   array|WP_Error           Le schéma traité ou une erreur.
     */
    private function process_ai_response( $response, $schema_type ) {
        // Nettoyer la réponse
        $response = trim( $response );

        // Extraire le JSON
        if ( preg_match( '/```json(.*?)```/s', $response, $matches ) ) {
            $json_string = trim( $matches[1] );
        } else {
            $json_string = $response;
        }

        // Décoder le JSON
        $schema = json_decode( $json_string, true );

        if ( json_last_error() !== JSON_ERROR_NONE ) {
            return new WP_Error( 'invalid_json', __( 'La réponse de l\'IA n\'est pas un JSON valide.', 'boss-seo' ) );
        }

        // Vérifier que le schéma est du bon type
        if ( ! isset( $schema['@type'] ) || $schema['@type'] !== $schema_type ) {
            // Corriger le type
            $schema['@type'] = $schema_type;
        }

        // Vérifier que le contexte est présent
        if ( ! isset( $schema['@context'] ) ) {
            $schema['@context'] = 'https://schema.org';
        }

        return $schema;
    }

    /**
     * Crée un schéma à partir des données générées par l'IA.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID du post.
     * @param    array     $schema     Les données du schéma.
     * @return   int|WP_Error          L'ID du schéma créé ou une erreur.
     */
    public function create_schema_from_ai( $post_id, $schema ) {
        // Vérifier que le schéma est valide
        if ( ! isset( $schema['@type'] ) ) {
            return new WP_Error( 'invalid_schema', __( 'Le schéma n\'est pas valide.', 'boss-seo' ) );
        }

        // Récupérer le post
        $post = get_post( $post_id );

        if ( ! $post ) {
            return new WP_Error( 'post_not_found', __( 'Post non trouvé.', 'boss-seo' ) );
        }

        // Préparer les données du schéma
        $schema_data = array(
            'title' => sprintf( __( 'Schéma %s pour %s', 'boss-seo' ), $schema['@type'], $post->post_title ),
            'type' => $schema['@type'],
            'properties' => $this->extract_properties_from_schema( $schema ),
            'active' => true
        );

        // Créer le schéma
        if ( class_exists( 'Boss_Structured_Schemas_Manager' ) ) {
            $schema_manager = new Boss_Structured_Schemas_Manager( $this->plugin_name, $this->version, $this->settings );
            $schema_id = $schema_manager->create_schema( $schema_data );

            if ( ! is_wp_error( $schema_id ) ) {
                // Créer une règle pour appliquer ce schéma au post
                $this->create_rule_for_schema( $schema_id, $post_id );
            }

            return $schema_id;
        }

        return new WP_Error( 'schema_manager_unavailable', __( 'Le gestionnaire de schémas n\'est pas disponible.', 'boss-seo' ) );
    }

    /**
     * Extrait les propriétés d'un schéma.
     *
     * @since    1.2.0
     * @param    array     $schema    Le schéma.
     * @return   array                Les propriétés du schéma.
     */
    private function extract_properties_from_schema( $schema ) {
        $properties = array();

        foreach ( $schema as $key => $value ) {
            // Ignorer les propriétés spéciales
            if ( $key === '@context' || $key === '@type' ) {
                continue;
            }

            $properties[ $key ] = $value;
        }

        return $properties;
    }

    /**
     * Crée une règle pour appliquer un schéma à un post.
     *
     * @since    1.2.0
     * @param    int       $schema_id    L'ID du schéma.
     * @param    int       $post_id      L'ID du post.
     * @return   int|WP_Error            L'ID de la règle créée ou une erreur.
     */
    private function create_rule_for_schema( $schema_id, $post_id ) {
        if ( class_exists( 'Boss_Structured_Schemas_Rules' ) ) {
            $rules_manager = new Boss_Structured_Schemas_Rules( $this->plugin_name, $this->version, $this->settings );

            $rule_data = array(
                'title' => sprintf( __( 'Règle pour le post #%d', 'boss-seo' ), $post_id ),
                'schema_ids' => array( $schema_id ),
                'conditions' => array(
                    array(
                        'type' => 'post_id',
                        'operator' => 'is',
                        'value' => $post_id
                    )
                ),
                'priority' => 10,
                'active' => true
            );

            return $rules_manager->create_rule( $rule_data );
        }

        return new WP_Error( 'rules_manager_unavailable', __( 'Le gestionnaire de règles n\'est pas disponible.', 'boss-seo' ) );
    }

    /**
     * Génère un schéma de base sans IA.
     *
     * @since    1.2.0
     * @param    int       $post_id       L'ID du post.
     * @param    string    $schema_type   Le type de schéma à générer.
     * @return   array                    Le schéma généré.
     */
    private function generate_fallback_schema( $post_id, $schema_type ) {
        $post = get_post( $post_id );

        if ( ! $post ) {
            return new WP_Error( 'post_not_found', __( 'Post non trouvé.', 'boss-seo' ) );
        }

        $post_data = $this->prepare_post_data( $post );

        // Schéma de base selon le type
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => $schema_type
        );

        switch ( $schema_type ) {
            case 'Article':
                $schema = array_merge( $schema, array(
                    'headline' => $post_data['title'],
                    'description' => $post_data['excerpt'] ?: wp_trim_words( $post_data['content'], 30 ),
                    'url' => $post_data['permalink'],
                    'datePublished' => $post_data['date'],
                    'dateModified' => $post_data['modified'],
                    'author' => array(
                        '@type' => 'Person',
                        'name' => $post_data['author']['name'],
                        'url' => $post_data['author']['url']
                    ),
                    'publisher' => array(
                        '@type' => 'Organization',
                        'name' => get_bloginfo( 'name' ),
                        'url' => home_url()
                    )
                ) );

                if ( $post_data['featured_image'] ) {
                    $schema['image'] = $post_data['featured_image'];
                }

                if ( ! empty( $post_data['categories'] ) ) {
                    $schema['articleSection'] = array_column( $post_data['categories'], 'name' );
                }

                if ( ! empty( $post_data['tags'] ) ) {
                    $schema['keywords'] = implode( ', ', array_column( $post_data['tags'], 'name' ) );
                }
                break;

            case 'WebPage':
                $schema = array_merge( $schema, array(
                    'name' => $post_data['title'],
                    'description' => $post_data['excerpt'] ?: wp_trim_words( $post_data['content'], 30 ),
                    'url' => $post_data['permalink'],
                    'datePublished' => $post_data['date'],
                    'dateModified' => $post_data['modified'],
                    'isPartOf' => array(
                        '@type' => 'WebSite',
                        'name' => get_bloginfo( 'name' ),
                        'url' => home_url()
                    )
                ) );

                if ( $post_data['featured_image'] ) {
                    $schema['primaryImageOfPage'] = array(
                        '@type' => 'ImageObject',
                        'url' => $post_data['featured_image']
                    );
                }
                break;

            case 'Organization':
                $schema = array_merge( $schema, array(
                    'name' => $post_data['title'],
                    'description' => $post_data['excerpt'] ?: wp_trim_words( $post_data['content'], 30 ),
                    'url' => $post_data['permalink']
                ) );

                if ( $post_data['featured_image'] ) {
                    $schema['logo'] = $post_data['featured_image'];
                }
                break;

            case 'Person':
                $schema = array_merge( $schema, array(
                    'name' => $post_data['title'],
                    'description' => $post_data['excerpt'] ?: wp_trim_words( $post_data['content'], 30 ),
                    'url' => $post_data['permalink']
                ) );

                if ( $post_data['featured_image'] ) {
                    $schema['image'] = $post_data['featured_image'];
                }
                break;

            case 'Product':
                $schema = array_merge( $schema, array(
                    'name' => $post_data['title'],
                    'description' => $post_data['excerpt'] ?: wp_trim_words( $post_data['content'], 30 ),
                    'url' => $post_data['permalink']
                ) );

                if ( $post_data['featured_image'] ) {
                    $schema['image'] = $post_data['featured_image'];
                }
                break;

            default:
                $schema = array_merge( $schema, array(
                    'name' => $post_data['title'],
                    'description' => $post_data['excerpt'] ?: wp_trim_words( $post_data['content'], 30 ),
                    'url' => $post_data['permalink']
                ) );
                break;
        }

        return $schema;
    }
}
