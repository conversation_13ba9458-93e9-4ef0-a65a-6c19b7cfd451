import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  TextControl,
  SelectControl,
  ToggleControl,
  Notice,
  Dashicon
} from '@wordpress/components';

const PreconnectSettings = ({ 
  preconnectUrls, 
  onAddPreconnect, 
  onRemovePreconnect, 
  onSaveSettings, 
  isSaving,
  settings
}) => {
  // États
  const [newUrl, setNewUrl] = useState('');
  const [newType, setNewType] = useState('preconnect');
  const [urlError, setUrlError] = useState(null);
  const [updatedSettings, setUpdatedSettings] = useState(settings);
  
  // Fonction pour valider l'URL
  const validateUrl = () => {
    if (!newUrl) {
      setUrlError(__('Veuillez entrer une URL', 'boss-seo'));
      return false;
    }
    
    try {
      new URL(newUrl);
      setUrlError(null);
      return true;
    } catch (e) {
      setUrlError(__('URL invalide. Veuillez entrer une URL complète (ex: https://example.com)', 'boss-seo'));
      return false;
    }
  };
  
  // Fonction pour ajouter une URL de préconnexion
  const handleAddUrl = () => {
    if (validateUrl()) {
      onAddPreconnect(newUrl, newType);
      setNewUrl('');
    }
  };
  
  // Fonction pour mettre à jour les paramètres
  const updateSetting = (key, value) => {
    setUpdatedSettings({
      ...updatedSettings,
      [key]: value
    });
  };
  
  // Fonction pour enregistrer les paramètres
  const handleSaveSettings = () => {
    onSaveSettings(updatedSettings);
  };
  
  // Fonction pour obtenir le texte du type
  const getTypeText = (type) => {
    switch (type) {
      case 'preconnect':
        return __('Préconnexion', 'boss-seo');
      case 'dns-prefetch':
        return __('Préchargement DNS', 'boss-seo');
      case 'preload':
        return __('Préchargement', 'boss-seo');
      case 'prefetch':
        return __('Préextraction', 'boss-seo');
      default:
        return type;
    }
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du type
  const getTypeColorClass = (type) => {
    switch (type) {
      case 'preconnect':
        return 'boss-bg-blue-100 boss-text-blue-800';
      case 'dns-prefetch':
        return 'boss-bg-green-100 boss-text-green-800';
      case 'preload':
        return 'boss-bg-purple-100 boss-text-purple-800';
      case 'prefetch':
        return 'boss-bg-yellow-100 boss-text-yellow-800';
      default:
        return 'boss-bg-gray-100 boss-text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Préconnexion et préchargement', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
          <div>
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
              {__('URLs configurées', 'boss-seo')}
            </h3>
            
            <div className="boss-mb-6">
              <div className="boss-flex boss-space-x-2 boss-mb-4">
                <TextControl
                  placeholder={__('https://example.com', 'boss-seo')}
                  value={newUrl}
                  onChange={(value) => {
                    setNewUrl(value);
                    if (urlError) setUrlError(null);
                  }}
                  className="boss-flex-1"
                />
                <SelectControl
                  value={newType}
                  options={[
                    { label: __('Préconnexion', 'boss-seo'), value: 'preconnect' },
                    { label: __('Préchargement DNS', 'boss-seo'), value: 'dns-prefetch' },
                    { label: __('Préchargement', 'boss-seo'), value: 'preload' },
                    { label: __('Préextraction', 'boss-seo'), value: 'prefetch' }
                  ]}
                  onChange={setNewType}
                  className="boss-w-48"
                />
                <div className="boss-flex boss-items-end boss-mb-2">
                  <Button
                    isPrimary
                    onClick={handleAddUrl}
                  >
                    {__('Ajouter', 'boss-seo')}
                  </Button>
                </div>
              </div>
              
              {urlError && (
                <Notice status="error" isDismissible={false} className="boss-mb-4">
                  {urlError}
                </Notice>
              )}
            </div>
            
            <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-overflow-hidden">
              {preconnectUrls.length === 0 ? (
                <div className="boss-p-4 boss-text-center boss-text-boss-gray">
                  {__('Aucune URL configurée.', 'boss-seo')}
                </div>
              ) : (
                <div className="boss-divide-y boss-divide-gray-200">
                  {preconnectUrls.map((item, index) => (
                    <div key={index} className="boss-p-4 boss-flex boss-justify-between boss-items-center">
                      <div>
                        <div className="boss-font-medium boss-text-boss-dark boss-mb-1">{item.url}</div>
                        <span className={`boss-px-2 boss-py-1 boss-text-xs boss-rounded-full ${getTypeColorClass(item.type)}`}>
                          {getTypeText(item.type)}
                        </span>
                      </div>
                      <Button
                        isDestructive
                        isSmall
                        onClick={() => onRemovePreconnect(index)}
                      >
                        <Dashicon icon="trash" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div className="boss-mt-4 boss-p-4 boss-bg-gray-50 boss-rounded-lg boss-text-sm boss-text-boss-gray">
              <h4 className="boss-font-medium boss-text-boss-dark boss-mb-2">
                {__('À quoi servent ces options ?', 'boss-seo')}
              </h4>
              <ul className="boss-list-disc boss-pl-5 boss-space-y-1">
                <li>
                  <strong>{__('Préconnexion', 'boss-seo')}</strong>: {__('Établit une connexion anticipée à un domaine externe.', 'boss-seo')}
                </li>
                <li>
                  <strong>{__('Préchargement DNS', 'boss-seo')}</strong>: {__('Résout le DNS d\'un domaine à l\'avance.', 'boss-seo')}
                </li>
                <li>
                  <strong>{__('Préchargement', 'boss-seo')}</strong>: {__('Charge une ressource spécifique avant qu\'elle ne soit nécessaire.', 'boss-seo')}
                </li>
                <li>
                  <strong>{__('Préextraction', 'boss-seo')}</strong>: {__('Récupère une ressource pour une utilisation future.', 'boss-seo')}
                </li>
              </ul>
            </div>
          </div>
          
          <div>
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
              {__('Paramètres de compression', 'boss-seo')}
            </h3>
            
            <div className="boss-space-y-4">
              <ToggleControl
                label={__('Activer la compression Gzip', 'boss-seo')}
                help={__('Compresse les ressources pour réduire le temps de chargement', 'boss-seo')}
                checked={updatedSettings.enableGzip}
                onChange={(value) => updateSetting('enableGzip', value)}
              />
              
              <ToggleControl
                label={__('Activer la compression Brotli', 'boss-seo')}
                help={__('Algorithme de compression plus efficace que Gzip (si disponible sur le serveur)', 'boss-seo')}
                checked={updatedSettings.enableBrotli}
                onChange={(value) => updateSetting('enableBrotli', value)}
              />
              
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mt-6 boss-mb-3">
                {__('Paramètres de cache', 'boss-seo')}
              </h3>
              
              <ToggleControl
                label={__('Activer le cache du navigateur', 'boss-seo')}
                help={__('Configure les en-têtes de cache pour les ressources statiques', 'boss-seo')}
                checked={updatedSettings.enableBrowserCache}
                onChange={(value) => updateSetting('enableBrowserCache', value)}
              />
              
              <SelectControl
                label={__('Durée du cache pour les images', 'boss-seo')}
                value={updatedSettings.imageCacheDuration}
                options={[
                  { label: __('1 jour', 'boss-seo'), value: '1d' },
                  { label: __('1 semaine', 'boss-seo'), value: '1w' },
                  { label: __('2 semaines', 'boss-seo'), value: '2w' },
                  { label: __('1 mois', 'boss-seo'), value: '1m' },
                  { label: __('6 mois', 'boss-seo'), value: '6m' },
                  { label: __('1 an', 'boss-seo'), value: '1y' }
                ]}
                onChange={(value) => updateSetting('imageCacheDuration', value)}
                disabled={!updatedSettings.enableBrowserCache}
              />
              
              <SelectControl
                label={__('Durée du cache pour les CSS/JS', 'boss-seo')}
                value={updatedSettings.assetsCacheDuration}
                options={[
                  { label: __('1 jour', 'boss-seo'), value: '1d' },
                  { label: __('1 semaine', 'boss-seo'), value: '1w' },
                  { label: __('2 semaines', 'boss-seo'), value: '2w' },
                  { label: __('1 mois', 'boss-seo'), value: '1m' },
                  { label: __('6 mois', 'boss-seo'), value: '6m' },
                  { label: __('1 an', 'boss-seo'), value: '1y' }
                ]}
                onChange={(value) => updateSetting('assetsCacheDuration', value)}
                disabled={!updatedSettings.enableBrowserCache}
              />
              
              <ToggleControl
                label={__('Inclure ETag', 'boss-seo')}
                help={__('Ajoute un identifiant unique pour valider si une ressource a changé', 'boss-seo')}
                checked={updatedSettings.includeEtag}
                onChange={(value) => updateSetting('includeEtag', value)}
                disabled={!updatedSettings.enableBrowserCache}
              />
            </div>
          </div>
        </div>
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200">
        <div className="boss-flex boss-justify-end">
          <Button
            isPrimary
            onClick={handleSaveSettings}
            isBusy={isSaving}
            disabled={isSaving}
          >
            {isSaving ? __('Enregistrement...', 'boss-seo') : __('Enregistrer les paramètres', 'boss-seo')}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default PreconnectSettings;
