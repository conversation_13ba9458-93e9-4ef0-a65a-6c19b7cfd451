/**
 * Service pour les fonctionnalités de performance
 * 
 * Gère les communications avec l'API pour les métriques de performance
 */

import apiFetch from '@wordpress/api-fetch';

class PerformanceService {
  /**
   * Récupère les métriques Core Web Vitals
   * 
   * @returns {Promise} Promesse contenant les métriques Core Web Vitals
   */
  async getCoreWebVitals() {
    try {
      const path = '/boss-seo/v1/performance/core-web-vitals';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des Core Web Vitals:', error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des performances
   * 
   * @param {number} days - Nombre de jours d'historique à récupérer
   * @returns {Promise} Promesse contenant l'historique des performances
   */
  async getPerformanceHistory(days = 30) {
    try {
      const path = `/boss-seo/v1/performance/history?days=${days}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des performances:', error);
      throw error;
    }
  }

  /**
   * Lance une analyse de performance
   * 
   * @returns {Promise} Promesse contenant les résultats de l'analyse
   */
  async analyzePerformance() {
    try {
      const path = '/boss-seo/v1/performance/analyze';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'analyse des performances:', error);
      throw error;
    }
  }

  /**
   * Récupère les recommandations de performance
   * 
   * @returns {Promise} Promesse contenant les recommandations
   */
  async getPerformanceRecommendations() {
    try {
      const path = '/boss-seo/v1/performance/recommendations';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations de performance:', error);
      throw error;
    }
  }

  /**
   * Récupère les données de PageSpeed Insights
   * 
   * @param {string} strategy - Stratégie (mobile ou desktop)
   * @returns {Promise} Promesse contenant les données de PageSpeed Insights
   */
  async getPageSpeedInsights(strategy = 'mobile') {
    try {
      const path = `/boss-seo/v1/performance/pagespeed?strategy=${strategy}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des données PageSpeed Insights:', error);
      throw error;
    }
  }
}

export default new PerformanceService();
