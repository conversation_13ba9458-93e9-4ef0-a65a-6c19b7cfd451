import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  TextControl,
  SelectControl,
  ToggleControl,
  Notice,
  Spinner
} from '@wordpress/components';

// Services
import optimizerService from '../../services/OptimizerService';

const ApiSettings = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [apiSettings, setApiSettings] = useState({
    ai: {
      provider: 'openai',
      openaiApiKey: '',
      openaiModel: 'gpt-4',
      openaiTemperature: 0.7,
      anthropicApiKey: '',
      anthropicModel: 'claude-3-opus',
      anthropicTemperature: 0.7,
      geminiApiKey: '',
      geminiModel: 'gemini-1.5-pro',
      geminiTemperature: 0.7,
      useAiForTitles: true,
      useAiForDescriptions: true,
      useAiForContent: true,
      useAiForAltText: true
    },
    analytics: {
      googleAnalyticsId: '',
      googleAnalyticsVersion: 'ga4',
      googleSearchConsoleVerification: '',
      bingWebmasterVerification: '',
      yandexWebmasterVerification: '',
      baiduWebmasterVerification: '',
      enableEnhancedLinkAttribution: true,
      enableDemographicReporting: true,
      anonymizeIp: true
    },
    services: {
      semrushApiKey: '',
      moz: {
        accessId: '',
        secretKey: ''
      },
      ahrefs: {
        apiKey: ''
      },
      majestic: {
        apiKey: ''
      },
      serpapi: {
        apiKey: ''
      }
    }
  });

  // Charger les données
  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true);

      try {
        // Charger les paramètres d'IA
        const aiSettings = await optimizerService.getAiSettings();

        // Mettre à jour les paramètres
        setApiSettings(prevSettings => ({
          ...prevSettings,
          ai: aiSettings
        }));

        // Charger les autres paramètres si nécessaire
        // Pour l'instant, nous utilisons les valeurs par défaut pour analytics et services

      } catch (error) {
        console.error('Erreur lors du chargement des paramètres:', error);
        // En cas d'erreur, conserver les valeurs par défaut
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // Fonction pour mettre à jour les paramètres API
  const updateApiSettings = (category, key, value) => {
    if (key.includes('.')) {
      const [subCategory, subKey] = key.split('.');
      setApiSettings({
        ...apiSettings,
        [category]: {
          ...apiSettings[category],
          [subCategory]: {
            ...apiSettings[category][subCategory],
            [subKey]: value
          }
        }
      });
    } else {
      setApiSettings({
        ...apiSettings,
        [category]: {
          ...apiSettings[category],
          [key]: value
        }
      });
    }
  };

  // Fonction pour enregistrer les paramètres
  const handleSaveSettings = async () => {
    setIsSaving(true);

    try {
      // Appeler le service pour enregistrer les paramètres d'IA
      const aiResponse = await optimizerService.saveAiSettings(apiSettings.ai);

      if (aiResponse.success) {
        // Enregistrer les autres paramètres si nécessaire
        try {
          // Appeler le service pour enregistrer les paramètres généraux
          await optimizerService.saveSettings({
            analytics: apiSettings.analytics,
            services: apiSettings.services
          });
        } catch (otherError) {
          console.error('Erreur lors de l\'enregistrement des autres paramètres:', otherError);
          // Continuer même si les autres paramètres échouent
        }

        setShowSuccess(true);

        // Masquer le message de succès après 3 secondes
        setTimeout(() => {
          setShowSuccess(false);
        }, 3000);
      } else {
        // Afficher un message d'erreur
        alert(__(`Erreur lors de l'enregistrement des paramètres : ${aiResponse.message}`, 'boss-seo'));
      }
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres:', error);
      alert(__('Une erreur est survenue lors de l\'enregistrement des paramètres. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsSaving(false);
    }
  };

  // État pour les messages de vérification
  const [verificationStatus, setVerificationStatus] = useState({
    show: false,
    isSuccess: false,
    message: ''
  });

  // Fonction pour vérifier une clé API
  const handleVerifyApi = async (category, key) => {
    // Réinitialiser le statut de vérification
    setVerificationStatus({
      show: false,
      isSuccess: false,
      message: ''
    });

    setIsVerifying(true);

    try {
      let provider = '';
      let apiKey = '';
      let providerName = '';

      // Déterminer le fournisseur et la clé API en fonction de la catégorie et de la clé
      if (category === 'ai') {
        if (key === 'openaiApiKey') {
          provider = 'openai';
          apiKey = apiSettings.ai.openaiApiKey;
          providerName = 'OpenAI';
        } else if (key === 'anthropicApiKey') {
          provider = 'anthropic';
          apiKey = apiSettings.ai.anthropicApiKey;
          providerName = 'Anthropic';
        } else if (key === 'geminiApiKey') {
          provider = 'gemini';
          apiKey = apiSettings.ai.geminiApiKey;
          providerName = 'Google Gemini';
        }
      } else if (category === 'services') {
        if (key === 'semrushApiKey') {
          provider = 'semrush';
          apiKey = apiSettings.services.semrushApiKey;
          providerName = 'SEMrush';
        } else if (key === 'moz') {
          provider = 'moz';
          apiKey = apiSettings.services.moz.secretKey;
          providerName = 'Moz';
        } else if (key === 'ahrefs') {
          provider = 'ahrefs';
          apiKey = apiSettings.services.ahrefs.apiKey;
          providerName = 'Ahrefs';
        } else if (key === 'majestic') {
          provider = 'majestic';
          apiKey = apiSettings.services.majestic.apiKey;
          providerName = 'Majestic';
        } else if (key === 'serpapi') {
          provider = 'serpapi';
          apiKey = apiSettings.services.serpapi.apiKey;
          providerName = 'SerpAPI';
        }
      }

      if (!provider || !apiKey) {
        throw new Error(__('Fournisseur ou clé API non valide', 'boss-seo'));
      }

      // Appeler le service pour vérifier la clé API
      const response = await optimizerService.verifyApiKey(provider, apiKey);

      if (response.success) {
        // Afficher un message de succès
        setVerificationStatus({
          show: true,
          isSuccess: true,
          message: __(`Clé API ${providerName} vérifiée avec succès !`, 'boss-seo')
        });

        // Masquer le message après 5 secondes
        setTimeout(() => {
          setVerificationStatus(prev => ({ ...prev, show: false }));
        }, 5000);
      } else {
        // Afficher un message d'erreur
        setVerificationStatus({
          show: true,
          isSuccess: false,
          message: __(`Erreur lors de la vérification de la clé API ${providerName} : ${response.message}`, 'boss-seo')
        });
      }
    } catch (error) {
      console.error('Erreur lors de la vérification de la clé API:', error);

      // Afficher un message d'erreur
      setVerificationStatus({
        show: true,
        isSuccess: false,
        message: __(`Erreur lors de la vérification de la clé API : ${error.message}`, 'boss-seo')
      });
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {__('Paramètres API enregistrés avec succès !', 'boss-seo')}
            </Notice>
          )}

          {verificationStatus.show && (
            <Notice
              status={verificationStatus.isSuccess ? 'success' : 'error'}
              isDismissible={true}
              className="boss-mb-6"
              onRemove={() => setVerificationStatus(prev => ({ ...prev, show: false }))}
            >
              {verificationStatus.message}
            </Notice>
          )}

          <Card className="boss-mb-6">
            <CardHeader className="boss-border-b boss-border-gray-200">
              <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                {__('Paramètres d\'IA', 'boss-seo')}
              </h2>
            </CardHeader>
            <CardBody>
              <div className="boss-space-y-4">
                <SelectControl
                  label={__('Fournisseur d\'IA', 'boss-seo')}
                  value={apiSettings.ai.provider}
                  options={[
                    { label: 'OpenAI', value: 'openai' },
                    { label: 'Anthropic', value: 'anthropic' },
                    { label: 'Google Gemini', value: 'gemini' }
                  ]}
                  onChange={(value) => updateApiSettings('ai', 'provider', value)}
                />

                {apiSettings.ai.provider === 'openai' && (
                  <div className="boss-space-y-4">
                    <div className="boss-flex boss-space-x-2">
                      <TextControl
                        label={__('Clé API OpenAI', 'boss-seo')}
                        help={__('Votre clé API OpenAI pour accéder aux modèles GPT', 'boss-seo')}
                        value={apiSettings.ai.openaiApiKey}
                        onChange={(value) => updateApiSettings('ai', 'openaiApiKey', value)}
                        type="password"
                        className="boss-flex-1"
                      />
                      <div className="boss-flex boss-items-end boss-mb-2">
                        <Button
                          isSecondary
                          onClick={() => handleVerifyApi('ai', 'openaiApiKey')}
                          disabled={!apiSettings.ai.openaiApiKey || isVerifying}
                          isBusy={isVerifying}
                        >
                          {__('Vérifier', 'boss-seo')}
                        </Button>
                      </div>
                    </div>

                    <SelectControl
                      label={__('Modèle OpenAI', 'boss-seo')}
                      value={apiSettings.ai.openaiModel}
                      options={[
                        { label: 'GPT-4', value: 'gpt-4' },
                        { label: 'GPT-4 Turbo', value: 'gpt-4-turbo' },
                        { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' }
                      ]}
                      onChange={(value) => updateApiSettings('ai', 'openaiModel', value)}
                    />

                    <div className="boss-flex boss-items-center boss-space-x-4">
                      <div className="boss-flex-1">
                        <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium boss-text-boss-dark">
                          {__('Température', 'boss-seo')} ({apiSettings.ai.openaiTemperature})
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={apiSettings.ai.openaiTemperature}
                          onChange={(e) => updateApiSettings('ai', 'openaiTemperature', parseFloat(e.target.value))}
                          className="boss-w-full"
                        />
                      </div>
                      <div className="boss-text-sm boss-text-boss-gray boss-w-32">
                        <div className="boss-flex boss-justify-between">
                          <span>{__('Précis', 'boss-seo')}</span>
                          <span>{__('Créatif', 'boss-seo')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {apiSettings.ai.provider === 'anthropic' && (
                  <div className="boss-space-y-4">
                    <div className="boss-flex boss-space-x-2">
                      <TextControl
                        label={__('Clé API Anthropic', 'boss-seo')}
                        help={__('Votre clé API Anthropic pour accéder aux modèles Claude', 'boss-seo')}
                        value={apiSettings.ai.anthropicApiKey}
                        onChange={(value) => updateApiSettings('ai', 'anthropicApiKey', value)}
                        type="password"
                        className="boss-flex-1"
                      />
                      <div className="boss-flex boss-items-end boss-mb-2">
                        <Button
                          isSecondary
                          onClick={() => handleVerifyApi('ai', 'anthropicApiKey')}
                          disabled={!apiSettings.ai.anthropicApiKey || isVerifying}
                          isBusy={isVerifying}
                        >
                          {__('Vérifier', 'boss-seo')}
                        </Button>
                      </div>
                    </div>

                    <SelectControl
                      label={__('Modèle Anthropic', 'boss-seo')}
                      value={apiSettings.ai.anthropicModel}
                      options={[
                        { label: 'Claude 3 Opus', value: 'claude-3-opus' },
                        { label: 'Claude 3 Sonnet', value: 'claude-3-sonnet' },
                        { label: 'Claude 3 Haiku', value: 'claude-3-haiku' }
                      ]}
                      onChange={(value) => updateApiSettings('ai', 'anthropicModel', value)}
                    />

                    <div className="boss-flex boss-items-center boss-space-x-4">
                      <div className="boss-flex-1">
                        <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium boss-text-boss-dark">
                          {__('Température', 'boss-seo')} ({apiSettings.ai.anthropicTemperature})
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={apiSettings.ai.anthropicTemperature}
                          onChange={(e) => updateApiSettings('ai', 'anthropicTemperature', parseFloat(e.target.value))}
                          className="boss-w-full"
                        />
                      </div>
                      <div className="boss-text-sm boss-text-boss-gray boss-w-32">
                        <div className="boss-flex boss-justify-between">
                          <span>{__('Précis', 'boss-seo')}</span>
                          <span>{__('Créatif', 'boss-seo')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {apiSettings.ai.provider === 'gemini' && (
                  <div className="boss-space-y-4">
                    <div className="boss-flex boss-space-x-2">
                      <TextControl
                        label={__('Clé API Google Gemini', 'boss-seo')}
                        help={__('Votre clé API Google pour accéder aux modèles Gemini', 'boss-seo')}
                        value={apiSettings.ai.geminiApiKey}
                        onChange={(value) => updateApiSettings('ai', 'geminiApiKey', value)}
                        type="password"
                        className="boss-flex-1"
                      />
                      <div className="boss-flex boss-items-end boss-mb-2">
                        <Button
                          isSecondary
                          onClick={() => handleVerifyApi('ai', 'geminiApiKey')}
                          disabled={!apiSettings.ai.geminiApiKey || isVerifying}
                          isBusy={isVerifying}
                        >
                          {__('Vérifier', 'boss-seo')}
                        </Button>
                      </div>
                    </div>

                    <SelectControl
                      label={__('Modèle Gemini', 'boss-seo')}
                      value={apiSettings.ai.geminiModel}
                      options={[
                        // Modèles Gemini 2.5
                        { label: 'Gemini 2.5 Pro (Expérimental)', value: 'gemini-2.5-pro' },

                        // Modèles Gemini 2.0
                        { label: 'Gemini 2.0 Flash', value: 'gemini-2.0-flash' },
                        { label: 'Gemini 2.0 Flash-Lite', value: 'gemini-2.0-flash-lite' },

                        // Modèles Gemini 1.5
                        { label: 'Gemini 1.5 Pro', value: 'gemini-1.5-pro' },
                        { label: 'Gemini 1.5 Flash', value: 'gemini-1.5-flash' },
                        { label: 'Gemini 1.5 Flash-8B', value: 'gemini-1.5-flash-8b' },
                        { label: 'Gemini 1.5 Pro Vision', value: 'gemini-1.5-pro-vision' },
                        { label: 'Gemini 1.5 Flash Vision', value: 'gemini-1.5-flash-vision' },

                        // Modèles Gemini 1.0 (anciens)
                        { label: 'Gemini 1.0 Pro', value: 'gemini-1.0-pro' },
                        { label: 'Gemini 1.0 Pro Vision', value: 'gemini-1.0-pro-vision' }
                      ]}
                      onChange={(value) => updateApiSettings('ai', 'geminiModel', value)}
                    />

                    <div className="boss-flex boss-items-center boss-space-x-4">
                      <div className="boss-flex-1">
                        <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium boss-text-boss-dark">
                          {__('Température', 'boss-seo')} ({apiSettings.ai.geminiTemperature})
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={apiSettings.ai.geminiTemperature}
                          onChange={(e) => updateApiSettings('ai', 'geminiTemperature', parseFloat(e.target.value))}
                          className="boss-w-full"
                        />
                      </div>
                      <div className="boss-text-sm boss-text-boss-gray boss-w-32">
                        <div className="boss-flex boss-justify-between">
                          <span>{__('Précis', 'boss-seo')}</span>
                          <span>{__('Créatif', 'boss-seo')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="boss-mt-6">
                  <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                    {__('Utilisation de l\'IA', 'boss-seo')}
                  </h3>

                  <div className="boss-space-y-3">
                    <ToggleControl
                      label={__('Utiliser l\'IA pour les titres', 'boss-seo')}
                      help={__('Génère des suggestions de titres optimisés pour le SEO', 'boss-seo')}
                      checked={apiSettings.ai.useAiForTitles}
                      onChange={(value) => updateApiSettings('ai', 'useAiForTitles', value)}
                    />

                    <ToggleControl
                      label={__('Utiliser l\'IA pour les descriptions', 'boss-seo')}
                      help={__('Génère des suggestions de méta-descriptions optimisées', 'boss-seo')}
                      checked={apiSettings.ai.useAiForDescriptions}
                      onChange={(value) => updateApiSettings('ai', 'useAiForDescriptions', value)}
                    />

                    <ToggleControl
                      label={__('Utiliser l\'IA pour le contenu', 'boss-seo')}
                      help={__('Génère des suggestions de contenu optimisé pour le SEO', 'boss-seo')}
                      checked={apiSettings.ai.useAiForContent}
                      onChange={(value) => updateApiSettings('ai', 'useAiForContent', value)}
                    />

                    <ToggleControl
                      label={__('Utiliser l\'IA pour les textes alternatifs', 'boss-seo')}
                      help={__('Génère automatiquement des textes alternatifs pour les images', 'boss-seo')}
                      checked={apiSettings.ai.useAiForAltText}
                      onChange={(value) => updateApiSettings('ai', 'useAiForAltText', value)}
                    />
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="boss-mb-6">
            <CardHeader className="boss-border-b boss-border-gray-200">
              <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                {__('Paramètres d\'analyse', 'boss-seo')}
              </h2>
            </CardHeader>
            <CardBody>
              <div className="boss-space-y-4">
                <TextControl
                  label={__('ID Google Analytics', 'boss-seo')}
                  help={__('Votre ID Google Analytics (ex: G-XXXXXXXXXX)', 'boss-seo')}
                  value={apiSettings.analytics.googleAnalyticsId}
                  onChange={(value) => updateApiSettings('analytics', 'googleAnalyticsId', value)}
                />

                <SelectControl
                  label={__('Version Google Analytics', 'boss-seo')}
                  value={apiSettings.analytics.googleAnalyticsVersion}
                  options={[
                    { label: 'Google Analytics 4', value: 'ga4' },
                    { label: 'Universal Analytics', value: 'ua' }
                  ]}
                  onChange={(value) => updateApiSettings('analytics', 'googleAnalyticsVersion', value)}
                />

                <TextControl
                  label={__('Code de vérification Google Search Console', 'boss-seo')}
                  help={__('Balise meta de vérification pour Google Search Console', 'boss-seo')}
                  value={apiSettings.analytics.googleSearchConsoleVerification}
                  onChange={(value) => updateApiSettings('analytics', 'googleSearchConsoleVerification', value)}
                />

                <TextControl
                  label={__('Code de vérification Bing Webmaster', 'boss-seo')}
                  help={__('Balise meta de vérification pour Bing Webmaster Tools', 'boss-seo')}
                  value={apiSettings.analytics.bingWebmasterVerification}
                  onChange={(value) => updateApiSettings('analytics', 'bingWebmasterVerification', value)}
                />

                <div className="boss-mt-6">
                  <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                    {__('Options d\'analyse', 'boss-seo')}
                  </h3>

                  <div className="boss-space-y-3">
                    <ToggleControl
                      label={__('Activer l\'attribution de lien améliorée', 'boss-seo')}
                      help={__('Permet de mieux comprendre comment les utilisateurs naviguent sur votre site', 'boss-seo')}
                      checked={apiSettings.analytics.enableEnhancedLinkAttribution}
                      onChange={(value) => updateApiSettings('analytics', 'enableEnhancedLinkAttribution', value)}
                    />

                    <ToggleControl
                      label={__('Activer les rapports démographiques', 'boss-seo')}
                      help={__('Collecte des données démographiques sur vos visiteurs', 'boss-seo')}
                      checked={apiSettings.analytics.enableDemographicReporting}
                      onChange={(value) => updateApiSettings('analytics', 'enableDemographicReporting', value)}
                    />

                    <ToggleControl
                      label={__('Anonymiser les adresses IP', 'boss-seo')}
                      help={__('Anonymise les adresses IP des visiteurs pour la conformité RGPD', 'boss-seo')}
                      checked={apiSettings.analytics.anonymizeIp}
                      onChange={(value) => updateApiSettings('analytics', 'anonymizeIp', value)}
                    />
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="boss-mb-6">
            <CardHeader className="boss-border-b boss-border-gray-200">
              <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                {__('Services tiers', 'boss-seo')}
              </h2>
            </CardHeader>
            <CardBody>
              <div className="boss-space-y-4">
                <div className="boss-flex boss-space-x-2">
                  <TextControl
                    label={__('Clé API SEMrush', 'boss-seo')}
                    help={__('Votre clé API SEMrush pour les données de mots-clés', 'boss-seo')}
                    value={apiSettings.services.semrushApiKey}
                    onChange={(value) => updateApiSettings('services', 'semrushApiKey', value)}
                    type="password"
                    className="boss-flex-1"
                  />
                  <div className="boss-flex boss-items-end boss-mb-2">
                    <Button
                      isSecondary
                      onClick={() => handleVerifyApi('services', 'semrushApiKey')}
                      disabled={!apiSettings.services.semrushApiKey || isVerifying}
                      isBusy={isVerifying}
                    >
                      {__('Vérifier', 'boss-seo')}
                    </Button>
                  </div>
                </div>

                <div className="boss-p-4 boss-bg-gray-50 boss-rounded-lg boss-mb-4">
                  <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                    {__('Moz', 'boss-seo')}
                  </h3>

                  <div className="boss-space-y-4">
                    <TextControl
                      label={__('ID d\'accès Moz', 'boss-seo')}
                      value={apiSettings.services.moz.accessId}
                      onChange={(value) => updateApiSettings('services', 'moz.accessId', value)}
                    />

                    <div className="boss-flex boss-space-x-2">
                      <TextControl
                        label={__('Clé secrète Moz', 'boss-seo')}
                        value={apiSettings.services.moz.secretKey}
                        onChange={(value) => updateApiSettings('services', 'moz.secretKey', value)}
                        type="password"
                        className="boss-flex-1"
                      />
                      <div className="boss-flex boss-items-end boss-mb-2">
                        <Button
                          isSecondary
                          onClick={() => handleVerifyApi('services', 'moz')}
                          disabled={!apiSettings.services.moz.accessId || !apiSettings.services.moz.secretKey || isVerifying}
                          isBusy={isVerifying}
                        >
                          {__('Vérifier', 'boss-seo')}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="boss-flex boss-space-x-2">
                  <TextControl
                    label={__('Clé API Ahrefs', 'boss-seo')}
                    value={apiSettings.services.ahrefs.apiKey}
                    onChange={(value) => updateApiSettings('services', 'ahrefs.apiKey', value)}
                    type="password"
                    className="boss-flex-1"
                  />
                  <div className="boss-flex boss-items-end boss-mb-2">
                    <Button
                      isSecondary
                      onClick={() => handleVerifyApi('services', 'ahrefs')}
                      disabled={!apiSettings.services.ahrefs.apiKey || isVerifying}
                      isBusy={isVerifying}
                    >
                      {__('Vérifier', 'boss-seo')}
                    </Button>
                  </div>
                </div>

                <div className="boss-flex boss-space-x-2">
                  <TextControl
                    label={__('Clé API Majestic', 'boss-seo')}
                    value={apiSettings.services.majestic.apiKey}
                    onChange={(value) => updateApiSettings('services', 'majestic.apiKey', value)}
                    type="password"
                    className="boss-flex-1"
                  />
                  <div className="boss-flex boss-items-end boss-mb-2">
                    <Button
                      isSecondary
                      onClick={() => handleVerifyApi('services', 'majestic')}
                      disabled={!apiSettings.services.majestic.apiKey || isVerifying}
                      isBusy={isVerifying}
                    >
                      {__('Vérifier', 'boss-seo')}
                    </Button>
                  </div>
                </div>

                <div className="boss-flex boss-space-x-2">
                  <TextControl
                    label={__('Clé API SerpAPI', 'boss-seo')}
                    help={__('Pour récupérer les données SERP en temps réel', 'boss-seo')}
                    value={apiSettings.services.serpapi.apiKey}
                    onChange={(value) => updateApiSettings('services', 'serpapi.apiKey', value)}
                    type="password"
                    className="boss-flex-1"
                  />
                  <div className="boss-flex boss-items-end boss-mb-2">
                    <Button
                      isSecondary
                      onClick={() => handleVerifyApi('services', 'serpapi')}
                      disabled={!apiSettings.services.serpapi.apiKey || isVerifying}
                      isBusy={isVerifying}
                    >
                      {__('Vérifier', 'boss-seo')}
                    </Button>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          <div className="boss-mt-6 boss-flex boss-justify-end">
            <Button
              isPrimary
              onClick={handleSaveSettings}
              isBusy={isSaving}
              disabled={isSaving}
              className="boss-px-6"
            >
              {isSaving ? __('Enregistrement...', 'boss-seo') : __('Enregistrer les paramètres API', 'boss-seo')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApiSettings;
