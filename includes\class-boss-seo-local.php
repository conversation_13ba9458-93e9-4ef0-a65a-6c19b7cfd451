<?php
/**
 * Classe principale pour le module SEO Local.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe principale pour le module SEO Local.
 *
 * Cette classe gère toutes les fonctionnalités liées au module SEO Local.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Local {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * L'instance de la classe Boss_Location.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Location    $location    L'instance de la classe Boss_Location.
     */
    protected $location;

    /**
     * L'instance de la classe Boss_Business_Info.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Business_Info    $business_info    L'instance de la classe Boss_Business_Info.
     */
    protected $business_info;

    /**
     * L'instance de la classe Boss_Local_Page_Generator.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Page_Generator    $page_generator    L'instance de la classe Boss_Local_Page_Generator.
     */
    protected $page_generator;

    /**
     * L'instance de la classe Boss_Local_Schema.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Schema    $schema    L'instance de la classe Boss_Local_Schema.
     */
    protected $schema;

    /**
     * L'instance de la classe Boss_Local_Rankings.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Rankings    $rankings    L'instance de la classe Boss_Local_Rankings.
     */
    protected $rankings;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->load_dependencies();
        $this->init_components();
    }

    /**
     * Charge les dépendances nécessaires pour ce module.
     *
     * @since    1.2.0
     * @access   private
     */
    private function load_dependencies() {
        /**
         * La classe qui gère les emplacements.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-location.php';

        /**
         * La classe qui gère les informations d'entreprise.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-business-info.php';

        /**
         * La classe qui gère la génération de pages locales.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-page-generator.php';

        /**
         * La classe qui gère les schémas structurés locaux.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-schema.php';

        /**
         * La classe qui gère le suivi des positions locales.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-rankings.php';
    }

    /**
     * Initialise les composants du module.
     *
     * @since    1.2.0
     * @access   private
     */
    private function init_components() {
        $this->location = new Boss_Location( $this->plugin_name, $this->version );
        $this->business_info = new Boss_Business_Info( $this->plugin_name, $this->version );
        $this->page_generator = new Boss_Local_Page_Generator( $this->plugin_name, $this->version );
        $this->schema = new Boss_Local_Schema( $this->plugin_name, $this->version );
        $this->rankings = new Boss_Local_Rankings( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Enregistrer les hooks pour chaque composant
        $this->location->register_hooks();
        $this->business_info->register_hooks();
        $this->page_generator->register_hooks();
        $this->schema->register_hooks();
        $this->rankings->register_hooks();

        // Ajouter les actions pour les assets
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_styles' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_public_styles' ) );
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_public_scripts' ) );

        // Ajouter les actions pour les menus
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );

        // Ajouter les actions pour les widgets
        add_action( 'widgets_init', array( $this, 'register_widgets' ) );

        // Ajouter les actions pour les shortcodes
        add_action( 'init', array( $this, 'register_shortcodes' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Enregistrer les routes REST API pour chaque composant
        $this->location->register_rest_routes();
        $this->business_info->register_rest_routes();
        $this->page_generator->register_rest_routes();
        $this->schema->register_rest_routes();
        $this->rankings->register_rest_routes();
    }

    /**
     * Enregistre les styles pour l'administration.
     *
     * @since    1.2.0
     * @param    string    $hook_suffix    Le suffixe du hook.
     */
    public function enqueue_admin_styles( $hook_suffix ) {
        // Vérifier si nous sommes sur une page du plugin
        if ( strpos( $hook_suffix, 'boss-seo' ) !== false || strpos( $hook_suffix, 'boss_location' ) !== false ) {
            wp_enqueue_style( 'boss-seo-local-admin', plugin_dir_url( dirname( __FILE__ ) ) . 'admin/css/boss-seo-local-admin.css', array(), $this->version, 'all' );
        }
    }

    /**
     * Enregistre les scripts pour l'administration.
     *
     * @since    1.2.0
     * @param    string    $hook_suffix    Le suffixe du hook.
     */
    public function enqueue_admin_scripts( $hook_suffix ) {
        // Vérifier si nous sommes sur une page du plugin
        if ( strpos( $hook_suffix, 'boss-seo' ) !== false || strpos( $hook_suffix, 'boss_location' ) !== false ) {
            wp_enqueue_script( 'boss-seo-local-admin', plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/boss-seo-local-admin.js', array( 'jquery' ), $this->version, false );

            // Ajouter les variables locales
            wp_localize_script( 'boss-seo-local-admin', 'boss_seo_local', array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'boss_seo_local_nonce' ),
                'strings'  => array(
                    'confirm_delete'     => __( 'Êtes-vous sûr de vouloir supprimer cet élément ?', 'boss-seo' ),
                    'confirm_regenerate' => __( 'Êtes-vous sûr de vouloir régénérer cette page ?', 'boss-seo' ),
                    'error'              => __( 'Une erreur s\'est produite.', 'boss-seo' ),
                    'success'            => __( 'Opération réussie.', 'boss-seo' ),
                ),
            ) );
        }
    }

    /**
     * Enregistre les styles pour le front-end.
     *
     * @since    1.2.0
     */
    public function enqueue_public_styles() {
        wp_enqueue_style( 'boss-seo-local-public', plugin_dir_url( dirname( __FILE__ ) ) . 'public/css/boss-seo-local-public.css', array(), $this->version, 'all' );
    }

    /**
     * Enregistre les scripts pour le front-end.
     *
     * @since    1.2.0
     */
    public function enqueue_public_scripts() {
        wp_enqueue_script( 'boss-seo-local-public', plugin_dir_url( dirname( __FILE__ ) ) . 'public/js/boss-seo-local-public.js', array( 'jquery' ), $this->version, false );
    }

    /**
     * Ajoute les menus d'administration.
     *
     * @since    1.2.0
     */
    public function add_admin_menu() {
        // Nous n'ajoutons plus de sous-menus ici car ils sont gérés par l'interface à onglets
        // dans la page "SEO local & e-commerce"
    }

    /**
     * Affiche la page d'administration principale.
     *
     * @since    1.2.0
     */
    public function display_admin_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-seo-local-admin-display.php';
    }

    /**
     * Affiche la page d'informations d'entreprise.
     *
     * @since    1.2.0
     */
    public function display_business_info_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-seo-business-info-display.php';
    }

    /**
     * Affiche la page du générateur de pages.
     *
     * @since    1.2.0
     */
    public function display_page_generator_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-seo-page-generator-display.php';
    }

    /**
     * Affiche la page des schémas structurés.
     *
     * @since    1.2.0
     */
    public function display_schema_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-seo-schema-display.php';
    }

    /**
     * Affiche la page de suivi des positions.
     *
     * @since    1.2.0
     */
    public function display_rankings_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-seo-rankings-display.php';
    }

    /**
     * Enregistre les widgets.
     *
     * @since    1.2.0
     */
    public function register_widgets() {
        // Enregistrer les widgets
        register_widget( 'Boss_Local_Info_Widget' );
        register_widget( 'Boss_Local_Map_Widget' );
        register_widget( 'Boss_Local_Hours_Widget' );
    }

    /**
     * Enregistre les shortcodes.
     *
     * @since    1.2.0
     */
    public function register_shortcodes() {
        // Les shortcodes sont déjà enregistrés dans la classe Boss_Local_Page_Generator
    }

    /**
     * Récupère l'instance de la classe Boss_Location.
     *
     * @since    1.2.0
     * @return   Boss_Location    L'instance de la classe Boss_Location.
     */
    public function get_location() {
        return $this->location;
    }

    /**
     * Récupère l'instance de la classe Boss_Business_Info.
     *
     * @since    1.2.0
     * @return   Boss_Business_Info    L'instance de la classe Boss_Business_Info.
     */
    public function get_business_info() {
        return $this->business_info;
    }

    /**
     * Récupère l'instance de la classe Boss_Local_Page_Generator.
     *
     * @since    1.2.0
     * @return   Boss_Local_Page_Generator    L'instance de la classe Boss_Local_Page_Generator.
     */
    public function get_page_generator() {
        return $this->page_generator;
    }

    /**
     * Récupère l'instance de la classe Boss_Local_Schema.
     *
     * @since    1.2.0
     * @return   Boss_Local_Schema    L'instance de la classe Boss_Local_Schema.
     */
    public function get_schema() {
        return $this->schema;
    }

    /**
     * Récupère l'instance de la classe Boss_Local_Rankings.
     *
     * @since    1.2.0
     * @return   Boss_Local_Rankings    L'instance de la classe Boss_Local_Rankings.
     */
    public function get_rankings() {
        return $this->rankings;
    }
}
