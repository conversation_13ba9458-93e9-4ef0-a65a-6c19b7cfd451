import { useState, useEffect, useRef } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { Button, Modal } from '@wordpress/components';

/**
 * Composant de tour guidé interactif
 * 
 * @param {Object} props - Propriétés du composant
 * @param {Array} props.steps - Étapes du tour guidé
 * @param {boolean} props.isOpen - Le tour est-il ouvert
 * @param {Function} props.onClose - Callback à la fermeture
 * @param {Function} props.onComplete - Callback à la fin du tour
 * @param {string} props.tourId - Identifiant unique du tour
 * @param {boolean} props.showProgress - Afficher la progression
 * @returns {React.ReactElement} Composant TourGuide
 */
const TourGuide = ({ 
  steps = [], 
  isOpen = false, 
  onClose = () => {}, 
  onComplete = () => {},
  tourId = 'default-tour',
  showProgress = true
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(isOpen);
  const [targetElement, setTargetElement] = useState(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const tourRef = useRef(null);
  
  // Mettre à jour la visibilité lorsque isOpen change
  useEffect(() => {
    setIsVisible(isOpen);
    if (isOpen) {
      setCurrentStep(0);
    }
  }, [isOpen]);
  
  // Trouver l'élément cible pour l'étape actuelle
  useEffect(() => {
    if (!isVisible || !steps[currentStep]) return;
    
    const findTarget = () => {
      const { target } = steps[currentStep];
      let element = null;
      
      if (typeof target === 'string') {
        // Sélecteur CSS
        element = document.querySelector(target);
      } else if (target instanceof HTMLElement) {
        // Élément HTML direct
        element = target;
      }
      
      if (element) {
        setTargetElement(element);
        positionTour(element);
      } else {
        // Si l'élément n'est pas trouvé, positionner au centre
        setTargetElement(null);
        setPosition({
          top: window.innerHeight / 2 - 150,
          left: window.innerWidth / 2 - 150
        });
      }
    };
    
    // Attendre que le DOM soit prêt
    setTimeout(findTarget, 100);
    
    // Ajouter une classe à l'élément cible pour le mettre en évidence
    if (targetElement) {
      targetElement.classList.add('boss-tour-highlight');
    }
    
    return () => {
      // Nettoyer la classe de mise en évidence
      if (targetElement) {
        targetElement.classList.remove('boss-tour-highlight');
      }
    };
  }, [isVisible, currentStep, steps]);
  
  // Positionner le tour par rapport à l'élément cible
  const positionTour = (element) => {
    if (!element) return;
    
    const rect = element.getBoundingClientRect();
    const tourHeight = 300; // Hauteur estimée du tour
    const tourWidth = 300; // Largeur estimée du tour
    
    // Déterminer la position en fonction de l'espace disponible
    let top, left;
    
    // Préférer en dessous de l'élément
    if (rect.bottom + tourHeight < window.innerHeight) {
      top = rect.bottom + 10;
    } 
    // Sinon, au-dessus si possible
    else if (rect.top - tourHeight > 0) {
      top = rect.top - tourHeight - 10;
    } 
    // Sinon, à côté
    else {
      top = Math.max(10, rect.top);
    }
    
    // Centrer horizontalement par rapport à l'élément
    left = rect.left + rect.width / 2 - tourWidth / 2;
    
    // S'assurer que le tour reste dans la fenêtre
    left = Math.max(10, Math.min(window.innerWidth - tourWidth - 10, left));
    
    setPosition({ top, left });
  };
  
  // Passer à l'étape suivante
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };
  
  // Revenir à l'étape précédente
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  // Terminer le tour
  const handleComplete = () => {
    setIsVisible(false);
    onComplete();
    
    // Enregistrer que l'utilisateur a terminé ce tour
    localStorage.setItem(`boss_tour_completed_${tourId}`, 'true');
  };
  
  // Fermer le tour
  const handleClose = () => {
    setIsVisible(false);
    onClose();
  };
  
  // Si le tour n'est pas visible, ne rien afficher
  if (!isVisible || !steps.length) {
    return null;
  }
  
  const currentStepData = steps[currentStep];
  
  return (
    <div 
      ref={tourRef}
      className="boss-fixed boss-z-50 boss-bg-white boss-rounded-lg boss-shadow-xl boss-w-80 boss-transition-all boss-duration-300 boss-ease-in-out"
      style={{ 
        top: `${position.top}px`, 
        left: `${position.left}px` 
      }}
    >
      <div className="boss-p-4">
        {/* En-tête */}
        <div className="boss-flex boss-justify-between boss-items-center boss-mb-3">
          <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark">
            {currentStepData.title || __('Tour guidé', 'boss-seo')}
          </h3>
          <button 
            className="boss-text-gray-400 boss-hover:boss-text-gray-600 focus:boss-outline-none"
            onClick={handleClose}
            aria-label={__('Fermer', 'boss-seo')}
          >
            <span className="dashicons dashicons-no-alt"></span>
          </button>
        </div>
        
        {/* Contenu */}
        <div className="boss-mb-4 boss-text-boss-gray boss-text-sm">
          {currentStepData.content}
        </div>
        
        {/* Barre de progression */}
        {showProgress && (
          <div className="boss-mb-4">
            <div className="boss-h-1 boss-w-full boss-bg-gray-200 boss-rounded-full">
              <div 
                className="boss-h-1 boss-bg-boss-blue-500 boss-rounded-full boss-transition-all boss-duration-300 boss-ease-in-out"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              ></div>
            </div>
            <div className="boss-text-xs boss-text-boss-gray boss-mt-1 boss-text-right">
              {currentStep + 1} / {steps.length}
            </div>
          </div>
        )}
        
        {/* Actions */}
        <div className="boss-flex boss-justify-between">
          <Button
            isSecondary
            isSmall
            onClick={handlePrevious}
            disabled={currentStep === 0}
          >
            {__('Précédent', 'boss-seo')}
          </Button>
          
          <div className="boss-flex boss-space-x-2">
            <Button
              isLink
              isSmall
              onClick={handleClose}
            >
              {__('Ignorer', 'boss-seo')}
            </Button>
            
            <Button
              isPrimary
              isSmall
              onClick={handleNext}
            >
              {currentStep < steps.length - 1 ? __('Suivant', 'boss-seo') : __('Terminer', 'boss-seo')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Composant pour démarrer un tour guidé
 * 
 * @param {Object} props - Propriétés du composant
 * @param {Array} props.steps - Étapes du tour guidé
 * @param {string} props.tourId - Identifiant unique du tour
 * @param {string} props.buttonText - Texte du bouton
 * @param {boolean} props.showAsIcon - Afficher comme une icône
 * @param {boolean} props.autoStart - Démarrer automatiquement
 * @param {Function} props.onComplete - Callback à la fin du tour
 * @returns {React.ReactElement} Composant TourGuideStarter
 */
export const TourGuideStarter = ({
  steps = [],
  tourId = 'default-tour',
  buttonText = __('Démarrer le tour guidé', 'boss-seo'),
  showAsIcon = false,
  autoStart = false,
  onComplete = () => {}
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  // Vérifier si le tour a déjà été complété
  const isTourCompleted = () => {
    return localStorage.getItem(`boss_tour_completed_${tourId}`) === 'true';
  };
  
  // Démarrer automatiquement si nécessaire
  useEffect(() => {
    if (autoStart && !isTourCompleted()) {
      setIsOpen(true);
    }
  }, [autoStart, tourId]);
  
  return (
    <>
      {showAsIcon ? (
        <button
          className="boss-text-boss-blue-500 boss-hover:boss-text-boss-blue-700 focus:boss-outline-none"
          onClick={() => setIsOpen(true)}
          aria-label={buttonText}
        >
          <span className="dashicons dashicons-welcome-learn-more"></span>
        </button>
      ) : (
        <Button
          isSecondary
          onClick={() => setIsOpen(true)}
        >
          <span className="dashicons dashicons-welcome-learn-more boss-mr-1"></span>
          {buttonText}
        </Button>
      )}
      
      <TourGuide
        steps={steps}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={() => {
          setIsOpen(false);
          onComplete();
        }}
        tourId={tourId}
      />
    </>
  );
};

export default TourGuide;
