<?php
/**
 * La classe d'analyse SEO du module Boss Optimizer.
 *
 * Cette classe effectue l'analyse SEO des contenus WordPress.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * La classe d'analyse SEO du module Boss Optimizer.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_Analysis {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string                   $plugin_name    Le nom du plugin.
     * @param    Boss_Optimizer_Settings  $settings       Instance de la classe de paramètres.
     */
    public function __construct( $plugin_name, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->settings = $settings;
    }

    /**
     * Effectue une analyse SEO complète d'un contenu.
     *
     * @since    1.1.0
     * @param    int|WP_Post    $post    L'ID du contenu ou l'objet WP_Post.
     * @return   array                   Les résultats de l'analyse.
     */
    public function analyze( $post ) {
        // S'assurer que nous avons un objet WP_Post
        if ( is_numeric( $post ) ) {
            $post = get_post( $post );
        }

        if ( ! $post instanceof WP_Post ) {
            return array(
                'success' => false,
                'message' => 'Contenu non trouvé'
            );
        }

        // Récupérer les métadonnées SEO
        $meta_description = get_post_meta( $post->ID, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post->ID, '_boss_seo_focus_keyword', true );

        // Si pas de mot-clé principal, utiliser le titre comme mot-clé
        if ( empty( $focus_keyword ) ) {
            $focus_keyword = $post->post_title;
        }

        // Initialiser les scores par catégorie
        $scores = array(
            'title' => 0,
            'content' => 0,
            'headings' => 0,
            'images' => 0,
            'links' => 0,
            'keywords' => 0,
            'readability' => 0,
            'meta_description' => 0
        );

        // Initialiser les recommandations
        $recommendations = array();

        // Analyser le titre
        if ( $this->settings->get( 'optimizer', 'analyze_title', true ) ) {
            $title_analysis = $this->analyze_title( $post->post_title, $focus_keyword );
            $scores['title'] = $title_analysis['score'];
            $recommendations = array_merge( $recommendations, $title_analysis['recommendations'] );
        }

        // Analyser le contenu
        if ( $this->settings->get( 'optimizer', 'analyze_content', true ) ) {
            $content_analysis = $this->analyze_content( $post->post_content, $focus_keyword );
            $scores['content'] = $content_analysis['score'];
            $recommendations = array_merge( $recommendations, $content_analysis['recommendations'] );
        }

        // Analyser les titres (h1, h2, etc.)
        if ( $this->settings->get( 'optimizer', 'analyze_headings', true ) ) {
            $headings_analysis = $this->analyze_headings( $post->post_content, $focus_keyword );
            $scores['headings'] = $headings_analysis['score'];
            $recommendations = array_merge( $recommendations, $headings_analysis['recommendations'] );
        }

        // Analyser les images
        if ( $this->settings->get( 'optimizer', 'analyze_images', true ) ) {
            $images_analysis = $this->analyze_images( $post->post_content, $post->ID );
            $scores['images'] = $images_analysis['score'];
            $recommendations = array_merge( $recommendations, $images_analysis['recommendations'] );
        }

        // Analyser les liens
        if ( $this->settings->get( 'optimizer', 'analyze_links', true ) ) {
            $links_analysis = $this->analyze_links( $post->post_content );
            $scores['links'] = $links_analysis['score'];
            $recommendations = array_merge( $recommendations, $links_analysis['recommendations'] );
        }

        // Analyser les mots-clés
        if ( $this->settings->get( 'optimizer', 'analyze_keywords', true ) ) {
            $keywords_analysis = $this->analyze_keywords( $post->post_title, $post->post_content, $focus_keyword );
            $scores['keywords'] = $keywords_analysis['score'];
            $recommendations = array_merge( $recommendations, $keywords_analysis['recommendations'] );
        }

        // Analyser la lisibilité
        if ( $this->settings->get( 'optimizer', 'analyze_readability', true ) ) {
            $readability_analysis = $this->analyze_readability( $post->post_content );
            $scores['readability'] = $readability_analysis['score'];
            $recommendations = array_merge( $recommendations, $readability_analysis['recommendations'] );
        }

        // Analyser la méta-description
        if ( $this->settings->get( 'optimizer', 'analyze_title', true ) ) {
            $meta_description_analysis = $this->analyze_meta_description( $meta_description, $focus_keyword );
            $scores['meta_description'] = $meta_description_analysis['score'];
            $recommendations = array_merge( $recommendations, $meta_description_analysis['recommendations'] );
        }

        // Calculer le score global
        $overall_score = $this->calculate_overall_score( $scores );

        // Trier les recommandations par priorité
        usort( $recommendations, function( $a, $b ) {
            $priority_order = array( 'critical' => 0, 'warning' => 1, 'info' => 2 );
            return $priority_order[ $a['type'] ] - $priority_order[ $b['type'] ];
        });

        // Ajouter des identifiants uniques aux recommandations
        foreach ( $recommendations as $key => $recommendation ) {
            $recommendations[ $key ]['id'] = $key + 1;
        }

        // Enregistrer les résultats dans les métadonnées du post
        update_post_meta( $post->ID, '_boss_seo_score', $overall_score );
        update_post_meta( $post->ID, '_boss_seo_recommendations', $recommendations );
        update_post_meta( $post->ID, '_boss_seo_analysis_date', current_time( 'mysql' ) );

        // S'assurer que les métadonnées de base sont définies
        if ( empty( get_post_meta( $post->ID, '_boss_seo_meta_description', true ) ) ) {
            $excerpt = wp_trim_words( $post->post_content, 30 );
            update_post_meta( $post->ID, '_boss_seo_meta_description', $excerpt );
        }

        if ( empty( get_post_meta( $post->ID, '_boss_seo_title', true ) ) ) {
            update_post_meta( $post->ID, '_boss_seo_title', $post->post_title );
        }

        if ( empty( get_post_meta( $post->ID, '_boss_seo_focus_keyword', true ) ) ) {
            update_post_meta( $post->ID, '_boss_seo_focus_keyword', $focus_keyword );
        }

        // Retourner les résultats
        return array(
            'success' => true,
            'post_id' => $post->ID,
            'scores' => $scores,
            'overall_score' => $overall_score,
            'recommendations' => $recommendations,
            'analysis_date' => current_time( 'mysql' )
        );
    }

    /**
     * Analyse le titre d'un contenu.
     *
     * @since    1.1.0
     * @param    string    $title           Le titre à analyser.
     * @param    string    $focus_keyword   Le mot-clé principal.
     * @return   array                      Les résultats de l'analyse.
     */
    protected function analyze_title( $title, $focus_keyword ) {
        $recommendations = array();
        $score = 0;
        $title_length = mb_strlen( $title );
        $min_length = $this->settings->get( 'optimizer', 'min_title_length', 20 );
        $max_length = $this->settings->get( 'optimizer', 'max_title_length', 60 );

        // Vérifier la longueur du titre
        if ( empty( $title ) ) {
            $recommendations[] = array(
                'type' => 'critical',
                'text' => __( 'Le titre est vide. Ajoutez un titre à votre contenu.', 'boss-seo' ),
                'element' => 'title'
            );
            $score += 0;
        } elseif ( $title_length < $min_length ) {
            $recommendations[] = array(
                'type' => 'warning',
                'text' => sprintf(
                    __( 'Le titre est trop court (%1$d caractères). Il devrait contenir au moins %2$d caractères.', 'boss-seo' ),
                    $title_length,
                    $min_length
                ),
                'element' => 'title'
            );
            $score += 50;
        } elseif ( $title_length > $max_length ) {
            $recommendations[] = array(
                'type' => 'warning',
                'text' => sprintf(
                    __( 'Le titre est trop long (%1$d caractères). Il devrait contenir au maximum %2$d caractères.', 'boss-seo' ),
                    $title_length,
                    $max_length
                ),
                'element' => 'title'
            );
            $score += 50;
        } else {
            $score += 100;
        }

        // Vérifier la présence du mot-clé principal dans le titre
        if ( ! empty( $focus_keyword ) && stripos( $title, $focus_keyword ) === false ) {
            $recommendations[] = array(
                'type' => 'warning',
                'text' => sprintf(
                    __( 'Le mot-clé principal "%s" n\'apparaît pas dans le titre.', 'boss-seo' ),
                    $focus_keyword
                ),
                'element' => 'title'
            );
            $score = max( 0, $score - 30 );
        }

        // Vérifier si le titre commence par le mot-clé principal
        if ( ! empty( $focus_keyword ) && stripos( $title, $focus_keyword ) !== 0 ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => sprintf(
                    __( 'Pour une meilleure optimisation, commencez votre titre par le mot-clé principal "%s".', 'boss-seo' ),
                    $focus_keyword
                ),
                'element' => 'title'
            );
            $score = max( 0, $score - 10 );
        }

        // Vérifier la présence de mots puissants dans le titre
        $power_words = $this->get_power_words();
        $title_has_power_word = false;

        foreach ( $power_words as $word ) {
            if ( stripos( $title, $word ) !== false ) {
                $title_has_power_word = true;
                break;
            }
        }

        if ( ! $title_has_power_word ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => __( 'Ajoutez des mots puissants à votre titre pour le rendre plus attractif.', 'boss-seo' ),
                'element' => 'title'
            );
            $score = max( 0, $score - 10 );
        }

        // Normaliser le score
        $score = min( 100, max( 0, $score ) );

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Analyse le contenu d'un article.
     *
     * @since    1.1.0
     * @param    string    $content         Le contenu à analyser.
     * @param    string    $focus_keyword   Le mot-clé principal.
     * @return   array                      Les résultats de l'analyse.
     */
    protected function analyze_content( $content, $focus_keyword ) {
        $recommendations = array();
        $score = 0;

        // Nettoyer le contenu (supprimer les balises HTML)
        $clean_content = wp_strip_all_tags( $content );
        $content_length = mb_strlen( $clean_content );
        $min_content_length = $this->settings->get( 'optimizer', 'min_content_length', 300 );

        // Vérifier la longueur du contenu
        if ( empty( $clean_content ) ) {
            $recommendations[] = array(
                'type' => 'critical',
                'text' => __( 'Le contenu est vide. Ajoutez du contenu à votre page.', 'boss-seo' ),
                'element' => 'content'
            );
            $score += 0;
        } elseif ( $content_length < $min_content_length ) {
            $recommendations[] = array(
                'type' => 'warning',
                'text' => sprintf(
                    __( 'Le contenu est trop court (%1$d caractères). Il devrait contenir au moins %2$d caractères.', 'boss-seo' ),
                    $content_length,
                    $min_content_length
                ),
                'element' => 'content'
            );
            $score += 50;
        } else {
            $score += 100;
        }

        // Vérifier la présence du mot-clé principal dans le contenu
        if ( ! empty( $focus_keyword ) && stripos( $clean_content, $focus_keyword ) === false ) {
            $recommendations[] = array(
                'type' => 'critical',
                'text' => sprintf(
                    __( 'Le mot-clé principal "%s" n\'apparaît pas dans le contenu.', 'boss-seo' ),
                    $focus_keyword
                ),
                'element' => 'content'
            );
            $score = max( 0, $score - 50 );
        } else if ( ! empty( $focus_keyword ) ) {
            // Vérifier la densité du mot-clé
            $keyword_count = substr_count( strtolower( $clean_content ), strtolower( $focus_keyword ) );
            $word_count = str_word_count( $clean_content );
            $keyword_density = ( $word_count > 0 ) ? ( $keyword_count / $word_count ) * 100 : 0;

            if ( $keyword_density < 0.5 ) {
                $recommendations[] = array(
                    'type' => 'warning',
                    'text' => sprintf(
                        __( 'La densité du mot-clé principal "%s" est trop faible (%.1f%%). Essayez d\'atteindre une densité entre 0.5%% et 2.5%%.', 'boss-seo' ),
                        $focus_keyword,
                        $keyword_density
                    ),
                    'element' => 'content'
                );
                $score = max( 0, $score - 20 );
            } elseif ( $keyword_density > 2.5 ) {
                $recommendations[] = array(
                    'type' => 'warning',
                    'text' => sprintf(
                        __( 'La densité du mot-clé principal "%s" est trop élevée (%.1f%%). Essayez de réduire la densité en dessous de 2.5%%.', 'boss-seo' ),
                        $focus_keyword,
                        $keyword_density
                    ),
                    'element' => 'content'
                );
                $score = max( 0, $score - 20 );
            }

            // Vérifier si le mot-clé apparaît dans les 100 premiers mots
            $first_100_words = implode( ' ', array_slice( explode( ' ', $clean_content ), 0, 100 ) );
            if ( stripos( $first_100_words, $focus_keyword ) === false ) {
                $recommendations[] = array(
                    'type' => 'info',
                    'text' => sprintf(
                        __( 'Le mot-clé principal "%s" n\'apparaît pas dans les 100 premiers mots du contenu.', 'boss-seo' ),
                        $focus_keyword
                    ),
                    'element' => 'content'
                );
                $score = max( 0, $score - 10 );
            }
        }

        // Vérifier la présence de paragraphes
        if ( substr_count( $content, '</p>' ) < 3 ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => __( 'Votre contenu contient peu de paragraphes. Structurez votre contenu avec au moins 3 paragraphes.', 'boss-seo' ),
                'element' => 'content'
            );
            $score = max( 0, $score - 10 );
        }

        // Normaliser le score
        $score = min( 100, max( 0, $score ) );

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Analyse les titres (h1, h2, etc.) d'un contenu.
     *
     * @since    1.1.0
     * @param    string    $content         Le contenu à analyser.
     * @param    string    $focus_keyword   Le mot-clé principal.
     * @return   array                      Les résultats de l'analyse.
     */
    protected function analyze_headings( $content, $focus_keyword ) {
        $recommendations = array();
        $score = 0;

        // Extraire les titres
        preg_match_all( '/<h([1-6])[^>]*>(.*?)<\/h\1>/i', $content, $headings, PREG_SET_ORDER );

        // Vérifier la présence de titres
        if ( empty( $headings ) ) {
            $recommendations[] = array(
                'type' => 'warning',
                'text' => __( 'Aucun titre (h1, h2, etc.) n\'a été trouvé dans le contenu. Ajoutez des titres pour structurer votre contenu.', 'boss-seo' ),
                'element' => 'headings'
            );
            $score += 0;
        } else {
            $score += 100;

            // Vérifier la hiérarchie des titres
            $has_h1 = false;
            $has_h2 = false;
            $headings_by_level = array();

            foreach ( $headings as $heading ) {
                $level = $heading[1];
                $text = wp_strip_all_tags( $heading[2] );

                if ( $level == 1 ) {
                    $has_h1 = true;
                } elseif ( $level == 2 ) {
                    $has_h2 = true;
                }

                if ( ! isset( $headings_by_level[ $level ] ) ) {
                    $headings_by_level[ $level ] = array();
                }

                $headings_by_level[ $level ][] = $text;
            }

            // Vérifier la présence d'un titre h1
            if ( ! $has_h1 ) {
                $recommendations[] = array(
                    'type' => 'info',
                    'text' => __( 'Aucun titre h1 n\'a été trouvé. Considérez ajouter un titre h1 principal à votre contenu.', 'boss-seo' ),
                    'element' => 'headings'
                );
                $score = max( 0, $score - 10 );
            } elseif ( isset( $headings_by_level[1] ) && count( $headings_by_level[1] ) > 1 ) {
                $recommendations[] = array(
                    'type' => 'warning',
                    'text' => __( 'Plusieurs titres h1 ont été trouvés. Il est recommandé de n\'avoir qu\'un seul titre h1 par page.', 'boss-seo' ),
                    'element' => 'headings'
                );
                $score = max( 0, $score - 20 );
            }

            // Vérifier la présence de titres h2
            if ( ! $has_h2 && count( $headings ) > 1 ) {
                $recommendations[] = array(
                    'type' => 'info',
                    'text' => __( 'Aucun titre h2 n\'a été trouvé. Utilisez des titres h2 pour structurer votre contenu.', 'boss-seo' ),
                    'element' => 'headings'
                );
                $score = max( 0, $score - 10 );
            }

            // Vérifier la présence du mot-clé dans les titres
            if ( ! empty( $focus_keyword ) ) {
                $keyword_in_headings = false;

                foreach ( $headings as $heading ) {
                    $text = wp_strip_all_tags( $heading[2] );

                    if ( stripos( $text, $focus_keyword ) !== false ) {
                        $keyword_in_headings = true;
                        break;
                    }
                }

                if ( ! $keyword_in_headings ) {
                    $recommendations[] = array(
                        'type' => 'warning',
                        'text' => sprintf(
                            __( 'Le mot-clé principal "%s" n\'apparaît dans aucun titre. Essayez d\'inclure votre mot-clé dans au moins un titre.', 'boss-seo' ),
                            $focus_keyword
                        ),
                        'element' => 'headings'
                    );
                    $score = max( 0, $score - 20 );
                }
            }
        }

        // Normaliser le score
        $score = min( 100, max( 0, $score ) );

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Analyse les images d'un contenu.
     *
     * @since    1.1.0
     * @param    string    $content    Le contenu à analyser.
     * @param    int       $post_id    L'ID du contenu.
     * @return   array                 Les résultats de l'analyse.
     */
    protected function analyze_images( $content, $post_id ) {
        $recommendations = array();
        $score = 0;

        // Extraire les images
        preg_match_all( '/<img[^>]+>/i', $content, $images );

        // Vérifier la présence d'images
        if ( empty( $images[0] ) ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => __( 'Aucune image n\'a été trouvée dans le contenu. Considérez ajouter des images pour rendre votre contenu plus attractif.', 'boss-seo' ),
                'element' => 'images'
            );
            $score += 50; // Score moyen car les images ne sont pas obligatoires
        } else {
            $score += 100;
            $missing_alt = 0;

            foreach ( $images[0] as $img ) {
                // Vérifier la présence de l'attribut alt
                if ( ! preg_match( '/alt=(["\'])(.*?)\1/i', $img, $alt ) || empty( $alt[2] ) ) {
                    $missing_alt++;
                }
            }

            // Vérifier les attributs alt manquants
            if ( $missing_alt > 0 ) {
                $recommendations[] = array(
                    'type' => 'warning',
                    'text' => sprintf(
                        _n(
                            '%d image n\'a pas d\'attribut alt. Ajoutez des attributs alt descriptifs à toutes vos images.',
                            '%d images n\'ont pas d\'attribut alt. Ajoutez des attributs alt descriptifs à toutes vos images.',
                            $missing_alt,
                            'boss-seo'
                        ),
                        $missing_alt
                    ),
                    'element' => 'images'
                );
                $score = max( 0, $score - ( $missing_alt * 20 ) );
            }

            // Vérifier si l'image mise en avant est définie
            if ( ! has_post_thumbnail( $post_id ) ) {
                $recommendations[] = array(
                    'type' => 'info',
                    'text' => __( 'Aucune image mise en avant n\'est définie. Définissez une image mise en avant pour améliorer l\'apparence de votre contenu dans les résultats de recherche et sur les réseaux sociaux.', 'boss-seo' ),
                    'element' => 'images'
                );
                $score = max( 0, $score - 10 );
            }
        }

        // Normaliser le score
        $score = min( 100, max( 0, $score ) );

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Analyse les liens d'un contenu.
     *
     * @since    1.1.0
     * @param    string    $content    Le contenu à analyser.
     * @return   array                 Les résultats de l'analyse.
     */
    protected function analyze_links( $content ) {
        $recommendations = array();
        $score = 0;

        // Extraire les liens
        preg_match_all( '/<a[^>]+href=(["\'])(.*?)\1[^>]*>(.*?)<\/a>/i', $content, $links, PREG_SET_ORDER );

        // Compter les liens internes et externes
        $internal_links = 0;
        $external_links = 0;
        $empty_links = 0;
        $nofollow_links = 0;

        foreach ( $links as $link ) {
            $url = $link[2];
            $text = wp_strip_all_tags( $link[3] );

            // Vérifier si le lien est vide
            if ( empty( $url ) || $url === '#' ) {
                $empty_links++;
                continue;
            }

            // Vérifier si le lien est interne ou externe
            if ( strpos( $url, home_url() ) === 0 || strpos( $url, '/' ) === 0 ) {
                $internal_links++;
            } else {
                $external_links++;

                // Vérifier si le lien externe a l'attribut nofollow
                if ( strpos( $link[0], 'rel="nofollow"' ) !== false || strpos( $link[0], "rel='nofollow'" ) !== false ) {
                    $nofollow_links++;
                }
            }
        }

        // Vérifier la présence de liens
        if ( empty( $links ) ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => __( 'Aucun lien n\'a été trouvé dans le contenu. Considérez ajouter des liens internes et externes pour améliorer la navigation et le référencement.', 'boss-seo' ),
                'element' => 'links'
            );
            $score += 50; // Score moyen car les liens ne sont pas obligatoires
        } else {
            $score += 100;

            // Vérifier les liens vides
            if ( $empty_links > 0 ) {
                $recommendations[] = array(
                    'type' => 'warning',
                    'text' => sprintf(
                        _n(
                            '%d lien est vide ou pointe vers "#". Corrigez ce lien.',
                            '%d liens sont vides ou pointent vers "#". Corrigez ces liens.',
                            $empty_links,
                            'boss-seo'
                        ),
                        $empty_links
                    ),
                    'element' => 'links'
                );
                $score = max( 0, $score - ( $empty_links * 10 ) );
            }

            // Vérifier la présence de liens internes
            if ( $internal_links === 0 ) {
                $recommendations[] = array(
                    'type' => 'info',
                    'text' => __( 'Aucun lien interne n\'a été trouvé. Ajoutez des liens vers d\'autres pages de votre site pour améliorer la navigation et le référencement.', 'boss-seo' ),
                    'element' => 'links'
                );
                $score = max( 0, $score - 10 );
            }

            // Vérifier les liens externes sans nofollow
            if ( $external_links > 0 && $external_links !== $nofollow_links ) {
                $recommendations[] = array(
                    'type' => 'info',
                    'text' => sprintf(
                        _n(
                            '%d lien externe n\'a pas l\'attribut "nofollow". Considérez ajouter cet attribut pour éviter de transférer de l\'autorité à des sites externes.',
                            '%d liens externes n\'ont pas l\'attribut "nofollow". Considérez ajouter cet attribut pour éviter de transférer de l\'autorité à des sites externes.',
                            $external_links - $nofollow_links,
                            'boss-seo'
                        ),
                        $external_links - $nofollow_links
                    ),
                    'element' => 'links'
                );
                $score = max( 0, $score - 5 );
            }
        }

        // Normaliser le score
        $score = min( 100, max( 0, $score ) );

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Analyse les mots-clés d'un contenu.
     *
     * @since    1.1.0
     * @param    string    $title           Le titre du contenu.
     * @param    string    $content         Le contenu à analyser.
     * @param    string    $focus_keyword   Le mot-clé principal.
     * @return   array                      Les résultats de l'analyse.
     */
    protected function analyze_keywords( $title, $content, $focus_keyword ) {
        $recommendations = array();
        $score = 0;

        // Si pas de mot-clé principal, on ne peut pas faire d'analyse
        if ( empty( $focus_keyword ) ) {
            $recommendations[] = array(
                'type' => 'critical',
                'text' => __( 'Aucun mot-clé principal n\'a été défini. Définissez un mot-clé principal pour améliorer l\'analyse SEO.', 'boss-seo' ),
                'element' => 'keywords'
            );
            return array(
                'score' => 0,
                'recommendations' => $recommendations
            );
        }

        // Nettoyer le contenu
        $clean_content = wp_strip_all_tags( $content );

        // Vérifier la présence du mot-clé dans le titre
        $keyword_in_title = stripos( $title, $focus_keyword ) !== false;

        // Vérifier la présence du mot-clé dans le contenu
        $keyword_in_content = stripos( $clean_content, $focus_keyword ) !== false;

        // Vérifier la présence du mot-clé dans l'URL
        global $post;
        $keyword_in_url = false;

        if ( isset( $post ) && isset( $post->post_name ) ) {
            $keyword_in_url = stripos( $post->post_name, str_replace( ' ', '-', strtolower( $focus_keyword ) ) ) !== false;
        }

        // Calculer le score
        $score = 0;

        if ( $keyword_in_title ) {
            $score += 40;
        } else {
            $recommendations[] = array(
                'type' => 'warning',
                'text' => sprintf(
                    __( 'Le mot-clé principal "%s" n\'apparaît pas dans le titre.', 'boss-seo' ),
                    $focus_keyword
                ),
                'element' => 'keywords'
            );
        }

        if ( $keyword_in_content ) {
            $score += 40;
        } else {
            $recommendations[] = array(
                'type' => 'critical',
                'text' => sprintf(
                    __( 'Le mot-clé principal "%s" n\'apparaît pas dans le contenu.', 'boss-seo' ),
                    $focus_keyword
                ),
                'element' => 'keywords'
            );
        }

        if ( $keyword_in_url ) {
            $score += 20;
        } else {
            $recommendations[] = array(
                'type' => 'info',
                'text' => sprintf(
                    __( 'Le mot-clé principal "%s" n\'apparaît pas dans l\'URL.', 'boss-seo' ),
                    $focus_keyword
                ),
                'element' => 'keywords'
            );
        }

        // Vérifier la longueur du mot-clé
        $keyword_words = explode( ' ', $focus_keyword );
        if ( count( $keyword_words ) > 4 ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => sprintf(
                    __( 'Le mot-clé principal "%s" est assez long (%d mots). Envisagez d\'utiliser un mot-clé plus court pour une meilleure optimisation.', 'boss-seo' ),
                    $focus_keyword,
                    count( $keyword_words )
                ),
                'element' => 'keywords'
            );
            $score = max( 0, $score - 5 );
        }

        // Normaliser le score
        $score = min( 100, max( 0, $score ) );

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Analyse la lisibilité d'un contenu.
     *
     * @since    1.1.0
     * @param    string    $content    Le contenu à analyser.
     * @return   array                 Les résultats de l'analyse.
     */
    protected function analyze_readability( $content ) {
        $recommendations = array();
        $score = 0;

        // Nettoyer le contenu
        $clean_content = wp_strip_all_tags( $content );

        // Vérifier la longueur des paragraphes
        preg_match_all( '/<p[^>]*>(.*?)<\/p>/is', $content, $paragraphs );

        $long_paragraphs = 0;
        $total_paragraphs = count( $paragraphs[0] );

        foreach ( $paragraphs[1] as $paragraph ) {
            $paragraph_text = wp_strip_all_tags( $paragraph );
            $word_count = str_word_count( $paragraph_text );

            if ( $word_count > 100 ) {
                $long_paragraphs++;
            }
        }

        // Vérifier la présence de paragraphes trop longs
        if ( $long_paragraphs > 0 ) {
            $recommendations[] = array(
                'type' => 'warning',
                'text' => sprintf(
                    _n(
                        '%d paragraphe contient plus de 100 mots. Essayez de diviser les paragraphes longs pour améliorer la lisibilité.',
                        '%d paragraphes contiennent plus de 100 mots. Essayez de diviser les paragraphes longs pour améliorer la lisibilité.',
                        $long_paragraphs,
                        'boss-seo'
                    ),
                    $long_paragraphs
                ),
                'element' => 'readability'
            );
            $score = max( 0, 100 - ( $long_paragraphs * 10 ) );
        } else {
            $score = 100;
        }

        // Vérifier la présence de phrases trop longues
        $sentences = preg_split( '/[.!?]+/', $clean_content, -1, PREG_SPLIT_NO_EMPTY );
        $long_sentences = 0;

        foreach ( $sentences as $sentence ) {
            $word_count = str_word_count( trim( $sentence ) );

            if ( $word_count > 20 ) {
                $long_sentences++;
            }
        }

        if ( $long_sentences > 0 ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => sprintf(
                    _n(
                        '%d phrase contient plus de 20 mots. Essayez de raccourcir les phrases longues pour améliorer la lisibilité.',
                        '%d phrases contiennent plus de 20 mots. Essayez de raccourcir les phrases longues pour améliorer la lisibilité.',
                        $long_sentences,
                        'boss-seo'
                    ),
                    $long_sentences
                ),
                'element' => 'readability'
            );
            $score = max( 0, $score - ( $long_sentences * 5 ) );
        }

        // Vérifier l'utilisation de sous-titres
        if ( $total_paragraphs > 5 ) {
            preg_match_all( '/<h[2-6][^>]*>(.*?)<\/h[2-6]>/i', $content, $subheadings );

            if ( count( $subheadings[0] ) < 1 ) {
                $recommendations[] = array(
                    'type' => 'info',
                    'text' => __( 'Votre contenu ne contient pas de sous-titres (h2-h6). Utilisez des sous-titres pour structurer votre contenu et améliorer la lisibilité.', 'boss-seo' ),
                    'element' => 'readability'
                );
                $score = max( 0, $score - 10 );
            }
        }

        // Vérifier l'utilisation de listes
        if ( strpos( $content, '<ul' ) === false && strpos( $content, '<ol' ) === false ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => __( 'Votre contenu ne contient pas de listes (ul/ol). Utilisez des listes pour présenter des informations de manière structurée et améliorer la lisibilité.', 'boss-seo' ),
                'element' => 'readability'
            );
            $score = max( 0, $score - 5 );
        }

        // Normaliser le score
        $score = min( 100, max( 0, $score ) );

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Analyse la méta-description d'un contenu.
     *
     * @since    1.1.0
     * @param    string    $meta_description    La méta-description à analyser.
     * @param    string    $focus_keyword       Le mot-clé principal.
     * @return   array                          Les résultats de l'analyse.
     */
    protected function analyze_meta_description( $meta_description, $focus_keyword ) {
        $recommendations = array();
        $score = 0;

        // Vérifier la présence de la méta-description
        if ( empty( $meta_description ) ) {
            $recommendations[] = array(
                'type' => 'critical',
                'text' => __( 'La méta-description est vide. Ajoutez une méta-description pour améliorer l\'apparence de votre contenu dans les résultats de recherche.', 'boss-seo' ),
                'element' => 'meta_description'
            );
            return array(
                'score' => 0,
                'recommendations' => $recommendations
            );
        }

        // Vérifier la longueur de la méta-description
        $meta_length = mb_strlen( $meta_description );
        $min_length = $this->settings->get( 'optimizer', 'min_description_length', 120 );
        $max_length = $this->settings->get( 'optimizer', 'max_description_length', 160 );

        if ( $meta_length < $min_length ) {
            $recommendations[] = array(
                'type' => 'warning',
                'text' => sprintf(
                    __( 'La méta-description est trop courte (%1$d caractères). Elle devrait contenir au moins %2$d caractères.', 'boss-seo' ),
                    $meta_length,
                    $min_length
                ),
                'element' => 'meta_description'
            );
            $score += 50;
        } elseif ( $meta_length > $max_length ) {
            $recommendations[] = array(
                'type' => 'warning',
                'text' => sprintf(
                    __( 'La méta-description est trop longue (%1$d caractères). Elle devrait contenir au maximum %2$d caractères pour éviter qu\'elle soit tronquée dans les résultats de recherche.', 'boss-seo' ),
                    $meta_length,
                    $max_length
                ),
                'element' => 'meta_description'
            );
            $score += 50;
        } else {
            $score += 100;
        }

        // Vérifier la présence du mot-clé principal dans la méta-description
        if ( ! empty( $focus_keyword ) && stripos( $meta_description, $focus_keyword ) === false ) {
            $recommendations[] = array(
                'type' => 'warning',
                'text' => sprintf(
                    __( 'Le mot-clé principal "%s" n\'apparaît pas dans la méta-description.', 'boss-seo' ),
                    $focus_keyword
                ),
                'element' => 'meta_description'
            );
            $score = max( 0, $score - 20 );
        }

        // Normaliser le score
        $score = min( 100, max( 0, $score ) );

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Calcule le score global à partir des scores par catégorie.
     *
     * @since    1.1.0
     * @param    array    $scores    Les scores par catégorie.
     * @return   int                 Le score global.
     */
    protected function calculate_overall_score( $scores ) {
        // Définir les poids pour chaque catégorie
        $weights = array(
            'title' => 15,
            'content' => 20,
            'headings' => 10,
            'images' => 10,
            'links' => 10,
            'keywords' => 15,
            'readability' => 10,
            'meta_description' => 10
        );

        // Calculer la somme pondérée
        $weighted_sum = 0;
        $total_weight = 0;

        foreach ( $scores as $category => $score ) {
            if ( isset( $weights[ $category ] ) ) {
                $weighted_sum += $score * $weights[ $category ];
                $total_weight += $weights[ $category ];
            }
        }

        // Éviter la division par zéro
        if ( $total_weight === 0 ) {
            return 0;
        }

        // Calculer et arrondir le score global
        return round( $weighted_sum / $total_weight );
    }

    /**
     * Récupère une liste de mots puissants pour l'analyse des titres.
     *
     * @since    1.1.0
     * @return   array    Liste de mots puissants.
     */
    protected function get_power_words() {
        return array(
            'gratuit', 'exclusif', 'facile', 'incroyable', 'extraordinaire',
            'meilleur', 'étonnant', 'essentiel', 'prouvé', 'garanti',
            'puissant', 'secret', 'ultime', 'instantané', 'révolutionnaire',
            'découvrez', 'comment', 'pourquoi', 'maintenant', 'nouveau',
            'guide', 'astuces', 'conseils', 'erreurs', 'mythes',
            'vérité', 'faits', 'révélé', 'dévoilé', 'exposé'
        );
    }
}
