import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  TabPanel,
  <PERSON><PERSON>,
  <PERSON><PERSON>
} from '@wordpress/components';

// Importer les composants
import HelpSearch from '../components/help/HelpSearch';
import KnowledgeBase from '../components/help/KnowledgeBase';
import Tutorials from '../components/help/Tutorials';
import Feedback from '../components/help/Feedback';
import { TourGuideStarter } from '../components/help/TourGuide';

const Help = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('knowledge-base');
  const [articles, setArticles] = useState([]);
  const [categories, setCategories] = useState([]);
  const [tutorials, setTutorials] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  
  // Charger les données
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour les catégories
      const mockCategories = [
        { id: 'getting-started', name: __('Démarrage', 'boss-seo') },
        { id: 'content-optimization', name: __('Optimisation de contenu', 'boss-seo') },
        { id: 'technical-seo', name: __('SEO technique', 'boss-seo') },
        { id: 'schemas', name: __('Schémas structurés', 'boss-seo') },
        { id: 'analytics', name: __('Analytics', 'boss-seo') },
        { id: 'local-seo', name: __('SEO local', 'boss-seo') },
        { id: 'ecommerce', name: __('E-commerce', 'boss-seo') },
        { id: 'technical-management', name: __('Gestion technique', 'boss-seo') },
        { id: 'reports', name: __('Rapports', 'boss-seo') },
        { id: 'settings', name: __('Paramètres', 'boss-seo') }
      ];
      
      // Données fictives pour les articles
      const mockArticles = [
        {
          id: 1,
          title: __('Premiers pas avec Boss SEO', 'boss-seo'),
          content: `<p>Bienvenue dans Boss SEO ! Ce guide vous aidera à configurer le plugin et à commencer à optimiser votre site.</p>
                    <h3>Configuration initiale</h3>
                    <p>Après l'installation, vous devez d'abord configurer les paramètres de base :</p>
                    <ol>
                      <li>Accédez à l'onglet "Paramètres" et entrez votre clé de licence</li>
                      <li>Configurez les informations de base de votre site</li>
                      <li>Connectez vos comptes Google Analytics et Search Console</li>
                    </ol>
                    <h3>Premiers pas</h3>
                    <p>Une fois la configuration terminée, nous vous recommandons de :</p>
                    <ul>
                      <li>Effectuer une analyse technique complète</li>
                      <li>Configurer vos schémas structurés</li>
                      <li>Commencer à optimiser votre contenu existant</li>
                    </ul>`,
          category: 'getting-started',
          tags: ['débutant', 'configuration', 'installation'],
          isPopular: true,
          viewCount: 1250,
          updatedAt: '2023-06-15',
          excerpt: __('Guide de démarrage pour configurer Boss SEO et commencer à optimiser votre site.', 'boss-seo'),
          externalUrl: 'https://docs.example.com/boss-seo/getting-started',
          relatedArticles: [2, 3, 5]
        },
        {
          id: 2,
          title: __('Optimisation des méta-titres et descriptions', 'boss-seo'),
          content: `<p>Les méta-titres et descriptions sont essentiels pour le SEO et les taux de clics dans les résultats de recherche.</p>
                    <h3>Bonnes pratiques pour les méta-titres</h3>
                    <ul>
                      <li>Limitez la longueur à 50-60 caractères</li>
                      <li>Incluez votre mot-clé principal près du début</li>
                      <li>Rendez-le attrayant et pertinent pour l'utilisateur</li>
                    </ul>
                    <h3>Bonnes pratiques pour les méta-descriptions</h3>
                    <ul>
                      <li>Limitez la longueur à 150-160 caractères</li>
                      <li>Incluez un appel à l'action</li>
                      <li>Intégrez naturellement vos mots-clés principaux</li>
                    </ul>
                    <p>Boss SEO vous aide à optimiser ces éléments avec des suggestions en temps réel et des analyses de longueur.</p>`,
          category: 'content-optimization',
          tags: ['méta-titres', 'méta-descriptions', 'SERP'],
          isPopular: true,
          viewCount: 980,
          updatedAt: '2023-05-20',
          excerpt: __('Guide pour optimiser vos méta-titres et descriptions pour un meilleur SEO et CTR.', 'boss-seo'),
          externalUrl: 'https://docs.example.com/boss-seo/meta-optimization',
          relatedArticles: [3, 4]
        },
        {
          id: 3,
          title: __('Configuration des schémas structurés', 'boss-seo'),
          content: `<p>Les schémas structurés aident les moteurs de recherche à comprendre le contenu de votre site et peuvent améliorer votre visibilité dans les SERP.</p>
                    <h3>Types de schémas courants</h3>
                    <ul>
                      <li>Organization</li>
                      <li>LocalBusiness</li>
                      <li>Product</li>
                      <li>Article</li>
                      <li>FAQPage</li>
                      <li>Recipe</li>
                    </ul>
                    <h3>Configuration avec Boss SEO</h3>
                    <p>Notre module de schémas structurés vous permet de configurer facilement ces données :</p>
                    <ol>
                      <li>Accédez au module "Schémas structurés"</li>
                      <li>Sélectionnez le type de schéma approprié</li>
                      <li>Remplissez les champs requis</li>
                      <li>Définissez les règles d'application</li>
                    </ol>`,
          category: 'schemas',
          tags: ['schema.org', 'rich snippets', 'structured data'],
          isPopular: true,
          viewCount: 850,
          updatedAt: '2023-04-10',
          excerpt: __('Guide pour configurer les schémas structurés avec Boss SEO pour améliorer votre visibilité dans les SERP.', 'boss-seo'),
          externalUrl: 'https://docs.example.com/boss-seo/structured-schemas',
          relatedArticles: [1, 5]
        },
        {
          id: 4,
          title: __('Analyse et correction des erreurs techniques', 'boss-seo'),
          content: `<p>Les problèmes techniques peuvent avoir un impact significatif sur votre SEO. Boss SEO vous aide à les identifier et les corriger.</p>
                    <h3>Problèmes techniques courants</h3>
                    <ul>
                      <li>Erreurs d'exploration</li>
                      <li>Problèmes de vitesse de page</li>
                      <li>Problèmes de compatibilité mobile</li>
                      <li>Erreurs de balisage</li>
                      <li>Problèmes de canonicalisation</li>
                    </ul>
                    <h3>Utilisation de l'analyse technique</h3>
                    <ol>
                      <li>Lancez une analyse technique complète</li>
                      <li>Examinez les problèmes par ordre de priorité</li>
                      <li>Suivez les instructions de correction pour chaque problème</li>
                      <li>Relancez l'analyse pour vérifier les corrections</li>
                    </ol>`,
          category: 'technical-seo',
          tags: ['analyse technique', 'erreurs', 'crawl'],
          isPopular: false,
          viewCount: 720,
          updatedAt: '2023-03-25',
          excerpt: __('Guide pour identifier et corriger les erreurs techniques SEO avec Boss SEO.', 'boss-seo'),
          externalUrl: 'https://docs.example.com/boss-seo/technical-analysis',
          relatedArticles: [5, 6]
        },
        {
          id: 5,
          title: __('Configuration de Google Analytics 4', 'boss-seo'),
          content: `<p>Boss SEO s'intègre avec Google Analytics 4 pour vous fournir des données précieuses sur le trafic et le comportement des utilisateurs.</p>
                    <h3>Configuration de l'intégration</h3>
                    <ol>
                      <li>Accédez à l'onglet "Paramètres" > "API"</li>
                      <li>Entrez votre ID de mesure GA4 (format G-XXXXXXXX)</li>
                      <li>Connectez votre compte Google</li>
                      <li>Sélectionnez les propriétés à intégrer</li>
                    </ol>
                    <h3>Données disponibles</h3>
                    <ul>
                      <li>Trafic organique</li>
                      <li>Comportement des utilisateurs</li>
                      <li>Conversions</li>
                      <li>Performances des pages</li>
                    </ul>
                    <p>Une fois configuré, vous verrez ces données dans votre tableau de bord Boss SEO et dans les rapports détaillés.</p>`,
          category: 'analytics',
          tags: ['google analytics', 'GA4', 'tracking'],
          isPopular: true,
          viewCount: 890,
          updatedAt: '2023-06-05',
          excerpt: __('Guide pour configurer l\'intégration de Google Analytics 4 avec Boss SEO.', 'boss-seo'),
          externalUrl: 'https://docs.example.com/boss-seo/ga4-integration',
          relatedArticles: [1, 6]
        }
      ];
      
      // Données fictives pour les tutoriels
      const mockTutorials = [
        {
          id: 1,
          title: __('Démarrage avec Boss SEO', 'boss-seo'),
          description: __('Apprenez à configurer Boss SEO et à utiliser ses fonctionnalités de base.', 'boss-seo'),
          category: __('Démarrage', 'boss-seo'),
          duration: 10,
          level: 'beginner',
          steps: [
            {
              title: __('Installation et activation', 'boss-seo'),
              content: __('<p>Bienvenue dans le tutoriel de démarrage de Boss SEO ! Commençons par l\'installation et l\'activation du plugin.</p><p>Si vous n\'avez pas encore installé Boss SEO, vous pouvez le faire depuis le répertoire des plugins WordPress ou en téléchargeant le fichier ZIP depuis votre compte.</p>', 'boss-seo'),
              image: 'https://example.com/images/installation.jpg'
            },
            {
              title: __('Activation de la licence', 'boss-seo'),
              content: __('<p>Pour accéder à toutes les fonctionnalités, vous devez activer votre licence.</p><p>Accédez à l\'onglet "Paramètres" > "Licence & Crédits" et entrez votre clé de licence.</p>', 'boss-seo'),
              image: 'https://example.com/images/license.jpg'
            },
            {
              title: __('Configuration initiale', 'boss-seo'),
              content: __('<p>Configurons maintenant les paramètres de base de votre site.</p><p>Accédez à l\'onglet "Paramètres" > "Général" et remplissez les informations de votre site.</p>', 'boss-seo'),
              image: 'https://example.com/images/settings.jpg',
              tip: __('Assurez-vous que le titre et la description de votre site sont optimisés pour les moteurs de recherche.', 'boss-seo')
            },
            {
              title: __('Première analyse', 'boss-seo'),
              content: __('<p>Maintenant, lançons votre première analyse SEO pour identifier les opportunités d\'amélioration.</p><p>Accédez au tableau de bord et cliquez sur "Lancer l\'analyse".</p>', 'boss-seo'),
              image: 'https://example.com/images/analysis.jpg'
            },
            {
              title: __('Prochaines étapes', 'boss-seo'),
              content: __('<p>Félicitations ! Vous avez configuré Boss SEO avec succès.</p><p>Voici les prochaines étapes recommandées :</p><ul><li>Optimisez vos méta-titres et descriptions</li><li>Configurez vos schémas structurés</li><li>Analysez et corrigez les erreurs techniques</li></ul>', 'boss-seo'),
              image: 'https://example.com/images/next-steps.jpg'
            }
          ]
        },
        {
          id: 2,
          title: __('Optimisation de contenu avec Boss SEO', 'boss-seo'),
          description: __('Apprenez à utiliser les outils d\'optimisation de contenu pour améliorer votre SEO.', 'boss-seo'),
          category: __('Optimisation de contenu', 'boss-seo'),
          duration: 15,
          level: 'intermediate',
          steps: [
            {
              title: __('Introduction à l\'optimisation de contenu', 'boss-seo'),
              content: __('<p>L\'optimisation de contenu est essentielle pour le SEO. Dans ce tutoriel, vous apprendrez à utiliser les outils de Boss SEO pour optimiser votre contenu.</p>', 'boss-seo'),
              image: 'https://example.com/images/content-optimization.jpg'
            },
            {
              title: __('Analyse de mots-clés', 'boss-seo'),
              content: __('<p>Commençons par rechercher des mots-clés pertinents pour votre contenu.</p><p>Accédez au module "Optimisation de contenu" et utilisez l\'outil de recherche de mots-clés.</p>', 'boss-seo'),
              image: 'https://example.com/images/keyword-research.jpg',
              tip: __('Choisissez des mots-clés avec un bon équilibre entre volume de recherche et difficulté.', 'boss-seo')
            },
            {
              title: __('Optimisation des méta-données', 'boss-seo'),
              content: __('<p>Maintenant, optimisons les méta-titres et descriptions de vos pages.</p><p>Accédez à l\'éditeur de page et utilisez l\'outil d\'optimisation de méta-données.</p>', 'boss-seo'),
              image: 'https://example.com/images/meta-optimization.jpg'
            },
            {
              title: __('Analyse de contenu', 'boss-seo'),
              content: __('<p>Boss SEO analyse votre contenu en temps réel et vous donne des suggestions d\'amélioration.</p><p>Écrivez ou modifiez votre contenu et observez les suggestions dans le panneau d\'analyse.</p>', 'boss-seo'),
              image: 'https://example.com/images/content-analysis.jpg'
            },
            {
              title: __('Utilisation de l\'IA pour l\'optimisation', 'boss-seo'),
              content: __('<p>Boss SEO intègre des outils d\'IA pour vous aider à optimiser votre contenu.</p><p>Utilisez l\'outil "AI Content Studio" pour générer des suggestions d\'amélioration.</p>', 'boss-seo'),
              video: 'https://example.com/videos/ai-optimization.mp4'
            },
            {
              title: __('Vérification finale', 'boss-seo'),
              content: __('<p>Avant de publier, vérifiez votre score d\'optimisation et assurez-vous que tous les éléments importants sont optimisés.</p>', 'boss-seo'),
              image: 'https://example.com/images/final-check.jpg'
            }
          ]
        }
      ];
      
      setCategories(mockCategories);
      setArticles(mockArticles);
      setTutorials(mockTutorials);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  // Gérer la recherche
  const handleSearch = (results) => {
    setSearchResults(results);
    setIsSearching(results.length > 0);
    
    // Si des résultats sont trouvés, passer à l'onglet de la base de connaissances
    if (results.length > 0) {
      setActiveTab('knowledge-base');
    }
  };
  
  // Étapes du tour guidé
  const tourSteps = [
    {
      target: '.boss-help-search',
      title: __('Recherche d\'aide', 'boss-seo'),
      content: __('Utilisez cette barre de recherche pour trouver rapidement des articles et des tutoriels sur n\'importe quel sujet.', 'boss-seo')
    },
    {
      target: '.boss-help-tabs',
      title: __('Navigation', 'boss-seo'),
      content: __('Utilisez ces onglets pour naviguer entre la base de connaissances, les tutoriels et le système de feedback.', 'boss-seo')
    },
    {
      target: '.boss-knowledge-base',
      title: __('Base de connaissances', 'boss-seo'),
      content: __('Explorez notre base de connaissances complète avec des articles détaillés sur toutes les fonctionnalités de Boss SEO.', 'boss-seo')
    },
    {
      target: '.boss-tutorials',
      title: __('Tutoriels interactifs', 'boss-seo'),
      content: __('Suivez nos tutoriels étape par étape pour apprendre à utiliser Boss SEO efficacement.', 'boss-seo')
    },
    {
      target: '.boss-feedback',
      title: __('Système de feedback', 'boss-seo'),
      content: __('Signalez des bugs, suggérez des fonctionnalités ou partagez votre expérience avec Boss SEO.', 'boss-seo')
    }
  ];

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Aide et documentation', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Trouvez de l\'aide, des tutoriels et des ressources pour tirer le meilleur parti de Boss SEO.', 'boss-seo')}
          </p>
        </div>
        
        <div className="boss-mb-6 boss-help-search">
          <HelpSearch 
            articles={articles}
            onSearch={handleSearch}
            showInstantResults={true}
          />
        </div>
        
        <div className="boss-flex boss-justify-between boss-items-center boss-mb-6">
          <div className="boss-help-tabs">
            <TabPanel
              className="boss-mb-6"
              activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
              onSelect={(tabName) => setActiveTab(tabName)}
              tabs={[
                {
                  name: 'knowledge-base',
                  title: __('Base de connaissances', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                },
                {
                  name: 'tutorials',
                  title: __('Tutoriels', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                },
                {
                  name: 'feedback',
                  title: __('Feedback & Support', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                }
              ]}
            >
              {(tab) => {
                if (isLoading) {
                  return (
                    <Card>
                      <CardBody>
                        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
                          <Spinner />
                        </div>
                      </CardBody>
                    </Card>
                  );
                }

                switch (tab.name) {
                  case 'knowledge-base':
                    return (
                      <div className="boss-knowledge-base">
                        <KnowledgeBase 
                          articles={isSearching ? searchResults : articles}
                          categories={categories}
                          isLoading={isLoading}
                        />
                      </div>
                    );
                  case 'tutorials':
                    return (
                      <div className="boss-tutorials">
                        <Tutorials 
                          tutorials={tutorials}
                          isLoading={isLoading}
                        />
                      </div>
                    );
                  case 'feedback':
                    return (
                      <div className="boss-feedback">
                        <Feedback />
                      </div>
                    );
                  default:
                    return (
                      <div className="boss-knowledge-base">
                        <KnowledgeBase 
                          articles={articles}
                          categories={categories}
                          isLoading={isLoading}
                        />
                      </div>
                    );
                }
              }}
            </TabPanel>
          </div>
          
          <div>
            <TourGuideStarter
              steps={tourSteps}
              tourId="help-page-tour"
              buttonText={__('Visite guidée', 'boss-seo')}
              showAsIcon={false}
              autoStart={false}
            />
          </div>
        </div>
        
        {/* Informations contextuelles */}
        <Card>
          <CardBody>
            <div className="boss-flex boss-items-start boss-gap-4">
              <div className="boss-flex-shrink-0 boss-w-8 boss-h-8 boss-bg-blue-100 boss-rounded-full boss-flex boss-items-center boss-justify-center">
                <span className="dashicons dashicons-info boss-text-blue-600"></span>
              </div>
              <div>
                <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-2">
                  {activeTab === 'knowledge-base' && __('À propos de la base de connaissances', 'boss-seo')}
                  {activeTab === 'tutorials' && __('À propos des tutoriels', 'boss-seo')}
                  {activeTab === 'feedback' && __('À propos du feedback et du support', 'boss-seo')}
                </h3>
                <div className="boss-text-boss-gray">
                  {activeTab === 'knowledge-base' && (
                    <p>
                      {__('Notre base de connaissances contient des articles détaillés sur toutes les fonctionnalités de Boss SEO. Utilisez la recherche ou parcourez les catégories pour trouver l\'information dont vous avez besoin.', 'boss-seo')}
                    </p>
                  )}
                  {activeTab === 'tutorials' && (
                    <p>
                      {__('Nos tutoriels interactifs vous guident étape par étape dans l\'utilisation de Boss SEO. Suivez-les pour apprendre à utiliser efficacement toutes les fonctionnalités du plugin.', 'boss-seo')}
                    </p>
                  )}
                  {activeTab === 'feedback' && (
                    <p>
                      {__('Votre feedback est important pour nous ! Utilisez ces formulaires pour signaler des bugs, suggérer des fonctionnalités ou partager votre expérience avec Boss SEO.', 'boss-seo')}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default Help;
