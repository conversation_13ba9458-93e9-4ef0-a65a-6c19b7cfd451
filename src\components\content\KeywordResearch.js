import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Dashicon,
  Notice,
  Panel,
  PanelBody,
  PanelRow,
  SearchControl,
  SelectControl,
  Spinner,
  TextControl,
  ToggleControl,
  Tooltip
} from '@wordpress/components';

/**
 * Composant pour afficher un graphique de tendance simple
 */
const TrendChart = ({ data, color = 'boss-primary' }) => {
  // Trouver les valeurs min et max pour l'échelle
  const values = data.map(item => item.value);
  const maxValue = Math.max(...values, 0);
  const minValue = Math.min(...values, 0);
  const range = maxValue - minValue;
  
  return (
    <div className="boss-relative boss-h-10 boss-w-full">
      <div className="boss-absolute boss-inset-0 boss-flex boss-items-end boss-justify-between">
        {data.map((item, index) => {
          // Calculer la hauteur de la barre
          const barHeight = range !== 0 
            ? ((item.value - minValue) / range) * 40 
            : 0;
          
          return (
            <div 
              key={index} 
              className="boss-relative boss-flex boss-flex-col boss-items-center boss-group"
              style={{ height: '100%', width: `${100 / data.length}%` }}
            >
              {/* Barre du graphique */}
              <div 
                className={`boss-w-2 boss-rounded-t boss-transition-all boss-duration-500 boss-ease-out boss-bg-${color}`}
                style={{ height: `${Math.max(barHeight, 2)}px` }}
              ></div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

/**
 * Composant pour afficher un mot-clé avec ses métriques
 */
const KeywordItem = ({ keyword, onSave, onSetFocus, isSaved, isFocus }) => {
  return (
    <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-mb-4 boss-bg-white boss-hover:boss-shadow-sm boss-transition-shadow boss-duration-200">
      <div className="boss-flex boss-justify-between boss-items-start boss-mb-3">
        <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark">
          {keyword.text}
        </h3>
        <div className="boss-flex boss-space-x-2">
          <Button
            isSmall
            isSecondary={!isFocus}
            isPrimary={isFocus}
            onClick={() => onSetFocus(keyword)}
            className="boss-flex boss-items-center"
            icon="tag"
            label={__('Définir comme mot-clé principal', 'boss-seo')}
            showTooltip
          >
            {isFocus ? __('Mot-clé principal', 'boss-seo') : __('Utiliser', 'boss-seo')}
          </Button>
          
          <Button
            isSmall
            isSecondary={!isSaved}
            isPrimary={isSaved}
            onClick={() => onSave(keyword)}
            icon={isSaved ? 'star-filled' : 'star-empty'}
            label={isSaved ? __('Retirer des favoris', 'boss-seo') : __('Ajouter aux favoris', 'boss-seo')}
            showTooltip
          />
        </div>
      </div>
      
      <div className="boss-grid boss-grid-cols-3 boss-gap-4 boss-mb-4">
        <div>
          <div className="boss-text-sm boss-text-boss-gray boss-mb-1">
            {__('Volume de recherche', 'boss-seo')}
          </div>
          <div className="boss-flex boss-items-center">
            <span className="boss-text-xl boss-font-bold boss-text-boss-dark boss-mr-2">
              {keyword.volume.toLocaleString()}
            </span>
            {keyword.volumeTrend > 0 ? (
              <span className="boss-text-green-600 boss-text-sm boss-flex boss-items-center">
                <Dashicon icon="arrow-up-alt" className="boss-mr-1" />
                {keyword.volumeTrend}%
              </span>
            ) : keyword.volumeTrend < 0 ? (
              <span className="boss-text-red-600 boss-text-sm boss-flex boss-items-center">
                <Dashicon icon="arrow-down-alt" className="boss-mr-1" />
                {Math.abs(keyword.volumeTrend)}%
              </span>
            ) : null}
          </div>
        </div>
        
        <div>
          <div className="boss-text-sm boss-text-boss-gray boss-mb-1">
            {__('Difficulté', 'boss-seo')}
          </div>
          <div className="boss-flex boss-items-center">
            <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2 boss-mr-2">
              <div 
                className={`boss-h-2 boss-rounded-full ${
                  keyword.difficulty < 30 ? 'boss-bg-green-500' :
                  keyword.difficulty < 60 ? 'boss-bg-yellow-500' :
                  'boss-bg-red-500'
                }`}
                style={{ width: `${keyword.difficulty}%` }}
              ></div>
            </div>
            <span className="boss-text-sm boss-font-medium boss-text-boss-dark">
              {keyword.difficulty}/100
            </span>
          </div>
        </div>
        
        <div>
          <div className="boss-text-sm boss-text-boss-gray boss-mb-1">
            {__('CPC', 'boss-seo')}
          </div>
          <div className="boss-text-xl boss-font-bold boss-text-boss-dark">
            {keyword.cpc.toFixed(2)} €
          </div>
        </div>
      </div>
      
      <div className="boss-mb-4">
        <div className="boss-text-sm boss-text-boss-gray boss-mb-1">
          {__('Tendance (12 derniers mois)', 'boss-seo')}
        </div>
        <TrendChart data={keyword.trend} color={
          keyword.volumeTrend > 0 ? 'green-500' :
          keyword.volumeTrend < 0 ? 'red-500' :
          'boss-primary'
        } />
      </div>
      
      {keyword.related && keyword.related.length > 0 && (
        <div>
          <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
            {__('Mots-clés connexes', 'boss-seo')}
          </div>
          <div className="boss-flex boss-flex-wrap boss-gap-2">
            {keyword.related.map((relatedKeyword, index) => (
              <div 
                key={index}
                className="boss-bg-gray-100 boss-text-boss-dark boss-px-3 boss-py-1 boss-rounded-full boss-text-sm"
              >
                {relatedKeyword}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Composant principal de recherche de mots-clés
 */
const KeywordResearch = ({ focusKeyword, savedKeywords, onSaveKeyword, onSetFocusKeyword }) => {
  // État pour gérer la recherche
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  
  // État pour gérer les filtres
  const [filters, setFilters] = useState({
    difficulty: 'all',
    volume: 'all',
    trend: 'all'
  });
  
  // Données fictives pour les mots-clés
  const mockKeywords = [
    {
      text: 'référencement naturel',
      volume: 12500,
      volumeTrend: 5,
      difficulty: 75,
      cpc: 2.45,
      trend: [
        { month: 'Jan', value: 10000 },
        { month: 'Feb', value: 10500 },
        { month: 'Mar', value: 11000 },
        { month: 'Apr', value: 10800 },
        { month: 'May', value: 11200 },
        { month: 'Jun', value: 11500 },
        { month: 'Jul', value: 11800 },
        { month: 'Aug', value: 12000 },
        { month: 'Sep', value: 12200 },
        { month: 'Oct', value: 12300 },
        { month: 'Nov', value: 12400 },
        { month: 'Dec', value: 12500 }
      ],
      related: ['seo', 'optimisation moteur de recherche', 'google ranking', 'référencement google']
    },
    {
      text: 'optimisation seo',
      volume: 8200,
      volumeTrend: 12,
      difficulty: 65,
      cpc: 3.10,
      trend: [
        { month: 'Jan', value: 7000 },
        { month: 'Feb', value: 7200 },
        { month: 'Mar', value: 7300 },
        { month: 'Apr', value: 7500 },
        { month: 'May', value: 7600 },
        { month: 'Jun', value: 7800 },
        { month: 'Jul', value: 7900 },
        { month: 'Aug', value: 8000 },
        { month: 'Sep', value: 8100 },
        { month: 'Oct', value: 8150 },
        { month: 'Nov', value: 8180 },
        { month: 'Dec', value: 8200 }
      ],
      related: ['techniques seo', 'améliorer référencement', 'optimisation site web', 'seo on page']
    },
    {
      text: 'mots-clés seo',
      volume: 5400,
      volumeTrend: -3,
      difficulty: 45,
      cpc: 1.85,
      trend: [
        { month: 'Jan', value: 5600 },
        { month: 'Feb', value: 5650 },
        { month: 'Mar', value: 5600 },
        { month: 'Apr', value: 5550 },
        { month: 'May', value: 5500 },
        { month: 'Jun', value: 5480 },
        { month: 'Jul', value: 5450 },
        { month: 'Aug', value: 5430 },
        { month: 'Sep', value: 5420 },
        { month: 'Oct', value: 5410 },
        { month: 'Nov', value: 5405 },
        { month: 'Dec', value: 5400 }
      ],
      related: ['recherche de mots-clés', 'mots-clés longue traîne', 'outils mots-clés', 'keyword planner']
    },
    {
      text: 'backlinks seo',
      volume: 4800,
      volumeTrend: 8,
      difficulty: 70,
      cpc: 2.75,
      trend: [
        { month: 'Jan', value: 4400 },
        { month: 'Feb', value: 4450 },
        { month: 'Mar', value: 4500 },
        { month: 'Apr', value: 4550 },
        { month: 'May', value: 4600 },
        { month: 'Jun', value: 4650 },
        { month: 'Jul', value: 4700 },
        { month: 'Aug', value: 4720 },
        { month: 'Sep', value: 4740 },
        { month: 'Oct', value: 4760 },
        { month: 'Nov', value: 4780 },
        { month: 'Dec', value: 4800 }
      ],
      related: ['netlinking', 'liens externes', 'stratégie de liens', 'link building']
    },
    {
      text: 'audit seo',
      volume: 3200,
      volumeTrend: 15,
      difficulty: 55,
      cpc: 4.20,
      trend: [
        { month: 'Jan', value: 2700 },
        { month: 'Feb', value: 2750 },
        { month: 'Mar', value: 2800 },
        { month: 'Apr', value: 2850 },
        { month: 'May', value: 2900 },
        { month: 'Jun', value: 2950 },
        { month: 'Jul', value: 3000 },
        { month: 'Aug', value: 3050 },
        { month: 'Sep', value: 3100 },
        { month: 'Oct', value: 3150 },
        { month: 'Nov', value: 3180 },
        { month: 'Dec', value: 3200 }
      ],
      related: ['analyse seo', 'audit technique', 'audit site web', 'analyse référencement']
    }
  ];
  
  // Fonction pour rechercher des mots-clés
  const handleSearch = () => {
    if (!searchQuery) return;
    
    setIsSearching(true);
    
    // Simuler un appel à une API de recherche de mots-clés
    setTimeout(() => {
      // Filtrer les mots-clés en fonction de la recherche
      const results = mockKeywords.filter(keyword => 
        keyword.text.toLowerCase().includes(searchQuery.toLowerCase())
      );
      
      setSearchResults(results);
      setIsSearching(false);
    }, 1000);
  };
  
  // Fonction pour vérifier si un mot-clé est enregistré
  const isKeywordSaved = (keyword) => {
    return savedKeywords.some(k => k.text === keyword.text);
  };
  
  // Fonction pour vérifier si un mot-clé est le mot-clé principal
  const isKeywordFocus = (keyword) => {
    return keyword.text === focusKeyword;
  };
  
  // Effet pour lancer la recherche lorsque la requête change
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery) {
        handleSearch();
      }
    }, 500);
    
    return () => clearTimeout(timer);
  }, [searchQuery]);
  
  return (
    <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-6">
      {/* Colonne de gauche : Recherche et filtres */}
      <div className="boss-col-span-1">
        <Card className="boss-mb-6">
          <CardHeader>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Recherche de mots-clés', 'boss-seo')}
            </h3>
          </CardHeader>
          <CardBody>
            <SearchControl
              value={searchQuery}
              onChange={setSearchQuery}
              label={__('Rechercher des mots-clés', 'boss-seo')}
              placeholder={__('Entrez un mot-clé...', 'boss-seo')}
              className="boss-mb-4"
            />
            
            <div className="boss-mb-4">
              <h4 className="boss-text-sm boss-font-medium boss-text-boss-gray boss-mb-2">
                {__('Filtres', 'boss-seo')}
              </h4>
              
              <SelectControl
                label={__('Difficulté', 'boss-seo')}
                value={filters.difficulty}
                options={[
                  { label: __('Toutes les difficultés', 'boss-seo'), value: 'all' },
                  { label: __('Facile (0-30)', 'boss-seo'), value: 'easy' },
                  { label: __('Moyenne (31-60)', 'boss-seo'), value: 'medium' },
                  { label: __('Difficile (61-100)', 'boss-seo'), value: 'hard' }
                ]}
                onChange={(value) => setFilters({...filters, difficulty: value})}
                className="boss-mb-3"
              />
              
              <SelectControl
                label={__('Volume de recherche', 'boss-seo')}
                value={filters.volume}
                options={[
                  { label: __('Tous les volumes', 'boss-seo'), value: 'all' },
                  { label: __('Faible (< 1000)', 'boss-seo'), value: 'low' },
                  { label: __('Moyen (1000-10000)', 'boss-seo'), value: 'medium' },
                  { label: __('Élevé (> 10000)', 'boss-seo'), value: 'high' }
                ]}
                onChange={(value) => setFilters({...filters, volume: value})}
                className="boss-mb-3"
              />
              
              <SelectControl
                label={__('Tendance', 'boss-seo')}
                value={filters.trend}
                options={[
                  { label: __('Toutes les tendances', 'boss-seo'), value: 'all' },
                  { label: __('En hausse', 'boss-seo'), value: 'up' },
                  { label: __('Stable', 'boss-seo'), value: 'stable' },
                  { label: __('En baisse', 'boss-seo'), value: 'down' }
                ]}
                onChange={(value) => setFilters({...filters, trend: value})}
              />
            </div>
            
            <Button
              isPrimary
              isBusy={isSearching}
              onClick={handleSearch}
              className="boss-w-full boss-justify-center"
            >
              {isSearching ? __('Recherche en cours...', 'boss-seo') : __('Rechercher', 'boss-seo')}
            </Button>
          </CardBody>
        </Card>
        
        <Card>
          <CardHeader>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Mots-clés enregistrés', 'boss-seo')}
            </h3>
          </CardHeader>
          <CardBody>
            {savedKeywords.length === 0 ? (
              <p className="boss-text-boss-gray boss-italic">
                {__('Aucun mot-clé enregistré. Utilisez la recherche pour trouver et enregistrer des mots-clés.', 'boss-seo')}
              </p>
            ) : (
              <div className="boss-space-y-3">
                {savedKeywords.map((keyword, index) => (
                  <div 
                    key={index}
                    className="boss-flex boss-justify-between boss-items-center boss-p-3 boss-border boss-border-gray-200 boss-rounded-lg"
                  >
                    <div>
                      <div className="boss-font-medium boss-text-boss-dark">
                        {keyword.text}
                      </div>
                      {keyword.volume && (
                        <div className="boss-text-sm boss-text-boss-gray">
                          {keyword.volume.toLocaleString()} {__('recherches/mois', 'boss-seo')}
                        </div>
                      )}
                    </div>
                    <Button
                      isSmall
                      isSecondary={keyword.text !== focusKeyword}
                      isPrimary={keyword.text === focusKeyword}
                      onClick={() => onSetFocusKeyword(keyword)}
                      icon="tag"
                    >
                      {keyword.text === focusKeyword ? __('Principal', 'boss-seo') : __('Utiliser', 'boss-seo')}
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </div>
      
      {/* Colonne de droite : Résultats de recherche */}
      <div className="boss-col-span-2">
        <Card>
          <CardHeader>
            <div className="boss-flex boss-justify-between boss-items-center">
              <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                {__('Résultats de recherche', 'boss-seo')}
              </h3>
              {searchResults.length > 0 && (
                <span className="boss-text-sm boss-text-boss-gray">
                  {searchResults.length} {__('résultats trouvés', 'boss-seo')}
                </span>
              )}
            </div>
          </CardHeader>
          <CardBody>
            {isSearching ? (
              <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
                <Spinner />
              </div>
            ) : searchResults.length === 0 ? (
              searchQuery ? (
                <div className="boss-text-center boss-p-8">
                  <Dashicon icon="search" className="boss-text-4xl boss-text-boss-gray boss-mb-2" />
                  <p className="boss-text-boss-gray">
                    {__('Aucun résultat trouvé pour', 'boss-seo')} "{searchQuery}".
                  </p>
                  <p className="boss-text-sm boss-text-boss-gray boss-mt-2">
                    {__('Essayez avec d\'autres termes ou moins de filtres.', 'boss-seo')}
                  </p>
                </div>
              ) : (
                <div className="boss-text-center boss-p-8">
                  <Dashicon icon="search" className="boss-text-4xl boss-text-boss-gray boss-mb-2" />
                  <p className="boss-text-boss-gray">
                    {__('Entrez un mot-clé dans la barre de recherche pour commencer.', 'boss-seo')}
                  </p>
                </div>
              )
            ) : (
              <div>
                {searchResults.map((keyword, index) => (
                  <KeywordItem 
                    key={index}
                    keyword={keyword}
                    onSave={onSaveKeyword}
                    onSetFocus={onSetFocusKeyword}
                    isSaved={isKeywordSaved(keyword)}
                    isFocus={isKeywordFocus(keyword)}
                  />
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default KeywordResearch;
