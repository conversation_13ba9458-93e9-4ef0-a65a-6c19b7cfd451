import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  TextControl,
  TextareaControl,
  SelectControl,
  ToggleControl,
  Modal,
  Notice,
  Spinner,
  TabPanel
} from '@wordpress/components';

const LocalPageGenerator = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [locations, setLocations] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [generatedPages, setGeneratedPages] = useState([]);
  const [pageSettings, setPageSettings] = useState({
    title: '',
    slug: '',
    metaDescription: '',
    featuredImage: '',
    includeMap: true,
    includeContactForm: true,
    includeReviews: true,
    includeOpeningHours: true,
    includeDirections: true,
    includeSchema: true
  });
  
  // Charger les données
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour les emplacements
      const mockLocations = [
        {
          id: 1,
          name: 'Paris - Siège social',
          address: '123 Avenue des Champs-Élysées, 75008 Paris',
          phone: '01 23 45 67 89',
          email: '<EMAIL>',
          openingHours: 'Lun-Ven: 9h-18h, Sam: 10h-17h',
          description: 'Notre siège social au cœur de Paris',
          hasPage: true,
          pageUrl: '/locations/paris-siege-social/'
        },
        {
          id: 2,
          name: 'Lyon - Succursale',
          address: '45 Rue de la République, 69002 Lyon',
          phone: '04 78 12 34 56',
          email: '<EMAIL>',
          openingHours: 'Lun-Ven: 9h-18h, Sam: 10h-16h',
          description: 'Notre boutique au centre de Lyon',
          hasPage: false,
          pageUrl: ''
        },
        {
          id: 3,
          name: 'Marseille - Boutique',
          address: '78 La Canebière, 13001 Marseille',
          phone: '04 91 23 45 67',
          email: '<EMAIL>',
          openingHours: 'Lun-Sam: 10h-19h',
          description: 'Notre nouvelle boutique à Marseille',
          hasPage: false,
          pageUrl: ''
        }
      ];
      
      // Données fictives pour les templates
      const mockTemplates = [
        {
          id: 1,
          name: 'Template standard',
          description: 'Un template simple et efficace pour présenter votre emplacement local.',
          preview: 'https://example.com/template-standard.jpg',
          sections: ['header', 'description', 'map', 'contact', 'hours', 'reviews']
        },
        {
          id: 2,
          name: 'Template boutique',
          description: 'Idéal pour les magasins et boutiques avec mise en avant des produits.',
          preview: 'https://example.com/template-boutique.jpg',
          sections: ['header', 'products', 'description', 'map', 'contact', 'hours']
        },
        {
          id: 3,
          name: 'Template restaurant',
          description: 'Parfait pour les restaurants avec mise en avant du menu et des photos.',
          preview: 'https://example.com/template-restaurant.jpg',
          sections: ['header', 'menu', 'gallery', 'description', 'map', 'hours', 'reviews']
        }
      ];
      
      // Données fictives pour les pages générées
      const mockGeneratedPages = [
        {
          id: 1,
          locationId: 1,
          title: 'Notre magasin à Paris',
          url: '/locations/paris-siege-social/',
          template: 'Template standard',
          dateCreated: '2023-06-15',
          status: 'published'
        }
      ];
      
      setLocations(mockLocations);
      setTemplates(mockTemplates);
      setGeneratedPages(mockGeneratedPages);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  // Effet pour mettre à jour les paramètres de page lorsqu'un emplacement est sélectionné
  useEffect(() => {
    if (selectedLocation) {
      setPageSettings({
        ...pageSettings,
        title: `${selectedLocation.name} - Visitez notre emplacement`,
        slug: selectedLocation.name.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
        metaDescription: `Visitez notre emplacement à ${selectedLocation.name.split(' - ')[0]}. ${selectedLocation.description}`
      });
    }
  }, [selectedLocation]);
  
  // Fonction pour générer une page
  const handleGeneratePage = () => {
    if (!selectedLocation || !selectedTemplate) return;
    
    setIsLoading(true);
    
    // Simuler la génération de page
    setTimeout(() => {
      const newPage = {
        id: generatedPages.length + 1,
        locationId: selectedLocation.id,
        title: pageSettings.title,
        url: `/locations/${pageSettings.slug}/`,
        template: selectedTemplate.name,
        dateCreated: new Date().toISOString().split('T')[0],
        status: 'published'
      };
      
      // Mettre à jour la liste des pages générées
      setGeneratedPages([...generatedPages, newPage]);
      
      // Mettre à jour l'emplacement pour indiquer qu'il a une page
      const updatedLocations = locations.map(location => {
        if (location.id === selectedLocation.id) {
          return {
            ...location,
            hasPage: true,
            pageUrl: `/locations/${pageSettings.slug}/`
          };
        }
        return location;
      });
      
      setLocations(updatedLocations);
      setIsLoading(false);
      setShowSuccess(true);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1500);
  };
  
  // Fonction pour prévisualiser une page
  const handlePreviewPage = () => {
    if (!selectedLocation || !selectedTemplate) return;
    setShowPreview(true);
  };
  
  // Fonction pour supprimer une page
  const handleDeletePage = (pageId, locationId) => {
    // Supprimer la page de la liste des pages générées
    const updatedPages = generatedPages.filter(page => page.id !== pageId);
    setGeneratedPages(updatedPages);
    
    // Mettre à jour l'emplacement pour indiquer qu'il n'a plus de page
    const updatedLocations = locations.map(location => {
      if (location.id === locationId) {
        return {
          ...location,
          hasPage: false,
          pageUrl: ''
        };
      }
      return location;
    });
    
    setLocations(updatedLocations);
  };
  
  // Fonction pour obtenir l'emplacement par ID
  const getLocationById = (locationId) => {
    return locations.find(location => location.id === locationId);
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {__('La page a été générée avec succès !', 'boss-seo')}
            </Notice>
          )}
          
          <TabPanel
            className="boss-mb-6"
            activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
            tabs={[
              {
                name: 'generator',
                title: __('Générateur de pages', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'pages',
                title: __('Pages générées', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              }
            ]}
          >
            {(tab) => {
              if (tab.name === 'generator') {
                return (
                  <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
                    {/* Panneau principal */}
                    <div className="lg:boss-col-span-2">
                      <Card className="boss-mb-6">
                        <CardHeader className="boss-border-b boss-border-gray-200">
                          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                            {__('Générateur de pages locales', 'boss-seo')}
                          </h2>
                        </CardHeader>
                        <CardBody>
                          <div className="boss-mb-6">
                            <SelectControl
                              label={__('Sélectionner un emplacement', 'boss-seo')}
                              value={selectedLocation ? selectedLocation.id : ''}
                              options={[
                                { label: __('-- Sélectionner un emplacement --', 'boss-seo'), value: '' },
                                ...locations.map(location => ({
                                  label: `${location.name}${location.hasPage ? ` (${__('Page existante', 'boss-seo')})` : ''}`,
                                  value: location.id
                                }))
                              ]}
                              onChange={(value) => {
                                const location = locations.find(loc => loc.id === parseInt(value));
                                setSelectedLocation(location);
                              }}
                            />
                          </div>
                          
                          {selectedLocation && (
                            <div>
                              <div className="boss-mb-6">
                                <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                                  {__('Informations de l\'emplacement', 'boss-seo')}
                                </h3>
                                
                                <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-mb-4">
                                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                                    <div>
                                      <span className="boss-text-sm boss-text-boss-gray boss-block boss-mb-1">
                                        {__('Adresse :', 'boss-seo')}
                                      </span>
                                      <span className="boss-font-medium">{selectedLocation.address}</span>
                                    </div>
                                    
                                    <div>
                                      <span className="boss-text-sm boss-text-boss-gray boss-block boss-mb-1">
                                        {__('Téléphone :', 'boss-seo')}
                                      </span>
                                      <span className="boss-font-medium">{selectedLocation.phone}</span>
                                    </div>
                                    
                                    <div>
                                      <span className="boss-text-sm boss-text-boss-gray boss-block boss-mb-1">
                                        {__('Email :', 'boss-seo')}
                                      </span>
                                      <span className="boss-font-medium">{selectedLocation.email}</span>
                                    </div>
                                    
                                    <div>
                                      <span className="boss-text-sm boss-text-boss-gray boss-block boss-mb-1">
                                        {__('Horaires :', 'boss-seo')}
                                      </span>
                                      <span className="boss-font-medium">{selectedLocation.openingHours}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                              <div className="boss-mb-6">
                                <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                                  {__('Paramètres de la page', 'boss-seo')}
                                </h3>
                                
                                <div className="boss-space-y-4">
                                  <TextControl
                                    label={__('Titre de la page', 'boss-seo')}
                                    value={pageSettings.title}
                                    onChange={(value) => setPageSettings({ ...pageSettings, title: value })}
                                  />
                                  
                                  <TextControl
                                    label={__('Slug', 'boss-seo')}
                                    value={pageSettings.slug}
                                    onChange={(value) => setPageSettings({ ...pageSettings, slug: value })}
                                  />
                                  
                                  <TextareaControl
                                    label={__('Meta description', 'boss-seo')}
                                    value={pageSettings.metaDescription}
                                    onChange={(value) => setPageSettings({ ...pageSettings, metaDescription: value })}
                                  />
                                  
                                  <TextControl
                                    label={__('URL de l\'image à la une', 'boss-seo')}
                                    value={pageSettings.featuredImage}
                                    onChange={(value) => setPageSettings({ ...pageSettings, featuredImage: value })}
                                  />
                                </div>
                              </div>
                              
                              <div className="boss-mb-6">
                                <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                                  {__('Éléments à inclure', 'boss-seo')}
                                </h3>
                                
                                <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                                  <ToggleControl
                                    label={__('Carte Google Maps', 'boss-seo')}
                                    checked={pageSettings.includeMap}
                                    onChange={(checked) => setPageSettings({ ...pageSettings, includeMap: checked })}
                                  />
                                  
                                  <ToggleControl
                                    label={__('Formulaire de contact', 'boss-seo')}
                                    checked={pageSettings.includeContactForm}
                                    onChange={(checked) => setPageSettings({ ...pageSettings, includeContactForm: checked })}
                                  />
                                  
                                  <ToggleControl
                                    label={__('Avis clients', 'boss-seo')}
                                    checked={pageSettings.includeReviews}
                                    onChange={(checked) => setPageSettings({ ...pageSettings, includeReviews: checked })}
                                  />
                                  
                                  <ToggleControl
                                    label={__('Horaires d\'ouverture', 'boss-seo')}
                                    checked={pageSettings.includeOpeningHours}
                                    onChange={(checked) => setPageSettings({ ...pageSettings, includeOpeningHours: checked })}
                                  />
                                  
                                  <ToggleControl
                                    label={__('Itinéraire', 'boss-seo')}
                                    checked={pageSettings.includeDirections}
                                    onChange={(checked) => setPageSettings({ ...pageSettings, includeDirections: checked })}
                                  />
                                  
                                  <ToggleControl
                                    label={__('Schéma LocalBusiness', 'boss-seo')}
                                    checked={pageSettings.includeSchema}
                                    onChange={(checked) => setPageSettings({ ...pageSettings, includeSchema: checked })}
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </CardBody>
                        <CardFooter className="boss-border-t boss-border-gray-200">
                          <div className="boss-flex boss-justify-end boss-space-x-3">
                            <Button
                              isSecondary
                              onClick={handlePreviewPage}
                              disabled={!selectedLocation || !selectedTemplate}
                            >
                              {__('Prévisualiser', 'boss-seo')}
                            </Button>
                            <Button
                              isPrimary
                              onClick={handleGeneratePage}
                              disabled={!selectedLocation || !selectedTemplate}
                            >
                              {__('Générer la page', 'boss-seo')}
                            </Button>
                          </div>
                        </CardFooter>
                      </Card>
                    </div>
                    
                    {/* Panneau latéral */}
                    <div>
                      <Card>
                        <CardHeader className="boss-border-b boss-border-gray-200">
                          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                            {__('Templates disponibles', 'boss-seo')}
                          </h2>
                        </CardHeader>
                        <CardBody className="boss-p-0">
                          <div className="boss-divide-y boss-divide-gray-200">
                            {templates.map(template => (
                              <div 
                                key={template.id} 
                                className={`boss-p-4 boss-cursor-pointer boss-transition-colors ${
                                  selectedTemplate && selectedTemplate.id === template.id
                                    ? 'boss-bg-blue-50'
                                    : 'boss-hover:boss-bg-gray-50'
                                }`}
                                onClick={() => setSelectedTemplate(template)}
                              >
                                <div className="boss-flex boss-items-start">
                                  <div className="boss-flex-1">
                                    <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                                      {template.name}
                                    </h3>
                                    <p className="boss-text-sm boss-text-boss-gray boss-mb-2">
                                      {template.description}
                                    </p>
                                    <div className="boss-flex boss-flex-wrap boss-gap-1">
                                      {template.sections.map((section, index) => (
                                        <span 
                                          key={index}
                                          className="boss-px-2 boss-py-1 boss-bg-gray-100 boss-text-boss-gray boss-text-xs boss-rounded"
                                        >
                                          {section}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                  {selectedTemplate && selectedTemplate.id === template.id && (
                                    <div className="boss-text-boss-primary">
                                      <Dashicon icon="yes-alt" />
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardBody>
                      </Card>
                    </div>
                  </div>
                );
              } else if (tab.name === 'pages') {
                return (
                  <Card>
                    <CardHeader className="boss-border-b boss-border-gray-200">
                      <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                        {__('Pages locales générées', 'boss-seo')}
                      </h2>
                    </CardHeader>
                    <CardBody className="boss-p-0">
                      {generatedPages.length === 0 ? (
                        <div className="boss-text-center boss-py-8 boss-text-boss-gray">
                          {__('Aucune page locale n\'a encore été générée.', 'boss-seo')}
                        </div>
                      ) : (
                        <div className="boss-divide-y boss-divide-gray-200">
                          {generatedPages.map(page => {
                            const location = getLocationById(page.locationId);
                            return (
                              <div key={page.id} className="boss-p-4 boss-hover:boss-bg-gray-50">
                                <div className="boss-flex boss-flex-col md:boss-flex-row boss-justify-between boss-items-start boss-gap-4">
                                  <div>
                                    <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                                      {page.title}
                                    </h3>
                                    <div className="boss-flex boss-flex-wrap boss-gap-2 boss-text-sm boss-text-boss-gray boss-mb-2">
                                      <span>{location ? location.name : __('Emplacement inconnu', 'boss-seo')}</span>
                                      <span>•</span>
                                      <span>{page.template}</span>
                                      <span>•</span>
                                      <span>{__('Créée le', 'boss-seo')} {page.dateCreated}</span>
                                    </div>
                                    <div className="boss-flex boss-items-center">
                                      <span className={`boss-px-2 boss-py-1 boss-text-xs boss-rounded-full ${
                                        page.status === 'published'
                                          ? 'boss-bg-green-100 boss-text-green-800'
                                          : 'boss-bg-yellow-100 boss-text-yellow-800'
                                      }`}>
                                        {page.status === 'published'
                                          ? __('Publiée', 'boss-seo')
                                          : __('Brouillon', 'boss-seo')}
                                      </span>
                                      <span className="boss-text-sm boss-text-boss-gray boss-ml-2">
                                        {page.url}
                                      </span>
                                    </div>
                                  </div>
                                  <div className="boss-flex boss-space-x-2">
                                    <Button
                                      isSecondary
                                      isSmall
                                      href={page.url}
                                      target="_blank"
                                    >
                                      {__('Voir', 'boss-seo')}
                                    </Button>
                                    <Button
                                      isSecondary
                                      isSmall
                                      href={`/wp-admin/post.php?post=${page.id}&action=edit`}
                                      target="_blank"
                                    >
                                      {__('Éditer', 'boss-seo')}
                                    </Button>
                                    <Button
                                      isDestructive
                                      isSmall
                                      onClick={() => handleDeletePage(page.id, page.locationId)}
                                    >
                                      {__('Supprimer', 'boss-seo')}
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </CardBody>
                  </Card>
                );
              }
            }}
          </TabPanel>
          
          {/* Modal de prévisualisation */}
          {showPreview && selectedLocation && selectedTemplate && (
            <Modal
              title={__('Prévisualisation de la page', 'boss-seo')}
              onRequestClose={() => setShowPreview(false)}
              className="boss-preview-modal"
            >
              <div className="boss-p-6">
                <div className="boss-bg-gray-100 boss-p-4 boss-rounded-lg boss-mb-6">
                  <h2 className="boss-text-xl boss-font-bold boss-mb-4">{pageSettings.title}</h2>
                  
                  {pageSettings.featuredImage && (
                    <div className="boss-mb-4 boss-bg-gray-200 boss-h-40 boss-flex boss-items-center boss-justify-center boss-rounded">
                      <Dashicon icon="format-image" className="boss-text-3xl boss-text-boss-gray" />
                    </div>
                  )}
                  
                  <div className="boss-mb-4">
                    <h3 className="boss-font-medium boss-mb-2">{selectedLocation.name}</h3>
                    <p className="boss-text-boss-gray boss-mb-2">{selectedLocation.description}</p>
                  </div>
                  
                  {pageSettings.includeMap && (
                    <div className="boss-mb-4 boss-bg-gray-200 boss-h-40 boss-flex boss-items-center boss-justify-center boss-rounded">
                      <div className="boss-text-center boss-text-boss-gray">
                        <Dashicon icon="location" className="boss-text-3xl boss-mb-2" />
                        <p>{__('Carte Google Maps', 'boss-seo')}</p>
                      </div>
                    </div>
                  )}
                  
                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mb-4">
                    {pageSettings.includeOpeningHours && (
                      <div>
                        <h3 className="boss-font-medium boss-mb-2">{__('Horaires d\'ouverture', 'boss-seo')}</h3>
                        <p className="boss-text-boss-gray">{selectedLocation.openingHours}</p>
                      </div>
                    )}
                    
                    <div>
                      <h3 className="boss-font-medium boss-mb-2">{__('Contact', 'boss-seo')}</h3>
                      <p className="boss-text-boss-gray">{selectedLocation.phone}</p>
                      <p className="boss-text-boss-gray">{selectedLocation.email}</p>
                    </div>
                  </div>
                  
                  {pageSettings.includeContactForm && (
                    <div className="boss-mb-4 boss-bg-gray-200 boss-p-4 boss-rounded">
                      <h3 className="boss-font-medium boss-mb-2">{__('Formulaire de contact', 'boss-seo')}</h3>
                      <div className="boss-space-y-2">
                        <div className="boss-h-8 boss-bg-white boss-rounded"></div>
                        <div className="boss-h-8 boss-bg-white boss-rounded"></div>
                        <div className="boss-h-20 boss-bg-white boss-rounded"></div>
                        <div className="boss-w-24 boss-h-8 boss-bg-boss-primary boss-rounded"></div>
                      </div>
                    </div>
                  )}
                  
                  {pageSettings.includeDirections && (
                    <div className="boss-mb-4">
                      <h3 className="boss-font-medium boss-mb-2">{__('Comment nous trouver', 'boss-seo')}</h3>
                      <p className="boss-text-boss-gray">{selectedLocation.address}</p>
                      <div className="boss-mt-2">
                        <div className="boss-w-40 boss-h-8 boss-bg-boss-primary boss-rounded boss-flex boss-items-center boss-justify-center boss-text-white">
                          <Dashicon icon="location-alt" className="boss-mr-1" />
                          {__('Obtenir l\'itinéraire', 'boss-seo')}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {pageSettings.includeReviews && (
                    <div>
                      <h3 className="boss-font-medium boss-mb-2">{__('Avis clients', 'boss-seo')}</h3>
                      <div className="boss-space-y-2">
                        <div className="boss-bg-white boss-p-3 boss-rounded">
                          <div className="boss-flex boss-text-yellow-500 boss-mb-1">
                            <Dashicon icon="star-filled" />
                            <Dashicon icon="star-filled" />
                            <Dashicon icon="star-filled" />
                            <Dashicon icon="star-filled" />
                            <Dashicon icon="star-filled" />
                          </div>
                          <p className="boss-text-sm boss-text-boss-gray">
                            {__('Excellent service, personnel très professionnel.', 'boss-seo')}
                          </p>
                        </div>
                        <div className="boss-bg-white boss-p-3 boss-rounded">
                          <div className="boss-flex boss-text-yellow-500 boss-mb-1">
                            <Dashicon icon="star-filled" />
                            <Dashicon icon="star-filled" />
                            <Dashicon icon="star-filled" />
                            <Dashicon icon="star-filled" />
                            <Dashicon icon="star-half" />
                          </div>
                          <p className="boss-text-sm boss-text-boss-gray">
                            {__('Très satisfait de ma visite, je recommande !', 'boss-seo')}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="boss-flex boss-justify-end">
                  <Button
                    isPrimary
                    onClick={() => setShowPreview(false)}
                  >
                    {__('Fermer la prévisualisation', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </Modal>
          )}
        </div>
      )}
    </div>
  );
};

export default LocalPageGenerator;
