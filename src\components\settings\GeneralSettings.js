import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  TextControl,
  SelectControl,
  ToggleControl,
  RadioControl,
  RangeControl,
  Notice,
  Spinner
} from '@wordpress/components';

// Importer le service des paramètres
import SettingsService from '../../services/SettingsService';

const GeneralSettings = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [settings, setSettings] = useState({
    general: {
      siteTitle: '',
      siteDescription: '',
      separator: '|',
      homePageMetaTitle: '',
      homePageMetaDescription: '',
      noindexArchives: false,
      noindexCategories: false,
      noindexTags: false,
      noindexAuthorPages: true
    },
    content: {
      autoAddAltText: true,
      autoAddTitleAttribute: false,
      addNoopener: true,
      openExternalLinksInNewTab: true,
      maxTitleLength: 60,
      maxDescriptionLength: 160,
      maxKeywords: 5
    },
    advanced: {
      removeShortlinks: true,
      removeRSD: true,
      removeWLWManifest: true,
      disableEmojis: false,
      disableEmbeds: false,
      cleanupHeader: true,
      enableSitemap: true,
      enableRobotsTxt: true
    },
    backup: {
      autoBackup: true,
      backupFrequency: 'weekly',
      maxBackups: 5,
      includeSettings: true,
      includeData: true
    }
  });

  // Charger les données
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true);

        // Charger les paramètres généraux
        const generalSettings = await SettingsService.getGeneralSettings();

        // Charger les paramètres avancés
        const advancedSettings = await SettingsService.getAdvancedSettings();

        // Charger les paramètres de sauvegarde
        const backupSettings = await SettingsService.getBackupSettings();

        // Fusionner les paramètres
        const allSettings = {
          ...generalSettings,
          ...advancedSettings,
          ...backupSettings
        };

        setSettings(allSettings);
      } catch (error) {
        console.error('Erreur lors du chargement des paramètres:', error);
        // En cas d'erreur, utiliser des valeurs par défaut
        const defaultSettings = {
          general: {
            siteTitle: '',
            siteDescription: '',
            separator: '|',
            homePageMetaTitle: '%%site_title%% %%separator%% %%site_description%%',
            homePageMetaDescription: '',
            noindexArchives: false,
            noindexCategories: false,
            noindexTags: false,
            noindexAuthorPages: true
          },
          content: {
            autoAddAltText: true,
            autoAddTitleAttribute: false,
            addNoopener: true,
            openExternalLinksInNewTab: true,
            maxTitleLength: 60,
            maxDescriptionLength: 160,
            maxKeywords: 5
          },
          advanced: {
            removeShortlinks: true,
            removeRSD: true,
            removeWLWManifest: true,
            disableEmojis: false,
            disableEmbeds: false,
            cleanupHeader: true,
            enableSitemap: true,
            enableRobotsTxt: true
          },
          backup: {
            autoBackup: true,
            backupFrequency: 'weekly',
            maxBackups: 5,
            includeSettings: true,
            includeData: true
          }
        };

        setSettings(defaultSettings);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // Fonction pour mettre à jour les paramètres
  const updateSettings = (category, key, value) => {
    setSettings({
      ...settings,
      [category]: {
        ...settings[category],
        [key]: value
      }
    });
  };

  // Fonction pour enregistrer les paramètres
  const handleSaveSettings = async () => {
    setIsSaving(true);

    try {
      // Enregistrer les paramètres généraux
      await SettingsService.saveGeneralSettings({
        general: settings.general,
        content: settings.content
      });

      // Enregistrer les paramètres avancés
      await SettingsService.saveAdvancedSettings({
        advanced: settings.advanced
      });

      // Enregistrer les paramètres de sauvegarde
      await SettingsService.saveBackupSettings({
        backup: settings.backup
      });

      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres:', error);
      // Afficher un message d'erreur
      alert(__('Une erreur est survenue lors de l\'enregistrement des paramètres. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsSaving(false);
    }
  };

  // Fonction pour créer une sauvegarde
  const handleCreateBackup = async () => {
    setIsSaving(true);

    try {
      // Créer une sauvegarde avec un nom basé sur la date actuelle
      const date = new Date();
      const backupName = `backup-${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}-${date.getHours()}-${date.getMinutes()}-${date.getSeconds()}`;

      const response = await SettingsService.createBackup(
        backupName,
        settings.backup.includeSettings,
        settings.backup.includeData
      );

      alert(__('Sauvegarde créée avec succès !', 'boss-seo'));
    } catch (error) {
      console.error('Erreur lors de la création de la sauvegarde:', error);
      alert(__('Une erreur est survenue lors de la création de la sauvegarde. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsSaving(false);
    }
  };

  // Fonction pour restaurer une sauvegarde
  const handleRestoreBackup = async () => {
    try {
      // Récupérer la liste des sauvegardes
      const backups = await SettingsService.getBackups();

      if (backups.length === 0) {
        alert(__('Aucune sauvegarde disponible.', 'boss-seo'));
        return;
      }

      // Trier les sauvegardes par date (la plus récente en premier)
      backups.sort((a, b) => new Date(b.date) - new Date(a.date));

      // Récupérer la sauvegarde la plus récente
      const latestBackup = backups[0];

      if (confirm(__('Êtes-vous sûr de vouloir restaurer la dernière sauvegarde ? Cette action remplacera tous vos paramètres actuels.', 'boss-seo'))) {
        setIsSaving(true);

        await SettingsService.restoreBackup(latestBackup.name);

        alert(__('Sauvegarde restaurée avec succès !', 'boss-seo'));

        // Recharger la page
        window.location.reload();
      }
    } catch (error) {
      console.error('Erreur lors de la restauration de la sauvegarde:', error);
      alert(__('Une erreur est survenue lors de la restauration de la sauvegarde. Veuillez réessayer.', 'boss-seo'));
      setIsSaving(false);
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {__('Paramètres enregistrés avec succès !', 'boss-seo')}
            </Notice>
          )}

          <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
            {/* Panneau de gauche */}
            <div className="lg:boss-col-span-2">
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Paramètres généraux', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <TextControl
                      label={__('Titre du site', 'boss-seo')}
                      help={__('Le titre de votre site utilisé dans les balises méta', 'boss-seo')}
                      value={settings.general.siteTitle}
                      onChange={(value) => updateSettings('general', 'siteTitle', value)}
                    />

                    <TextControl
                      label={__('Description du site', 'boss-seo')}
                      help={__('La description de votre site utilisée dans les balises méta', 'boss-seo')}
                      value={settings.general.siteDescription}
                      onChange={(value) => updateSettings('general', 'siteDescription', value)}
                    />

                    <SelectControl
                      label={__('Séparateur de titre', 'boss-seo')}
                      value={settings.general.separator}
                      options={[
                        { label: '|', value: '|' },
                        { label: '-', value: '-' },
                        { label: '»', value: '»' },
                        { label: '·', value: '·' },
                        { label: ':', value: ':' },
                        { label: '/', value: '/' }
                      ]}
                      onChange={(value) => updateSettings('general', 'separator', value)}
                    />

                    <TextControl
                      label={__('Titre méta de la page d\'accueil', 'boss-seo')}
                      help={__('Utilisez des variables comme %%site_title%%, %%separator%%, %%site_description%%', 'boss-seo')}
                      value={settings.general.homePageMetaTitle}
                      onChange={(value) => updateSettings('general', 'homePageMetaTitle', value)}
                    />

                    <TextControl
                      label={__('Description méta de la page d\'accueil', 'boss-seo')}
                      help={__('Utilisez des variables comme %%site_title%%, %%site_description%%', 'boss-seo')}
                      value={settings.general.homePageMetaDescription}
                      onChange={(value) => updateSettings('general', 'homePageMetaDescription', value)}
                    />

                    <div className="boss-mt-6">
                      <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                        {__('Indexation', 'boss-seo')}
                      </h3>

                      <div className="boss-space-y-3">
                        <ToggleControl
                          label={__('Ne pas indexer les archives', 'boss-seo')}
                          help={__('Ajoute noindex aux pages d\'archives', 'boss-seo')}
                          checked={settings.general.noindexArchives}
                          onChange={(value) => updateSettings('general', 'noindexArchives', value)}
                        />

                        <ToggleControl
                          label={__('Ne pas indexer les catégories', 'boss-seo')}
                          help={__('Ajoute noindex aux pages de catégories', 'boss-seo')}
                          checked={settings.general.noindexCategories}
                          onChange={(value) => updateSettings('general', 'noindexCategories', value)}
                        />

                        <ToggleControl
                          label={__('Ne pas indexer les étiquettes', 'boss-seo')}
                          help={__('Ajoute noindex aux pages d\'étiquettes', 'boss-seo')}
                          checked={settings.general.noindexTags}
                          onChange={(value) => updateSettings('general', 'noindexTags', value)}
                        />

                        <ToggleControl
                          label={__('Ne pas indexer les pages d\'auteur', 'boss-seo')}
                          help={__('Ajoute noindex aux pages d\'auteur', 'boss-seo')}
                          checked={settings.general.noindexAuthorPages}
                          onChange={(value) => updateSettings('general', 'noindexAuthorPages', value)}
                        />
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>

              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Paramètres de contenu', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4">
                      <RangeControl
                        label={__('Longueur maximale du titre', 'boss-seo')}
                        value={settings.content.maxTitleLength}
                        onChange={(value) => updateSettings('content', 'maxTitleLength', value)}
                        min={10}
                        max={100}
                        step={1}
                      />

                      <RangeControl
                        label={__('Longueur maximale de la description', 'boss-seo')}
                        value={settings.content.maxDescriptionLength}
                        onChange={(value) => updateSettings('content', 'maxDescriptionLength', value)}
                        min={50}
                        max={320}
                        step={10}
                      />

                      <RangeControl
                        label={__('Nombre maximal de mots-clés', 'boss-seo')}
                        value={settings.content.maxKeywords}
                        onChange={(value) => updateSettings('content', 'maxKeywords', value)}
                        min={1}
                        max={10}
                        step={1}
                      />
                    </div>

                    <div className="boss-mt-4">
                      <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                        {__('Options d\'image et de lien', 'boss-seo')}
                      </h3>

                      <div className="boss-space-y-3">
                        <ToggleControl
                          label={__('Ajouter automatiquement le texte alternatif aux images', 'boss-seo')}
                          help={__('Génère automatiquement un texte alternatif pour les images sans attribut alt', 'boss-seo')}
                          checked={settings.content.autoAddAltText}
                          onChange={(value) => updateSettings('content', 'autoAddAltText', value)}
                        />

                        <ToggleControl
                          label={__('Ajouter automatiquement l\'attribut title aux images', 'boss-seo')}
                          help={__('Ajoute l\'attribut title aux images basé sur le nom du fichier ou l\'attribut alt', 'boss-seo')}
                          checked={settings.content.autoAddTitleAttribute}
                          onChange={(value) => updateSettings('content', 'autoAddTitleAttribute', value)}
                        />

                        <ToggleControl
                          label={__('Ajouter rel="noopener" aux liens externes', 'boss-seo')}
                          help={__('Améliore la sécurité en ajoutant rel="noopener" aux liens externes', 'boss-seo')}
                          checked={settings.content.addNoopener}
                          onChange={(value) => updateSettings('content', 'addNoopener', value)}
                        />

                        <ToggleControl
                          label={__('Ouvrir les liens externes dans un nouvel onglet', 'boss-seo')}
                          help={__('Ajoute automatiquement target="_blank" aux liens externes', 'boss-seo')}
                          checked={settings.content.openExternalLinksInNewTab}
                          onChange={(value) => updateSettings('content', 'openExternalLinksInNewTab', value)}
                        />
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>

              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Paramètres avancés', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                      <div>
                        <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                          {__('Nettoyage d\'en-tête', 'boss-seo')}
                        </h3>

                        <div className="boss-space-y-3">
                          <ToggleControl
                            label={__('Supprimer les liens courts', 'boss-seo')}
                            help={__('Supprime les liens courts de l\'en-tête WordPress', 'boss-seo')}
                            checked={settings.advanced.removeShortlinks}
                            onChange={(value) => updateSettings('advanced', 'removeShortlinks', value)}
                          />

                          <ToggleControl
                            label={__('Supprimer RSD Link', 'boss-seo')}
                            help={__('Supprime le lien RSD (Really Simple Discovery) de l\'en-tête', 'boss-seo')}
                            checked={settings.advanced.removeRSD}
                            onChange={(value) => updateSettings('advanced', 'removeRSD', value)}
                          />

                          <ToggleControl
                            label={__('Supprimer WLW Manifest', 'boss-seo')}
                            help={__('Supprime le manifeste Windows Live Writer de l\'en-tête', 'boss-seo')}
                            checked={settings.advanced.removeWLWManifest}
                            onChange={(value) => updateSettings('advanced', 'removeWLWManifest', value)}
                          />

                          <ToggleControl
                            label={__('Nettoyer l\'en-tête', 'boss-seo')}
                            help={__('Supprime les métadonnées inutiles de l\'en-tête WordPress', 'boss-seo')}
                            checked={settings.advanced.cleanupHeader}
                            onChange={(value) => updateSettings('advanced', 'cleanupHeader', value)}
                          />
                        </div>
                      </div>

                      <div>
                        <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                          {__('Optimisation WordPress', 'boss-seo')}
                        </h3>

                        <div className="boss-space-y-3">
                          <ToggleControl
                            label={__('Désactiver les emojis', 'boss-seo')}
                            help={__('Supprime le script emoji de WordPress pour améliorer les performances', 'boss-seo')}
                            checked={settings.advanced.disableEmojis}
                            onChange={(value) => updateSettings('advanced', 'disableEmojis', value)}
                          />

                          <ToggleControl
                            label={__('Désactiver les embeds', 'boss-seo')}
                            help={__('Désactive la fonctionnalité d\'intégration de WordPress pour améliorer les performances', 'boss-seo')}
                            checked={settings.advanced.disableEmbeds}
                            onChange={(value) => updateSettings('advanced', 'disableEmbeds', value)}
                          />

                          <ToggleControl
                            label={__('Activer le sitemap XML', 'boss-seo')}
                            help={__('Génère automatiquement un sitemap XML pour votre site', 'boss-seo')}
                            checked={settings.advanced.enableSitemap}
                            onChange={(value) => updateSettings('advanced', 'enableSitemap', value)}
                          />

                          <ToggleControl
                            label={__('Activer le fichier robots.txt', 'boss-seo')}
                            help={__('Génère un fichier robots.txt optimisé pour votre site', 'boss-seo')}
                            checked={settings.advanced.enableRobotsTxt}
                            onChange={(value) => updateSettings('advanced', 'enableRobotsTxt', value)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Panneau de droite */}
            <div>
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Sauvegarde et restauration', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <ToggleControl
                      label={__('Sauvegarde automatique', 'boss-seo')}
                      help={__('Crée automatiquement des sauvegardes de vos paramètres', 'boss-seo')}
                      checked={settings.backup.autoBackup}
                      onChange={(value) => updateSettings('backup', 'autoBackup', value)}
                    />

                    <SelectControl
                      label={__('Fréquence de sauvegarde', 'boss-seo')}
                      value={settings.backup.backupFrequency}
                      options={[
                        { label: __('Quotidienne', 'boss-seo'), value: 'daily' },
                        { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                        { label: __('Mensuelle', 'boss-seo'), value: 'monthly' }
                      ]}
                      onChange={(value) => updateSettings('backup', 'backupFrequency', value)}
                      disabled={!settings.backup.autoBackup}
                    />

                    <RangeControl
                      label={__('Nombre maximal de sauvegardes', 'boss-seo')}
                      value={settings.backup.maxBackups}
                      onChange={(value) => updateSettings('backup', 'maxBackups', value)}
                      min={1}
                      max={10}
                      step={1}
                      disabled={!settings.backup.autoBackup}
                    />

                    <div className="boss-mt-2">
                      <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                        {__('Contenu de la sauvegarde', 'boss-seo')}
                      </h3>

                      <div className="boss-space-y-3">
                        <ToggleControl
                          label={__('Inclure les paramètres', 'boss-seo')}
                          checked={settings.backup.includeSettings}
                          onChange={(value) => updateSettings('backup', 'includeSettings', value)}
                        />

                        <ToggleControl
                          label={__('Inclure les données', 'boss-seo')}
                          help={__('Inclut les données SEO, les redirections, etc.', 'boss-seo')}
                          checked={settings.backup.includeData}
                          onChange={(value) => updateSettings('backup', 'includeData', value)}
                        />
                      </div>
                    </div>

                    <div className="boss-mt-4 boss-grid boss-grid-cols-2 boss-gap-4">
                      <Button
                        isPrimary
                        onClick={handleCreateBackup}
                        isBusy={isSaving}
                        disabled={isSaving}
                      >
                        {__('Créer une sauvegarde', 'boss-seo')}
                      </Button>

                      <Button
                        isSecondary
                        onClick={handleRestoreBackup}
                        isBusy={isSaving}
                        disabled={isSaving}
                      >
                        {__('Restaurer', 'boss-seo')}
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Réinitialisation', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <p className="boss-text-boss-gray">
                      {__('La réinitialisation supprimera tous vos paramètres et données Boss SEO. Cette action est irréversible.', 'boss-seo')}
                    </p>

                    <div className="boss-mt-4">
                      <Button
                        isDestructive
                        onClick={() => {
                          if (confirm(__('Êtes-vous absolument sûr de vouloir réinitialiser tous les paramètres ? Cette action est irréversible.', 'boss-seo'))) {
                            alert(__('Paramètres réinitialisés avec succès !', 'boss-seo'));
                            window.location.reload();
                          }
                        }}
                      >
                        {__('Réinitialiser tous les paramètres', 'boss-seo')}
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>

          <div className="boss-mt-6 boss-flex boss-justify-end">
            <Button
              isPrimary
              onClick={handleSaveSettings}
              isBusy={isSaving}
              disabled={isSaving}
              className="boss-px-6"
            >
              {isSaving ? __('Enregistrement...', 'boss-seo') : __('Enregistrer les paramètres', 'boss-seo')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GeneralSettings;
