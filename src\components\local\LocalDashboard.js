import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Dashicon,
  Spinner,
  Notice
} from '@wordpress/components';

// Importer le service
import LocalSeoService from '../../services/LocalSeoService';

const LocalDashboard = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [locations, setLocations] = useState([]);
  const [stats, setStats] = useState({
    totalLocations: 0,
    completedProfiles: 0,
    averageScore: 0,
    topKeywords: [],
    recentReviews: []
  });

  // Créer une instance du service
  const localSeoService = new LocalSeoService();

  // Charger les données
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Récupérer les données du tableau de bord
        const dashboardData = await localSeoService.getDashboardData();

        // Récupérer les emplacements récents
        const locationsData = await localSeoService.getDashboardLocations();

        // Mettre à jour les états
        setLocations(locationsData.locations || []);
        setStats({
          totalLocations: dashboardData.stats.totalLocations || 0,
          completedProfiles: dashboardData.stats.completedProfiles || 0,
          averageScore: dashboardData.stats.averageScore || 0,
          topKeywords: dashboardData.rankings || [],
          recentReviews: dashboardData.reviews || []
        });
      } catch (err) {
        console.error('Erreur lors du chargement des données du tableau de bord:', err);
        setError(__('Erreur lors du chargement des données. Veuillez réessayer.', 'boss-seo'));

        // Utiliser des données fictives en cas d'erreur pour le développement
        // À supprimer en production
        const mockLocations = [
          {
            id: 1,
            name: 'Paris - Siège social',
            address: '123 Avenue des Champs-Élysées, 75008 Paris',
            score: 87,
            status: 'verified',
            lastUpdated: '2023-06-15'
          },
          {
            id: 2,
            name: 'Lyon - Succursale',
            address: '45 Rue de la République, 69002 Lyon',
            score: 72,
            status: 'verified',
            lastUpdated: '2023-06-10'
          },
          {
            id: 3,
            name: 'Marseille - Boutique',
            address: '78 La Canebière, 13001 Marseille',
            score: 65,
            status: 'pending',
            lastUpdated: '2023-06-05'
          }
        ];

        const mockStats = {
          totalLocations: mockLocations.length,
          completedProfiles: mockLocations.filter(loc => loc.status === 'verified').length,
          averageScore: Math.round(mockLocations.reduce((sum, loc) => sum + loc.score, 0) / mockLocations.length),
          topKeywords: [
            { keyword: 'restaurant paris', position: 3, change: -1 },
            { keyword: 'boutique lyon centre', position: 5, change: 2 },
            { keyword: 'magasin marseille', position: 8, change: 0 }
          ],
          recentReviews: [
            {
              location: 'Paris - Siège social',
              rating: 4,
              text: 'Excellent service, personnel très professionnel.',
              author: 'Jean D.',
              date: '2023-06-12'
            },
            {
              location: 'Lyon - Succursale',
              rating: 5,
              text: 'Très satisfait de ma visite, je recommande !',
              author: 'Marie L.',
              date: '2023-06-08'
            }
          ]
        };

        setLocations(mockLocations);
        setStats(mockStats);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fonction pour obtenir la classe de couleur en fonction du score
  const getScoreColorClass = (score) => {
    if (score >= 80) return 'boss-text-green-600';
    if (score >= 60) return 'boss-text-yellow-600';
    return 'boss-text-red-600';
  };

  // Fonction pour obtenir l'icône de changement de position
  const getPositionChangeIcon = (change) => {
    if (change < 0) return 'arrow-up-alt';
    if (change > 0) return 'arrow-down-alt';
    return 'minus';
  };

  // Fonction pour obtenir la classe de couleur en fonction du changement de position
  const getPositionChangeColorClass = (change) => {
    if (change < 0) return 'boss-text-green-600';
    if (change > 0) return 'boss-text-red-600';
    return 'boss-text-gray-500';
  };

  // Fonction pour rendre les étoiles d'une note
  const renderStars = (rating) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Dashicon
          key={i}
          icon="star-filled"
          className={i <= rating ? 'boss-text-yellow-500' : 'boss-text-gray-300'}
        />
      );
    }
    return stars;
  };

  return (
    <div>
      {error && (
        <Notice status="error" isDismissible={false} className="boss-mb-4">
          {error}
        </Notice>
      )}

      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {/* Statistiques principales */}
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-6 boss-mb-6">
            <Card>
              <CardBody>
                <div className="boss-flex boss-justify-between boss-items-start">
                  <div>
                    <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                      {__('Emplacements', 'boss-seo')}
                    </h3>
                    <div className="boss-flex boss-items-baseline">
                      <span className="boss-text-2xl boss-font-bold boss-mr-2">
                        {stats.totalLocations}
                      </span>
                      <span className="boss-text-sm boss-text-boss-gray">
                        {stats.completedProfiles}/{stats.totalLocations} {__('vérifiés', 'boss-seo')}
                      </span>
                    </div>
                  </div>
                  <div className="boss-bg-blue-100 boss-p-2 boss-rounded-lg">
                    <Dashicon icon="location" className="boss-text-blue-600 boss-text-xl" />
                  </div>
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <div className="boss-flex boss-justify-between boss-items-start">
                  <div>
                    <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                      {__('Score SEO local moyen', 'boss-seo')}
                    </h3>
                    <div className="boss-flex boss-items-baseline">
                      <span className={`boss-text-2xl boss-font-bold boss-mr-2 ${getScoreColorClass(stats.averageScore)}`}>
                        {stats.averageScore}/100
                      </span>
                    </div>
                  </div>
                  <div className="boss-bg-green-100 boss-p-2 boss-rounded-lg">
                    <Dashicon icon="chart-line" className="boss-text-green-600 boss-text-xl" />
                  </div>
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <div className="boss-flex boss-justify-between boss-items-start">
                  <div>
                    <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                      {__('Actions recommandées', 'boss-seo')}
                    </h3>
                    <div className="boss-flex boss-items-baseline">
                      <span className="boss-text-2xl boss-font-bold boss-mr-2">
                        {locations.filter(loc => loc.score < 70).length}
                      </span>
                      <span className="boss-text-sm boss-text-boss-gray">
                        {__('emplacements à optimiser', 'boss-seo')}
                      </span>
                    </div>
                  </div>
                  <div className="boss-bg-yellow-100 boss-p-2 boss-rounded-lg">
                    <Dashicon icon="warning" className="boss-text-yellow-600 boss-text-xl" />
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Emplacements récents et mots-clés */}
          <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-2 boss-gap-6 boss-mb-6">
            {/* Emplacements récents */}
            <Card>
              <CardHeader className="boss-border-b boss-border-gray-200">
                <div className="boss-flex boss-justify-between boss-items-center">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Emplacements récents', 'boss-seo')}
                  </h2>
                  <Button
                    isSecondary
                    isSmall
                    href="#locations"
                  >
                    {__('Voir tous', 'boss-seo')}
                  </Button>
                </div>
              </CardHeader>
              <CardBody className="boss-p-0">
                <div className="boss-divide-y boss-divide-gray-200">
                  {locations.map(location => (
                    <div key={location.id} className="boss-p-4 boss-hover:boss-bg-gray-50">
                      <div className="boss-flex boss-justify-between boss-items-start">
                        <div>
                          <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                            {location.name}
                          </h3>
                          <p className="boss-text-sm boss-text-boss-gray boss-mb-2">
                            {location.address}
                          </p>
                          <div className="boss-flex boss-items-center boss-text-xs">
                            <span className={`boss-px-2 boss-py-1 boss-rounded-full ${
                              location.status === 'verified'
                                ? 'boss-bg-green-100 boss-text-green-800'
                                : 'boss-bg-yellow-100 boss-text-yellow-800'
                            }`}>
                              {location.status === 'verified'
                                ? __('Vérifié', 'boss-seo')
                                : __('En attente', 'boss-seo')}
                            </span>
                            <span className="boss-text-boss-gray boss-ml-2">
                              {__('Mis à jour le', 'boss-seo')} {location.lastUpdated}
                            </span>
                          </div>
                        </div>
                        <div className="boss-flex boss-flex-col boss-items-end">
                          <span className={`boss-text-lg boss-font-bold ${getScoreColorClass(location.score)}`}>
                            {location.score}
                          </span>
                          <Button
                            isSmall
                            isSecondary
                            className="boss-mt-2"
                          >
                            {__('Éditer', 'boss-seo')}
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Mots-clés locaux */}
            <Card>
              <CardHeader className="boss-border-b boss-border-gray-200">
                <div className="boss-flex boss-justify-between boss-items-center">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Mots-clés locaux', 'boss-seo')}
                  </h2>
                  <Button
                    isSecondary
                    isSmall
                    href="#rankings"
                  >
                    {__('Voir tous', 'boss-seo')}
                  </Button>
                </div>
              </CardHeader>
              <CardBody className="boss-p-0">
                <div className="boss-divide-y boss-divide-gray-200">
                  {stats.topKeywords.map((keyword, index) => (
                    <div key={index} className="boss-p-4 boss-hover:boss-bg-gray-50">
                      <div className="boss-flex boss-justify-between boss-items-center">
                        <div>
                          <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                            {keyword.keyword}
                          </h3>
                        </div>
                        <div className="boss-flex boss-items-center">
                          <span className="boss-font-bold boss-mr-2">
                            {keyword.position}
                          </span>
                          <Dashicon
                            icon={getPositionChangeIcon(keyword.change)}
                            className={getPositionChangeColorClass(keyword.change)}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Avis récents */}
          <Card>
            <CardHeader className="boss-border-b boss-border-gray-200">
              <div className="boss-flex boss-justify-between boss-items-center">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Avis récents', 'boss-seo')}
                </h2>
                <Button
                  isSecondary
                  isSmall
                >
                  {__('Gérer les avis', 'boss-seo')}
                </Button>
              </div>
            </CardHeader>
            <CardBody className="boss-p-0">
              <div className="boss-divide-y boss-divide-gray-200">
                {stats.recentReviews.map((review, index) => (
                  <div key={index} className="boss-p-4 boss-hover:boss-bg-gray-50">
                    <div className="boss-flex boss-justify-between boss-items-start">
                      <div>
                        <div className="boss-flex boss-items-center boss-mb-1">
                          <div className="boss-flex boss-mr-2">
                            {renderStars(review.rating)}
                          </div>
                          <span className="boss-text-sm boss-text-boss-gray">
                            {review.location}
                          </span>
                        </div>
                        <p className="boss-text-boss-dark boss-mb-2">
                          "{review.text}"
                        </p>
                        <div className="boss-flex boss-items-center boss-text-xs boss-text-boss-gray">
                          <span>{review.author}</span>
                          <span className="boss-mx-1">•</span>
                          <span>{review.date}</span>
                        </div>
                      </div>
                      <Button
                        isSmall
                        isSecondary
                      >
                        {__('Répondre', 'boss-seo')}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </div>
      )}
    </div>
  );
};

export default LocalDashboard;
