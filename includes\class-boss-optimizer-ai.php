<?php
/**
 * La classe d'intégration avec les services d'IA du module Boss Optimizer.
 *
 * Cette classe gère l'intégration avec les services d'IA comme OpenAI et Anthropic.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * La classe d'intégration avec les services d'IA du module Boss Optimizer.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_AI {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string                   $plugin_name    Le nom du plugin.
     * @param    Boss_Optimizer_Settings  $settings       Instance de la classe de paramètres.
     */
    public function __construct( $plugin_name, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->settings = $settings;
    }

    /**
     * Vérifie si l'IA est configurée et disponible.
     *
     * @since    1.1.0
     * @return   boolean    True si l'IA est configurée, false sinon.
     */
    public function is_available() {
        return $this->settings->is_ai_configured();
    }

    /**
     * Génère du contenu à l'aide de l'IA.
     *
     * @since    1.1.0
     * @param    string    $prompt     Le prompt à envoyer à l'IA.
     * @param    array     $options    Options supplémentaires pour la génération.
     * @return   array                 Le résultat de la génération.
     */
    public function generate_content( $prompt, $options = array() ) {
        if ( ! $this->is_available() ) {
            return array(
                'success' => false,
                'message' => __( 'Les services d\'IA ne sont pas configurés.', 'boss-seo' )
            );
        }

        $provider = $this->settings->get_ai_provider();

        if ( $provider === 'openai' ) {
            return $this->generate_with_openai( $prompt, $options );
        } elseif ( $provider === 'anthropic' ) {
            return $this->generate_with_anthropic( $prompt, $options );
        } elseif ( $provider === 'gemini' ) {
            return $this->generate_with_gemini( $prompt, $options );
        }

        return array(
            'success' => false,
            'message' => __( 'Fournisseur d\'IA non pris en charge.', 'boss-seo' )
        );
    }

    /**
     * Génère du contenu à l'aide de l'API OpenAI.
     *
     * @since    1.1.0
     * @param    string    $prompt     Le prompt à envoyer à l'API.
     * @param    array     $options    Options supplémentaires pour la génération.
     * @return   array                 Le résultat de la génération.
     */
    protected function generate_with_openai( $prompt, $options = array() ) {
        $api_key = $this->settings->get( 'ai', 'openai_api_key', '' );
        $model = $this->settings->get( 'ai', 'openai_model', 'gpt-4' );
        $temperature = $this->settings->get( 'ai', 'openai_temperature', 0.7 );

        if ( empty( $api_key ) ) {
            return array(
                'success' => false,
                'message' => __( 'Clé API OpenAI non configurée.', 'boss-seo' )
            );
        }

        // Préparer les données pour l'API
        $data = array(
            'model' => $model,
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => __( 'Tu es un expert en SEO et en rédaction web. Tu aides à optimiser le contenu pour les moteurs de recherche tout en maintenant une qualité rédactionnelle élevée.', 'boss-seo' )
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'temperature' => floatval( $temperature ),
            'max_tokens' => isset( $options['max_tokens'] ) ? intval( $options['max_tokens'] ) : 1000,
            'top_p' => 1,
            'frequency_penalty' => 0,
            'presence_penalty' => 0
        );

        // Appeler l'API OpenAI
        $response = wp_remote_post(
            'https://api.openai.com/v1/chat/completions',
            array(
                'headers' => array(
                    'Authorization' => 'Bearer ' . $api_key,
                    'Content-Type' => 'application/json'
                ),
                'body' => json_encode( $data ),
                'timeout' => 60
            )
        );

        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $body = json_decode( wp_remote_retrieve_body( $response ), true );

        if ( isset( $body['error'] ) ) {
            return array(
                'success' => false,
                'message' => $body['error']['message']
            );
        }

        if ( ! isset( $body['choices'][0]['message']['content'] ) ) {
            return array(
                'success' => false,
                'message' => __( 'Réponse inattendue de l\'API OpenAI.', 'boss-seo' )
            );
        }

        return array(
            'success' => true,
            'content' => $body['choices'][0]['message']['content'],
            'model' => $model,
            'provider' => 'openai'
        );
    }

    /**
     * Génère du contenu à l'aide de l'API Anthropic.
     *
     * @since    1.1.0
     * @param    string    $prompt     Le prompt à envoyer à l'API.
     * @param    array     $options    Options supplémentaires pour la génération.
     * @return   array                 Le résultat de la génération.
     */
    protected function generate_with_anthropic( $prompt, $options = array() ) {
        $api_key = $this->settings->get( 'ai', 'anthropic_api_key', '' );
        $model = $this->settings->get( 'ai', 'anthropic_model', 'claude-3-opus' );
        $temperature = $this->settings->get( 'ai', 'anthropic_temperature', 0.7 );

        if ( empty( $api_key ) ) {
            return array(
                'success' => false,
                'message' => __( 'Clé API Anthropic non configurée.', 'boss-seo' )
            );
        }

        // Préparer les données pour l'API
        $data = array(
            'model' => $model,
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => __( 'Tu es un expert en SEO et en rédaction web. Tu aides à optimiser le contenu pour les moteurs de recherche tout en maintenant une qualité rédactionnelle élevée.', 'boss-seo' )
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'temperature' => floatval( $temperature ),
            'max_tokens' => isset( $options['max_tokens'] ) ? intval( $options['max_tokens'] ) : 1000
        );

        // Appeler l'API Anthropic
        $response = wp_remote_post(
            'https://api.anthropic.com/v1/messages',
            array(
                'headers' => array(
                    'x-api-key' => $api_key,
                    'anthropic-version' => '2023-06-01',
                    'Content-Type' => 'application/json'
                ),
                'body' => json_encode( $data ),
                'timeout' => 60
            )
        );

        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $body = json_decode( wp_remote_retrieve_body( $response ), true );

        if ( isset( $body['error'] ) ) {
            return array(
                'success' => false,
                'message' => $body['error']['message']
            );
        }

        if ( ! isset( $body['content'][0]['text'] ) ) {
            return array(
                'success' => false,
                'message' => __( 'Réponse inattendue de l\'API Anthropic.', 'boss-seo' )
            );
        }

        return array(
            'success' => true,
            'content' => $body['content'][0]['text'],
            'model' => $model,
            'provider' => 'anthropic'
        );
    }

    /**
     * Génère du contenu à l'aide de l'API Google Gemini.
     *
     * @since    1.1.0
     * @param    string    $prompt     Le prompt à envoyer à l'API.
     * @param    array     $options    Options supplémentaires pour la génération.
     * @return   array                 Le résultat de la génération.
     */
    /**
     * Vérifie si un modèle Gemini est disponible pour l'utilisateur
     *
     * @since    1.1.0
     * @param    string    $model     Le modèle à vérifier
     * @param    string    $api_key   La clé API Gemini
     * @return   boolean              True si le modèle est disponible, false sinon
     */
    protected function is_gemini_model_available( $model, $api_key ) {
        // Construire l'URL pour récupérer les modèles disponibles
        $url = 'https://generativelanguage.googleapis.com/v1/models?key=' . $api_key;

        // Appeler l'API Gemini pour récupérer les modèles disponibles
        $response = wp_remote_get( $url );

        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return false;
        }

        $body = json_decode( wp_remote_retrieve_body( $response ), true );

        // Vérifier si la réponse contient des modèles
        if ( ! isset( $body['models'] ) || ! is_array( $body['models'] ) ) {
            return false;
        }

        // Vérifier si le modèle demandé est disponible
        foreach ( $body['models'] as $available_model ) {
            if ( isset( $available_model['name'] ) && strpos( $available_model['name'], $model ) !== false ) {
                return true;
            }
        }

        return false;
    }

    protected function generate_with_gemini( $prompt, $options = array() ) {
        $api_key = $this->settings->get( 'ai', 'gemini_api_key', '' );
        $model = $this->settings->get( 'ai', 'gemini_model', 'gemini-1.5-pro' );
        $temperature = $this->settings->get( 'ai', 'gemini_temperature', 0.7 );

        if ( empty( $api_key ) ) {
            return array(
                'success' => false,
                'message' => __( 'Clé API Google Gemini non configurée.', 'boss-seo' )
            );
        }

        // Vérifier si le modèle est disponible pour l'utilisateur
        // Si le modèle n'est pas disponible, utiliser un modèle de repli
        if ( ! $this->is_gemini_model_available( $model, $api_key ) ) {
            // Essayer de trouver un modèle de repli approprié
            if ( strpos( $model, 'gemini-2.5' ) === 0 ) {
                $model = 'gemini-1.5-pro'; // Repli pour Gemini 2.5
            } elseif ( strpos( $model, 'gemini-2.0' ) === 0 ) {
                $model = 'gemini-1.5-pro'; // Repli pour Gemini 2.0
            } elseif ( strpos( $model, 'gemini-1.5-flash-8b' ) === 0 ) {
                $model = 'gemini-1.5-flash'; // Repli pour Gemini 1.5 Flash-8B
            } elseif ( strpos( $model, 'gemini-1.5-pro-vision' ) === 0 || strpos( $model, 'gemini-1.5-flash-vision' ) === 0 ) {
                $model = 'gemini-1.5-pro'; // Repli pour les modèles Vision
            }
        }

        // Déterminer la version de l'API en fonction du modèle
        $api_version = 'v1beta';

        // Pour les modèles Gemini 2.0 et 2.5, utiliser la nouvelle version de l'API
        if (strpos($model, 'gemini-2') === 0) {
            $api_version = 'v1';
        }

        // Préparer les données pour l'API en fonction de la version
        if ($api_version === 'v1') {
            // Format pour l'API v1 (Gemini 2.x)
            $system_instruction = __( 'Tu es un expert en SEO et en rédaction web. Tu aides à optimiser le contenu pour les moteurs de recherche tout en maintenant une qualité rédactionnelle élevée. Réponds en français.', 'boss-seo' );

            // Ajouter l'instruction système au début du prompt
            $full_prompt = $system_instruction . "\n\n" . $prompt;

            $data = array(
                'contents' => array(
                    array(
                        'role' => 'user',
                        'parts' => array(
                            array(
                                'text' => $full_prompt
                            )
                        )
                    )
                ),
                'generationConfig' => array(
                    'temperature' => floatval( $temperature ),
                    'maxOutputTokens' => isset( $options['max_tokens'] ) ? intval( $options['max_tokens'] ) : 2048,
                    'topP' => 0.95,
                    'topK' => 40
                ),
                'safetySettings' => array(
                    array(
                        'category' => 'HARM_CATEGORY_HARASSMENT',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ),
                    array(
                        'category' => 'HARM_CATEGORY_HATE_SPEECH',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ),
                    array(
                        'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ),
                    array(
                        'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    )
                )
            );
        } else {
            // Format pour l'API v1beta (Gemini 1.x)
            // Pour les modèles plus anciens, utiliser un format différent
            if (strpos($model, 'gemini-1.0') === 0) {
                // Format pour Gemini 1.0
                $data = array(
                    'contents' => array(
                        array(
                            'parts' => array(
                                array(
                                    'text' => __( 'Tu es un expert en SEO et en rédaction web. Tu aides à optimiser le contenu pour les moteurs de recherche tout en maintenant une qualité rédactionnelle élevée. Réponds en français.', 'boss-seo' ) . "\n\n" . $prompt
                                )
                            )
                        )
                    ),
                    'generationConfig' => array(
                        'temperature' => floatval( $temperature ),
                        'maxOutputTokens' => isset( $options['max_tokens'] ) ? intval( $options['max_tokens'] ) : 2048,
                        'topP' => 0.95,
                        'topK' => 40
                    )
                );
            } else {
                // Format pour Gemini 1.5
                $data = array(
                    'contents' => array(
                        array(
                            'role' => 'user',
                            'parts' => array(
                                array(
                                    'text' => $prompt
                                )
                            )
                        )
                    ),
                    'generationConfig' => array(
                        'temperature' => floatval( $temperature ),
                        'maxOutputTokens' => isset( $options['max_tokens'] ) ? intval( $options['max_tokens'] ) : 2048,
                        'topP' => 0.95,
                        'topK' => 40
                    ),
                    'safetySettings' => array(
                        array(
                            'category' => 'HARM_CATEGORY_HARASSMENT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ),
                        array(
                            'category' => 'HARM_CATEGORY_HATE_SPEECH',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ),
                        array(
                            'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ),
                        array(
                            'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        )
                    )
                );

                // Ajouter l'instruction système au début du prompt pour Gemini 1.5
                $data['contents'][0]['parts'][0]['text'] = __( 'Tu es un expert en SEO et en rédaction web. Tu aides à optimiser le contenu pour les moteurs de recherche tout en maintenant une qualité rédactionnelle élevée. Réponds en français.', 'boss-seo' ) . "\n\n" . $prompt;
            }
        }

        // Construire l'URL avec la clé API
        $url = 'https://generativelanguage.googleapis.com/' . $api_version . '/models/' . $model . ':generateContent?key=' . $api_key;

        // Appeler l'API Gemini
        $response = wp_remote_post(
            $url,
            array(
                'headers' => array(
                    'Content-Type' => 'application/json'
                ),
                'body' => json_encode( $data ),
                'timeout' => 60
            )
        );

        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $body = json_decode( wp_remote_retrieve_body( $response ), true );

        if ( isset( $body['error'] ) ) {
            return array(
                'success' => false,
                'message' => $body['error']['message']
            );
        }

        // Vérifier si la réponse contient un contenu généré
        if ( ! isset( $body['candidates'] ) || empty( $body['candidates'] ) ) {
            return array(
                'success' => false,
                'message' => __( 'Aucun contenu généré par l\'API Google Gemini.', 'boss-seo' )
            );
        }

        // Vérifier si la réponse contient un blocage de sécurité
        if ( isset( $body['promptFeedback'] ) && isset( $body['promptFeedback']['blockReason'] ) ) {
            return array(
                'success' => false,
                'message' => sprintf( __( 'Contenu bloqué par les filtres de sécurité de Google Gemini. Raison: %s', 'boss-seo' ), $body['promptFeedback']['blockReason'] )
            );
        }

        // Extraire le texte de la réponse
        $content = '';
        if ( isset( $body['candidates'][0]['content']['parts'] ) ) {
            foreach ( $body['candidates'][0]['content']['parts'] as $part ) {
                if ( isset( $part['text'] ) ) {
                    $content .= $part['text'];
                }
            }
        }

        if ( empty( $content ) ) {
            return array(
                'success' => false,
                'message' => __( 'Réponse vide ou inattendue de l\'API Google Gemini.', 'boss-seo' )
            );
        }

        return array(
            'success' => true,
            'content' => $content,
            'model' => $model,
            'provider' => 'gemini'
        );
    }

    /**
     * Génère des textes alternatifs pour les images à l'aide de l'IA.
     *
     * @since    1.1.0
     * @param    int       $attachment_id    L'ID de l'image.
     * @return   array                       Le résultat de la génération.
     */
    public function generate_alt_text( $attachment_id ) {
        if ( ! $this->is_available() || ! $this->settings->get( 'ai', 'use_ai_for_alt_text', true ) ) {
            return array(
                'success' => false,
                'message' => __( 'La génération de textes alternatifs n\'est pas activée.', 'boss-seo' )
            );
        }

        // Récupérer les informations sur l'image
        $attachment = get_post( $attachment_id );

        if ( ! $attachment ) {
            return array(
                'success' => false,
                'message' => __( 'Image non trouvée.', 'boss-seo' )
            );
        }

        $title = $attachment->post_title;
        $caption = $attachment->post_excerpt;
        $description = $attachment->post_content;

        // Construire le prompt
        $prompt = sprintf(
            __( 'Génère un texte alternatif SEO descriptif et concis (maximum 125 caractères) pour une image avec les informations suivantes : Titre: %s. %s %s', 'boss-seo' ),
            $title,
            ! empty( $caption ) ? 'Légende: ' . $caption . '.' : '',
            ! empty( $description ) ? 'Description: ' . $description . '.' : ''
        );

        // Générer le texte alternatif
        $result = $this->generate_content( $prompt, array( 'max_tokens' => 100 ) );

        if ( ! $result['success'] ) {
            return $result;
        }

        // Nettoyer et limiter le texte alternatif
        $alt_text = wp_strip_all_tags( $result['content'] );
        $alt_text = trim( $alt_text, " \t\n\r\0\x0B\"'" );
        $alt_text = mb_substr( $alt_text, 0, 125 );

        // Mettre à jour le texte alternatif
        update_post_meta( $attachment_id, '_wp_attachment_image_alt', $alt_text );

        return array(
            'success' => true,
            'alt_text' => $alt_text,
            'message' => __( 'Texte alternatif généré avec succès.', 'boss-seo' )
        );
    }
}
