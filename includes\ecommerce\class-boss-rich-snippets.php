<?php
/**
 * Classe pour les extraits enrichis.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour les extraits enrichis.
 *
 * Cette classe gère les extraits enrichis pour les produits.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Rich_Snippets {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_rich_snippets_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_generate_rich_snippet', array( $this, 'ajax_generate_rich_snippet' ) );
        add_action( 'wp_ajax_boss_seo_test_rich_snippet', array( $this, 'ajax_test_rich_snippet' ) );

        // Ajouter les extraits enrichis
        add_action( 'woocommerce_after_shop_loop_item', array( $this, 'display_rich_snippets' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/rich-snippets/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_rich_snippet' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'update_rich_snippet' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/rich-snippets/(?P<id>\d+)/test',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'test_rich_snippet' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour générer un extrait enrichi.
     *
     * @since    1.2.0
     */
    public function ajax_generate_rich_snippet() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );

        // Générer l'extrait enrichi
        $snippet = $this->generate_rich_snippet( $product_id );

        if ( is_wp_error( $snippet ) ) {
            wp_send_json_error( array( 'message' => $snippet->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Extrait enrichi généré avec succès.', 'boss-seo' ),
            'snippet' => $snippet,
            'snippet_html' => $this->render_rich_snippet( $snippet ),
        ) );
    }

    /**
     * Gère les requêtes AJAX pour tester un extrait enrichi.
     *
     * @since    1.2.0
     */
    public function ajax_test_rich_snippet() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );

        // Générer l'extrait enrichi
        $snippet = $this->generate_rich_snippet( $product_id );

        if ( is_wp_error( $snippet ) ) {
            wp_send_json_error( array( 'message' => $snippet->get_error_message() ) );
        }

        // Tester l'extrait enrichi
        $result = $this->test_snippet_with_google( $snippet );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Extrait enrichi testé avec succès.', 'boss-seo' ),
            'result'  => $result,
        ) );
    }

    /**
     * Récupère l'extrait enrichi d'un produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_rich_snippet( $request ) {
        $product_id = $request['id'];

        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Récupérer l'extrait enrichi
        $snippet = $this->generate_rich_snippet( $product_id );

        if ( is_wp_error( $snippet ) ) {
            return $snippet;
        }

        return rest_ensure_response( array(
            'snippet'      => $snippet,
            'snippet_html' => $this->render_rich_snippet( $snippet ),
        ) );
    }

    /**
     * Met à jour l'extrait enrichi d'un produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_rich_snippet( $request ) {
        $product_id = $request['id'];
        $params = $request->get_params();

        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Mettre à jour les options de l'extrait enrichi
        $snippet_type = isset( $params['snippet_type'] ) ? sanitize_text_field( $params['snippet_type'] ) : 'product';
        $snippet_options = isset( $params['snippet_options'] ) && is_array( $params['snippet_options'] ) ? $params['snippet_options'] : array();

        update_post_meta( $product_id, '_boss_rich_snippet_type', $snippet_type );
        update_post_meta( $product_id, '_boss_rich_snippet_options', $snippet_options );

        // Générer l'extrait enrichi mis à jour
        $snippet = $this->generate_rich_snippet( $product_id );

        if ( is_wp_error( $snippet ) ) {
            return $snippet;
        }

        return rest_ensure_response( array(
            'message'      => __( 'Extrait enrichi mis à jour avec succès.', 'boss-seo' ),
            'snippet'      => $snippet,
            'snippet_html' => $this->render_rich_snippet( $snippet ),
        ) );
    }

    /**
     * Teste l'extrait enrichi d'un produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function test_rich_snippet( $request ) {
        $product_id = $request['id'];

        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Générer l'extrait enrichi
        $snippet = $this->generate_rich_snippet( $product_id );

        if ( is_wp_error( $snippet ) ) {
            return $snippet;
        }

        // Tester l'extrait enrichi
        $result = $this->test_snippet_with_google( $snippet );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Extrait enrichi testé avec succès.', 'boss-seo' ),
            'result'  => $result,
        ) );
    }

    /**
     * Affiche les extraits enrichis sur la page du produit.
     *
     * @since    1.2.0
     */
    public function display_rich_snippets() {
        if ( ! is_product() ) {
            return;
        }

        $product_id = get_the_ID();
        $snippet = $this->generate_rich_snippet( $product_id );

        if ( is_wp_error( $snippet ) ) {
            return;
        }

        echo $this->render_rich_snippet( $snippet );
    }

    /**
     * Affiche la métabox pour les extraits enrichis.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post.
     */
    public function render_meta_box( $post ) {
        // Récupérer les données du produit
        $product_id = $post->ID;
        $snippet_type = get_post_meta( $product_id, '_boss_rich_snippet_type', true );
        $snippet_options = get_post_meta( $product_id, '_boss_rich_snippet_options', true );

        if ( empty( $snippet_type ) ) {
            $snippet_type = 'product';
        }

        if ( empty( $snippet_options ) || ! is_array( $snippet_options ) ) {
            $snippet_options = array(
                'show_price' => true,
                'show_availability' => true,
                'show_rating' => true,
                'show_reviews' => true,
                'show_brand' => true,
                'show_sku' => true,
                'show_description' => true,
                'show_image' => true,
            );
        }

        // Afficher le formulaire
        wp_nonce_field( 'boss_seo_rich_snippet_meta_box', 'boss_seo_rich_snippet_meta_box_nonce' );
        ?>
        <div class="boss-seo-rich-snippet-meta-box">
            <p>
                <label for="boss_rich_snippet_type"><?php esc_html_e( 'Type d\'extrait enrichi :', 'boss-seo' ); ?></label>
                <select id="boss_rich_snippet_type" name="boss_rich_snippet_type">
                    <option value="product" <?php selected( $snippet_type, 'product' ); ?>><?php esc_html_e( 'Produit', 'boss-seo' ); ?></option>
                    <option value="offer" <?php selected( $snippet_type, 'offer' ); ?>><?php esc_html_e( 'Offre', 'boss-seo' ); ?></option>
                    <option value="review" <?php selected( $snippet_type, 'review' ); ?>><?php esc_html_e( 'Avis', 'boss-seo' ); ?></option>
                    <option value="aggregate_rating" <?php selected( $snippet_type, 'aggregate_rating' ); ?>><?php esc_html_e( 'Note moyenne', 'boss-seo' ); ?></option>
                    <option value="product_with_offer" <?php selected( $snippet_type, 'product_with_offer' ); ?>><?php esc_html_e( 'Produit avec offre', 'boss-seo' ); ?></option>
                    <option value="product_with_review" <?php selected( $snippet_type, 'product_with_review' ); ?>><?php esc_html_e( 'Produit avec avis', 'boss-seo' ); ?></option>
                    <option value="product_with_aggregate_rating" <?php selected( $snippet_type, 'product_with_aggregate_rating' ); ?>><?php esc_html_e( 'Produit avec note moyenne', 'boss-seo' ); ?></option>
                    <option value="product_complete" <?php selected( $snippet_type, 'product_complete' ); ?>><?php esc_html_e( 'Produit complet', 'boss-seo' ); ?></option>
                </select>
            </p>

            <p><?php esc_html_e( 'Options de l\'extrait enrichi :', 'boss-seo' ); ?></p>
            <ul>
                <li>
                    <label>
                        <input type="checkbox" name="boss_rich_snippet_options[show_price]" value="1" <?php checked( isset( $snippet_options['show_price'] ) ? $snippet_options['show_price'] : true ); ?>>
                        <?php esc_html_e( 'Afficher le prix', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_rich_snippet_options[show_availability]" value="1" <?php checked( isset( $snippet_options['show_availability'] ) ? $snippet_options['show_availability'] : true ); ?>>
                        <?php esc_html_e( 'Afficher la disponibilité', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_rich_snippet_options[show_rating]" value="1" <?php checked( isset( $snippet_options['show_rating'] ) ? $snippet_options['show_rating'] : true ); ?>>
                        <?php esc_html_e( 'Afficher la note', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_rich_snippet_options[show_reviews]" value="1" <?php checked( isset( $snippet_options['show_reviews'] ) ? $snippet_options['show_reviews'] : true ); ?>>
                        <?php esc_html_e( 'Afficher les avis', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_rich_snippet_options[show_brand]" value="1" <?php checked( isset( $snippet_options['show_brand'] ) ? $snippet_options['show_brand'] : true ); ?>>
                        <?php esc_html_e( 'Afficher la marque', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_rich_snippet_options[show_sku]" value="1" <?php checked( isset( $snippet_options['show_sku'] ) ? $snippet_options['show_sku'] : true ); ?>>
                        <?php esc_html_e( 'Afficher le SKU', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_rich_snippet_options[show_description]" value="1" <?php checked( isset( $snippet_options['show_description'] ) ? $snippet_options['show_description'] : true ); ?>>
                        <?php esc_html_e( 'Afficher la description', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_rich_snippet_options[show_image]" value="1" <?php checked( isset( $snippet_options['show_image'] ) ? $snippet_options['show_image'] : true ); ?>>
                        <?php esc_html_e( 'Afficher l\'image', 'boss-seo' ); ?>
                    </label>
                </li>
            </ul>

            <div class="boss-seo-rich-snippet-actions">
                <button type="button" class="button button-secondary" id="boss-seo-generate-rich-snippet" data-product-id="<?php echo esc_attr( $product_id ); ?>"><?php esc_html_e( 'Générer l\'extrait enrichi', 'boss-seo' ); ?></button>
                <button type="button" class="button button-secondary" id="boss-seo-test-rich-snippet" data-product-id="<?php echo esc_attr( $product_id ); ?>"><?php esc_html_e( 'Tester l\'extrait enrichi', 'boss-seo' ); ?></button>
            </div>

            <div class="boss-seo-rich-snippet-result" style="display: none;">
                <h4><?php esc_html_e( 'Extrait enrichi généré :', 'boss-seo' ); ?></h4>
                <div id="boss-seo-rich-snippet-preview"></div>
            </div>

            <div class="boss-seo-rich-snippet-test-result" style="display: none;">
                <h4><?php esc_html_e( 'Résultat du test :', 'boss-seo' ); ?></h4>
                <div id="boss-seo-rich-snippet-test-result"></div>
            </div>
        </div>
        <?php
    }

    /**
     * Affiche le panneau de données pour les extraits enrichis.
     *
     * @since    1.2.0
     */
    public function render_product_data_panel() {
        global $post;

        // Récupérer les données du produit
        $product_id = $post->ID;
        $snippet_type = get_post_meta( $product_id, '_boss_rich_snippet_type', true );
        $snippet_options = get_post_meta( $product_id, '_boss_rich_snippet_options', true );

        if ( empty( $snippet_type ) ) {
            $snippet_type = 'product';
        }

        if ( empty( $snippet_options ) || ! is_array( $snippet_options ) ) {
            $snippet_options = array(
                'show_price' => true,
                'show_availability' => true,
                'show_rating' => true,
                'show_reviews' => true,
                'show_brand' => true,
                'show_sku' => true,
                'show_description' => true,
                'show_image' => true,
            );
        }

        // Afficher le formulaire
        woocommerce_wp_select( array(
            'id'          => 'boss_rich_snippet_type',
            'label'       => __( 'Type d\'extrait enrichi', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Sélectionnez le type d\'extrait enrichi pour ce produit.', 'boss-seo' ),
            'options'     => array(
                'product'                     => __( 'Produit', 'boss-seo' ),
                'offer'                       => __( 'Offre', 'boss-seo' ),
                'review'                      => __( 'Avis', 'boss-seo' ),
                'aggregate_rating'            => __( 'Note moyenne', 'boss-seo' ),
                'product_with_offer'          => __( 'Produit avec offre', 'boss-seo' ),
                'product_with_review'         => __( 'Produit avec avis', 'boss-seo' ),
                'product_with_aggregate_rating' => __( 'Produit avec note moyenne', 'boss-seo' ),
                'product_complete'            => __( 'Produit complet', 'boss-seo' ),
            ),
            'value'       => $snippet_type,
        ) );

        echo '<p>' . esc_html__( 'Options de l\'extrait enrichi :', 'boss-seo' ) . '</p>';

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_rich_snippet_options_show_price',
            'label'       => __( 'Afficher le prix', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Afficher le prix dans l\'extrait enrichi.', 'boss-seo' ),
            'value'       => isset( $snippet_options['show_price'] ) ? $snippet_options['show_price'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_rich_snippet_options_show_availability',
            'label'       => __( 'Afficher la disponibilité', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Afficher la disponibilité dans l\'extrait enrichi.', 'boss-seo' ),
            'value'       => isset( $snippet_options['show_availability'] ) ? $snippet_options['show_availability'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_rich_snippet_options_show_rating',
            'label'       => __( 'Afficher la note', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Afficher la note dans l\'extrait enrichi.', 'boss-seo' ),
            'value'       => isset( $snippet_options['show_rating'] ) ? $snippet_options['show_rating'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_rich_snippet_options_show_reviews',
            'label'       => __( 'Afficher les avis', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Afficher les avis dans l\'extrait enrichi.', 'boss-seo' ),
            'value'       => isset( $snippet_options['show_reviews'] ) ? $snippet_options['show_reviews'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_rich_snippet_options_show_brand',
            'label'       => __( 'Afficher la marque', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Afficher la marque dans l\'extrait enrichi.', 'boss-seo' ),
            'value'       => isset( $snippet_options['show_brand'] ) ? $snippet_options['show_brand'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_rich_snippet_options_show_sku',
            'label'       => __( 'Afficher le SKU', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Afficher le SKU dans l\'extrait enrichi.', 'boss-seo' ),
            'value'       => isset( $snippet_options['show_sku'] ) ? $snippet_options['show_sku'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_rich_snippet_options_show_description',
            'label'       => __( 'Afficher la description', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Afficher la description dans l\'extrait enrichi.', 'boss-seo' ),
            'value'       => isset( $snippet_options['show_description'] ) ? $snippet_options['show_description'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_rich_snippet_options_show_image',
            'label'       => __( 'Afficher l\'image', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Afficher l\'image dans l\'extrait enrichi.', 'boss-seo' ),
            'value'       => isset( $snippet_options['show_image'] ) ? $snippet_options['show_image'] : 'yes',
        ) );

        echo '<div class="boss-seo-rich-snippet-actions">';
        echo '<button type="button" class="button button-secondary" id="boss-seo-generate-rich-snippet" data-product-id="' . esc_attr( $product_id ) . '">' . esc_html__( 'Générer l\'extrait enrichi', 'boss-seo' ) . '</button>';
        echo '<button type="button" class="button button-secondary" id="boss-seo-test-rich-snippet" data-product-id="' . esc_attr( $product_id ) . '">' . esc_html__( 'Tester l\'extrait enrichi', 'boss-seo' ) . '</button>';
        echo '</div>';

        echo '<div class="boss-seo-rich-snippet-result" style="display: none;">';
        echo '<h4>' . esc_html__( 'Extrait enrichi généré :', 'boss-seo' ) . '</h4>';
        echo '<div id="boss-seo-rich-snippet-preview"></div>';
        echo '</div>';

        echo '<div class="boss-seo-rich-snippet-test-result" style="display: none;">';
        echo '<h4>' . esc_html__( 'Résultat du test :', 'boss-seo' ) . '</h4>';
        echo '<div id="boss-seo-rich-snippet-test-result"></div>';
        echo '</div>';
    }

    /**
     * Enregistre les métadonnées du produit.
     *
     * @since    1.2.0
     * @param    int    $post_id    L'ID du produit.
     */
    public function save_meta( $post_id ) {
        // Vérifier le nonce
        if ( ! isset( $_POST['boss_seo_rich_snippet_meta_box_nonce'] ) || ! wp_verify_nonce( $_POST['boss_seo_rich_snippet_meta_box_nonce'], 'boss_seo_rich_snippet_meta_box' ) ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Enregistrer le type d'extrait enrichi
        if ( isset( $_POST['boss_rich_snippet_type'] ) ) {
            update_post_meta( $post_id, '_boss_rich_snippet_type', sanitize_text_field( $_POST['boss_rich_snippet_type'] ) );
        }

        // Enregistrer les options de l'extrait enrichi
        $snippet_options = array(
            'show_price'        => isset( $_POST['boss_rich_snippet_options_show_price'] ) && $_POST['boss_rich_snippet_options_show_price'] === 'yes',
            'show_availability' => isset( $_POST['boss_rich_snippet_options_show_availability'] ) && $_POST['boss_rich_snippet_options_show_availability'] === 'yes',
            'show_rating'       => isset( $_POST['boss_rich_snippet_options_show_rating'] ) && $_POST['boss_rich_snippet_options_show_rating'] === 'yes',
            'show_reviews'      => isset( $_POST['boss_rich_snippet_options_show_reviews'] ) && $_POST['boss_rich_snippet_options_show_reviews'] === 'yes',
            'show_brand'        => isset( $_POST['boss_rich_snippet_options_show_brand'] ) && $_POST['boss_rich_snippet_options_show_brand'] === 'yes',
            'show_sku'          => isset( $_POST['boss_rich_snippet_options_show_sku'] ) && $_POST['boss_rich_snippet_options_show_sku'] === 'yes',
            'show_description'  => isset( $_POST['boss_rich_snippet_options_show_description'] ) && $_POST['boss_rich_snippet_options_show_description'] === 'yes',
            'show_image'        => isset( $_POST['boss_rich_snippet_options_show_image'] ) && $_POST['boss_rich_snippet_options_show_image'] === 'yes',
        );

        update_post_meta( $post_id, '_boss_rich_snippet_options', $snippet_options );
    }

    /**
     * Génère le shortcode pour les extraits enrichis.
     *
     * @since    1.2.0
     * @param    array     $atts       Les attributs du shortcode.
     * @param    string    $content    Le contenu du shortcode.
     * @return   string                Le contenu généré.
     */
    public function snippet_shortcode( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'product_id' => 0,
            'type' => '',
        ), $atts, 'boss_rich_snippet' );

        $product_id = absint( $atts['product_id'] );
        $type = sanitize_text_field( $atts['type'] );

        if ( ! $product_id ) {
            return '';
        }

        // Récupérer les options de l'extrait enrichi
        $snippet_type = get_post_meta( $product_id, '_boss_rich_snippet_type', true );

        if ( $type && $type !== $snippet_type ) {
            $snippet_type = $type;
        }

        if ( empty( $snippet_type ) ) {
            $snippet_type = 'product';
        }

        // Générer l'extrait enrichi
        $snippet = $this->generate_rich_snippet( $product_id, $snippet_type );

        if ( is_wp_error( $snippet ) ) {
            return '';
        }

        return $this->render_rich_snippet( $snippet );
    }

    /**
     * Génère l'extrait enrichi pour un produit.
     *
     * @since    1.2.0
     * @param    int       $product_id     L'ID du produit.
     * @param    string    $snippet_type   Le type d'extrait enrichi.
     * @return   array|WP_Error            L'extrait enrichi ou une erreur.
     */
    public function generate_rich_snippet( $product_id, $snippet_type = '' ) {
        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ) );
        }

        // Récupérer les options de l'extrait enrichi
        if ( empty( $snippet_type ) ) {
            $snippet_type = get_post_meta( $product_id, '_boss_rich_snippet_type', true );
        }

        if ( empty( $snippet_type ) ) {
            $snippet_type = 'product';
        }

        $snippet_options = get_post_meta( $product_id, '_boss_rich_snippet_options', true );

        if ( empty( $snippet_options ) || ! is_array( $snippet_options ) ) {
            $snippet_options = array(
                'show_price' => true,
                'show_availability' => true,
                'show_rating' => true,
                'show_reviews' => true,
                'show_brand' => true,
                'show_sku' => true,
                'show_description' => true,
                'show_image' => true,
            );
        }

        // Générer l'extrait enrichi selon le type
        switch ( $snippet_type ) {
            case 'product':
                return $this->generate_product_snippet( $product, $snippet_options );

            case 'offer':
                return $this->generate_offer_snippet( $product, $snippet_options );

            case 'review':
                return $this->generate_review_snippet( $product, $snippet_options );

            case 'aggregate_rating':
                return $this->generate_aggregate_rating_snippet( $product, $snippet_options );

            case 'product_with_offer':
                return $this->generate_product_with_offer_snippet( $product, $snippet_options );

            case 'product_with_review':
                return $this->generate_product_with_review_snippet( $product, $snippet_options );

            case 'product_with_aggregate_rating':
                return $this->generate_product_with_aggregate_rating_snippet( $product, $snippet_options );

            case 'product_complete':
                return $this->generate_product_complete_snippet( $product, $snippet_options );

            default:
                return new WP_Error( 'invalid_snippet_type', __( 'Type d\'extrait enrichi non valide.', 'boss-seo' ) );
        }
    }

    /**
     * Génère l'extrait enrichi pour un produit.
     *
     * @since    1.2.0
     * @param    WC_Product    $product           Le produit.
     * @param    array         $snippet_options   Les options de l'extrait enrichi.
     * @return   array                            L'extrait enrichi.
     */
    private function generate_product_snippet( $product, $snippet_options ) {
        $snippet = array(
            '@context' => 'https://schema.org',
            '@type' => 'Product',
            'name' => $product->get_name(),
            'url' => get_permalink( $product->get_id() ),
        );

        // Ajouter la description
        if ( isset( $snippet_options['show_description'] ) && $snippet_options['show_description'] ) {
            $snippet['description'] = $product->get_description() ? $product->get_description() : $product->get_short_description();
        }

        // Ajouter l'image
        if ( isset( $snippet_options['show_image'] ) && $snippet_options['show_image'] ) {
            $image_id = $product->get_image_id();
            if ( $image_id ) {
                $image_url = wp_get_attachment_url( $image_id );
                if ( $image_url ) {
                    $snippet['image'] = $image_url;
                }
            }
        }

        // Ajouter le SKU
        if ( isset( $snippet_options['show_sku'] ) && $snippet_options['show_sku'] ) {
            $sku = $product->get_sku();
            if ( $sku ) {
                $snippet['sku'] = $sku;
            }
        }

        // Ajouter la marque
        if ( isset( $snippet_options['show_brand'] ) && $snippet_options['show_brand'] ) {
            $brand = get_post_meta( $product->get_id(), '_boss_product_brand', true );
            if ( $brand ) {
                $snippet['brand'] = array(
                    '@type' => 'Brand',
                    'name' => $brand,
                );
            }
        }

        return $snippet;
    }

    /**
     * Génère l'extrait enrichi pour une offre.
     *
     * @since    1.2.0
     * @param    WC_Product    $product           Le produit.
     * @param    array         $snippet_options   Les options de l'extrait enrichi.
     * @return   array                            L'extrait enrichi.
     */
    private function generate_offer_snippet( $product, $snippet_options ) {
        $snippet = array(
            '@context' => 'https://schema.org',
            '@type' => 'Offer',
            'url' => get_permalink( $product->get_id() ),
        );

        // Ajouter le prix
        if ( isset( $snippet_options['show_price'] ) && $snippet_options['show_price'] ) {
            $snippet['price'] = $product->get_price();
            $snippet['priceCurrency'] = get_woocommerce_currency();
        }

        // Ajouter la disponibilité
        if ( isset( $snippet_options['show_availability'] ) && $snippet_options['show_availability'] ) {
            $snippet['availability'] = $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock';
        }

        return $snippet;
    }

    /**
     * Génère l'extrait enrichi pour un avis.
     *
     * @since    1.2.0
     * @param    WC_Product    $product           Le produit.
     * @param    array         $snippet_options   Les options de l'extrait enrichi.
     * @return   array                            L'extrait enrichi.
     */
    private function generate_review_snippet( $product, $snippet_options ) {
        // Récupérer le dernier avis
        $args = array(
            'post_id' => $product->get_id(),
            'status' => 'approve',
            'type' => 'review',
            'number' => 1,
        );

        $reviews = get_comments( $args );

        if ( empty( $reviews ) ) {
            return new WP_Error( 'no_reviews', __( 'Aucun avis trouvé.', 'boss-seo' ) );
        }

        $review = $reviews[0];
        $rating = get_comment_meta( $review->comment_ID, 'rating', true );

        $snippet = array(
            '@context' => 'https://schema.org',
            '@type' => 'Review',
            'reviewBody' => $review->comment_content,
            'datePublished' => $review->comment_date,
            'author' => array(
                '@type' => 'Person',
                'name' => $review->comment_author,
            ),
            'itemReviewed' => array(
                '@type' => 'Product',
                'name' => $product->get_name(),
                'url' => get_permalink( $product->get_id() ),
            ),
        );

        if ( $rating ) {
            $snippet['reviewRating'] = array(
                '@type' => 'Rating',
                'ratingValue' => $rating,
                'bestRating' => '5',
                'worstRating' => '1',
            );
        }

        return $snippet;
    }

    /**
     * Génère l'extrait enrichi pour une note moyenne.
     *
     * @since    1.2.0
     * @param    WC_Product    $product           Le produit.
     * @param    array         $snippet_options   Les options de l'extrait enrichi.
     * @return   array                            L'extrait enrichi.
     */
    private function generate_aggregate_rating_snippet( $product, $snippet_options ) {
        $rating_count = $product->get_rating_count();
        $average_rating = $product->get_average_rating();

        if ( $rating_count === 0 ) {
            return new WP_Error( 'no_ratings', __( 'Aucune note trouvée.', 'boss-seo' ) );
        }

        $snippet = array(
            '@context' => 'https://schema.org',
            '@type' => 'AggregateRating',
            'ratingValue' => $average_rating,
            'reviewCount' => $rating_count,
            'bestRating' => '5',
            'worstRating' => '1',
            'itemReviewed' => array(
                '@type' => 'Product',
                'name' => $product->get_name(),
                'url' => get_permalink( $product->get_id() ),
            ),
        );

        return $snippet;
    }

    /**
     * Génère l'extrait enrichi pour un produit avec offre.
     *
     * @since    1.2.0
     * @param    WC_Product    $product           Le produit.
     * @param    array         $snippet_options   Les options de l'extrait enrichi.
     * @return   array                            L'extrait enrichi.
     */
    private function generate_product_with_offer_snippet( $product, $snippet_options ) {
        $snippet = $this->generate_product_snippet( $product, $snippet_options );

        // Ajouter l'offre
        $offer = array(
            '@type' => 'Offer',
            'url' => get_permalink( $product->get_id() ),
        );

        // Ajouter le prix
        if ( isset( $snippet_options['show_price'] ) && $snippet_options['show_price'] ) {
            $offer['price'] = $product->get_price();
            $offer['priceCurrency'] = get_woocommerce_currency();
        }

        // Ajouter la disponibilité
        if ( isset( $snippet_options['show_availability'] ) && $snippet_options['show_availability'] ) {
            $offer['availability'] = $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock';
        }

        $snippet['offers'] = $offer;

        return $snippet;
    }

    /**
     * Génère l'extrait enrichi pour un produit avec avis.
     *
     * @since    1.2.0
     * @param    WC_Product    $product           Le produit.
     * @param    array         $snippet_options   Les options de l'extrait enrichi.
     * @return   array                            L'extrait enrichi.
     */
    private function generate_product_with_review_snippet( $product, $snippet_options ) {
        $snippet = $this->generate_product_snippet( $product, $snippet_options );

        // Récupérer les avis
        $args = array(
            'post_id' => $product->get_id(),
            'status' => 'approve',
            'type' => 'review',
        );

        $reviews = get_comments( $args );

        if ( empty( $reviews ) ) {
            return $snippet;
        }

        $snippet_reviews = array();

        foreach ( $reviews as $review ) {
            $rating = get_comment_meta( $review->comment_ID, 'rating', true );

            $snippet_review = array(
                '@type' => 'Review',
                'reviewBody' => $review->comment_content,
                'datePublished' => $review->comment_date,
                'author' => array(
                    '@type' => 'Person',
                    'name' => $review->comment_author,
                ),
            );

            if ( $rating ) {
                $snippet_review['reviewRating'] = array(
                    '@type' => 'Rating',
                    'ratingValue' => $rating,
                    'bestRating' => '5',
                    'worstRating' => '1',
                );
            }

            $snippet_reviews[] = $snippet_review;
        }

        $snippet['review'] = $snippet_reviews;

        return $snippet;
    }

    /**
     * Génère l'extrait enrichi pour un produit avec note moyenne.
     *
     * @since    1.2.0
     * @param    WC_Product    $product           Le produit.
     * @param    array         $snippet_options   Les options de l'extrait enrichi.
     * @return   array                            L'extrait enrichi.
     */
    private function generate_product_with_aggregate_rating_snippet( $product, $snippet_options ) {
        $snippet = $this->generate_product_snippet( $product, $snippet_options );

        $rating_count = $product->get_rating_count();
        $average_rating = $product->get_average_rating();

        if ( $rating_count === 0 ) {
            return $snippet;
        }

        $snippet['aggregateRating'] = array(
            '@type' => 'AggregateRating',
            'ratingValue' => $average_rating,
            'reviewCount' => $rating_count,
            'bestRating' => '5',
            'worstRating' => '1',
        );

        return $snippet;
    }

    /**
     * Génère l'extrait enrichi complet pour un produit.
     *
     * @since    1.2.0
     * @param    WC_Product    $product           Le produit.
     * @param    array         $snippet_options   Les options de l'extrait enrichi.
     * @return   array                            L'extrait enrichi.
     */
    private function generate_product_complete_snippet( $product, $snippet_options ) {
        $snippet = $this->generate_product_snippet( $product, $snippet_options );

        // Ajouter l'offre
        $offer = array(
            '@type' => 'Offer',
            'url' => get_permalink( $product->get_id() ),
        );

        // Ajouter le prix
        if ( isset( $snippet_options['show_price'] ) && $snippet_options['show_price'] ) {
            $offer['price'] = $product->get_price();
            $offer['priceCurrency'] = get_woocommerce_currency();
        }

        // Ajouter la disponibilité
        if ( isset( $snippet_options['show_availability'] ) && $snippet_options['show_availability'] ) {
            $offer['availability'] = $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock';
        }

        $snippet['offers'] = $offer;

        // Ajouter les avis
        $args = array(
            'post_id' => $product->get_id(),
            'status' => 'approve',
            'type' => 'review',
        );

        $reviews = get_comments( $args );

        if ( ! empty( $reviews ) ) {
            $snippet_reviews = array();

            foreach ( $reviews as $review ) {
                $rating = get_comment_meta( $review->comment_ID, 'rating', true );

                $snippet_review = array(
                    '@type' => 'Review',
                    'reviewBody' => $review->comment_content,
                    'datePublished' => $review->comment_date,
                    'author' => array(
                        '@type' => 'Person',
                        'name' => $review->comment_author,
                    ),
                );

                if ( $rating ) {
                    $snippet_review['reviewRating'] = array(
                        '@type' => 'Rating',
                        'ratingValue' => $rating,
                        'bestRating' => '5',
                        'worstRating' => '1',
                    );
                }

                $snippet_reviews[] = $snippet_review;
            }

            $snippet['review'] = $snippet_reviews;
        }

        // Ajouter la note moyenne
        $rating_count = $product->get_rating_count();
        $average_rating = $product->get_average_rating();

        if ( $rating_count > 0 ) {
            $snippet['aggregateRating'] = array(
                '@type' => 'AggregateRating',
                'ratingValue' => $average_rating,
                'reviewCount' => $rating_count,
                'bestRating' => '5',
                'worstRating' => '1',
            );
        }

        return $snippet;
    }

    /**
     * Affiche l'extrait enrichi.
     *
     * @since    1.2.0
     * @param    array     $snippet    L'extrait enrichi.
     * @return   string                Le HTML de l'extrait enrichi.
     */
    public function render_rich_snippet( $snippet ) {
        if ( empty( $snippet ) || ! is_array( $snippet ) ) {
            return '';
        }

        $output = '<div class="boss-rich-snippet" style="display: none;">';
        $output .= '<script type="application/ld+json">' . wp_json_encode( $snippet ) . '</script>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Teste un extrait enrichi avec l'outil de test de Google.
     *
     * @since    1.2.0
     * @param    array     $snippet    L'extrait enrichi à tester.
     * @return   array|WP_Error       Les résultats du test ou une erreur.
     */
    private function test_snippet_with_google( $snippet ) {
        // URL de l'API de test de schéma de Google
        $api_url = 'https://search.google.com/test/rich-results/perform';

        // Préparer les données
        $data = array(
            'url' => home_url(),
            'html' => '<html><head><script type="application/ld+json">' . wp_json_encode( $snippet ) . '</script></head><body></body></html>',
        );

        // Effectuer la requête
        $response = wp_remote_post( $api_url, array(
            'body' => $data,
            'timeout' => 30,
        ) );

        // Vérifier la réponse
        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $body = wp_remote_retrieve_body( $response );
        $result = json_decode( $body, true );

        if ( ! $result ) {
            return new WP_Error( 'invalid_response', __( 'Réponse invalide de l\'API de test de Google.', 'boss-seo' ) );
        }

        return $result;
    }
}