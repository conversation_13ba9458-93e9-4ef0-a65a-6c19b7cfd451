import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  Card<PERSON>ody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  SelectControl,
  TextControl,
  ToggleControl,
  CheckboxControl,
  Modal,
  Notice,
  Spinner,
  TabPanel
} from '@wordpress/components';

// Importer le service
import EcommerceService from '../../services/EcommerceService';

const ProductSchemaConfig = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [schemaJson, setSchemaJson] = useState('');
  const [schemaSettings, setSchemaSettings] = useState({
    type: 'Product',
    includeOffers: true,
    includeAggregateRating: true,
    includeReviews: true,
    includeBrand: true,
    includeManufacturer: true,
    includeWeight: true,
    includeDimensions: true,
    includeGtin: true,
    includeMpn: true,
    includeSkuProperty: true
  });

  // Créer une instance du service
  const ecommerceService = new EcommerceService();

  // Charger les données
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Récupérer les produits
        const productsResponse = await ecommerceService.getProducts();

        // Extraire les catégories uniques des produits
        const uniqueCategories = [];
        const categoryIds = new Set();

        productsResponse.products.forEach(product => {
          if (product.categoryId && !categoryIds.has(product.categoryId)) {
            categoryIds.add(product.categoryId);
            uniqueCategories.push({
              id: product.categoryId,
              name: product.category
            });
          }
        });

        // Trier les catégories par nom
        uniqueCategories.sort((a, b) => a.name.localeCompare(b.name));

        // Mettre à jour les états
        setCategories(uniqueCategories);
        setProducts(productsResponse.products || []);
      } catch (err) {
        console.error('Erreur lors du chargement des produits:', err);

        // Vérifier si c'est une erreur spécifique de WooCommerce
        if (err && err.code) {
          if (err.code === 'woocommerce_not_available') {
            // WooCommerce n'est pas installé ou activé
            setError(__('WooCommerce n\'est pas installé ou activé. Veuillez installer et activer WooCommerce pour utiliser cette fonctionnalité.', 'boss-seo'));
          } else if (err.code === 'no_products_available') {
            // Aucun produit n'est disponible
            setError(__('Aucun produit n\'est disponible dans WooCommerce. Veuillez ajouter des produits pour utiliser cette fonctionnalité.', 'boss-seo'));
          } else {
            // Autre erreur
            setError(err.message || __('Erreur lors du chargement des produits. Veuillez réessayer.', 'boss-seo'));
          }
        } else {
          // Erreur générique
          setError(__('Erreur lors du chargement des produits. Veuillez réessayer.', 'boss-seo'));
        }

        // Réinitialiser les états
        setCategories([]);
        setProducts([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Effet pour générer le JSON-LD lorsque le produit ou les paramètres changent
  useEffect(() => {
    if (selectedProduct) {
      generateSchemaJson();
    }
  }, [selectedProduct, schemaSettings]);

  // Fonction pour filtrer les produits
  const getFilteredProducts = () => {
    return products.filter(product => {
      // Filtrer par catégorie
      const matchesCategory = selectedCategory === 'all' || product.categoryId === parseInt(selectedCategory);

      // Filtrer par recherche
      const matchesSearch = searchQuery === '' ||
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.category.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesCategory && matchesSearch;
    });
  };

  // Obtenir les produits filtrés
  const filteredProducts = getFilteredProducts();

  // Fonction pour générer le JSON-LD
  const generateSchemaJson = async () => {
    if (!selectedProduct) return;

    try {
      setIsLoading(true);
      setError(null);

      // Appeler le service pour générer le schéma du produit
      const response = await ecommerceService.generateProductSchema(selectedProduct.id, schemaSettings);

      // Mettre à jour l'état avec le schéma généré
      setSchemaJson(JSON.stringify(response.schema, null, 2));
    } catch (err) {
      console.error('Erreur lors de la génération du schéma:', err);
      setError(__('Erreur lors de la génération du schéma. Veuillez réessayer.', 'boss-seo'));

      // Générer un schéma localement en cas d'erreur (pour le développement)
      // À supprimer en production
      // Créer l'objet de base du schéma
      const schema = {
        '@context': 'https://schema.org',
        '@type': schemaSettings.type,
        'name': selectedProduct.name,
        'description': selectedProduct.description,
        'image': selectedProduct.images,
        'sku': schemaSettings.includeSkuProperty ? selectedProduct.sku : undefined
      };

      // Ajouter les propriétés optionnelles en fonction des paramètres
      if (schemaSettings.includeBrand) {
        schema.brand = {
          '@type': 'Brand',
          'name': selectedProduct.brand
        };
      }

      if (schemaSettings.includeManufacturer) {
        schema.manufacturer = {
          '@type': 'Organization',
          'name': selectedProduct.manufacturer
        };
      }

      if (schemaSettings.includeGtin) {
        schema.gtin13 = selectedProduct.gtin;
      }

      if (schemaSettings.includeMpn) {
        schema.mpn = selectedProduct.mpn;
      }

      if (schemaSettings.includeWeight) {
        schema.weight = {
          '@type': 'QuantitativeValue',
          'value': selectedProduct.weight.split(' ')[0],
          'unitCode': 'KGM'
        };
      }

      if (schemaSettings.includeDimensions) {
        schema.height = {
          '@type': 'QuantitativeValue',
          'value': selectedProduct.dimensions.height.split(' ')[0],
          'unitCode': 'CMT'
        };
        schema.width = {
          '@type': 'QuantitativeValue',
          'value': selectedProduct.dimensions.width.split(' ')[0],
          'unitCode': 'CMT'
        };
        schema.depth = {
          '@type': 'QuantitativeValue',
          'value': selectedProduct.dimensions.length.split(' ')[0],
          'unitCode': 'CMT'
        };
      }

      if (schemaSettings.includeOffers) {
        schema.offers = {
          '@type': 'Offer',
          'price': selectedProduct.price,
          'priceCurrency': 'EUR',
          'availability': selectedProduct.stock > 0 ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
          'url': `https://example.com/products/${selectedProduct.id}`
        };
      }

      if (schemaSettings.includeAggregateRating) {
        schema.aggregateRating = {
          '@type': 'AggregateRating',
          'ratingValue': selectedProduct.rating.average,
          'reviewCount': selectedProduct.rating.count
        };
      }

      if (schemaSettings.includeReviews) {
        schema.review = selectedProduct.reviews.map(review => ({
          '@type': 'Review',
          'reviewRating': {
            '@type': 'Rating',
            'ratingValue': review.rating,
            'bestRating': '5'
          },
          'author': {
            '@type': 'Person',
            'name': review.author
          },
          'datePublished': review.date,
          'reviewBody': review.text
        }));
      }

      // Convertir l'objet en JSON formaté
      setSchemaJson(JSON.stringify(schema, null, 2));
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour copier le JSON-LD
  const copyJsonLd = () => {
    navigator.clipboard.writeText(schemaJson);

    // Afficher un message de succès
    setShowSuccess(true);

    // Masquer le message après 3 secondes
    setTimeout(() => {
      setShowSuccess(false);
    }, 3000);
  };

  // Fonction pour sauvegarder le schéma
  const saveSchema = async () => {
    if (!selectedProduct) return;

    try {
      setIsLoading(true);
      setError(null);

      // Appeler le service pour enregistrer le schéma
      await ecommerceService.saveProductSchema(selectedProduct.id, JSON.parse(schemaJson));

      // Mettre à jour les produits dans l'état local
      const updatedProducts = products.map(product => {
        if (product.id === selectedProduct.id) {
          return {
            ...product,
            hasSchema: true
          };
        }
        return product;
      });

      // Mettre à jour l'état
      setProducts(updatedProducts);
      setSelectedProduct({
        ...selectedProduct,
        hasSchema: true
      });

      // Afficher le message de succès
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (err) {
      console.error('Erreur lors de l\'enregistrement du schéma:', err);
      setError(__('Erreur lors de l\'enregistrement du schéma. Veuillez réessayer.', 'boss-seo'));

      // Simuler l'enregistrement en cas d'erreur (pour le développement)
      // À supprimer en production
      const updatedProducts = products.map(product => {
        if (product.id === selectedProduct.id) {
          return {
            ...product,
            hasSchema: true
          };
        }
        return product;
      });

      setProducts(updatedProducts);
      setSelectedProduct({
        ...selectedProduct,
        hasSchema: true
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {error && (
            <div className="boss-text-center boss-p-8 boss-bg-white boss-rounded-lg boss-shadow boss-mb-6">
              <Dashicon icon="warning" size={36} className="boss-text-yellow-500 boss-mb-4" />
              <h2 className="boss-text-xl boss-font-bold boss-mb-2">{__('Attention', 'boss-seo')}</h2>
              <p className="boss-text-gray-600 boss-mb-4">{error}</p>
              {error.includes('WooCommerce') && (
                <a
                  href="https://wordpress.org/plugins/woocommerce/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="boss-inline-block boss-bg-blue-500 boss-text-white boss-px-4 boss-py-2 boss-rounded boss-hover:boss-bg-blue-600 boss-transition"
                >
                  {__('Installer WooCommerce', 'boss-seo')}
                </a>
              )}
            </div>
          )}

          {showSuccess && !error && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {__('Opération réussie !', 'boss-seo')}
            </Notice>
          )}

          {!error && (
            <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
              {/* Panneau de sélection de produit */}
              <div>
                <Card className="boss-mb-6">
                  <CardHeader className="boss-border-b boss-border-gray-200">
                    <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                      {__('Sélection du produit', 'boss-seo')}
                    </h2>
                  </CardHeader>
                  <CardBody>
                    <div className="boss-space-y-4">
                      <TextControl
                        placeholder={__('Rechercher un produit...', 'boss-seo')}
                        value={searchQuery}
                        onChange={setSearchQuery}
                      />

                      <SelectControl
                        label={__('Catégorie', 'boss-seo')}
                        value={selectedCategory}
                        options={[
                          { label: __('Toutes les catégories', 'boss-seo'), value: 'all' },
                          ...categories.map(category => ({
                            label: category.name,
                            value: category.id.toString()
                          }))
                        ]}
                        onChange={setSelectedCategory}
                      />

                      <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-max-h-96 boss-overflow-y-auto">
                        <div className="boss-divide-y boss-divide-gray-200">
                          {filteredProducts.length === 0 ? (
                            <div className="boss-p-4 boss-text-center boss-text-boss-gray">
                              {__('Aucun produit trouvé.', 'boss-seo')}
                            </div>
                          ) : (
                            filteredProducts.map(product => (
                              <div
                                key={product.id}
                                className={`boss-p-4 boss-cursor-pointer boss-transition-colors ${
                                  selectedProduct && selectedProduct.id === product.id
                                    ? 'boss-bg-blue-50'
                                    : 'boss-hover:boss-bg-gray-50'
                                }`}
                                onClick={() => setSelectedProduct(product)}
                              >
                                <div className="boss-flex boss-justify-between boss-items-start">
                                  <div>
                                    <div className="boss-font-medium boss-text-boss-dark boss-mb-1">{product.name}</div>
                                    <div className="boss-text-sm boss-text-boss-gray boss-mb-1">{product.category}</div>
                                    <div className="boss-text-sm boss-text-boss-primary">{product.price} €</div>
                                  </div>
                                  {product.hasSchema && (
                                    <span className="boss-text-green-600 boss-flex boss-items-center">
                                      <Dashicon icon="yes-alt" className="boss-mr-1" />
                                    </span>
                                  )}
                                </div>
                              </div>
                            ))
                          )}
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>

              {selectedProduct && (
                <Card>
                  <CardHeader className="boss-border-b boss-border-gray-200">
                    <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                      {__('Paramètres du schéma', 'boss-seo')}
                    </h2>
                  </CardHeader>
                  <CardBody>
                    <div className="boss-space-y-4">
                      <SelectControl
                        label={__('Type de schéma', 'boss-seo')}
                        value={schemaSettings.type}
                        options={[
                          { label: 'Product', value: 'Product' },
                          { label: 'IndividualProduct', value: 'IndividualProduct' },
                          { label: 'ProductModel', value: 'ProductModel' },
                          { label: 'SomeProducts', value: 'SomeProducts' },
                          { label: 'Vehicle', value: 'Vehicle' },
                          { label: 'Book', value: 'Book' },
                          { label: 'SoftwareApplication', value: 'SoftwareApplication' }
                        ]}
                        onChange={(value) => setSchemaSettings({ ...schemaSettings, type: value })}
                      />

                      <ToggleControl
                        label={__('Inclure les offres', 'boss-seo')}
                        checked={schemaSettings.includeOffers}
                        onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeOffers: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure la note moyenne', 'boss-seo')}
                        checked={schemaSettings.includeAggregateRating}
                        onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeAggregateRating: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure les avis', 'boss-seo')}
                        checked={schemaSettings.includeReviews}
                        onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeReviews: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure la marque', 'boss-seo')}
                        checked={schemaSettings.includeBrand}
                        onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeBrand: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure le fabricant', 'boss-seo')}
                        checked={schemaSettings.includeManufacturer}
                        onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeManufacturer: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure le poids', 'boss-seo')}
                        checked={schemaSettings.includeWeight}
                        onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeWeight: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure les dimensions', 'boss-seo')}
                        checked={schemaSettings.includeDimensions}
                        onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeDimensions: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure le GTIN', 'boss-seo')}
                        checked={schemaSettings.includeGtin}
                        onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeGtin: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure le MPN', 'boss-seo')}
                        checked={schemaSettings.includeMpn}
                        onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeMpn: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure le SKU', 'boss-seo')}
                        checked={schemaSettings.includeSkuProperty}
                        onChange={(checked) => setSchemaSettings({ ...schemaSettings, includeSkuProperty: checked })}
                      />
                    </div>
                  </CardBody>
                </Card>
              )}
            </div>

              {/* Panneau de prévisualisation du schéma */}
              <div className="lg:boss-col-span-2">
                {selectedProduct ? (
                  <Card>
                    <CardHeader className="boss-border-b boss-border-gray-200">
                      <div className="boss-flex boss-justify-between boss-items-center">
                        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                          {__('Prévisualisation du schéma', 'boss-seo')}
                        </h2>
                        <div className="boss-flex boss-space-x-2">
                          <Button
                            isSecondary
                            onClick={copyJsonLd}
                            icon="clipboard"
                          >
                            {__('Copier', 'boss-seo')}
                          </Button>
                          <Button
                            isPrimary
                            onClick={saveSchema}
                          >
                            {__('Enregistrer', 'boss-seo')}
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardBody>
                      <div className="boss-mb-6">
                        <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-overflow-auto boss-max-h-96">
                          <pre className="boss-text-xs boss-font-mono boss-whitespace-pre boss-text-boss-dark">
                            {schemaJson}
                          </pre>
                        </div>
                      </div>

                      <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-mb-6">
                        <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                          {__('Prévisualisation Google', 'boss-seo')}
                        </h3>

                        <div className="boss-bg-white boss-shadow boss-rounded-lg boss-p-4">
                          <div className="boss-text-blue-600 boss-text-lg boss-font-medium boss-mb-1">
                            {selectedProduct.name}
                          </div>
                          <div className="boss-text-green-800 boss-text-sm boss-mb-2">
                            https://example.com/products/{selectedProduct.id}
                          </div>
                          <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
                            {selectedProduct.description.substring(0, 150)}...
                          </div>

                          {schemaSettings.includeOffers && (
                            <div className="boss-text-sm boss-text-boss-gray boss-mb-1">
                              <span className="boss-font-medium">{__('Prix:', 'boss-seo')}</span> {selectedProduct.price} €
                            </div>
                          )}

                          {schemaSettings.includeAggregateRating && (
                            <div className="boss-flex boss-items-center boss-text-yellow-500">
                              <Dashicon icon="star-filled" />
                              <Dashicon icon="star-filled" />
                              <Dashicon icon="star-filled" />
                              <Dashicon icon="star-filled" />
                              <Dashicon icon="star-half" />
                              <span className="boss-ml-1 boss-text-boss-gray boss-text-xs">
                                {selectedProduct.rating.average} ({selectedProduct.rating.count} {__('avis', 'boss-seo')})
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4">
                        <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                          {__('Comment utiliser ce schéma', 'boss-seo')}
                        </h3>

                        <div className="boss-space-y-4 boss-text-sm boss-text-boss-gray">
                          <p>
                            {__('Pour implémenter ce schéma structuré sur votre site, vous pouvez :', 'boss-seo')}
                          </p>

                          <ol className="boss-list-decimal boss-pl-5 boss-space-y-2">
                            <li>
                              {__('Ajouter le code JSON-LD dans la section head de la page produit.', 'boss-seo')}
                            </li>
                            <li>
                              {__('Utiliser un plugin WordPress pour insérer le code automatiquement.', 'boss-seo')}
                            </li>
                            <li>
                              {__('Intégrer le code via Google Tag Manager.', 'boss-seo')}
                            </li>
                          </ol>

                          <p>
                            {__('Vous pouvez tester votre schéma avec l\'outil de test des données structurées de Google :', 'boss-seo')}
                          </p>

                          <p>
                            <a
                              href="https://search.google.com/test/rich-results"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="boss-text-boss-primary boss-underline"
                            >
                              {__('Outil de test des résultats enrichis', 'boss-seo')}
                            </a>
                          </p>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ) : (
                  <Card>
                    <CardBody>
                      <div className="boss-text-center boss-py-8">
                        <div className="boss-text-5xl boss-text-boss-gray boss-mb-4">
                          <Dashicon icon="media-code" />
                        </div>
                        <h2 className="boss-text-xl boss-font-bold boss-mb-4">
                          {__('Sélectionnez un produit', 'boss-seo')}
                        </h2>
                        <p className="boss-text-boss-gray boss-mb-6">
                          {__('Veuillez sélectionner un produit pour configurer son schéma structuré.', 'boss-seo')}
                        </p>
                      </div>
                    </CardBody>
                  </Card>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProductSchemaConfig;
