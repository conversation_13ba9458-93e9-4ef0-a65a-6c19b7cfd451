<?php
/**
 * Classe pour gérer les préférences utilisateur du plugin Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.0.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/settings
 */

/**
 * Classe pour gérer les préférences utilisateur du plugin Boss SEO.
 *
 * Cette classe gère les préférences utilisateur du plugin, telles que la disposition
 * du tableau de bord, les notifications, le thème, etc.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/settings
 * <AUTHOR> SEO Team
 */
class Boss_User_Preferences {

    /**
     * Le nom du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_seo_user_preferences_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.0.0
     */
    public function register_hooks() {
        // Hooks pour les préférences utilisateur
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_styles' ) );
        add_action( 'admin_notices', array( $this, 'display_notifications' ) );
        add_action( 'admin_bar_menu', array( $this, 'add_admin_bar_notifications' ), 999 );
    }

    /**
     * Enregistre les styles en fonction des préférences utilisateur.
     *
     * @since    1.0.0
     * @param    string    $hook_suffix    Le suffixe du hook.
     */
    public function enqueue_styles( $hook_suffix ) {
        // Vérifie si nous sommes sur une page du plugin
        if ( strpos( $hook_suffix, 'boss-seo' ) === false ) {
            return;
        }
        
        // Récupère les préférences de l'utilisateur actuel
        $user_id = get_current_user_id();
        $preferences = $this->get_user_preferences( $user_id );
        
        // Enregistre les styles en fonction du thème
        if ( $preferences['display']['theme'] === 'dark' ) {
            wp_enqueue_style( 'boss-seo-dark-theme', plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'assets/css/boss-seo-dark-theme.css', array(), $this->version );
        }
        
        // Enregistre les styles en fonction de la taille de police
        $font_size_css = '';
        switch ( $preferences['display']['fontSize'] ) {
            case 'small':
                $font_size_css = 'body.boss-seo-admin { font-size: 13px; }';
                break;
            case 'medium':
                $font_size_css = 'body.boss-seo-admin { font-size: 14px; }';
                break;
            case 'large':
                $font_size_css = 'body.boss-seo-admin { font-size: 16px; }';
                break;
        }
        
        // Enregistre les styles en fonction du mode compact
        if ( $preferences['display']['compactMode'] ) {
            $font_size_css .= 'body.boss-seo-admin .boss-card { padding: 10px; margin-bottom: 10px; }';
            $font_size_css .= 'body.boss-seo-admin .boss-card-header { padding: 8px; }';
            $font_size_css .= 'body.boss-seo-admin .boss-card-body { padding: 8px; }';
            $font_size_css .= 'body.boss-seo-admin .boss-card-footer { padding: 8px; }';
        }
        
        // Enregistre les styles en fonction de la couleur principale
        $primary_color = $preferences['display']['primaryColor'];
        $font_size_css .= 'body.boss-seo-admin .boss-primary-color { color: ' . $primary_color . '; }';
        $font_size_css .= 'body.boss-seo-admin .boss-primary-bg { background-color: ' . $primary_color . '; }';
        $font_size_css .= 'body.boss-seo-admin .boss-primary-border { border-color: ' . $primary_color . '; }';
        
        // Ajoute les styles inline
        wp_add_inline_style( 'boss-seo-admin', $font_size_css );
        
        // Ajoute la classe au body
        add_filter( 'admin_body_class', function( $classes ) {
            return $classes . ' boss-seo-admin';
        } );
    }

    /**
     * Affiche les notifications en fonction des préférences utilisateur.
     *
     * @since    1.0.0
     */
    public function display_notifications() {
        // Vérifie si nous sommes sur une page du plugin
        $screen = get_current_screen();
        if ( ! $screen || strpos( $screen->id, 'boss-seo' ) === false ) {
            return;
        }
        
        // Récupère les préférences de l'utilisateur actuel
        $user_id = get_current_user_id();
        $preferences = $this->get_user_preferences( $user_id );
        
        // Vérifie si les notifications sont activées
        if ( ! $preferences['notifications']['enabled'] ) {
            return;
        }
        
        // Récupère les notifications non lues
        $notifications = $this->get_unread_notifications( $user_id );
        
        // Affiche les notifications
        foreach ( $notifications as $notification ) {
            $class = 'notice notice-' . $notification['type'];
            $message = $notification['message'];
            $dismissible = $notification['dismissible'] ? 'is-dismissible' : '';
            
            printf( '<div class="%1$s %2$s" data-notification-id="%3$s"><p>%4$s</p></div>', esc_attr( $class ), esc_attr( $dismissible ), esc_attr( $notification['id'] ), wp_kses_post( $message ) );
        }
        
        // Ajoute le script pour marquer les notifications comme lues
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('.notice.is-dismissible').on('click', '.notice-dismiss', function() {
                var $notice = $(this).parent('.notice');
                var notificationId = $notice.data('notification-id');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'boss_seo_mark_notification_read',
                        notification_id: notificationId,
                        nonce: '<?php echo wp_create_nonce( 'boss_seo_mark_notification_read' ); ?>'
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * Ajoute des notifications à la barre d'administration.
     *
     * @since    1.0.0
     * @param    WP_Admin_Bar    $wp_admin_bar    L'objet WP_Admin_Bar.
     */
    public function add_admin_bar_notifications( $wp_admin_bar ) {
        // Récupère les préférences de l'utilisateur actuel
        $user_id = get_current_user_id();
        $preferences = $this->get_user_preferences( $user_id );
        
        // Vérifie si les notifications sont activées et si elles doivent être affichées dans la barre d'administration
        if ( ! $preferences['notifications']['enabled'] || ! $preferences['notifications']['showInAdminBar'] ) {
            return;
        }
        
        // Récupère les notifications non lues
        $notifications = $this->get_unread_notifications( $user_id );
        
        // Ajoute le nœud parent
        $wp_admin_bar->add_node( array(
            'id'    => 'boss-seo-notifications',
            'title' => '<span class="ab-icon dashicons dashicons-megaphone"></span><span class="ab-label">' . count( $notifications ) . '</span>',
            'href'  => admin_url( 'admin.php?page=boss-seo-notifications' ),
            'meta'  => array(
                'title' => __( 'Notifications Boss SEO', 'boss-seo' ),
            ),
        ) );
        
        // Ajoute les notifications comme sous-nœuds
        foreach ( $notifications as $notification ) {
            $wp_admin_bar->add_node( array(
                'id'     => 'boss-seo-notification-' . $notification['id'],
                'parent' => 'boss-seo-notifications',
                'title'  => $notification['message'],
                'href'   => admin_url( 'admin.php?page=boss-seo-notifications' ),
                'meta'   => array(
                    'title' => $notification['message'],
                    'class' => 'boss-seo-notification-' . $notification['type'],
                ),
            ) );
        }
    }

    /**
     * Récupère les préférences d'un utilisateur.
     *
     * @since    1.0.0
     * @param    int       $user_id    L'ID de l'utilisateur.
     * @return   array                 Les préférences de l'utilisateur.
     */
    public function get_user_preferences( $user_id ) {
        $default_preferences = $this->get_default_preferences();
        $user_preferences = get_user_meta( $user_id, $this->option_prefix . 'settings', true );
        
        if ( ! is_array( $user_preferences ) ) {
            $user_preferences = array();
        }
        
        return wp_parse_args( $user_preferences, $default_preferences );
    }

    /**
     * Récupère les préférences par défaut.
     *
     * @since    1.0.0
     * @return   array    Les préférences par défaut.
     */
    public function get_default_preferences() {
        return array(
            'dashboard' => array(
                'layout' => 'default',
                'showWelcomeMessage' => true,
                'defaultTab' => 'overview',
                'cardsPerRow' => 3,
                'expandedCards' => array( 'seo-score', 'recent-content', 'keywords' )
            ),
            'notifications' => array(
                'enabled' => true,
                'showInAdminBar' => true,
                'emailNotifications' => false,
                'emailFrequency' => 'weekly',
                'notifyOnRankChanges' => true,
                'notifyOnErrors' => true,
                'notifyOnUpdates' => true,
                'notifyOnReports' => true
            ),
            'display' => array(
                'theme' => 'light',
                'primaryColor' => '#3b82f6',
                'fontSize' => 'medium',
                'compactMode' => false,
                'showThumbnails' => true,
                'tableRowsPerPage' => 20,
                'dateFormat' => 'Y-m-d'
            ),
            'editor' => array(
                'enableSeoPanel' => true,
                'showInSidebar' => true,
                'autoAnalyzeContent' => true,
                'highlightIssues' => true,
                'showScoreInAdminColumns' => true,
                'showMetaInfoInAdminColumns' => true,
                'enableAiSuggestions' => true
            )
        );
    }

    /**
     * Enregistre les préférences d'un utilisateur.
     *
     * @since    1.0.0
     * @param    int       $user_id       L'ID de l'utilisateur.
     * @param    array     $preferences   Les préférences à enregistrer.
     * @return   bool                     True si les préférences ont été enregistrées, false sinon.
     */
    public function save_user_preferences( $user_id, $preferences ) {
        // Sanitize les préférences
        $sanitized_preferences = $this->sanitize_preferences( $preferences );
        
        // Enregistre les préférences
        return update_user_meta( $user_id, $this->option_prefix . 'settings', $sanitized_preferences );
    }

    /**
     * Sanitize les préférences utilisateur.
     *
     * @since    1.0.0
     * @param    array     $preferences   Les préférences à sanitize.
     * @return   array                    Les préférences sanitized.
     */
    public function sanitize_preferences( $preferences ) {
        $sanitized = array();
        
        // Sanitize les préférences du tableau de bord
        if ( isset( $preferences['dashboard'] ) ) {
            $sanitized['dashboard'] = array(
                'layout' => isset( $preferences['dashboard']['layout'] ) ? sanitize_text_field( $preferences['dashboard']['layout'] ) : 'default',
                'showWelcomeMessage' => isset( $preferences['dashboard']['showWelcomeMessage'] ) ? (bool) $preferences['dashboard']['showWelcomeMessage'] : true,
                'defaultTab' => isset( $preferences['dashboard']['defaultTab'] ) ? sanitize_text_field( $preferences['dashboard']['defaultTab'] ) : 'overview',
                'cardsPerRow' => isset( $preferences['dashboard']['cardsPerRow'] ) ? absint( $preferences['dashboard']['cardsPerRow'] ) : 3,
                'expandedCards' => isset( $preferences['dashboard']['expandedCards'] ) && is_array( $preferences['dashboard']['expandedCards'] ) ? array_map( 'sanitize_text_field', $preferences['dashboard']['expandedCards'] ) : array( 'seo-score', 'recent-content', 'keywords' )
            );
        }
        
        // Sanitize les préférences de notifications
        if ( isset( $preferences['notifications'] ) ) {
            $sanitized['notifications'] = array(
                'enabled' => isset( $preferences['notifications']['enabled'] ) ? (bool) $preferences['notifications']['enabled'] : true,
                'showInAdminBar' => isset( $preferences['notifications']['showInAdminBar'] ) ? (bool) $preferences['notifications']['showInAdminBar'] : true,
                'emailNotifications' => isset( $preferences['notifications']['emailNotifications'] ) ? (bool) $preferences['notifications']['emailNotifications'] : false,
                'emailFrequency' => isset( $preferences['notifications']['emailFrequency'] ) ? sanitize_text_field( $preferences['notifications']['emailFrequency'] ) : 'weekly',
                'notifyOnRankChanges' => isset( $preferences['notifications']['notifyOnRankChanges'] ) ? (bool) $preferences['notifications']['notifyOnRankChanges'] : true,
                'notifyOnErrors' => isset( $preferences['notifications']['notifyOnErrors'] ) ? (bool) $preferences['notifications']['notifyOnErrors'] : true,
                'notifyOnUpdates' => isset( $preferences['notifications']['notifyOnUpdates'] ) ? (bool) $preferences['notifications']['notifyOnUpdates'] : true,
                'notifyOnReports' => isset( $preferences['notifications']['notifyOnReports'] ) ? (bool) $preferences['notifications']['notifyOnReports'] : true
            );
        }
        
        // Sanitize les préférences d'affichage
        if ( isset( $preferences['display'] ) ) {
            $sanitized['display'] = array(
                'theme' => isset( $preferences['display']['theme'] ) ? sanitize_text_field( $preferences['display']['theme'] ) : 'light',
                'primaryColor' => isset( $preferences['display']['primaryColor'] ) ? sanitize_hex_color( $preferences['display']['primaryColor'] ) : '#3b82f6',
                'fontSize' => isset( $preferences['display']['fontSize'] ) ? sanitize_text_field( $preferences['display']['fontSize'] ) : 'medium',
                'compactMode' => isset( $preferences['display']['compactMode'] ) ? (bool) $preferences['display']['compactMode'] : false,
                'showThumbnails' => isset( $preferences['display']['showThumbnails'] ) ? (bool) $preferences['display']['showThumbnails'] : true,
                'tableRowsPerPage' => isset( $preferences['display']['tableRowsPerPage'] ) ? absint( $preferences['display']['tableRowsPerPage'] ) : 20,
                'dateFormat' => isset( $preferences['display']['dateFormat'] ) ? sanitize_text_field( $preferences['display']['dateFormat'] ) : 'Y-m-d'
            );
        }
        
        // Sanitize les préférences d'éditeur
        if ( isset( $preferences['editor'] ) ) {
            $sanitized['editor'] = array(
                'enableSeoPanel' => isset( $preferences['editor']['enableSeoPanel'] ) ? (bool) $preferences['editor']['enableSeoPanel'] : true,
                'showInSidebar' => isset( $preferences['editor']['showInSidebar'] ) ? (bool) $preferences['editor']['showInSidebar'] : true,
                'autoAnalyzeContent' => isset( $preferences['editor']['autoAnalyzeContent'] ) ? (bool) $preferences['editor']['autoAnalyzeContent'] : true,
                'highlightIssues' => isset( $preferences['editor']['highlightIssues'] ) ? (bool) $preferences['editor']['highlightIssues'] : true,
                'showScoreInAdminColumns' => isset( $preferences['editor']['showScoreInAdminColumns'] ) ? (bool) $preferences['editor']['showScoreInAdminColumns'] : true,
                'showMetaInfoInAdminColumns' => isset( $preferences['editor']['showMetaInfoInAdminColumns'] ) ? (bool) $preferences['editor']['showMetaInfoInAdminColumns'] : true,
                'enableAiSuggestions' => isset( $preferences['editor']['enableAiSuggestions'] ) ? (bool) $preferences['editor']['enableAiSuggestions'] : true
            );
        }
        
        return $sanitized;
    }

    /**
     * Récupère les notifications non lues d'un utilisateur.
     *
     * @since    1.0.0
     * @param    int       $user_id    L'ID de l'utilisateur.
     * @return   array                 Les notifications non lues.
     */
    public function get_unread_notifications( $user_id ) {
        // Récupère toutes les notifications
        $all_notifications = get_option( 'boss_seo_notifications', array() );
        
        // Récupère les notifications lues par l'utilisateur
        $read_notifications = get_user_meta( $user_id, $this->option_prefix . 'read_notifications', true );
        if ( ! is_array( $read_notifications ) ) {
            $read_notifications = array();
        }
        
        // Filtre les notifications non lues
        $unread_notifications = array();
        foreach ( $all_notifications as $notification ) {
            if ( ! in_array( $notification['id'], $read_notifications ) ) {
                $unread_notifications[] = $notification;
            }
        }
        
        return $unread_notifications;
    }

    /**
     * Marque une notification comme lue pour un utilisateur.
     *
     * @since    1.0.0
     * @param    int       $user_id          L'ID de l'utilisateur.
     * @param    string    $notification_id  L'ID de la notification.
     * @return   bool                        True si la notification a été marquée comme lue, false sinon.
     */
    public function mark_notification_read( $user_id, $notification_id ) {
        // Récupère les notifications lues par l'utilisateur
        $read_notifications = get_user_meta( $user_id, $this->option_prefix . 'read_notifications', true );
        if ( ! is_array( $read_notifications ) ) {
            $read_notifications = array();
        }
        
        // Ajoute la notification à la liste des notifications lues
        if ( ! in_array( $notification_id, $read_notifications ) ) {
            $read_notifications[] = $notification_id;
            return update_user_meta( $user_id, $this->option_prefix . 'read_notifications', $read_notifications );
        }
        
        return true;
    }

    /**
     * Réinitialise les préférences d'un utilisateur.
     *
     * @since    1.0.0
     * @param    int       $user_id    L'ID de l'utilisateur.
     * @return   bool                  True si les préférences ont été réinitialisées, false sinon.
     */
    public function reset_user_preferences( $user_id ) {
        // Supprime les préférences de l'utilisateur
        delete_user_meta( $user_id, $this->option_prefix . 'settings' );
        
        // Supprime les notifications lues par l'utilisateur
        delete_user_meta( $user_id, $this->option_prefix . 'read_notifications' );
        
        return true;
    }
}
