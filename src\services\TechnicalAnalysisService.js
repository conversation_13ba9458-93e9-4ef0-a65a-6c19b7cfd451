/**
 * Service pour l'analyse technique
 *
 * Gère les communications avec l'API pour les fonctionnalités d'analyse technique
 */

import apiFetch from '@wordpress/api-fetch';

class TechnicalAnalysisService {
  /**
   * Lance une analyse technique complète du site
   *
   * @returns {Promise} Promesse contenant les résultats de l'analyse
   */
  async startAnalysis() {
    try {
      const path = '/boss-seo/v1/technical-analysis/start';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du lancement de l\'analyse technique:', error);
      throw error;
    }
  }

  /**
   * Récupère les résultats de la dernière analyse
   *
   * @returns {Promise} Promesse contenant les résultats de la dernière analyse
   */
  async getLatestAnalysis() {
    try {
      const path = '/boss-seo/v1/technical-analysis/latest';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de la dernière analyse:', error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des analyses
   *
   * @param {number} page - Numéro de page
   * @param {number} perPage - Nombre d'éléments par page
   * @returns {Promise} Promesse contenant l'historique des analyses
   */
  async getAnalysisHistory(page = 1, perPage = 10) {
    try {
      const path = `/boss-seo/v1/technical-analysis/history?page=${page}&per_page=${perPage}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des analyses:', error);
      throw error;
    }
  }

  /**
   * Récupère les problèmes détectés lors de la dernière analyse
   *
   * @param {string} category - Catégorie des problèmes (optionnel)
   * @param {string} severity - Gravité des problèmes (optionnel)
   * @returns {Promise} Promesse contenant les problèmes détectés
   */
  async getIssues(category = null, severity = null) {
    try {
      let path = '/boss-seo/v1/technical-analysis/issues';

      // Ajouter les paramètres de filtrage si nécessaire
      const params = [];
      if (category) params.push(`category=${category}`);
      if (severity) params.push(`severity=${severity}`);

      if (params.length > 0) {
        path += `?${params.join('&')}`;
      }

      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des problèmes:', error);
      throw error;
    }
  }

  /**
   * Marque un problème comme résolu
   *
   * @param {string} issueId - ID du problème
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async markIssueAsResolved(issueId) {
    try {
      const path = '/boss-seo/v1/technical-analysis/issues/resolve';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { issue_id: issueId }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du marquage du problème comme résolu:', error);
      throw error;
    }
  }

  /**
   * Récupère les données de performance
   *
   * @returns {Promise} Promesse contenant les données de performance
   */
  async getPerformanceData() {
    try {
      const path = '/boss-seo/v1/technical-analysis/performance';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des données de performance:', error);
      throw error;
    }
  }

  /**
   * Compare deux analyses
   *
   * @param {string} analysisId1 - ID de la première analyse
   * @param {string} analysisId2 - ID de la deuxième analyse
   * @returns {Promise} Promesse contenant les résultats de la comparaison
   */
  async compareAnalyses(analysisId1, analysisId2) {
    try {
      const path = '/boss-seo/v1/technical-analysis/compare';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          analysis_id_1: analysisId1,
          analysis_id_2: analysisId2
        }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la comparaison des analyses:', error);
      throw error;
    }
  }

  /**
   * Analyse une URL spécifique avec PageSpeed Insights
   *
   * @param {string} url - URL à analyser (optionnel, utilise la page d'accueil par défaut)
   * @param {string} strategy - Stratégie d'analyse ('mobile' ou 'desktop')
   * @returns {Promise} Promesse contenant les résultats de l'analyse
   */
  async analyzeSpecificUrl(url = '', strategy = 'mobile') {
    try {
      const path = '/boss-seo/v1/technical-analysis/analyze-url';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          url: url,
          strategy: strategy
        }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'analyse de l\'URL spécifique:', error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des analyses d'URL
   *
   * @returns {Promise} Promesse contenant l'historique des analyses d'URL
   */
  async getUrlAnalysisHistory() {
    try {
      const path = '/boss-seo/v1/technical-analysis/url-history';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des analyses d\'URL:', error);
      throw error;
    }
  }

  /**
   * Récupère les résultats d'une analyse d'URL spécifique
   *
   * @param {string} analysisId - ID de l'analyse
   * @returns {Promise} Promesse contenant les résultats de l'analyse
   */
  async getUrlAnalysisResults(analysisId) {
    try {
      const path = `/boss-seo/v1/technical-analysis/url-analysis/${analysisId}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des résultats de l\'analyse d\'URL:', error);
      throw error;
    }
  }
}

export default new TechnicalAnalysisService();
