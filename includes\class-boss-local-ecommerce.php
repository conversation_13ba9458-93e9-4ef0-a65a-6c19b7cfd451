<?php
/**
 * Classe principale pour le module SEO local & e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe principale pour le module SEO local & e-commerce.
 *
 * Cette classe gère toutes les fonctionnalités du module SEO local & e-commerce.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Local_Ecommerce {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance du module SEO local.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_SEO    $local_seo    Instance du module SEO local.
     */
    protected $local_seo;

    /**
     * Instance du module SEO e-commerce.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Ecommerce_SEO    $ecommerce_seo    Instance du module SEO e-commerce.
     */
    protected $ecommerce_seo;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->load_dependencies();
        $this->init_modules();
    }

    /**
     * Charge les dépendances nécessaires pour ce module.
     *
     * @since    1.2.0
     * @access   private
     */
    private function load_dependencies() {
        // Charger les classes pour le SEO local
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-seo.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-location.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-business-info.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-page-generator.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-schema.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-rankings.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/local/class-boss-local-analysis.php';

        // Charger les classes pour le SEO e-commerce
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-ecommerce-seo.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-product-optimizer.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-product-generator.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-product-schema.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-google-shopping.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-ecommerce-analysis.php';
    }

    /**
     * Initialise les modules SEO local et e-commerce.
     *
     * @since    1.2.0
     * @access   private
     */
    private function init_modules() {
        $this->local_seo = new Boss_Local_SEO( $this->plugin_name, $this->version );
        $this->ecommerce_seo = new Boss_Ecommerce_SEO( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Enregistrer les hooks pour le SEO local
        $this->local_seo->register_hooks();

        // Enregistrer les hooks pour le SEO e-commerce
        $this->ecommerce_seo->register_hooks();

        // Vérifier si WooCommerce est actif
        add_action( 'admin_init', array( $this, 'check_woocommerce' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Enregistrer les routes REST API pour le SEO local
        $this->local_seo->register_rest_routes();

        // Enregistrer les routes REST API pour le SEO e-commerce
        $this->ecommerce_seo->register_rest_routes();
    }

    /**
     * Vérifie si WooCommerce est actif et affiche une notification si ce n'est pas le cas.
     *
     * @since    1.2.0
     */
    public function check_woocommerce() {
        if ( ! class_exists( 'WooCommerce' ) ) {
            add_action( 'admin_notices', array( $this, 'woocommerce_notice' ) );
        }
    }

    /**
     * Affiche une notification si WooCommerce n'est pas actif.
     *
     * @since    1.2.0
     */
    public function woocommerce_notice() {
        $screen = get_current_screen();
        if ( $screen->id === 'boss-seo_page_boss-seo-local' ) {
            ?>
            <div class="notice notice-warning is-dismissible">
                <p><?php _e( 'Pour utiliser toutes les fonctionnalités du module SEO e-commerce, veuillez installer et activer WooCommerce.', 'boss-seo' ); ?></p>
                <p><a href="<?php echo admin_url( 'plugin-install.php?s=woocommerce&tab=search&type=term' ); ?>" class="button button-primary"><?php _e( 'Installer WooCommerce', 'boss-seo' ); ?></a></p>
            </div>
            <?php
        }
    }
}
