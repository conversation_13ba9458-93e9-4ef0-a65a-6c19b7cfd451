/**
 * Composant principal du module d'optimisation de contenu en multistep
 * 
 * Implémente un workflow en 5 étapes pour l'optimisation de contenu SEO
 */
import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Dashicon,
  Notice,
  Spinner
} from '@wordpress/components';

// Composants internes pour chaque étape
import StepObjective from '../components/content/multistep/StepObjective';
import StepKeywords from '../components/content/multistep/StepKeywords';
import StepContent from '../components/content/multistep/StepContent';
import StepImages from '../components/content/multistep/StepImages';
import StepPublish from '../components/content/multistep/StepPublish';
import ProgressBar from '../components/content/multistep/ProgressBar';
import Navigation from '../components/content/multistep/Navigation';

// Services
import optimizerService from '../services/OptimizerService';

/**
 * Composant principal du module d'optimisation de contenu multistep
 */
const ContentOptimizationMultistep = () => {
  // État pour gérer l'étape actuelle
  const [currentStep, setCurrentStep] = useState(1);
  
  // État pour gérer les données du workflow
  const [workflowData, setWorkflowData] = useState({
    objective: {
      contentType: 'article',
      tone: 'professional',
      audience: '',
    },
    keywords: {
      main: '',
      secondary: [],
      suggestions: [],
    },
    content: {
      title: '',
      metaDescription: '',
      content: '',
      format: 'article',
      seoScore: 0,
    },
    images: {
      selected: [],
      suggestions: [],
    },
    publish: {
      status: 'draft',
      postId: null,
    }
  });
  
  // États pour gérer le chargement et les erreurs
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // Effet pour charger les données sauvegardées dans localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('boss_seo_content_workflow');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setWorkflowData(parsedData);
      } catch (e) {
        console.error('Erreur lors du chargement des données sauvegardées:', e);
      }
    }
  }, []);
  
  // Effet pour sauvegarder les données dans localStorage
  useEffect(() => {
    localStorage.setItem('boss_seo_content_workflow', JSON.stringify(workflowData));
  }, [workflowData]);
  
  // Fonction pour mettre à jour les données d'une étape spécifique
  const updateStepData = (step, data) => {
    setWorkflowData(prevData => ({
      ...prevData,
      [step]: {
        ...prevData[step],
        ...data
      }
    }));
  };
  
  // Fonction pour passer à l'étape suivante
  const goToNextStep = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    }
  };
  
  // Fonction pour revenir à l'étape précédente
  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };
  
  // Fonction pour sauvegarder le brouillon
  const saveDraft = async () => {
    setIsLoading(true);
    setError('');
    setSuccess('');
    
    try {
      const response = await optimizerService.saveContentDraft({
        title: workflowData.content.title,
        content: workflowData.content.content,
        excerpt: workflowData.content.metaDescription,
        meta: {
          focus_keyword: workflowData.keywords.main,
          secondary_keywords: workflowData.keywords.secondary,
          content_type: workflowData.objective.contentType,
          tone: workflowData.objective.tone,
          audience: workflowData.objective.audience,
          selected_images: workflowData.images.selected
        }
      });
      
      if (response.success) {
        setSuccess(__('Brouillon sauvegardé avec succès.', 'boss-seo'));
        updateStepData('publish', { postId: response.post_id });
      } else {
        setError(response.message || __('Erreur lors de la sauvegarde du brouillon.', 'boss-seo'));
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du brouillon:', error);
      setError(__('Erreur lors de la sauvegarde du brouillon.', 'boss-seo'));
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fonction pour publier le contenu
  const publishContent = async () => {
    setIsLoading(true);
    setError('');
    setSuccess('');
    
    try {
      const response = await optimizerService.publishContent({
        post_id: workflowData.publish.postId,
        title: workflowData.content.title,
        content: workflowData.content.content,
        excerpt: workflowData.content.metaDescription,
        meta: {
          focus_keyword: workflowData.keywords.main,
          secondary_keywords: workflowData.keywords.secondary,
          content_type: workflowData.objective.contentType,
          tone: workflowData.objective.tone,
          audience: workflowData.objective.audience,
          selected_images: workflowData.images.selected
        }
      });
      
      if (response.success) {
        setSuccess(__('Contenu publié avec succès.', 'boss-seo'));
        updateStepData('publish', { status: 'publish', postId: response.post_id });
        
        // Rediriger vers l'éditeur WordPress après 2 secondes
        setTimeout(() => {
          window.location.href = response.edit_url;
        }, 2000);
      } else {
        setError(response.message || __('Erreur lors de la publication du contenu.', 'boss-seo'));
      }
    } catch (error) {
      console.error('Erreur lors de la publication du contenu:', error);
      setError(__('Erreur lors de la publication du contenu.', 'boss-seo'));
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fonction pour réinitialiser le workflow
  const resetWorkflow = () => {
    if (confirm(__('Êtes-vous sûr de vouloir réinitialiser le workflow ? Toutes les données non sauvegardées seront perdues.', 'boss-seo'))) {
      localStorage.removeItem('boss_seo_content_workflow');
      window.location.reload();
    }
  };
  
  // Rendu du composant en fonction de l'étape actuelle
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <StepObjective 
            data={workflowData.objective} 
            updateData={(data) => updateStepData('objective', data)} 
          />
        );
      case 2:
        return (
          <StepKeywords 
            data={workflowData.keywords} 
            updateData={(data) => updateStepData('keywords', data)} 
            objectiveData={workflowData.objective}
          />
        );
      case 3:
        return (
          <StepContent 
            data={workflowData.content} 
            updateData={(data) => updateStepData('content', data)} 
            keywordsData={workflowData.keywords}
            objectiveData={workflowData.objective}
          />
        );
      case 4:
        return (
          <StepImages 
            data={workflowData.images} 
            updateData={(data) => updateStepData('images', data)} 
            keywordsData={workflowData.keywords}
          />
        );
      case 5:
        return (
          <StepPublish 
            data={workflowData} 
            onSaveDraft={saveDraft} 
            onPublish={publishContent}
            isLoading={isLoading}
          />
        );
      default:
        return null;
    }
  };
  
  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Optimisation de contenu', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Créez et optimisez votre contenu pour améliorer votre référencement', 'boss-seo')}
          </p>
        </div>
        
        {/* Barre de progression */}
        <ProgressBar currentStep={currentStep} totalSteps={5} />
        
        {/* Messages d'erreur et de succès */}
        {error && (
          <Notice status="error" isDismissible={true} onRemove={() => setError('')} className="boss-mb-4">
            {error}
          </Notice>
        )}
        
        {success && (
          <Notice status="success" isDismissible={true} onRemove={() => setSuccess('')} className="boss-mb-4">
            {success}
          </Notice>
        )}
        
        {/* Contenu de l'étape actuelle */}
        <Card className="boss-mb-6">
          <CardBody>
            {renderStep()}
          </CardBody>
        </Card>
        
        {/* Navigation entre les étapes */}
        <Navigation 
          currentStep={currentStep} 
          totalSteps={5} 
          onNext={goToNextStep} 
          onPrevious={goToPreviousStep}
          onReset={resetWorkflow}
          isNextDisabled={isLoading}
          isPreviousDisabled={isLoading || currentStep === 1}
        />
      </div>
    </div>
  );
};

export default ContentOptimizationMultistep;
