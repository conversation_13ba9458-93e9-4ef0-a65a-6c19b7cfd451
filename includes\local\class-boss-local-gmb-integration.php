<?php
/**
 * Classe pour l'intégration de Google My Business avec le module SEO Local.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour l'intégration de Google My Business avec le module SEO Local.
 *
 * Cette classe gère l'intégration de l'API Google My Business avec le module SEO Local.
 * Elle permet d'importer des établissements depuis Google My Business et de synchroniser
 * les données entre WordPress et Google My Business.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_GMB_Integration {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_local_gmb_';

    /**
     * L'instance de l'API Google My Business.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_SEO_GMB_API    $gmb_api    L'instance de l'API Google My Business.
     */
    protected $gmb_api;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Charger les dépendances
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'api/class-boss-seo-gmb-api.php';

        // Initialiser l'API GMB
        $this->gmb_api = new Boss_SEO_GMB_API();
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_gmb_accounts', array( $this, 'ajax_get_gmb_accounts' ) );
        add_action( 'wp_ajax_boss_seo_get_gmb_locations', array( $this, 'ajax_get_gmb_locations' ) );
        add_action( 'wp_ajax_boss_seo_import_gmb_location', array( $this, 'ajax_import_gmb_location' ) );
        add_action( 'wp_ajax_boss_seo_sync_gmb_location', array( $this, 'ajax_sync_gmb_location' ) );
        add_action( 'wp_ajax_boss_seo_get_gmb_insights', array( $this, 'ajax_get_gmb_insights' ) );
        add_action( 'wp_ajax_boss_seo_get_gmb_reviews', array( $this, 'ajax_get_gmb_reviews' ) );

        // Ajouter les actions pour la synchronisation automatique
        add_action( 'boss_seo_daily_cron', array( $this, 'sync_all_locations' ) );

        // Ajouter les filtres pour les métaboxes
        add_filter( 'boss_local_location_meta_boxes', array( $this, 'add_gmb_meta_box' ) );
        add_action( 'save_post_boss_local_location', array( $this, 'save_gmb_meta_box' ), 10, 3 );

        // Ajouter les actions pour les colonnes d'administration
        add_filter( 'manage_boss_local_location_posts_columns', array( $this, 'add_gmb_admin_column' ) );
        add_action( 'manage_boss_local_location_posts_custom_column', array( $this, 'display_gmb_admin_column' ), 10, 2 );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/local/gmb/accounts',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_gmb_accounts' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/gmb/locations',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_gmb_locations' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/gmb/import',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'import_gmb_location' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/gmb/sync',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'sync_gmb_location' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/gmb/insights',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_gmb_insights' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/gmb/reviews',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_gmb_reviews' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les comptes Google My Business.
     *
     * @since    1.2.0
     */
    public function ajax_get_gmb_accounts() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ) ) );
        }

        // Récupérer les comptes
        $accounts = $this->gmb_api->get_accounts();

        if ( is_wp_error( $accounts ) ) {
            wp_send_json_error( array( 'message' => $accounts->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Comptes récupérés avec succès.', 'boss-seo' ),
            'accounts' => $accounts,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les établissements Google My Business.
     *
     * @since    1.2.0
     */
    public function ajax_get_gmb_locations() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['account_id'] ) || empty( $_POST['account_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'ID de compte manquant.', 'boss-seo' ) ) );
        }

        $account_id = sanitize_text_field( $_POST['account_id'] );

        // Récupérer les établissements
        $locations = $this->gmb_api->get_locations( $account_id );

        if ( is_wp_error( $locations ) ) {
            wp_send_json_error( array( 'message' => $locations->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'   => __( 'Établissements récupérés avec succès.', 'boss-seo' ),
            'locations' => $locations,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour importer un établissement Google My Business.
     *
     * @since    1.2.0
     */
    public function ajax_import_gmb_location() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['account_id'] ) || empty( $_POST['account_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'ID de compte manquant.', 'boss-seo' ) ) );
        }

        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'ID d\'établissement manquant.', 'boss-seo' ) ) );
        }

        $account_id = sanitize_text_field( $_POST['account_id'] );
        $location_id = sanitize_text_field( $_POST['location_id'] );

        // Importer l'établissement
        $result = $this->import_location_from_gmb( $account_id, $location_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'     => __( 'Établissement importé avec succès.', 'boss-seo' ),
            'location_id' => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour synchroniser un établissement avec Google My Business.
     *
     * @since    1.2.0
     */
    public function ajax_sync_gmb_location() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'ID d\'emplacement manquant.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );

        // Synchroniser l'établissement
        $result = $this->sync_location_with_gmb( $location_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Établissement synchronisé avec succès.', 'boss-seo' ),
            'result'  => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les statistiques d'un établissement Google My Business.
     *
     * @since    1.2.0
     */
    public function ajax_get_gmb_insights() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'ID d\'emplacement manquant.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );

        // Récupérer les métadonnées GMB
        $gmb_data = get_post_meta( $location_id, '_boss_local_gmb_data', true );

        if ( empty( $gmb_data ) || ! isset( $gmb_data['account_id'] ) || ! isset( $gmb_data['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Cet emplacement n\'est pas lié à Google My Business.', 'boss-seo' ) ) );
        }

        $account_id = $gmb_data['account_id'];
        $gmb_location_id = $gmb_data['location_id'];

        // Récupérer la période
        $metric = isset( $_POST['metric'] ) ? sanitize_text_field( $_POST['metric'] ) : 'ALL';
        $time_range = isset( $_POST['time_range'] ) ? sanitize_text_field( $_POST['time_range'] ) : 'MONTH';

        // Récupérer les statistiques
        $insights = $this->gmb_api->get_insights( $account_id, $gmb_location_id, $metric, $time_range );

        if ( is_wp_error( $insights ) ) {
            wp_send_json_error( array( 'message' => $insights->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Statistiques récupérées avec succès.', 'boss-seo' ),
            'insights' => $insights,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les avis d'un établissement Google My Business.
     *
     * @since    1.2.0
     */
    public function ajax_get_gmb_reviews() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'ID d\'emplacement manquant.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );

        // Récupérer les métadonnées GMB
        $gmb_data = get_post_meta( $location_id, '_boss_local_gmb_data', true );

        if ( empty( $gmb_data ) || ! isset( $gmb_data['account_id'] ) || ! isset( $gmb_data['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Cet emplacement n\'est pas lié à Google My Business.', 'boss-seo' ) ) );
        }

        $account_id = $gmb_data['account_id'];
        $gmb_location_id = $gmb_data['location_id'];

        // Récupérer les avis
        $reviews = $this->gmb_api->get_reviews( $account_id, $gmb_location_id );

        if ( is_wp_error( $reviews ) ) {
            wp_send_json_error( array( 'message' => $reviews->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Avis récupérés avec succès.', 'boss-seo' ),
            'reviews' => $reviews,
        ) );
    }

    /**
     * Récupère les comptes Google My Business via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_gmb_accounts( $request ) {
        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            return new WP_Error( 'gmb_not_connected', __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les comptes
        $accounts = $this->gmb_api->get_accounts();

        if ( is_wp_error( $accounts ) ) {
            return $accounts;
        }

        // Formater les données pour l'interface utilisateur
        $formatted_accounts = array();

        if ( isset( $accounts['accounts'] ) && is_array( $accounts['accounts'] ) ) {
            foreach ( $accounts['accounts'] as $account ) {
                $formatted_accounts[] = array(
                    'id'          => isset( $account['name'] ) ? $account['name'] : '',
                    'display_name' => isset( $account['accountName'] ) ? $account['accountName'] : __( 'Compte sans nom', 'boss-seo' ),
                    'type'        => isset( $account['type'] ) ? $account['type'] : '',
                    'role'        => isset( $account['role'] ) ? $account['role'] : '',
                    'state'       => isset( $account['state'] ) ? $account['state'] : '',
                    'account_number' => isset( $account['accountNumber'] ) ? $account['accountNumber'] : '',
                    'permission_level' => isset( $account['permissionLevel'] ) ? $account['permissionLevel'] : '',
                );
            }
        }

        return rest_ensure_response( array(
            'accounts' => $formatted_accounts,
            'raw'      => $accounts, // Pour le débogage
        ) );
    }

    /**
     * Récupère les établissements Google My Business via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_gmb_locations( $request ) {
        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            return new WP_Error( 'gmb_not_connected', __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer l'ID du compte
        $account_id = $request->get_param( 'account_id' );

        if ( empty( $account_id ) ) {
            return new WP_Error( 'missing_account_id', __( 'ID de compte manquant.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les établissements
        $locations = $this->gmb_api->get_locations( $account_id );

        if ( is_wp_error( $locations ) ) {
            return $locations;
        }

        // Formater les données pour l'interface utilisateur
        $formatted_locations = array();
        $existing_locations = $this->get_existing_gmb_locations();

        if ( isset( $locations['locations'] ) && is_array( $locations['locations'] ) ) {
            foreach ( $locations['locations'] as $location ) {
                $location_id = isset( $location['name'] ) ? $location['name'] : '';
                $is_imported = isset( $existing_locations[ $location_id ] );

                $formatted_locations[] = array(
                    'id'          => $location_id,
                    'title'       => isset( $location['locationName'] ) ? $location['locationName'] : __( 'Établissement sans nom', 'boss-seo' ),
                    'address'     => $this->format_gmb_address( $location ),
                    'phone'       => isset( $location['primaryPhone'] ) ? $location['primaryPhone'] : '',
                    'website'     => isset( $location['websiteUri'] ) ? $location['websiteUri'] : '',
                    'status'      => isset( $location['locationState']['isVerified'] ) && $location['locationState']['isVerified'] ? 'verified' : 'unverified',
                    'is_imported' => $is_imported,
                    'wp_location_id' => $is_imported ? $existing_locations[ $location_id ] : 0,
                    'raw'         => $location, // Pour le débogage
                );
            }
        }

        return rest_ensure_response( array(
            'locations' => $formatted_locations,
            'raw'       => $locations, // Pour le débogage
        ) );
    }

    /**
     * Récupère les établissements GMB déjà importés.
     *
     * @since    1.2.0
     * @return   array    Un tableau associatif avec les IDs GMB comme clés et les IDs WordPress comme valeurs.
     */
    private function get_existing_gmb_locations() {
        $existing_locations = array();

        // Récupérer tous les emplacements qui ont des métadonnées GMB
        $args = array(
            'post_type'      => 'boss_local_location',
            'posts_per_page' => -1,
            'meta_query'     => array(
                array(
                    'key'     => '_boss_local_gmb_data',
                    'compare' => 'EXISTS',
                ),
            ),
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();
                $post_id = get_the_ID();
                $gmb_data = get_post_meta( $post_id, '_boss_local_gmb_data', true );

                if ( ! empty( $gmb_data ) && isset( $gmb_data['location_id'] ) ) {
                    $existing_locations[ $gmb_data['location_id'] ] = $post_id;
                }
            }

            wp_reset_postdata();
        }

        return $existing_locations;
    }

    /**
     * Formate l'adresse d'un établissement GMB.
     *
     * @since    1.2.0
     * @param    array    $location    Les données de l'établissement GMB.
     * @return   string                L'adresse formatée.
     */
    private function format_gmb_address( $location ) {
        $address = '';

        if ( isset( $location['address'] ) ) {
            $address_parts = array();

            if ( isset( $location['address']['addressLines'] ) && is_array( $location['address']['addressLines'] ) ) {
                $address_parts[] = implode( ', ', $location['address']['addressLines'] );
            }

            if ( isset( $location['address']['locality'] ) ) {
                $address_parts[] = $location['address']['locality'];
            }

            if ( isset( $location['address']['administrativeArea'] ) ) {
                $address_parts[] = $location['address']['administrativeArea'];
            }

            if ( isset( $location['address']['postalCode'] ) ) {
                $address_parts[] = $location['address']['postalCode'];
            }

            if ( isset( $location['address']['regionCode'] ) ) {
                $address_parts[] = $location['address']['regionCode'];
            }

            $address = implode( ', ', $address_parts );
        }

        return $address;
    }

    /**
     * Importe un établissement Google My Business via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function import_gmb_location( $request ) {
        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            return new WP_Error( 'gmb_not_connected', __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les paramètres
        $account_id = $request->get_param( 'account_id' );
        $location_id = $request->get_param( 'location_id' );

        if ( empty( $account_id ) ) {
            return new WP_Error( 'missing_account_id', __( 'ID de compte manquant.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        if ( empty( $location_id ) ) {
            return new WP_Error( 'missing_location_id', __( 'ID d\'établissement manquant.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Vérifier si l'établissement existe déjà
        $existing_location = $this->get_location_by_gmb_id( $location_id );

        if ( $existing_location ) {
            return new WP_Error( 'location_already_imported', __( 'Cet établissement a déjà été importé.', 'boss-seo' ), array(
                'status' => 400,
                'location_id' => $existing_location->ID
            ) );
        }

        // Importer l'établissement
        $result = $this->import_location_from_gmb( $account_id, $location_id );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        // Récupérer l'emplacement importé
        $location = get_post( $result );

        if ( ! $location ) {
            return new WP_Error( 'location_not_found', __( 'Impossible de récupérer l\'emplacement importé.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array(
            'success'     => true,
            'message'     => __( 'Établissement importé avec succès.', 'boss-seo' ),
            'location_id' => $result,
            'location'    => array(
                'id'      => $location->ID,
                'title'   => $location->post_title,
                'status'  => $location->post_status,
                'link'    => get_edit_post_link( $location->ID, 'raw' ),
                'gmb_data' => get_post_meta( $location->ID, '_boss_local_gmb_data', true ),
            ),
        ) );
    }

    /**
     * Récupère un emplacement par son ID Google My Business.
     *
     * @since    1.2.0
     * @param    string    $gmb_id    L'ID Google My Business.
     * @return   WP_Post|false        Le post de l'emplacement ou false s'il n'existe pas.
     */
    private function get_location_by_gmb_id( $gmb_id ) {
        $args = array(
            'post_type'      => 'boss_local_location',
            'posts_per_page' => 1,
            'meta_query'     => array(
                array(
                    'key'     => '_boss_local_gmb_data',
                    'value'   => $gmb_id,
                    'compare' => 'LIKE',
                ),
            ),
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            return $query->posts[0];
        }

        return false;
    }

    /**
     * Importe un établissement depuis Google My Business.
     *
     * @since    1.2.0
     * @param    string    $account_id     L'ID du compte Google My Business.
     * @param    string    $location_id    L'ID de l'établissement Google My Business.
     * @return   int|WP_Error              L'ID du post créé ou une erreur.
     */
    public function import_location_from_gmb( $account_id, $location_id ) {
        try {
            // Récupérer les détails de l'établissement
            $gmb_location = $this->gmb_api->get_location( $account_id, $location_id );

            if ( is_wp_error( $gmb_location ) ) {
                return $gmb_location;
            }

            // Vérifier si l'établissement existe déjà
            $existing_location = $this->get_location_by_gmb_id( $location_id );

            if ( $existing_location ) {
                return new WP_Error( 'location_already_exists', __( 'Cet établissement a déjà été importé.', 'boss-seo' ) );
            }

            // Préparer les données du post
            $post_data = array(
                'post_title'   => isset( $gmb_location['locationName'] ) ? $gmb_location['locationName'] : __( 'Établissement Google My Business', 'boss-seo' ),
                'post_content' => isset( $gmb_location['profile']['description'] ) ? $gmb_location['profile']['description'] : '',
                'post_status'  => 'publish',
                'post_type'    => 'boss_local_location',
            );

            // Créer le post
            $post_id = wp_insert_post( $post_data );

            if ( is_wp_error( $post_id ) ) {
                return $post_id;
            }

            // Enregistrer les métadonnées GMB
            $gmb_data = array(
                'account_id'  => $account_id,
                'location_id' => $location_id,
                'imported_at' => current_time( 'mysql' ),
                'last_sync'   => current_time( 'mysql' ),
            );

            update_post_meta( $post_id, '_boss_local_gmb_data', $gmb_data );

            // Enregistrer les données de l'emplacement
            $location_data = array(
                'address'     => $this->extract_address_from_gmb( $gmb_location ),
                'phone'       => isset( $gmb_location['primaryPhone'] ) ? $gmb_location['primaryPhone'] : '',
                'email'       => isset( $gmb_location['primaryCategory']['displayName'] ) ? $gmb_location['primaryCategory']['displayName'] : '',
                'website'     => isset( $gmb_location['websiteUri'] ) ? $gmb_location['websiteUri'] : '',
                'latitude'    => isset( $gmb_location['latlng']['latitude'] ) ? $gmb_location['latlng']['latitude'] : 0,
                'longitude'   => isset( $gmb_location['latlng']['longitude'] ) ? $gmb_location['latlng']['longitude'] : 0,
                'hours'       => $this->extract_hours_from_gmb( $gmb_location ),
                'special_hours' => $this->extract_special_hours_from_gmb( $gmb_location ),
            );

            update_post_meta( $post_id, '_boss_local_location_data', $location_data );

            // Enregistrer les catégories
            if ( isset( $gmb_location['primaryCategory']['displayName'] ) ) {
                $category_name = $gmb_location['primaryCategory']['displayName'];
                $term = term_exists( $category_name, 'boss_local_location_type' );

                if ( ! $term ) {
                    $term = wp_insert_term( $category_name, 'boss_local_location_type' );
                }

                if ( ! is_wp_error( $term ) ) {
                    wp_set_post_terms( $post_id, array( $term['term_id'] ), 'boss_local_location_type' );
                }
            }

            // Importer l'image de profil si disponible
            if ( isset( $gmb_location['profile']['coverPhoto']['url'] ) ) {
                $this->import_gmb_image( $post_id, $gmb_location['profile']['coverPhoto']['url'] );
            }

            // Déclencher une action pour les extensions
            do_action( 'boss_local_after_gmb_import', $post_id, $gmb_location );

            return $post_id;
        } catch ( Exception $e ) {
            return new WP_Error( 'gmb_import_error', $e->getMessage() );
        }
    }

    /**
     * Extrait l'adresse d'un établissement GMB.
     *
     * @since    1.2.0
     * @param    array    $gmb_location    Les données de l'établissement GMB.
     * @return   array                     Les données d'adresse formatées.
     */
    private function extract_address_from_gmb( $gmb_location ) {
        $address = array(
            'street'      => '',
            'city'        => '',
            'state'       => '',
            'postal_code' => '',
            'country'     => '',
        );

        if ( isset( $gmb_location['address'] ) ) {
            if ( isset( $gmb_location['address']['addressLines'] ) && is_array( $gmb_location['address']['addressLines'] ) ) {
                $address['street'] = implode( ', ', $gmb_location['address']['addressLines'] );
            }

            if ( isset( $gmb_location['address']['locality'] ) ) {
                $address['city'] = $gmb_location['address']['locality'];
            }

            if ( isset( $gmb_location['address']['administrativeArea'] ) ) {
                $address['state'] = $gmb_location['address']['administrativeArea'];
            }

            if ( isset( $gmb_location['address']['postalCode'] ) ) {
                $address['postal_code'] = $gmb_location['address']['postalCode'];
            }

            if ( isset( $gmb_location['address']['regionCode'] ) ) {
                $address['country'] = $gmb_location['address']['regionCode'];
            }
        }

        return $address;
    }

    /**
     * Extrait les horaires d'ouverture d'un établissement GMB.
     *
     * @since    1.2.0
     * @param    array    $gmb_location    Les données de l'établissement GMB.
     * @return   array                     Les horaires d'ouverture formatés.
     */
    private function extract_hours_from_gmb( $gmb_location ) {
        $hours = array(
            'monday'    => array(),
            'tuesday'   => array(),
            'wednesday' => array(),
            'thursday'  => array(),
            'friday'    => array(),
            'saturday'  => array(),
            'sunday'    => array(),
        );

        if ( isset( $gmb_location['regularHours'] ) && isset( $gmb_location['regularHours']['periods'] ) ) {
            foreach ( $gmb_location['regularHours']['periods'] as $period ) {
                if ( isset( $period['openDay'] ) && isset( $period['openTime'] ) ) {
                    $day = strtolower( $period['openDay'] );

                    if ( isset( $hours[ $day ] ) ) {
                        $open_time = substr( $period['openTime'], 0, 2 ) . ':' . substr( $period['openTime'], 2 );
                        $close_time = isset( $period['closeTime'] ) ? substr( $period['closeTime'], 0, 2 ) . ':' . substr( $period['closeTime'], 2 ) : '23:59';

                        $hours[ $day ][] = array(
                            'open'  => $open_time,
                            'close' => $close_time,
                        );
                    }
                }
            }
        }

        return $hours;
    }

    /**
     * Extrait les horaires spéciaux d'un établissement GMB.
     *
     * @since    1.2.0
     * @param    array    $gmb_location    Les données de l'établissement GMB.
     * @return   array                     Les horaires spéciaux formatés.
     */
    private function extract_special_hours_from_gmb( $gmb_location ) {
        $special_hours = array();

        if ( isset( $gmb_location['specialHours'] ) && isset( $gmb_location['specialHours']['specialHourPeriods'] ) ) {
            foreach ( $gmb_location['specialHours']['specialHourPeriods'] as $period ) {
                if ( isset( $period['startDate'] ) ) {
                    $date = $period['startDate']['year'] . '-' .
                           sprintf( '%02d', $period['startDate']['month'] ) . '-' .
                           sprintf( '%02d', $period['startDate']['day'] );

                    $is_closed = isset( $period['isClosed'] ) && $period['isClosed'];

                    if ( $is_closed ) {
                        $special_hours[ $date ] = array(
                            'closed' => true,
                        );
                    } else if ( isset( $period['openTime'] ) ) {
                        $open_time = substr( $period['openTime'], 0, 2 ) . ':' . substr( $period['openTime'], 2 );
                        $close_time = isset( $period['closeTime'] ) ? substr( $period['closeTime'], 0, 2 ) . ':' . substr( $period['closeTime'], 2 ) : '23:59';

                        $special_hours[ $date ] = array(
                            'closed' => false,
                            'hours'  => array(
                                array(
                                    'open'  => $open_time,
                                    'close' => $close_time,
                                ),
                            ),
                        );
                    }
                }
            }
        }

        return $special_hours;
    }

    /**
     * Importe une image depuis une URL GMB.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID du post.
     * @param    string    $image_url  L'URL de l'image.
     * @return   int|WP_Error          L'ID de l'attachement ou une erreur.
     */
    private function import_gmb_image( $post_id, $image_url ) {
        require_once( ABSPATH . 'wp-admin/includes/image.php' );
        require_once( ABSPATH . 'wp-admin/includes/file.php' );
        require_once( ABSPATH . 'wp-admin/includes/media.php' );

        // Télécharger l'image
        $tmp = download_url( $image_url );

        if ( is_wp_error( $tmp ) ) {
            return $tmp;
        }

        $file_array = array(
            'name'     => basename( $image_url ),
            'tmp_name' => $tmp,
        );

        // Utiliser media_handle_sideload pour ajouter l'image à la médiathèque
        $attachment_id = media_handle_sideload( $file_array, $post_id );

        // Supprimer le fichier temporaire
        @unlink( $tmp );

        if ( is_wp_error( $attachment_id ) ) {
            return $attachment_id;
        }

        // Définir l'image comme image mise en avant
        set_post_thumbnail( $post_id, $attachment_id );

        return $attachment_id;
    }

    /**
     * Synchronise un établissement avec Google My Business via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function sync_gmb_location( $request ) {
        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            return new WP_Error( 'gmb_not_connected', __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les paramètres
        $location_id = $request->get_param( 'location_id' );

        if ( empty( $location_id ) ) {
            return new WP_Error( 'missing_location_id', __( 'ID d\'emplacement manquant.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Synchroniser l'établissement
        $result = $this->sync_location_with_gmb( $location_id );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Établissement synchronisé avec succès.', 'boss-seo' ),
            'result'  => $result,
        ) );
    }

    /**
     * Synchronise un établissement avec Google My Business.
     *
     * @since    1.2.0
     * @param    int    $location_id    L'ID de l'emplacement WordPress.
     * @return   array|WP_Error         Le résultat de la synchronisation ou une erreur.
     */
    public function sync_location_with_gmb( $location_id ) {
        try {
            // Vérifier si l'emplacement existe
            $location = get_post( $location_id );

            if ( ! $location || $location->post_type !== 'boss_local_location' ) {
                return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ) );
            }

            // Récupérer les métadonnées GMB
            $gmb_data = get_post_meta( $location_id, '_boss_local_gmb_data', true );

            if ( empty( $gmb_data ) || ! isset( $gmb_data['account_id'] ) || ! isset( $gmb_data['location_id'] ) ) {
                return new WP_Error( 'gmb_data_missing', __( 'Cet emplacement n\'est pas lié à Google My Business.', 'boss-seo' ) );
            }

            $account_id = $gmb_data['account_id'];
            $gmb_location_id = $gmb_data['location_id'];

            // Récupérer les détails de l'établissement
            $gmb_location = $this->gmb_api->get_location( $account_id, $gmb_location_id );

            if ( is_wp_error( $gmb_location ) ) {
                return $gmb_location;
            }

            // Mettre à jour le titre et le contenu
            $post_data = array(
                'ID'           => $location_id,
                'post_title'   => isset( $gmb_location['locationName'] ) ? $gmb_location['locationName'] : $location->post_title,
                'post_content' => isset( $gmb_location['profile']['description'] ) ? $gmb_location['profile']['description'] : $location->post_content,
            );

            wp_update_post( $post_data );

            // Mettre à jour les données de l'emplacement
            $location_data = get_post_meta( $location_id, '_boss_local_location_data', true );

            if ( ! is_array( $location_data ) ) {
                $location_data = array();
            }

            $location_data = array_merge( $location_data, array(
                'address'     => $this->extract_address_from_gmb( $gmb_location ),
                'phone'       => isset( $gmb_location['primaryPhone'] ) ? $gmb_location['primaryPhone'] : $location_data['phone'],
                'email'       => isset( $gmb_location['primaryCategory']['displayName'] ) ? $gmb_location['primaryCategory']['displayName'] : $location_data['email'],
                'website'     => isset( $gmb_location['websiteUri'] ) ? $gmb_location['websiteUri'] : $location_data['website'],
                'latitude'    => isset( $gmb_location['latlng']['latitude'] ) ? $gmb_location['latlng']['latitude'] : $location_data['latitude'],
                'longitude'   => isset( $gmb_location['latlng']['longitude'] ) ? $gmb_location['latlng']['longitude'] : $location_data['longitude'],
                'hours'       => $this->extract_hours_from_gmb( $gmb_location ),
                'special_hours' => $this->extract_special_hours_from_gmb( $gmb_location ),
            ) );

            update_post_meta( $location_id, '_boss_local_location_data', $location_data );

            // Mettre à jour les métadonnées GMB
            $gmb_data['last_sync'] = current_time( 'mysql' );
            update_post_meta( $location_id, '_boss_local_gmb_data', $gmb_data );

            // Enregistrer les catégories
            if ( isset( $gmb_location['primaryCategory']['displayName'] ) ) {
                $category_name = $gmb_location['primaryCategory']['displayName'];
                $term = term_exists( $category_name, 'boss_local_location_type' );

                if ( ! $term ) {
                    $term = wp_insert_term( $category_name, 'boss_local_location_type' );
                }

                if ( ! is_wp_error( $term ) ) {
                    wp_set_post_terms( $location_id, array( $term['term_id'] ), 'boss_local_location_type' );
                }
            }

            // Importer l'image de profil si disponible et si elle a changé
            if ( isset( $gmb_location['profile']['coverPhoto']['url'] ) ) {
                $this->import_gmb_image( $location_id, $gmb_location['profile']['coverPhoto']['url'] );
            }

            // Déclencher une action pour les extensions
            do_action( 'boss_local_after_gmb_sync', $location_id, $gmb_location );

            return array(
                'location_id' => $location_id,
                'gmb_location_id' => $gmb_location_id,
                'last_sync' => $gmb_data['last_sync'],
            );
        } catch ( Exception $e ) {
            return new WP_Error( 'gmb_sync_error', $e->getMessage() );
        }
    }

    /**
     * Synchronise tous les emplacements avec Google My Business.
     *
     * @since    1.2.0
     * @return   array    Le résultat de la synchronisation.
     */
    public function sync_all_locations() {
        // Vérifier si l'API est connectée
        if ( ! $this->gmb_api->is_connected() ) {
            return array(
                'success' => false,
                'message' => __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ),
            );
        }

        // Récupérer tous les emplacements qui ont des métadonnées GMB
        $args = array(
            'post_type'      => 'boss_local_location',
            'posts_per_page' => -1,
            'meta_query'     => array(
                array(
                    'key'     => '_boss_local_gmb_data',
                    'compare' => 'EXISTS',
                ),
            ),
        );

        $query = new WP_Query( $args );

        $results = array(
            'success' => true,
            'total'   => $query->post_count,
            'synced'  => 0,
            'failed'  => 0,
            'errors'  => array(),
        );

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();
                $post_id = get_the_ID();

                $result = $this->sync_location_with_gmb( $post_id );

                if ( is_wp_error( $result ) ) {
                    $results['failed']++;
                    $results['errors'][] = array(
                        'location_id' => $post_id,
                        'error'       => $result->get_error_message(),
                    );
                } else {
                    $results['synced']++;
                }
            }

            wp_reset_postdata();
        }

        return $results;
    }

    /**
     * Ajoute une métabox GMB à l'écran d'édition d'un emplacement.
     *
     * @since    1.2.0
     * @param    array    $meta_boxes    Les métaboxes existantes.
     * @return   array                   Les métaboxes modifiées.
     */
    public function add_gmb_meta_box( $meta_boxes ) {
        $meta_boxes[] = array(
            'id'       => 'boss_local_gmb_meta_box',
            'title'    => __( 'Google My Business', 'boss-seo' ),
            'context'  => 'side',
            'priority' => 'default',
            'callback' => array( $this, 'render_gmb_meta_box' ),
        );

        return $meta_boxes;
    }

    /**
     * Affiche le contenu de la métabox GMB.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post en cours d'édition.
     */
    public function render_gmb_meta_box( $post ) {
        // Récupérer les métadonnées GMB
        $gmb_data = get_post_meta( $post->ID, '_boss_local_gmb_data', true );

        // Vérifier si l'API est connectée
        $is_connected = $this->gmb_api->is_connected();

        // Afficher le contenu de la métabox
        ?>
        <div class="boss-local-gmb-meta-box">
            <?php if ( ! $is_connected ) : ?>
                <p><?php _e( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ); ?></p>
                <p><a href="<?php echo admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' ); ?>" class="button"><?php _e( 'Configurer l\'API', 'boss-seo' ); ?></a></p>
            <?php elseif ( empty( $gmb_data ) ) : ?>
                <p><?php _e( 'Cet emplacement n\'est pas lié à Google My Business.', 'boss-seo' ); ?></p>
                <p><a href="<?php echo admin_url( 'admin.php?page=boss-seo-local&tab=locations&action=import-gmb' ); ?>" class="button"><?php _e( 'Importer depuis GMB', 'boss-seo' ); ?></a></p>
            <?php else : ?>
                <p><strong><?php _e( 'Lié à Google My Business', 'boss-seo' ); ?></strong></p>
                <p><?php _e( 'Dernière synchronisation :', 'boss-seo' ); ?> <?php echo isset( $gmb_data['last_sync'] ) ? date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $gmb_data['last_sync'] ) ) : __( 'Jamais', 'boss-seo' ); ?></p>
                <p>
                    <button type="button" class="button boss-local-sync-gmb" data-location-id="<?php echo $post->ID; ?>" data-nonce="<?php echo wp_create_nonce( 'boss_seo_local_nonce' ); ?>">
                        <?php _e( 'Synchroniser maintenant', 'boss-seo' ); ?>
                    </button>
                </p>
                <div class="boss-local-gmb-sync-status"></div>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Enregistre les données de la métabox GMB.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID du post.
     * @param    WP_Post   $post       Le post en cours d'édition.
     * @param    bool      $update     True si c'est une mise à jour, false si c'est un nouveau post.
     */
    public function save_gmb_meta_box( $post_id, $post, $update ) {
        // Vérifier si c'est une sauvegarde automatique
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Rien à faire ici, les données GMB sont gérées par les méthodes d'importation et de synchronisation
    }

    /**
     * Ajoute une colonne GMB dans la liste des emplacements.
     *
     * @since    1.2.0
     * @param    array    $columns    Les colonnes existantes.
     * @return   array                Les colonnes modifiées.
     */
    public function add_gmb_admin_column( $columns ) {
        $new_columns = array();

        foreach ( $columns as $key => $value ) {
            $new_columns[ $key ] = $value;

            if ( $key === 'title' ) {
                $new_columns['gmb_status'] = __( 'Google My Business', 'boss-seo' );
            }
        }

        return $new_columns;
    }

    /**
     * Affiche le contenu de la colonne GMB.
     *
     * @since    1.2.0
     * @param    string    $column     Le nom de la colonne.
     * @param    int       $post_id    L'ID du post.
     */
    public function display_gmb_admin_column( $column, $post_id ) {
        if ( $column === 'gmb_status' ) {
            $gmb_data = get_post_meta( $post_id, '_boss_local_gmb_data', true );

            if ( empty( $gmb_data ) ) {
                echo '<span class="boss-local-gmb-status boss-local-gmb-status-not-linked">' . __( 'Non lié', 'boss-seo' ) . '</span>';
            } else {
                $last_sync = isset( $gmb_data['last_sync'] ) ? date_i18n( get_option( 'date_format' ), strtotime( $gmb_data['last_sync'] ) ) : __( 'Jamais', 'boss-seo' );
                echo '<span class="boss-local-gmb-status boss-local-gmb-status-linked">' . __( 'Lié', 'boss-seo' ) . '</span>';
                echo '<br><small>' . __( 'Dernière sync :', 'boss-seo' ) . ' ' . $last_sync . '</small>';
            }
        }
    }
}
