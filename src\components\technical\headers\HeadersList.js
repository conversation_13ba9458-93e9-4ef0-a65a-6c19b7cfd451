import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  CheckboxControl,
  TextControl,
  Dashicon,
  Modal
} from '@wordpress/components';

const HeadersList = ({ 
  headers, 
  onEdit, 
  onDelete, 
  onToggle,
  searchQuery,
  setSearchQuery
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [headerToDelete, setHeaderToDelete] = useState(null);
  
  // Fonction pour confirmer la suppression
  const confirmDelete = (header) => {
    setHeaderToDelete(header);
    setShowDeleteConfirm(true);
  };
  
  // Fonction pour effectuer la suppression
  const handleDelete = () => {
    if (headerToDelete) {
      onDelete(headerToDelete.id);
      setShowDeleteConfirm(false);
      setHeaderToDelete(null);
    }
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du type
  const getTypeColorClass = (type) => {
    switch (type) {
      case 'security':
        return 'boss-bg-red-100 boss-text-red-800';
      case 'cache':
        return 'boss-bg-blue-100 boss-text-blue-800';
      case 'performance':
        return 'boss-bg-green-100 boss-text-green-800';
      case 'cors':
        return 'boss-bg-purple-100 boss-text-purple-800';
      case 'custom':
        return 'boss-bg-gray-100 boss-text-gray-800';
      default:
        return 'boss-bg-gray-100 boss-text-gray-800';
    }
  };
  
  // Fonction pour obtenir le texte du type
  const getTypeText = (type) => {
    switch (type) {
      case 'security':
        return __('Sécurité', 'boss-seo');
      case 'cache':
        return __('Cache', 'boss-seo');
      case 'performance':
        return __('Performance', 'boss-seo');
      case 'cors':
        return __('CORS', 'boss-seo');
      case 'custom':
        return __('Personnalisé', 'boss-seo');
      default:
        return type;
    }
  };
  
  // Filtrer les en-têtes
  const filteredHeaders = headers.filter(header => {
    return searchQuery === '' || 
      header.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      header.value.toLowerCase().includes(searchQuery.toLowerCase());
  });

  return (
    <>
      <Card className="boss-mb-6">
        <CardHeader className="boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('En-têtes HTTP configurés', 'boss-seo')}
            </h2>
            <Button
              isPrimary
              onClick={() => onEdit(null)}
            >
              {__('Ajouter un en-tête', 'boss-seo')}
            </Button>
          </div>
        </CardHeader>
        <CardBody className="boss-p-4">
          <div className="boss-mb-6">
            <TextControl
              placeholder={__('Rechercher des en-têtes...', 'boss-seo')}
              value={searchQuery}
              onChange={setSearchQuery}
            />
          </div>
          
          <div className="boss-overflow-x-auto">
            <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
              <thead className="boss-bg-gray-50">
                <tr>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Nom', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Valeur', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Type', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Actif', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-right boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Actions', 'boss-seo')}
                  </th>
                </tr>
              </thead>
              <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                {filteredHeaders.length === 0 ? (
                  <tr>
                    <td colSpan="5" className="boss-px-6 boss-py-4 boss-text-center boss-text-boss-gray">
                      {__('Aucun en-tête trouvé.', 'boss-seo')}
                    </td>
                  </tr>
                ) : (
                  filteredHeaders.map(header => (
                    <tr key={header.id} className="boss-hover:boss-bg-gray-50">
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-font-medium boss-text-boss-dark">{header.name}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4">
                        <div className="boss-text-boss-gray boss-truncate boss-max-w-xs">{header.value}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <span className={`boss-px-2 boss-py-1 boss-text-xs boss-rounded-full ${getTypeColorClass(header.type)}`}>
                          {getTypeText(header.type)}
                        </span>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <CheckboxControl
                          checked={header.active}
                          onChange={() => onToggle(header.id)}
                          label=""
                        />
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-right">
                        <div className="boss-flex boss-justify-end boss-space-x-2">
                          <Button
                            isSecondary
                            isSmall
                            onClick={() => onEdit(header)}
                          >
                            <Dashicon icon="edit" />
                          </Button>
                          <Button
                            isDestructive
                            isSmall
                            onClick={() => confirmDelete(header)}
                          >
                            <Dashicon icon="trash" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardBody>
        <CardFooter className="boss-border-t boss-border-gray-200">
          <div className="boss-text-boss-gray boss-text-sm">
            {filteredHeaders.length} {__('en-têtes trouvés', 'boss-seo')}
          </div>
        </CardFooter>
      </Card>
      
      {/* Modal de confirmation de suppression */}
      {showDeleteConfirm && headerToDelete && (
        <Modal
          title={__('Confirmer la suppression', 'boss-seo')}
          onRequestClose={() => setShowDeleteConfirm(false)}
        >
          <div className="boss-p-6">
            <p className="boss-mb-6">
              {__('Êtes-vous sûr de vouloir supprimer l\'en-tête', 'boss-seo')} <strong>{headerToDelete.name}</strong> ?
              {__('Cette action est irréversible.', 'boss-seo')}
            </p>
            
            <div className="boss-flex boss-justify-end boss-space-x-3">
              <Button
                isSecondary
                onClick={() => setShowDeleteConfirm(false)}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                isDanger
                onClick={handleDelete}
              >
                {__('Supprimer', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default HeadersList;
