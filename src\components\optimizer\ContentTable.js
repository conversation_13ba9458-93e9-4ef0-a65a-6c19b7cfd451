import { __ } from '@wordpress/i18n';
import {
  Button,
  CheckboxControl,
  Dashicon,
  Dropdown,
  Tooltip
} from '@wordpress/components';

/**
 * Composant pour afficher le tableau des contenus
 */
const ContentTable = ({
  items,
  selectedItems,
  onSelectItem,
  onSelectAll,
  selectAll,
  onItemClick,
  onOptimize,
  onAnalyze,
  optimizingItems = [],
  visibleColumns,
  currentPage,
  totalPages,
  itemsPerPage,
  totalItems,
  onPageChange,
  onItemsPerPageChange
}) => {
  // Fonction pour obtenir la classe de couleur en fonction du score SEO
  const getSeoScoreColorClass = (score) => {
    if (score >= 80) return 'boss-bg-green-100 boss-text-green-800';
    if (score >= 60) return 'boss-bg-yellow-100 boss-text-yellow-800';
    if (score >= 40) return 'boss-bg-orange-100 boss-text-orange-800';
    return 'boss-bg-red-100 boss-text-red-800';
  };

  // Fonction pour obtenir le libellé du statut
  const getStatusLabel = (status) => {
    switch (status) {
      case 'publish':
        return __('Publié', 'boss-seo');
      case 'draft':
        return __('Brouillon', 'boss-seo');
      case 'pending':
        return __('En attente', 'boss-seo');
      case 'private':
        return __('Privé', 'boss-seo');
      default:
        return status;
    }
  };

  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColorClass = (status) => {
    switch (status) {
      case 'publish':
        return 'boss-bg-green-100 boss-text-green-800';
      case 'draft':
        return 'boss-bg-gray-100 boss-text-gray-800';
      case 'pending':
        return 'boss-bg-yellow-100 boss-text-yellow-800';
      case 'private':
        return 'boss-bg-purple-100 boss-text-purple-800';
      default:
        return 'boss-bg-gray-100 boss-text-gray-800';
    }
  };

  // Fonction pour obtenir le libellé du type de contenu
  const getContentTypeLabel = (type) => {
    switch (type) {
      case 'post':
        return __('Article', 'boss-seo');
      case 'page':
        return __('Page', 'boss-seo');
      case 'product':
        return __('Produit', 'boss-seo');
      case 'portfolio':
        return __('Portfolio', 'boss-seo');
      default:
        return type;
    }
  };

  // Fonction pour formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Générer les options de pagination
  const renderPagination = () => {
    const pages = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Bouton précédent
    pages.push(
      <Button
        key="prev"
        isSmall
        isSecondary
        disabled={currentPage === 1}
        onClick={() => onPageChange(currentPage - 1)}
        className="boss-mr-1"
      >
        <Dashicon icon="arrow-left-alt2" />
      </Button>
    );

    // Première page
    if (startPage > 1) {
      pages.push(
        <Button
          key="1"
          isSmall
          isSecondary={currentPage !== 1}
          isPrimary={currentPage === 1}
          onClick={() => onPageChange(1)}
          className="boss-mr-1"
        >
          1
        </Button>
      );

      if (startPage > 2) {
        pages.push(
          <span key="ellipsis1" className="boss-px-2 boss-text-boss-gray">...</span>
        );
      }
    }

    // Pages numérotées
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <Button
          key={i}
          isSmall
          isSecondary={currentPage !== i}
          isPrimary={currentPage === i}
          onClick={() => onPageChange(i)}
          className="boss-mr-1"
        >
          {i}
        </Button>
      );
    }

    // Dernière page
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(
          <span key="ellipsis2" className="boss-px-2 boss-text-boss-gray">...</span>
        );
      }

      pages.push(
        <Button
          key={totalPages}
          isSmall
          isSecondary={currentPage !== totalPages}
          isPrimary={currentPage === totalPages}
          onClick={() => onPageChange(totalPages)}
          className="boss-mr-1"
        >
          {totalPages}
        </Button>
      );
    }

    // Bouton suivant
    pages.push(
      <Button
        key="next"
        isSmall
        isSecondary
        disabled={currentPage === totalPages}
        onClick={() => onPageChange(currentPage + 1)}
        className="boss-ml-1"
      >
        <Dashicon icon="arrow-right-alt2" />
      </Button>
    );

    return pages;
  };

  return (
    <div className="boss-overflow-hidden">
      <table className="boss-w-full boss-border-collapse">
        <thead>
          <tr className="boss-bg-gray-50 boss-border-b boss-border-gray-200">
            <th className="boss-p-3 boss-text-left boss-w-10">
              <CheckboxControl
                checked={selectAll}
                onChange={onSelectAll}
                className="boss-m-0"
              />
            </th>

            {visibleColumns.title && (
              <th className="boss-p-3 boss-text-left boss-font-medium boss-text-boss-gray">
                {__('Titre', 'boss-seo')}
              </th>
            )}

            {visibleColumns.type && (
              <th className="boss-p-3 boss-text-left boss-font-medium boss-text-boss-gray boss-whitespace-nowrap">
                {__('Type', 'boss-seo')}
              </th>
            )}

            {visibleColumns.seoScore && (
              <th className="boss-p-3 boss-text-center boss-font-medium boss-text-boss-gray boss-whitespace-nowrap">
                {__('Score SEO', 'boss-seo')}
              </th>
            )}

            {visibleColumns.status && (
              <th className="boss-p-3 boss-text-left boss-font-medium boss-text-boss-gray boss-whitespace-nowrap">
                {__('Statut', 'boss-seo')}
              </th>
            )}

            {visibleColumns.date && (
              <th className="boss-p-3 boss-text-left boss-font-medium boss-text-boss-gray boss-whitespace-nowrap">
                {__('Date', 'boss-seo')}
              </th>
            )}

            {visibleColumns.author && (
              <th className="boss-p-3 boss-text-left boss-font-medium boss-text-boss-gray boss-whitespace-nowrap">
                {__('Auteur', 'boss-seo')}
              </th>
            )}

            {visibleColumns.actions && (
              <th className="boss-p-3 boss-text-right boss-font-medium boss-text-boss-gray boss-whitespace-nowrap">
                {__('Actions', 'boss-seo')}
              </th>
            )}
          </tr>
        </thead>

        <tbody>
          {items.length === 0 ? (
            <tr>
              <td colSpan={Object.values(visibleColumns).filter(Boolean).length + 1} className="boss-p-6 boss-text-center boss-text-boss-gray">
                {__('Aucun contenu trouvé.', 'boss-seo')}
              </td>
            </tr>
          ) : (
            items.map((item) => (
              <tr
                key={item.id}
                className="boss-border-b boss-border-gray-200 boss-hover:boss-bg-gray-50 boss-transition-colors boss-duration-150"
              >
                <td className="boss-p-3">
                  <CheckboxControl
                    checked={selectedItems.includes(item.id)}
                    onChange={() => onSelectItem(item.id)}
                    className="boss-m-0"
                  />
                </td>

                {visibleColumns.title && (
                  <td className="boss-p-3">
                    <button
                      className="boss-text-left boss-font-medium boss-text-boss-primary boss-hover:boss-text-boss-primary-dark boss-hover:boss-underline boss-focus:boss-outline-none"
                      onClick={() => onItemClick(item)}
                    >
                      {item.title}
                    </button>
                    <p className="boss-text-sm boss-text-boss-gray boss-mt-1 boss-truncate boss-max-w-md">
                      {item.excerpt}
                    </p>
                  </td>
                )}

                {visibleColumns.type && (
                  <td className="boss-p-3 boss-text-sm boss-text-boss-gray">
                    {getContentTypeLabel(item.type)}
                  </td>
                )}

                {visibleColumns.seoScore && (
                  <td className="boss-p-3 boss-text-center">
                    <span className={`boss-inline-flex boss-items-center boss-justify-center boss-w-10 boss-h-10 boss-rounded-full ${getSeoScoreColorClass(item.seo_score)} boss-font-medium`}>
                      {item.seo_score}
                    </span>
                  </td>
                )}

                {visibleColumns.status && (
                  <td className="boss-p-3">
                    <span className={`boss-inline-flex boss-items-center boss-px-2.5 boss-py-0.5 boss-rounded-full boss-text-xs boss-font-medium ${getStatusColorClass(item.status)}`}>
                      {getStatusLabel(item.status)}
                    </span>
                  </td>
                )}

                {visibleColumns.date && (
                  <td className="boss-p-3 boss-text-sm boss-text-boss-gray">
                    {formatDate(item.date)}
                  </td>
                )}

                {visibleColumns.author && (
                  <td className="boss-p-3 boss-text-sm boss-text-boss-gray">
                    {item.author}
                  </td>
                )}

                {visibleColumns.actions && (
                  <td className="boss-p-3 boss-text-right boss-whitespace-nowrap">
                    <Tooltip text={__('Optimiser', 'boss-seo')}>
                      <Button
                        isSecondary
                        isSmall
                        onClick={() => onOptimize(item.id)}
                        className="boss-mr-1"
                        icon="performance"
                        isBusy={optimizingItems.includes(item.id)}
                        disabled={optimizingItems.includes(item.id)}
                      />
                    </Tooltip>

                    <Tooltip text={__('Éditer', 'boss-seo')}>
                      <Button
                        isSecondary
                        isSmall
                        icon="edit"
                        className="boss-mr-1"
                        onClick={() => window.open(item.url, '_blank')}
                      />
                    </Tooltip>

                    <Tooltip text={__('Analyser', 'boss-seo')}>
                      <Button
                        isSecondary
                        isSmall
                        icon="search"
                        onClick={() => {
                          onAnalyze(item.id);
                          onItemClick(item);
                        }}
                        isBusy={optimizingItems.includes(item.id)}
                        disabled={optimizingItems.includes(item.id)}
                      />
                    </Tooltip>
                  </td>
                )}
              </tr>
            ))
          )}
        </tbody>
      </table>

      {/* Pagination */}
      <div className="boss-flex boss-justify-between boss-items-center boss-p-4 boss-border-t boss-border-gray-200">
        <div className="boss-flex boss-items-center boss-text-sm boss-text-boss-gray">
          <span className="boss-mr-2">
            {__('Afficher', 'boss-seo')}
          </span>
          <select
            value={itemsPerPage}
            onChange={(e) => onItemsPerPageChange(e.target.value)}
            className="boss-border boss-border-gray-300 boss-rounded boss-px-2 boss-py-1 boss-mr-2 boss-bg-white boss-text-boss-dark"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
          <span>
            {__('éléments par page', 'boss-seo')} | {__('Total :', 'boss-seo')} {totalItems} {__('éléments', 'boss-seo')}
          </span>
        </div>

        <div className="boss-flex boss-items-center">
          {renderPagination()}
        </div>
      </div>
    </div>
  );
};

export default ContentTable;
