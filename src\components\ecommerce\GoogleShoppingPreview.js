import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  Dashicon,
  Notice,
  Spinner,
  TabPanel
} from '@wordpress/components';

// Importer les sous-composants
import ProductSelector from './shopping/ProductSelector';
import ProductSettings from './shopping/ProductSettings';
import ProductPreview from './shopping/ProductPreview';
import FeedSettings from './shopping/FeedSettings';
import FeedList from './shopping/FeedList';

// Importer le service
import EcommerceService from '../../services/EcommerceService';

const GoogleShoppingPreview = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [feeds, setFeeds] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [activeTab, setActiveTab] = useState('products');

  // États pour les paramètres
  const [productSettings, setProductSettings] = useState({
    title: '',
    description: '',
    gtin: '',
    mpn: '',
    brand: '',
    condition: 'new',
    availability: 'in_stock',
    shipping: '',
    includeInShopping: true,
    enableDynamicRemarketing: false
  });

  const [feedSettings, setFeedSettings] = useState({
    name: 'Flux principal',
    merchantId: '',
    country: 'FR',
    language: 'fr',
    currency: 'EUR',
    includeAll: true,
    includeInStock: true,
    includeWithImages: true,
    includeWithPrice: true,
    autoUpdate: true
  });

  // Créer une instance du service
  const ecommerceService = new EcommerceService();

  // Charger les données
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Récupérer les produits
        const productsResponse = await ecommerceService.getProducts();

        // Récupérer les flux Google Shopping
        const feedsResponse = await ecommerceService.getShoppingFeeds();

        // Extraire les catégories uniques des produits
        const uniqueCategories = [];
        const categoryIds = new Set();

        productsResponse.products.forEach(product => {
          if (product.categoryId && !categoryIds.has(product.categoryId)) {
            categoryIds.add(product.categoryId);
            uniqueCategories.push({
              id: product.categoryId,
              name: product.category
            });
          }
        });

        // Trier les catégories par nom
        uniqueCategories.sort((a, b) => a.name.localeCompare(b.name));

        // Mettre à jour les états
        setCategories(uniqueCategories);
        setProducts(productsResponse.products || []);
        setFeeds(feedsResponse.feeds || []);
      } catch (err) {
        console.error('Erreur lors du chargement des données:', err);

        // Vérifier si c'est une erreur spécifique de WooCommerce
        if (err && err.code) {
          if (err.code === 'woocommerce_not_available') {
            // WooCommerce n'est pas installé ou activé
            setError(__('WooCommerce n\'est pas installé ou activé. Veuillez installer et activer WooCommerce pour utiliser cette fonctionnalité.', 'boss-seo'));
          } else if (err.code === 'no_products_available') {
            // Aucun produit n'est disponible
            setError(__('Aucun produit n\'est disponible dans WooCommerce. Veuillez ajouter des produits pour utiliser cette fonctionnalité.', 'boss-seo'));
          } else {
            // Autre erreur
            setError(err.message || __('Erreur lors du chargement des données. Veuillez réessayer.', 'boss-seo'));
          }
        } else {
          // Erreur générique
          setError(__('Erreur lors du chargement des données. Veuillez réessayer.', 'boss-seo'));
        }

        // Réinitialiser les états
        setCategories([]);
        setProducts([]);
        setFeeds([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Effet pour mettre à jour les paramètres du produit lorsqu'un produit est sélectionné
  useEffect(() => {
    if (selectedProduct) {
      setProductSettings({
        title: selectedProduct.name,
        description: selectedProduct.description,
        gtin: selectedProduct.gtin || '',
        mpn: selectedProduct.mpn || '',
        brand: selectedProduct.brand || '',
        condition: selectedProduct.condition || 'new',
        availability: selectedProduct.availability || 'in_stock',
        shipping: selectedProduct.shipping || '',
        includeInShopping: selectedProduct.isInShopping || false,
        enableDynamicRemarketing: false
      });
    }
  }, [selectedProduct]);

  // Fonction pour filtrer les produits
  const getFilteredProducts = () => {
    return products.filter(product => {
      // Filtrer par catégorie
      const matchesCategory = selectedCategory === 'all' || product.categoryId === parseInt(selectedCategory);

      // Filtrer par recherche
      const matchesSearch = searchQuery === '' ||
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.category.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesCategory && matchesSearch;
    });
  };

  // Obtenir les produits filtrés
  const filteredProducts = getFilteredProducts();

  // Fonction pour enregistrer les paramètres du produit
  const handleSaveProduct = async () => {
    if (!selectedProduct) return;

    try {
      setIsSaving(true);
      setError(null);

      // Préparer les données du produit
      const productData = {
        id: selectedProduct.id,
        name: productSettings.title,
        description: productSettings.description,
        gtin: productSettings.gtin,
        mpn: productSettings.mpn,
        brand: productSettings.brand,
        condition: productSettings.condition,
        availability: productSettings.availability,
        shipping: productSettings.shipping,
        isInShopping: productSettings.includeInShopping,
        enableDynamicRemarketing: productSettings.enableDynamicRemarketing
      };

      // Appeler le service pour mettre à jour le produit
      const response = await ecommerceService.updateProduct(selectedProduct.id, productData);

      // Mettre à jour les produits dans l'état local
      const updatedProducts = products.map(product => {
        if (product.id === selectedProduct.id) {
          return {
            ...product,
            name: productSettings.title,
            description: productSettings.description,
            gtin: productSettings.gtin,
            mpn: productSettings.mpn,
            brand: productSettings.brand,
            condition: productSettings.condition,
            availability: productSettings.availability,
            shipping: productSettings.shipping,
            isInShopping: productSettings.includeInShopping
          };
        }
        return product;
      });

      // Mettre à jour l'état
      setProducts(updatedProducts);
      setSelectedProduct({
        ...selectedProduct,
        name: productSettings.title,
        description: productSettings.description,
        gtin: productSettings.gtin,
        mpn: productSettings.mpn,
        brand: productSettings.brand,
        condition: productSettings.condition,
        availability: productSettings.availability,
        shipping: productSettings.shipping,
        isInShopping: productSettings.includeInShopping
      });

      // Afficher le message de succès
      setSuccessMessage(__('Les paramètres du produit ont été enregistrés avec succès !', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (err) {
      console.error('Erreur lors de l\'enregistrement des paramètres du produit:', err);
      setError(__('Erreur lors de l\'enregistrement des paramètres du produit. Veuillez réessayer.', 'boss-seo'));

      // Simuler l'enregistrement en cas d'erreur (pour le développement)
      // À supprimer en production
      const updatedProducts = products.map(product => {
        if (product.id === selectedProduct.id) {
          return {
            ...product,
            name: productSettings.title,
            description: productSettings.description,
            gtin: productSettings.gtin,
            mpn: productSettings.mpn,
            brand: productSettings.brand,
            condition: productSettings.condition,
            availability: productSettings.availability,
            shipping: productSettings.shipping,
            isInShopping: productSettings.includeInShopping
          };
        }
        return product;
      });

      setProducts(updatedProducts);
      setSelectedProduct({
        ...selectedProduct,
        name: productSettings.title,
        description: productSettings.description,
        gtin: productSettings.gtin,
        mpn: productSettings.mpn,
        brand: productSettings.brand,
        condition: productSettings.condition,
        availability: productSettings.availability,
        shipping: productSettings.shipping,
        isInShopping: productSettings.includeInShopping
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Fonction pour générer un flux
  const handleGenerateFeed = async () => {
    try {
      setIsGenerating(true);
      setError(null);

      // Appeler le service pour créer un nouveau flux
      const response = await ecommerceService.createShoppingFeed(feedSettings);

      // Générer le flux
      await ecommerceService.generateShoppingFeed(response.feed.id);

      // Récupérer la liste mise à jour des flux
      const feedsResponse = await ecommerceService.getShoppingFeeds();

      // Mettre à jour l'état
      setFeeds(feedsResponse.feeds || []);

      // Afficher le message de succès
      setSuccessMessage(__('Le flux a été généré avec succès !', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);

      // Passer à l'onglet des flux
      setActiveTab('feeds');
    } catch (err) {
      console.error('Erreur lors de la génération du flux:', err);
      setError(__('Erreur lors de la génération du flux. Veuillez réessayer.', 'boss-seo'));

      // Simuler la génération en cas d'erreur (pour le développement)
      // À supprimer en production
      const newFeed = {
        name: feedSettings.name,
        country: feedSettings.country,
        language: feedSettings.language,
        currency: feedSettings.currency,
        productCount: products.filter(p => p.isInShopping).length,
        status: 'active',
        lastUpdated: new Date().toISOString().split('T')[0],
        url: `https://example.com/feeds/${feedSettings.name.toLowerCase().replace(/\s+/g, '-')}.xml`
      };

      setFeeds([...feeds, newFeed]);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {error && (
            <div className="boss-text-center boss-p-8 boss-bg-white boss-rounded-lg boss-shadow boss-mb-6">
              <Dashicon icon="warning" size={36} className="boss-text-yellow-500 boss-mb-4" />
              <h2 className="boss-text-xl boss-font-bold boss-mb-2">{__('Attention', 'boss-seo')}</h2>
              <p className="boss-text-gray-600 boss-mb-4">{error}</p>
              {error.includes('WooCommerce') && (
                <a
                  href="https://wordpress.org/plugins/woocommerce/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="boss-inline-block boss-bg-blue-500 boss-text-white boss-px-4 boss-py-2 boss-rounded boss-hover:boss-bg-blue-600 boss-transition"
                >
                  {__('Installer WooCommerce', 'boss-seo')}
                </a>
              )}
            </div>
          )}

          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {successMessage}
            </Notice>
          )}

          {!error && (
            <TabPanel
              className="boss-mb-6"
              activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
              onSelect={(tabName) => setActiveTab(tabName)}
              tabs={[
                {
                  name: 'products',
                  title: __('Produits', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                },
                {
                  name: 'feeds',
                  title: __('Flux', 'boss-seo'),
                  className: 'boss-font-medium boss-px-4 boss-py-2'
                }
              ]}
            >
            {(tab) => {
              if (tab.name === 'products') {
                return (
                  <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
                    {/* Panneau de gauche */}
                    <div>
                      <ProductSelector
                        searchQuery={searchQuery}
                        setSearchQuery={setSearchQuery}
                        selectedCategory={selectedCategory}
                        setSelectedCategory={setSelectedCategory}
                        categories={categories}
                        filteredProducts={filteredProducts}
                        selectedProduct={selectedProduct}
                        setSelectedProduct={setSelectedProduct}
                      />

                      {selectedProduct && (
                        <ProductSettings
                          selectedProduct={selectedProduct}
                          productSettings={productSettings}
                          setProductSettings={setProductSettings}
                          handleSaveProduct={handleSaveProduct}
                          isSaving={isSaving}
                        />
                      )}
                    </div>

                    {/* Panneau de droite */}
                    <div className="lg:boss-col-span-2">
                      {selectedProduct ? (
                        <div>
                          <ProductPreview product={selectedProduct} />

                          <Card>
                            <CardBody>
                              <div className="boss-space-y-4">
                                <h3 className="boss-text-md boss-font-semibold">
                                  {__('Conseils pour Google Shopping', 'boss-seo')}
                                </h3>

                                <div className="boss-space-y-2 boss-text-sm boss-text-boss-gray">
                                  <p>
                                    <strong>{__('Titres optimisés:', 'boss-seo')}</strong> {__('Incluez des mots-clés pertinents, la marque et les caractéristiques principales.', 'boss-seo')}
                                  </p>
                                  <p>
                                    <strong>{__('Images de qualité:', 'boss-seo')}</strong> {__('Utilisez des images de haute qualité sur fond blanc.', 'boss-seo')}
                                  </p>
                                  <p>
                                    <strong>{__('GTIN/MPN:', 'boss-seo')}</strong> {__('Ajoutez ces identifiants pour améliorer la visibilité.', 'boss-seo')}
                                  </p>
                                  <p>
                                    <strong>{__('Descriptions détaillées:', 'boss-seo')}</strong> {__('Décrivez précisément votre produit avec ses caractéristiques.', 'boss-seo')}
                                  </p>
                                  <p>
                                    <strong>{__('Prix compétitifs:', 'boss-seo')}</strong> {__('Surveillez régulièrement vos concurrents.', 'boss-seo')}
                                  </p>
                                </div>

                                <div className="boss-mt-4">
                                  <a
                                    href="https://support.google.com/merchants/answer/6324350"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="boss-text-boss-primary boss-underline"
                                  >
                                    {__('En savoir plus sur les bonnes pratiques Google Shopping', 'boss-seo')}
                                  </a>
                                </div>
                              </div>
                            </CardBody>
                          </Card>
                        </div>
                      ) : (
                        <Card>
                          <CardBody>
                            <div className="boss-text-center boss-py-8">
                              <div className="boss-text-5xl boss-text-boss-gray boss-mb-4">
                                <Dashicon icon="cart" />
                              </div>
                              <h2 className="boss-text-xl boss-font-bold boss-mb-4">
                                {__('Sélectionnez un produit', 'boss-seo')}
                              </h2>
                              <p className="boss-text-boss-gray boss-mb-6">
                                {__('Veuillez sélectionner un produit pour configurer ses paramètres Google Shopping.', 'boss-seo')}
                              </p>
                            </div>
                          </CardBody>
                        </Card>
                      )}
                    </div>
                  </div>
                );
              } else if (tab.name === 'feeds') {
                return (
                  <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
                    {/* Panneau de gauche */}
                    <div>
                      <FeedSettings
                        feedSettings={feedSettings}
                        setFeedSettings={setFeedSettings}
                        handleGenerateFeed={handleGenerateFeed}
                        isGenerating={isGenerating}
                      />
                    </div>

                    {/* Panneau de droite */}
                    <div className="lg:boss-col-span-2">
                      <FeedList feeds={feeds} />
                    </div>
                  </div>
                );
              }
            }}
          </TabPanel>
          )}
        </div>
      )}
    </div>
  );
};

export default GoogleShoppingPreview;
