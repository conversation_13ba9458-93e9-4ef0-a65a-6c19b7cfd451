import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  __experimentalHeading as Heading,
  __experimentalText as Text,
  __experimentalSpacer as Spacer,
  Flex,
  <PERSON>lexItem,
  FlexBlock,
  Button,
  Notice,
} from '@wordpress/components';
import { info, external } from '@wordpress/icons';

/**
 * Composant pour afficher les Core Web Vitals améliorés
 */
const CoreWebVitalsCard = ({ data, isLoading, onRefresh }) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Heading level={3}>
            {__('Core Web Vitals', 'boss-seo')}
          </Heading>
        </CardHeader>
        <CardBody>
          <Text>{__('Chargement des métriques...', 'boss-seo')}</Text>
        </CardBody>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <Heading level={3}>
            {__('Core Web Vitals', 'boss-seo')}
          </Heading>
        </CardHeader>
        <CardBody>
          <Text>{__('Aucune donnée disponible', 'boss-seo')}</Text>
          {onRefresh && (
            <Button isPrimary onClick={onRefresh} style={{ marginTop: '16px' }}>
              {__('Actualiser les données', 'boss-seo')}
            </Button>
          )}
        </CardBody>
      </Card>
    );
  }

  /**
   * Retourne la couleur selon le statut de la métrique
   */
  const getStatusColor = (status) => {
    switch (status) {
      case 'good':
        return '#00a32a';
      case 'needs-improvement':
        return '#dba617';
      case 'poor':
        return '#d63638';
      default:
        return '#666';
    }
  };

  /**
   * Retourne le texte du statut en français
   */
  const getStatusText = (status) => {
    switch (status) {
      case 'good':
        return __('Bon', 'boss-seo');
      case 'needs-improvement':
        return __('À améliorer', 'boss-seo');
      case 'poor':
        return __('Mauvais', 'boss-seo');
      default:
        return __('Inconnu', 'boss-seo');
    }
  };

  /**
   * Retourne les informations détaillées sur une métrique
   */
  const getMetricInfo = (key) => {
    const info = {
      lcp: {
        title: __('Largest Contentful Paint', 'boss-seo'),
        description: __('Temps de chargement du plus grand élément visible', 'boss-seo'),
        impact: __('Impact direct sur l\'expérience utilisateur et le SEO', 'boss-seo'),
        threshold: __('Bon: ≤ 2.5s, À améliorer: ≤ 4.0s', 'boss-seo')
      },
      inp: {
        title: __('Interaction to Next Paint', 'boss-seo'),
        description: __('Temps de réponse aux interactions utilisateur', 'boss-seo'),
        impact: __('Remplace FID depuis 2024, crucial pour l\'interactivité', 'boss-seo'),
        threshold: __('Bon: ≤ 200ms, À améliorer: ≤ 500ms', 'boss-seo')
      },
      fid: {
        title: __('First Input Delay', 'boss-seo'),
        description: __('Délai de première interaction (obsolète)', 'boss-seo'),
        impact: __('Remplacé par INP, maintenu pour compatibilité', 'boss-seo'),
        threshold: __('Bon: ≤ 100ms, À améliorer: ≤ 300ms', 'boss-seo')
      },
      cls: {
        title: __('Cumulative Layout Shift', 'boss-seo'),
        description: __('Stabilité visuelle de la page', 'boss-seo'),
        impact: __('Évite les décalages visuels inattendus', 'boss-seo'),
        threshold: __('Bon: ≤ 0.1, À améliorer: ≤ 0.25', 'boss-seo')
      },
      ttfb: {
        title: __('Time to First Byte', 'boss-seo'),
        description: __('Temps de réponse du serveur', 'boss-seo'),
        impact: __('Fondamental pour toutes les autres métriques', 'boss-seo'),
        threshold: __('Bon: ≤ 800ms, À améliorer: ≤ 1800ms', 'boss-seo')
      },
      fcp: {
        title: __('First Contentful Paint', 'boss-seo'),
        description: __('Premier élément visible affiché', 'boss-seo'),
        impact: __('Première impression de vitesse pour l\'utilisateur', 'boss-seo'),
        threshold: __('Bon: ≤ 1.8s, À améliorer: ≤ 3.0s', 'boss-seo')
      }
    };

    return info[key] || {};
  };

  // Séparer les Core Web Vitals des autres métriques
  const coreVitals = ['lcp', 'inp', 'cls'];
  const otherMetrics = ['ttfb', 'fcp', 'fid'];

  const coreVitalsData = Object.fromEntries(
    Object.entries(data).filter(([key]) => coreVitals.includes(key))
  );

  const otherMetricsData = Object.fromEntries(
    Object.entries(data).filter(([key]) => otherMetrics.includes(key))
  );

  // Calculer le score global
  const scores = Object.values(coreVitalsData).map(metric => metric.score || 0);
  const globalScore = scores.length > 0 ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : 0;

  return (
    <Card>
      <CardHeader>
        <Flex align="center" justify="space-between">
          <FlexItem>
            <Heading level={3}>
              {__('Core Web Vitals', 'boss-seo')}
            </Heading>
          </FlexItem>
          <FlexItem>
            <div
              style={{
                fontSize: '1.5em',
                fontWeight: 'bold',
                color: getStatusColor(globalScore >= 75 ? 'good' : globalScore >= 50 ? 'needs-improvement' : 'poor')
              }}
            >
              {globalScore}/100
            </div>
          </FlexItem>
        </Flex>
      </CardHeader>
      <CardBody>
        {/* Notice INP */}
        {data.inp && (
          <Notice status="info" isDismissible={false}>
            <strong>{__('Nouveau:', 'boss-seo')}</strong> {__('INP remplace FID comme métrique Core Web Vitals depuis mars 2024.', 'boss-seo')}
          </Notice>
        )}

        <Spacer marginY={4} />

        {/* Core Web Vitals principaux */}
        <Heading level={4}>
          {__('Métriques Core Web Vitals', 'boss-seo')}
        </Heading>
        
        <div className="boss-grid boss-grid-cols-1 boss-md:grid-cols-3 boss-gap-4 boss-mt-4">
          {Object.entries(coreVitalsData).map(([key, metric]) => {
            const metricInfo = getMetricInfo(key);
            return (
              <div
                key={key}
                className="boss-p-4 boss-border boss-border-gray-200 boss-rounded-lg boss-relative"
                style={{ borderLeftColor: getStatusColor(metric.status), borderLeftWidth: '4px' }}
              >
                {/* Badge NEW pour INP */}
                {key === 'inp' && (
                  <div
                    className="boss-absolute boss-top-2 boss-right-2 boss-text-xs boss-px-2 boss-py-1 boss-rounded boss-bg-blue-100 boss-text-blue-800"
                  >
                    {__('NOUVEAU', 'boss-seo')}
                  </div>
                )}

                <div className="boss-text-center">
                  <div
                    className="boss-text-3xl boss-font-bold boss-mb-2"
                    style={{ color: getStatusColor(metric.status) }}
                  >
                    {metric.value}{metric.unit}
                  </div>
                  <div className="boss-text-sm boss-font-medium boss-mb-1">
                    {metric.name}
                  </div>
                  <div className="boss-text-xs boss-text-gray-600 boss-mb-3">
                    {metricInfo.description}
                  </div>
                  <div
                    className="boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded boss-mb-2"
                    style={{
                      backgroundColor: getStatusColor(metric.status) + '20',
                      color: getStatusColor(metric.status),
                    }}
                  >
                    {getStatusText(metric.status)}
                  </div>
                  {metric.score && (
                    <div className="boss-text-xs boss-text-gray-500">
                      {__('Score:', 'boss-seo')} {metric.score}/100
                    </div>
                  )}
                </div>

                {/* Seuils */}
                <div className="boss-mt-3 boss-pt-3 boss-border-t boss-border-gray-100">
                  <div className="boss-text-xs boss-text-gray-500">
                    {metricInfo.threshold}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Autres métriques importantes */}
        {Object.keys(otherMetricsData).length > 0 && (
          <>
            <Spacer marginY={6} />
            
            <Heading level={4}>
              {__('Métriques complémentaires', 'boss-seo')}
            </Heading>
            
            <div className="boss-grid boss-grid-cols-1 boss-md:grid-cols-3 boss-gap-4 boss-mt-4">
              {Object.entries(otherMetricsData).map(([key, metric]) => {
                const metricInfo = getMetricInfo(key);
                return (
                  <div
                    key={key}
                    className="boss-p-4 boss-border boss-border-gray-200 boss-rounded-lg boss-relative"
                    style={{ borderLeftColor: getStatusColor(metric.status), borderLeftWidth: '2px' }}
                  >
                    {/* Badge OBSOLÈTE pour FID */}
                    {key === 'fid' && (
                      <div
                        className="boss-absolute boss-top-2 boss-right-2 boss-text-xs boss-px-2 boss-py-1 boss-rounded boss-bg-gray-100 boss-text-gray-600"
                      >
                        {__('OBSOLÈTE', 'boss-seo')}
                      </div>
                    )}

                    <div className="boss-text-center">
                      <div
                        className="boss-text-2xl boss-font-bold boss-mb-2"
                        style={{ color: getStatusColor(metric.status) }}
                      >
                        {metric.value}{metric.unit}
                      </div>
                      <div className="boss-text-sm boss-font-medium boss-mb-1">
                        {metric.name}
                      </div>
                      <div className="boss-text-xs boss-text-gray-600 boss-mb-3">
                        {metricInfo.description}
                      </div>
                      <div
                        className="boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded"
                        style={{
                          backgroundColor: getStatusColor(metric.status) + '20',
                          color: getStatusColor(metric.status),
                        }}
                      >
                        {getStatusText(metric.status)}
                      </div>
                    </div>

                    {/* Seuils */}
                    <div className="boss-mt-3 boss-pt-3 boss-border-t boss-border-gray-100">
                      <div className="boss-text-xs boss-text-gray-500">
                        {metricInfo.threshold}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </>
        )}

        <Spacer marginY={6} />

        {/* Actions */}
        <Flex align="center" justify="space-between">
          <FlexItem>
            <Text variant="muted" size="small">
              {__('Données de laboratoire PageSpeed Insights. Mise à jour:', 'boss-seo')} {new Date().toLocaleString('fr-FR')}
            </Text>
          </FlexItem>
          <FlexItem>
            <Flex gap={2}>
              {onRefresh && (
                <Button isSecondary onClick={onRefresh}>
                  {__('Actualiser', 'boss-seo')}
                </Button>
              )}
              <Button
                isLink
                href="https://web.dev/vitals/"
                target="_blank"
                icon={external}
              >
                {__('En savoir plus', 'boss-seo')}
              </Button>
            </Flex>
          </FlexItem>
        </Flex>
      </CardBody>
    </Card>
  );
};

export default CoreWebVitalsCard;
