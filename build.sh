#!/bin/bash

# Script de build pour Boss SEO
# Ce script compile les assets et nettoie les fichiers temporaires

echo "🚀 Démarrage du build de Boss SEO..."

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    echo "❌ npm n'est pas installé. Veuillez l'installer pour continuer."
    exit 1
fi

# Créer les dossiers d'assets s'ils n'existent pas
mkdir -p assets/css
mkdir -p assets/js
mkdir -p assets/images

# Installer les dépendances temporairement
echo "📦 Installation des dépendances..."
npm install

# Compiler Tailwind CSS
echo "🎨 Compilation de Tailwind CSS..."
npx tailwindcss -i ./src/styles/tailwind.css -o ./assets/css/dashboard.css --minify

# Compiler le JavaScript avec Webpack
echo "📜 Compilation du JavaScript..."
npx webpack --mode=production

# Copier les images si nécessaire
if [ -d "./src/images" ]; then
    echo "🖼️ Copie des images..."
    cp -r ./src/images/* ./assets/images/
fi

# Nettoyer les fichiers temporaires
echo "🧹 Nettoyage des fichiers temporaires..."
rm -rf node_modules
rm -rf package-lock.json

echo "✅ Build terminé avec succès!"
echo "Les assets compilés se trouvent dans le dossier 'assets/'."
