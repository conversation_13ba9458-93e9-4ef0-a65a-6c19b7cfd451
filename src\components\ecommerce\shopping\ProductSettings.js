import { __ } from '@wordpress/i18n';
import { 
  Card, 
  CardHeader, 
  CardBody, 
  CardFooter, 
  Button, 
  TextControl, 
  SelectControl, 
  ToggleControl 
} from '@wordpress/components';

const ProductSettings = ({ 
  selectedProduct, 
  productSettings, 
  setProductSettings, 
  handleSaveProduct, 
  isSaving 
}) => {
  if (!selectedProduct) return null;

  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Paramètres Google Shopping', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        <div className="boss-space-y-4">
          <TextControl
            label={__('Titre du produit', 'boss-seo')}
            help={__('Maximum 150 caractères', 'boss-seo')}
            value={productSettings.title}
            onChange={(value) => setProductSettings({ ...productSettings, title: value })}
          />
          
          <TextControl
            label={__('Description', 'boss-seo')}
            help={__('Maximum 5000 caractères', 'boss-seo')}
            value={productSettings.description}
            onChange={(value) => setProductSettings({ ...productSettings, description: value })}
          />
          
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
            <TextControl
              label={__('GTIN', 'boss-seo')}
              help={__('Code-barres du produit', 'boss-seo')}
              value={productSettings.gtin}
              onChange={(value) => setProductSettings({ ...productSettings, gtin: value })}
            />
            
            <TextControl
              label={__('MPN', 'boss-seo')}
              help={__('Référence fabricant', 'boss-seo')}
              value={productSettings.mpn}
              onChange={(value) => setProductSettings({ ...productSettings, mpn: value })}
            />
          </div>
          
          <TextControl
            label={__('Marque', 'boss-seo')}
            value={productSettings.brand}
            onChange={(value) => setProductSettings({ ...productSettings, brand: value })}
          />
          
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
            <SelectControl
              label={__('Condition', 'boss-seo')}
              value={productSettings.condition}
              options={[
                { label: __('Neuf', 'boss-seo'), value: 'new' },
                { label: __('Reconditionné', 'boss-seo'), value: 'refurbished' },
                { label: __('Occasion', 'boss-seo'), value: 'used' }
              ]}
              onChange={(value) => setProductSettings({ ...productSettings, condition: value })}
            />
            
            <SelectControl
              label={__('Disponibilité', 'boss-seo')}
              value={productSettings.availability}
              options={[
                { label: __('En stock', 'boss-seo'), value: 'in_stock' },
                { label: __('Rupture de stock', 'boss-seo'), value: 'out_of_stock' },
                { label: __('Précommande', 'boss-seo'), value: 'preorder' },
                { label: __('Sur commande', 'boss-seo'), value: 'backorder' }
              ]}
              onChange={(value) => setProductSettings({ ...productSettings, availability: value })}
            />
          </div>
          
          <TextControl
            label={__('Frais de livraison', 'boss-seo')}
            value={productSettings.shipping}
            onChange={(value) => setProductSettings({ ...productSettings, shipping: value })}
          />
          
          <ToggleControl
            label={__('Inclure dans Google Shopping', 'boss-seo')}
            checked={productSettings.includeInShopping}
            onChange={(checked) => setProductSettings({ ...productSettings, includeInShopping: checked })}
          />
          
          <ToggleControl
            label={__('Activer les annonces dynamiques', 'boss-seo')}
            checked={productSettings.enableDynamicRemarketing}
            onChange={(checked) => setProductSettings({ ...productSettings, enableDynamicRemarketing: checked })}
          />
        </div>
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200">
        <Button
          isPrimary
          onClick={handleSaveProduct}
          isBusy={isSaving}
          disabled={isSaving}
          className="boss-w-full"
        >
          {isSaving ? __('Enregistrement...', 'boss-seo') : __('Enregistrer les paramètres', 'boss-seo')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ProductSettings;
