import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  ButtonGroup,
  Dashicon,
  SelectControl,
  TextControl,
  ToggleControl,
  Tooltip
} from '@wordpress/components';
import { KeywordsChart, PositionsChart } from './ChartComponents';

const KeywordPerformance = ({ data, selectedPeriod, onPeriodChange }) => {
  // États
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('position');
  const [sortOrder, setSortOrder] = useState('asc');
  const [filterPosition, setFilterPosition] = useState('all');
  const [activeTab, setActiveTab] = useState('all');
  
  // Fonction pour filtrer les mots-clés
  const getFilteredKeywords = () => {
    if (!data) return [];
    
    let filtered = [...data];
    
    // Filtrer par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(kw => kw.keyword.toLowerCase().includes(query));
    }
    
    // Filtrer par position
    if (filterPosition !== 'all') {
      switch (filterPosition) {
        case 'top3':
          filtered = filtered.filter(kw => kw.position <= 3);
          break;
        case 'top10':
          filtered = filtered.filter(kw => kw.position <= 10);
          break;
        case 'top20':
          filtered = filtered.filter(kw => kw.position <= 20);
          break;
        case 'top50':
          filtered = filtered.filter(kw => kw.position <= 50);
          break;
        case 'top100':
          filtered = filtered.filter(kw => kw.position <= 100);
          break;
      }
    }
    
    // Filtrer par onglet actif
    if (activeTab !== 'all') {
      switch (activeTab) {
        case 'improving':
          filtered = filtered.filter(kw => kw.change && kw.change < 0); // Position qui s'améliore (diminue)
          break;
        case 'declining':
          filtered = filtered.filter(kw => kw.change && kw.change > 0); // Position qui se dégrade (augmente)
          break;
        case 'noClicks':
          filtered = filtered.filter(kw => kw.clicks === 0);
          break;
        case 'highCTR':
          filtered = filtered.filter(kw => kw.ctr > 5); // CTR supérieur à 5%
          break;
      }
    }
    
    // Trier les résultats
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'keyword':
          comparison = a.keyword.localeCompare(b.keyword);
          break;
        case 'position':
          comparison = a.position - b.position;
          break;
        case 'clicks':
          comparison = b.clicks - a.clicks;
          break;
        case 'impressions':
          comparison = b.impressions - a.impressions;
          break;
        case 'ctr':
          comparison = b.ctr - a.ctr;
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
    
    return filtered;
  };
  
  // Obtenir les mots-clés filtrés
  const filteredKeywords = getFilteredKeywords();
  
  // Fonction pour basculer l'ordre de tri
  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };
  
  // Fonction pour définir la colonne de tri
  const handleSort = (column) => {
    if (sortBy === column) {
      toggleSortOrder();
    } else {
      setSortBy(column);
      setSortOrder(column === 'position' ? 'asc' : 'desc');
    }
  };
  
  // Fonction pour obtenir l'icône de tri
  const getSortIcon = (column) => {
    if (sortBy !== column) return null;
    return sortOrder === 'asc' ? 'arrow-up-alt2' : 'arrow-down-alt2';
  };
  
  // Fonction pour obtenir la classe de couleur en fonction de la position
  const getPositionColorClass = (position) => {
    if (position <= 3) return 'boss-text-green-600';
    if (position <= 10) return 'boss-text-blue-600';
    if (position <= 20) return 'boss-text-yellow-600';
    if (position <= 50) return 'boss-text-orange-500';
    return 'boss-text-red-500';
  };
  
  // Calculer les statistiques des mots-clés
  const calculateKeywordStats = () => {
    if (!data || data.length === 0) return { total: 0, top10: 0, top20: 0, top50: 0, avgPosition: 0 };
    
    const total = data.length;
    const top10 = data.filter(kw => kw.position <= 10).length;
    const top20 = data.filter(kw => kw.position <= 20).length;
    const top50 = data.filter(kw => kw.position <= 50).length;
    const avgPosition = data.reduce((sum, kw) => sum + kw.position, 0) / total;
    
    return {
      total,
      top10,
      top20,
      top50,
      avgPosition
    };
  };
  
  const keywordStats = calculateKeywordStats();

  return (
    <div>
      {/* Statistiques des mots-clés */}
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-5 boss-gap-4 boss-mb-6">
        <Card className="boss-card">
          <CardBody className="boss-p-4">
            <div className="boss-text-center">
              <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                {__('Total des mots-clés', 'boss-seo')}
              </h3>
              <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
                {keywordStats.total}
              </div>
            </div>
          </CardBody>
        </Card>
        
        <Card className="boss-card">
          <CardBody className="boss-p-4">
            <div className="boss-text-center">
              <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                {__('Top 10', 'boss-seo')}
              </h3>
              <div className="boss-text-2xl boss-font-bold boss-text-green-600">
                {keywordStats.top10}
              </div>
            </div>
          </CardBody>
        </Card>
        
        <Card className="boss-card">
          <CardBody className="boss-p-4">
            <div className="boss-text-center">
              <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                {__('Top 20', 'boss-seo')}
              </h3>
              <div className="boss-text-2xl boss-font-bold boss-text-blue-600">
                {keywordStats.top20}
              </div>
            </div>
          </CardBody>
        </Card>
        
        <Card className="boss-card">
          <CardBody className="boss-p-4">
            <div className="boss-text-center">
              <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                {__('Top 50', 'boss-seo')}
              </h3>
              <div className="boss-text-2xl boss-font-bold boss-text-yellow-600">
                {keywordStats.top50}
              </div>
            </div>
          </CardBody>
        </Card>
        
        <Card className="boss-card">
          <CardBody className="boss-p-4">
            <div className="boss-text-center">
              <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                {__('Position moyenne', 'boss-seo')}
              </h3>
              <div className="boss-text-2xl boss-font-bold boss-text-boss-primary">
                {keywordStats.avgPosition.toFixed(1)}
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
      
      {/* Graphiques */}
      <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-2 boss-gap-6 boss-mb-6">
        <Card>
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Clics par mot-clé', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <KeywordsChart data={filteredKeywords.slice(0, 5)} />
          </CardBody>
        </Card>
        
        <Card>
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Positions des mots-clés', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <PositionsChart data={filteredKeywords.slice(0, 5)} />
          </CardBody>
        </Card>
      </div>
      
      {/* Filtres et tableau */}
      <Card>
        <CardHeader className="boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-flex-col md:boss-flex-row boss-justify-between boss-items-start md:boss-items-center boss-gap-4">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Liste des mots-clés', 'boss-seo')}
            </h2>
            
            <div className="boss-flex boss-flex-col sm:boss-flex-row boss-gap-3">
              <TextControl
                placeholder={__('Rechercher un mot-clé...', 'boss-seo')}
                value={searchQuery}
                onChange={setSearchQuery}
                className="boss-min-w-[200px]"
              />
              
              <SelectControl
                value={filterPosition}
                options={[
                  { label: __('Toutes les positions', 'boss-seo'), value: 'all' },
                  { label: __('Top 3', 'boss-seo'), value: 'top3' },
                  { label: __('Top 10', 'boss-seo'), value: 'top10' },
                  { label: __('Top 20', 'boss-seo'), value: 'top20' },
                  { label: __('Top 50', 'boss-seo'), value: 'top50' },
                  { label: __('Top 100', 'boss-seo'), value: 'top100' }
                ]}
                onChange={setFilterPosition}
              />
            </div>
          </div>
        </CardHeader>
        
        <div className="boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-overflow-x-auto">
            <Button
              className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium ${activeTab === 'all' ? 'boss-border-boss-primary boss-text-boss-primary' : 'boss-border-transparent boss-text-boss-gray'}`}
              onClick={() => setActiveTab('all')}
            >
              {__('Tous', 'boss-seo')}
            </Button>
            <Button
              className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium ${activeTab === 'improving' ? 'boss-border-boss-primary boss-text-boss-primary' : 'boss-border-transparent boss-text-boss-gray'}`}
              onClick={() => setActiveTab('improving')}
            >
              {__('En progression', 'boss-seo')}
            </Button>
            <Button
              className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium ${activeTab === 'declining' ? 'boss-border-boss-primary boss-text-boss-primary' : 'boss-border-transparent boss-text-boss-gray'}`}
              onClick={() => setActiveTab('declining')}
            >
              {__('En baisse', 'boss-seo')}
            </Button>
            <Button
              className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium ${activeTab === 'noClicks' ? 'boss-border-boss-primary boss-text-boss-primary' : 'boss-border-transparent boss-text-boss-gray'}`}
              onClick={() => setActiveTab('noClicks')}
            >
              {__('Sans clics', 'boss-seo')}
            </Button>
            <Button
              className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium ${activeTab === 'highCTR' ? 'boss-border-boss-primary boss-text-boss-primary' : 'boss-border-transparent boss-text-boss-gray'}`}
              onClick={() => setActiveTab('highCTR')}
            >
              {__('CTR élevé', 'boss-seo')}
            </Button>
          </div>
        </div>
        
        <CardBody className="boss-p-0">
          <div className="boss-overflow-x-auto">
            <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
              <thead className="boss-bg-gray-50">
                <tr>
                  <th 
                    className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-cursor-pointer"
                    onClick={() => handleSort('keyword')}
                  >
                    <div className="boss-flex boss-items-center">
                      {__('Mot-clé', 'boss-seo')}
                      {getSortIcon('keyword') && <Dashicon icon={getSortIcon('keyword')} className="boss-ml-1" />}
                    </div>
                  </th>
                  <th 
                    className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-cursor-pointer"
                    onClick={() => handleSort('position')}
                  >
                    <div className="boss-flex boss-items-center">
                      {__('Position', 'boss-seo')}
                      {getSortIcon('position') && <Dashicon icon={getSortIcon('position')} className="boss-ml-1" />}
                    </div>
                  </th>
                  <th 
                    className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-cursor-pointer"
                    onClick={() => handleSort('clicks')}
                  >
                    <div className="boss-flex boss-items-center">
                      {__('Clics', 'boss-seo')}
                      {getSortIcon('clicks') && <Dashicon icon={getSortIcon('clicks')} className="boss-ml-1" />}
                    </div>
                  </th>
                  <th 
                    className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-cursor-pointer"
                    onClick={() => handleSort('impressions')}
                  >
                    <div className="boss-flex boss-items-center">
                      {__('Impressions', 'boss-seo')}
                      {getSortIcon('impressions') && <Dashicon icon={getSortIcon('impressions')} className="boss-ml-1" />}
                    </div>
                  </th>
                  <th 
                    className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-cursor-pointer"
                    onClick={() => handleSort('ctr')}
                  >
                    <div className="boss-flex boss-items-center">
                      {__('CTR', 'boss-seo')}
                      {getSortIcon('ctr') && <Dashicon icon={getSortIcon('ctr')} className="boss-ml-1" />}
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                {filteredKeywords.length === 0 ? (
                  <tr>
                    <td colSpan="5" className="boss-px-6 boss-py-4 boss-text-center boss-text-boss-gray">
                      {__('Aucun mot-clé trouvé.', 'boss-seo')}
                    </td>
                  </tr>
                ) : (
                  filteredKeywords.map((keyword, index) => (
                    <tr key={index} className="boss-hover:boss-bg-gray-50">
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-font-medium boss-text-boss-dark">{keyword.keyword}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className={`boss-font-medium ${getPositionColorClass(keyword.position)}`}>
                          {keyword.position.toFixed(1)}
                        </div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{keyword.clicks}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{keyword.impressions}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{keyword.ctr.toFixed(2)}%</div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default KeywordPerformance;
