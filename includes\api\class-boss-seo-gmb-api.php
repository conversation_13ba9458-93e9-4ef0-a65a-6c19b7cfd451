<?php
/**
 * Classe pour l'API Google My Business.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/api
 */

/**
 * Classe pour l'API Google My Business.
 *
 * Cette classe gère l'authentification et les appels à l'API Google My Business.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/api
 * <AUTHOR> SEO Team
 */
class Boss_SEO_GMB_API {

    /**
     * Le client ID OAuth 2.0.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $client_id    Le client ID OAuth 2.0.
     */
    private $client_id;

    /**
     * Le client secret OAuth 2.0.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $client_secret    Le client secret OAuth 2.0.
     */
    private $client_secret;

    /**
     * L'URI de redirection OAuth 2.0.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $redirect_uri    L'URI de redirection OAuth 2.0.
     */
    private $redirect_uri;

    /**
     * Le token d'accès OAuth 2.0.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $access_token    Le token d'accès OAuth 2.0.
     */
    private $access_token;

    /**
     * Le token de rafraîchissement OAuth 2.0.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $refresh_token    Le token de rafraîchissement OAuth 2.0.
     */
    private $refresh_token;

    /**
     * La date d'expiration du token d'accès.
     *
     * @since    1.2.0
     * @access   private
     * @var      int    $token_expires    La date d'expiration du token d'accès.
     */
    private $token_expires;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    private $option_prefix = 'boss_seo_gmb_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     */
    public function __construct() {
        // Charger les credentials depuis les variables d'environnement ou les options
        $this->client_id = Boss_SEO_Env::get( 'GMB_CLIENT_ID', get_option( $this->option_prefix . 'client_id', '' ) );
        $this->client_secret = Boss_SEO_Env::get( 'GMB_CLIENT_SECRET', get_option( $this->option_prefix . 'client_secret', '' ) );
        $this->redirect_uri = Boss_SEO_Env::get( 'GMB_REDIRECT_URI', get_option( $this->option_prefix . 'redirect_uri', '' ) );

        // Charger les tokens depuis les options
        $this->access_token = get_option( $this->option_prefix . 'access_token', '' );
        $this->refresh_token = get_option( $this->option_prefix . 'refresh_token', '' );
        $this->token_expires = get_option( $this->option_prefix . 'token_expires', 0 );

        // Rafraîchir le token si nécessaire
        if ( $this->is_connected() && $this->token_expires < time() ) {
            $this->refresh_access_token();
        }
    }

    /**
     * Vérifie si l'API est connectée.
     *
     * @since    1.2.0
     * @return   bool    True si l'API est connectée, false sinon.
     */
    public function is_connected() {
        return ! empty( $this->access_token ) && ! empty( $this->refresh_token );
    }

    /**
     * Vérifie si les credentials sont configurés.
     *
     * @since    1.2.0
     * @return   bool    True si les credentials sont configurés, false sinon.
     */
    public function is_configured() {
        return ! empty( $this->client_id ) && ! empty( $this->client_secret ) && ! empty( $this->redirect_uri );
    }

    /**
     * Génère l'URL d'authentification OAuth 2.0.
     *
     * @since    1.2.0
     * @return   string    L'URL d'authentification.
     */
    public function get_auth_url() {
        if ( ! $this->is_configured() ) {
            return '';
        }

        $auth_url = 'https://accounts.google.com/o/oauth2/auth';
        $params = array(
            'client_id'     => $this->client_id,
            'redirect_uri'  => $this->redirect_uri,
            'response_type' => 'code',
            'scope'         => 'https://www.googleapis.com/auth/business.manage',
            'access_type'   => 'offline',
            'prompt'        => 'consent',
        );

        return $auth_url . '?' . http_build_query( $params );
    }

    /**
     * Échange un code d'autorisation contre des tokens d'accès et de rafraîchissement.
     *
     * @since    1.2.0
     * @param    string    $code    Le code d'autorisation.
     * @return   bool|WP_Error      True en cas de succès, WP_Error en cas d'erreur.
     */
    public function exchange_code_for_tokens( $code ) {
        if ( ! $this->is_configured() ) {
            return new WP_Error( 'gmb_not_configured', __( 'L\'API Google My Business n\'est pas configurée.', 'boss-seo' ) );
        }

        $token_url = 'https://oauth2.googleapis.com/token';
        $params = array(
            'client_id'     => $this->client_id,
            'client_secret' => $this->client_secret,
            'redirect_uri'  => $this->redirect_uri,
            'code'          => $code,
            'grant_type'    => 'authorization_code',
        );

        $response = wp_remote_post( $token_url, array(
            'body'    => $params,
            'headers' => array(
                'Content-Type' => 'application/x-www-form-urlencoded',
            ),
        ) );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $body = json_decode( wp_remote_retrieve_body( $response ), true );

        if ( isset( $body['error'] ) ) {
            return new WP_Error( 'gmb_auth_error', $body['error_description'] );
        }

        // Enregistrer les tokens
        $this->access_token = $body['access_token'];
        $this->refresh_token = $body['refresh_token'];
        $this->token_expires = time() + $body['expires_in'];

        update_option( $this->option_prefix . 'access_token', $this->access_token );
        update_option( $this->option_prefix . 'refresh_token', $this->refresh_token );
        update_option( $this->option_prefix . 'token_expires', $this->token_expires );

        return true;
    }

    /**
     * Rafraîchit le token d'accès.
     *
     * @since    1.2.0
     * @return   bool|WP_Error    True en cas de succès, WP_Error en cas d'erreur.
     */
    public function refresh_access_token() {
        if ( ! $this->is_configured() || empty( $this->refresh_token ) ) {
            return new WP_Error( 'gmb_not_connected', __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ) );
        }

        $token_url = 'https://oauth2.googleapis.com/token';
        $params = array(
            'client_id'     => $this->client_id,
            'client_secret' => $this->client_secret,
            'refresh_token' => $this->refresh_token,
            'grant_type'    => 'refresh_token',
        );

        $response = wp_remote_post( $token_url, array(
            'body'    => $params,
            'headers' => array(
                'Content-Type' => 'application/x-www-form-urlencoded',
            ),
        ) );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $body = json_decode( wp_remote_retrieve_body( $response ), true );

        if ( isset( $body['error'] ) ) {
            return new WP_Error( 'gmb_refresh_error', $body['error_description'] );
        }

        // Mettre à jour le token d'accès
        $this->access_token = $body['access_token'];
        $this->token_expires = time() + $body['expires_in'];

        update_option( $this->option_prefix . 'access_token', $this->access_token );
        update_option( $this->option_prefix . 'token_expires', $this->token_expires );

        return true;
    }

    /**
     * Déconnecte l'API en supprimant les tokens.
     *
     * @since    1.2.0
     * @return   bool    True en cas de succès.
     */
    public function disconnect() {
        delete_option( $this->option_prefix . 'access_token' );
        delete_option( $this->option_prefix . 'refresh_token' );
        delete_option( $this->option_prefix . 'token_expires' );

        $this->access_token = '';
        $this->refresh_token = '';
        $this->token_expires = 0;

        return true;
    }

    /**
     * Effectue un appel à l'API Google My Business.
     *
     * @since    1.2.0
     * @param    string    $endpoint    Le point de terminaison de l'API.
     * @param    string    $method      La méthode HTTP (GET, POST, etc.).
     * @param    array     $params      Les paramètres de la requête.
     * @return   array|WP_Error         La réponse de l'API ou une erreur.
     */
    public function api_request( $endpoint, $method = 'GET', $params = array() ) {
        if ( ! $this->is_connected() ) {
            return new WP_Error( 'gmb_not_connected', __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ) );
        }

        // Rafraîchir le token si nécessaire
        if ( $this->token_expires < time() ) {
            $refresh_result = $this->refresh_access_token();
            if ( is_wp_error( $refresh_result ) ) {
                return $refresh_result;
            }
        }

        $api_url = 'https://mybusiness.googleapis.com/v4/' . $endpoint;
        $args = array(
            'method'  => $method,
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->access_token,
                'Content-Type'  => 'application/json',
            ),
        );

        if ( $method === 'GET' && ! empty( $params ) ) {
            $api_url = add_query_arg( $params, $api_url );
        } elseif ( $method !== 'GET' && ! empty( $params ) ) {
            $args['body'] = json_encode( $params );
        }

        $response = wp_remote_request( $api_url, $args );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $body = json_decode( wp_remote_retrieve_body( $response ), true );
        $status_code = wp_remote_retrieve_response_code( $response );

        if ( $status_code >= 400 ) {
            $error_message = isset( $body['error']['message'] ) ? $body['error']['message'] : __( 'Erreur inconnue', 'boss-seo' );
            return new WP_Error( 'gmb_api_error', $error_message, array( 'status' => $status_code ) );
        }

        return $body;
    }

    /**
     * Récupère la liste des comptes.
     *
     * @since    1.2.0
     * @return   array|WP_Error    La liste des comptes ou une erreur.
     */
    public function get_accounts() {
        return $this->api_request( 'accounts' );
    }

    /**
     * Récupère la liste des établissements pour un compte.
     *
     * @since    1.2.0
     * @param    string    $account_id    L'ID du compte.
     * @return   array|WP_Error           La liste des établissements ou une erreur.
     */
    public function get_locations( $account_id ) {
        return $this->api_request( "accounts/{$account_id}/locations", 'GET', array(
            'readMask' => 'name,title,storeCode,address,phoneNumbers,websiteUri,regularHours,specialHours,serviceArea,labels,adWordsLocationExtensions,latlng,openInfo,metadata,profile,relationshipData,moreHours',
        ) );
    }

    /**
     * Récupère les détails d'un établissement.
     *
     * @since    1.2.0
     * @param    string    $account_id     L'ID du compte.
     * @param    string    $location_id    L'ID de l'établissement.
     * @return   array|WP_Error            Les détails de l'établissement ou une erreur.
     */
    public function get_location( $account_id, $location_id ) {
        return $this->api_request( "accounts/{$account_id}/locations/{$location_id}", 'GET', array(
            'readMask' => 'name,title,storeCode,address,phoneNumbers,websiteUri,regularHours,specialHours,serviceArea,labels,adWordsLocationExtensions,latlng,openInfo,metadata,profile,relationshipData,moreHours',
        ) );
    }

    /**
     * Récupère les statistiques d'un établissement.
     *
     * @since    1.2.0
     * @param    string    $account_id     L'ID du compte.
     * @param    string    $location_id    L'ID de l'établissement.
     * @param    string    $metric         La métrique à récupérer.
     * @param    string    $time_range     La période.
     * @return   array|WP_Error            Les statistiques de l'établissement ou une erreur.
     */
    public function get_insights( $account_id, $location_id, $metric = 'ALL', $time_range = 'MONTH' ) {
        return $this->api_request( "accounts/{$account_id}/locations/{$location_id}/insights/basicMetricsReport", 'GET', array(
            'metric'    => $metric,
            'timeRange' => $time_range,
        ) );
    }

    /**
     * Récupère les avis d'un établissement.
     *
     * @since    1.2.0
     * @param    string    $account_id     L'ID du compte.
     * @param    string    $location_id    L'ID de l'établissement.
     * @return   array|WP_Error            Les avis de l'établissement ou une erreur.
     */
    public function get_reviews( $account_id, $location_id ) {
        return $this->api_request( "accounts/{$account_id}/locations/{$location_id}/reviews" );
    }
}
