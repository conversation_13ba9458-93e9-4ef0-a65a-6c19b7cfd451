<?php
/**
 * Script de test pour les redirections Boss SEO
 * 
 * Ce script permet de tester le fonctionnement des redirections
 * en créant quelques redirections de test et en vérifiant leur fonctionnement.
 */

// Vérifier que WordPress est chargé
if (!defined('WPINC')) {
    // Charger WordPress
    require_once dirname(__FILE__) . '/../../../wp-config.php';
}

// Charger la classe de redirections
require_once plugin_dir_path(__FILE__) . 'includes/technical/class-boss-redirections.php';

// Créer une instance de la classe
$redirections = new Boss_Redirections('boss-seo', '1.1.0');

// Créer les tables si elles n'existent pas
$redirections->create_tables();

echo "<h1>Test des Redirections Boss SEO</h1>\n";

// Tester la création d'une redirection
echo "<h2>1. Test de création de redirection</h2>\n";

global $wpdb;
$table_name = $wpdb->prefix . 'boss_seo_redirections';

// Supprimer les redirections de test existantes
$wpdb->delete($table_name, array('source' => '/test-page'));
$wpdb->delete($table_name, array('source' => '/old-page'));

// Créer une redirection de test
$result = $wpdb->insert(
    $table_name,
    array(
        'source' => '/test-page',
        'target' => '/new-page',
        'type' => '301',
        'status' => 'active',
        'match_case' => 0,
        'regex' => 0,
        'notes' => 'Redirection de test',
        'hits' => 0,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql'),
    ),
    array('%s', '%s', '%s', '%s', '%d', '%d', '%s', '%d', '%s', '%s')
);

if ($result) {
    echo "✅ Redirection créée avec succès : /test-page → /new-page\n<br>";
} else {
    echo "❌ Erreur lors de la création de la redirection\n<br>";
}

// Créer une deuxième redirection
$result2 = $wpdb->insert(
    $table_name,
    array(
        'source' => '/old-page',
        'target' => 'https://example.com',
        'type' => '302',
        'status' => 'active',
        'match_case' => 0,
        'regex' => 0,
        'notes' => 'Redirection externe de test',
        'hits' => 0,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql'),
    ),
    array('%s', '%s', '%s', '%s', '%d', '%d', '%s', '%d', '%s', '%s')
);

if ($result2) {
    echo "✅ Redirection externe créée avec succès : /old-page → https://example.com\n<br>";
} else {
    echo "❌ Erreur lors de la création de la redirection externe\n<br>";
}

// Vérifier que les redirections sont dans la base de données
echo "<h2>2. Vérification des redirections en base</h2>\n";
$redirections_in_db = $wpdb->get_results("SELECT * FROM $table_name WHERE status = 'active'");

echo "<p>Nombre de redirections actives : " . count($redirections_in_db) . "</p>\n";

foreach ($redirections_in_db as $redirect) {
    echo "<p>• {$redirect->source} → {$redirect->target} (Type: {$redirect->type})</p>\n";
}

// Tester la logique de correspondance
echo "<h2>3. Test de la logique de correspondance</h2>\n";

// Simuler différentes URLs
$test_urls = array(
    '/test-page',
    '/test-page?param=value',
    '/old-page',
    '/non-existent-page'
);

foreach ($test_urls as $test_url) {
    echo "<h3>Test pour l'URL : $test_url</h3>\n";
    
    // Simuler $_SERVER['REQUEST_URI']
    $_SERVER['REQUEST_URI'] = $test_url;
    
    // Récupérer les redirections actives
    $active_redirections = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE status = %s ORDER BY regex ASC, LENGTH(source) DESC",
            'active'
        )
    );
    
    $found_match = false;
    
    foreach ($active_redirections as $redirection) {
        $match = false;
        $target_url = $redirection->target;
        $clean_url = strtok($test_url, '?');
        
        if ($redirection->regex) {
            // Expression régulière
            $flags = $redirection->match_case ? '' : 'i';
            $pattern = '#' . $redirection->source . '#' . $flags;
            
            if (preg_match($pattern, $test_url, $matches)) {
                $match = true;
            }
        } else {
            // Correspondance directe
            $source_url = $redirection->source;
            
            if (strpos($source_url, '/') !== 0) {
                $source_url = '/' . $source_url;
            }
            
            if ($redirection->match_case) {
                $match = ($clean_url === $source_url || $test_url === $source_url);
            } else {
                $match = (strtolower($clean_url) === strtolower($source_url) || 
                         strtolower($test_url) === strtolower($source_url));
            }
        }
        
        if ($match) {
            echo "✅ Correspondance trouvée : {$redirection->source} → {$target_url} (Type: {$redirection->type})\n<br>";
            $found_match = true;
            break;
        }
    }
    
    if (!$found_match) {
        echo "ℹ️ Aucune redirection trouvée pour cette URL\n<br>";
    }
    
    echo "<br>\n";
}

// Instructions pour tester manuellement
echo "<h2>4. Test manuel</h2>\n";
echo "<p>Pour tester manuellement les redirections :</p>\n";
echo "<ol>\n";
echo "<li>Visitez <a href='" . home_url('/test-page') . "' target='_blank'>" . home_url('/test-page') . "</a></li>\n";
echo "<li>Visitez <a href='" . home_url('/old-page') . "' target='_blank'>" . home_url('/old-page') . "</a></li>\n";
echo "<li>Vérifiez que les redirections fonctionnent correctement</li>\n";
echo "</ol>\n";

// Nettoyer les redirections de test
echo "<h2>5. Nettoyage</h2>\n";
echo "<p><a href='?cleanup=1'>Cliquer ici pour supprimer les redirections de test</a></p>\n";

if (isset($_GET['cleanup']) && $_GET['cleanup'] == '1') {
    $deleted1 = $wpdb->delete($table_name, array('source' => '/test-page'));
    $deleted2 = $wpdb->delete($table_name, array('source' => '/old-page'));
    
    if ($deleted1 && $deleted2) {
        echo "<p>✅ Redirections de test supprimées avec succès</p>\n";
    } else {
        echo "<p>❌ Erreur lors de la suppression des redirections de test</p>\n";
    }
}

echo "<hr>\n";
echo "<p><strong>Note :</strong> Ce script de test a créé des redirections temporaires. ";
echo "Utilisez le lien de nettoyage ci-dessus pour les supprimer après les tests.</p>\n";
