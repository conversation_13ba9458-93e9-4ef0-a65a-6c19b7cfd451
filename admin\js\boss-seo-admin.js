/**
 * Scripts pour l'interface d'administration de Boss SEO
 */

(function( $ ) {
  'use strict';

  /**
   * Tout le code jQuery doit être contenu dans cette fonction pour éviter les conflits
   * avec d'autres plugins.
   */
  $(function() {
    // Ajustement de la hauteur du conteneur de l'application
    function adjustAppHeight() {
      const adminBarHeight = $('#wpadminbar').height() || 0;
      const windowHeight = window.innerHeight;

      // Liste des conteneurs d'application
      const appContainers = [
        '#boss-seo-dashboard-app',
        '#boss-seo-optimizer-app',
        '#boss-seo-technical-app',
        '#boss-seo-content-app',
        '#boss-seo-schema-app',
        '#boss-seo-analytics-app',
        '#boss-seo-local-app',
        '#boss-seo-reports-app',
        '#boss-seo-settings-app'
      ];

      // Ajuster la hauteur pour chaque conteneur
      appContainers.forEach(function(selector) {
        const appContainer = $(selector);
        if (appContainer.length) {
          appContainer.css('min-height', (windowHeight - adminBarHeight - 100) + 'px');
        }
      });
    }

    // Ajuster la hauteur au chargement et au redimensionnement
    adjustAppHeight();
    $(window).on('resize', adjustAppHeight);
  });

})( jQuery );
