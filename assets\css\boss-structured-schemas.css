/**
 * Styles pour le module de schémas structurés
 */

/* Styles généraux */
.boss-schema-container {
  margin-bottom: 20px;
}

.boss-schema-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.boss-schema-title {
  font-size: 18px;
  font-weight: 600;
  color: #23282d;
}

/* Styles pour les formulaires de schéma */
.boss-schema-form {
  background-color: #fff;
  border: 1px solid #e2e4e7;
  border-radius: 4px;
  padding: 20px;
}

.boss-schema-form-group {
  margin-bottom: 15px;
}

.boss-schema-form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.boss-schema-form-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #8d96a0;
  border-radius: 4px;
}

.boss-schema-form-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #8d96a0;
  border-radius: 4px;
}

.boss-schema-form-textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #8d96a0;
  border-radius: 4px;
  min-height: 100px;
}

/* Styles pour la liste des schémas */
.boss-schema-list {
  border: 1px solid #e2e4e7;
  border-radius: 4px;
  overflow: hidden;
}

.boss-schema-list-header {
  background-color: #f8f9fa;
  padding: 10px 15px;
  border-bottom: 1px solid #e2e4e7;
  font-weight: 600;
}

.boss-schema-list-item {
  padding: 15px;
  border-bottom: 1px solid #e2e4e7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.boss-schema-list-item:last-child {
  border-bottom: none;
}

.boss-schema-list-item-title {
  font-weight: 500;
}

.boss-schema-list-item-actions {
  display: flex;
  gap: 10px;
}

/* Styles pour les boutons */
.boss-schema-button {
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  text-decoration: none;
}

.boss-schema-button-primary {
  background-color: #007cba;
  color: #fff;
  border: 1px solid #007cba;
}

.boss-schema-button-secondary {
  background-color: #f8f9fa;
  color: #23282d;
  border: 1px solid #ccc;
}

.boss-schema-button-danger {
  background-color: #d63638;
  color: #fff;
  border: 1px solid #d63638;
}

/* Styles pour les badges */
.boss-schema-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

.boss-schema-badge-success {
  background-color: #edfaef;
  color: #00a32a;
}

.boss-schema-badge-warning {
  background-color: #fcf9e8;
  color: #dba617;
}

.boss-schema-badge-error {
  background-color: #fcf0f1;
  color: #d63638;
}

/* Styles pour les prévisualisations */
.boss-schema-preview {
  background-color: #f8f9fa;
  border: 1px solid #e2e4e7;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.boss-schema-preview-title {
  font-weight: 600;
  margin-bottom: 10px;
}

.boss-schema-preview-code {
  background-color: #fff;
  border: 1px solid #e2e4e7;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  white-space: pre-wrap;
  overflow-x: auto;
}

/* Styles pour les notifications */
.boss-schema-notice {
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.boss-schema-notice-success {
  background-color: #edfaef;
  border: 1px solid #c3e6cb;
  color: #00a32a;
}

.boss-schema-notice-warning {
  background-color: #fcf9e8;
  border: 1px solid #ffeeba;
  color: #dba617;
}

.boss-schema-notice-error {
  background-color: #fcf0f1;
  border: 1px solid #f5c6cb;
  color: #d63638;
}

/* Styles pour les onglets */
.boss-schema-tabs {
  margin-bottom: 20px;
}

.boss-schema-tabs-list {
  display: flex;
  border-bottom: 1px solid #e2e4e7;
  margin-bottom: 15px;
}

.boss-schema-tab {
  padding: 10px 15px;
  cursor: pointer;
  font-weight: 500;
  border-bottom: 2px solid transparent;
}

.boss-schema-tab-active {
  border-bottom-color: #007cba;
  color: #007cba;
}

.boss-schema-tab-content {
  display: none;
}

.boss-schema-tab-content-active {
  display: block;
}

/* Styles pour les modaux */
.boss-schema-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.boss-schema-modal {
  background-color: #fff;
  border-radius: 4px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.boss-schema-modal-header {
  padding: 15px;
  border-bottom: 1px solid #e2e4e7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.boss-schema-modal-title {
  font-size: 18px;
  font-weight: 600;
}

.boss-schema-modal-close {
  cursor: pointer;
  font-size: 20px;
  color: #757575;
}

.boss-schema-modal-body {
  padding: 15px;
}

.boss-schema-modal-footer {
  padding: 15px;
  border-top: 1px solid #e2e4e7;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
