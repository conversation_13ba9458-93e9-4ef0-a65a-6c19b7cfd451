import { __ } from '@wordpress/i18n';
import { 
  Button, 
  Card, 
  CardBody, 
  Dashicon, 
  Dropdown, 
  MenuGroup, 
  MenuItem,
  SelectControl,
  ToggleControl
} from '@wordpress/components';
import { useState } from '@wordpress/element';

/**
 * Composant pour la barre de filtres
 */
const FilterBar = ({ 
  filters, 
  onFilterChange,
  visibleColumns,
  onColumnToggle
}) => {
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(false);

  // Options pour les types de contenu
  const contentTypeOptions = [
    { label: __('Tous les types', 'boss-seo'), value: 'all' },
    { label: __('Articles', 'boss-seo'), value: 'post' },
    { label: __('Pages', 'boss-seo'), value: 'page' },
    { label: __('Produits', 'boss-seo'), value: 'product' },
    { label: __('Portfolio', 'boss-seo'), value: 'portfolio' }
  ];

  // Options pour les scores SEO
  const seoScoreOptions = [
    { label: __('Tous les scores', 'boss-seo'), value: 'all' },
    { label: __('Excellent (80-100)', 'boss-seo'), value: 'excellent' },
    { label: __('Bon (60-79)', 'boss-seo'), value: 'good' },
    { label: __('Moyen (40-59)', 'boss-seo'), value: 'average' },
    { label: __('Faible (0-39)', 'boss-seo'), value: 'poor' }
  ];

  // Options pour les statuts
  const statusOptions = [
    { label: __('Tous les statuts', 'boss-seo'), value: 'all' },
    { label: __('Publié', 'boss-seo'), value: 'publish' },
    { label: __('Brouillon', 'boss-seo'), value: 'draft' },
    { label: __('En attente', 'boss-seo'), value: 'pending' },
    { label: __('Privé', 'boss-seo'), value: 'private' }
  ];

  // Options pour les auteurs (fictifs)
  const authorOptions = [
    { label: __('Tous les auteurs', 'boss-seo'), value: 'all' },
    { label: 'John Doe', value: 'John Doe' },
    { label: 'Jane Smith', value: 'Jane Smith' },
    { label: 'Robert Johnson', value: 'Robert Johnson' },
    { label: 'Emily Davis', value: 'Emily Davis' }
  ];

  // Options pour les plages de dates
  const dateRangeOptions = [
    { label: __('Toutes les dates', 'boss-seo'), value: 'all' },
    { label: __('Aujourd\'hui', 'boss-seo'), value: 'today' },
    { label: __('Cette semaine', 'boss-seo'), value: 'this_week' },
    { label: __('Ce mois', 'boss-seo'), value: 'this_month' },
    { label: __('Cette année', 'boss-seo'), value: 'this_year' }
  ];

  // Noms des colonnes pour le menu de personnalisation
  const columnLabels = {
    title: __('Titre', 'boss-seo'),
    seoScore: __('Score SEO', 'boss-seo'),
    status: __('Statut', 'boss-seo'),
    date: __('Date', 'boss-seo'),
    author: __('Auteur', 'boss-seo'),
    type: __('Type', 'boss-seo'),
    actions: __('Actions', 'boss-seo')
  };

  return (
    <Card className="boss-mb-6">
      <CardBody>
        <div className="boss-flex boss-flex-wrap boss-justify-between boss-items-center">
          <div className="boss-flex boss-flex-wrap boss-items-center boss-gap-4 boss-flex-1">
            {/* Filtre par type de contenu */}
            <div className="boss-w-48">
              <SelectControl
                label={__('Type de contenu', 'boss-seo')}
                value={filters.contentType}
                options={contentTypeOptions}
                onChange={(value) => onFilterChange('contentType', value)}
                className="boss-mb-0"
              />
            </div>
            
            {/* Filtre par score SEO */}
            <div className="boss-w-48">
              <SelectControl
                label={__('Score SEO', 'boss-seo')}
                value={filters.seoScore}
                options={seoScoreOptions}
                onChange={(value) => onFilterChange('seoScore', value)}
                className="boss-mb-0"
              />
            </div>
            
            {/* Filtres supplémentaires (affichés uniquement si développés) */}
            {isFiltersExpanded && (
              <>
                {/* Filtre par statut */}
                <div className="boss-w-48">
                  <SelectControl
                    label={__('Statut', 'boss-seo')}
                    value={filters.status}
                    options={statusOptions}
                    onChange={(value) => onFilterChange('status', value)}
                    className="boss-mb-0"
                  />
                </div>
                
                {/* Filtre par auteur */}
                <div className="boss-w-48">
                  <SelectControl
                    label={__('Auteur', 'boss-seo')}
                    value={filters.author}
                    options={authorOptions}
                    onChange={(value) => onFilterChange('author', value)}
                    className="boss-mb-0"
                  />
                </div>
                
                {/* Filtre par date */}
                <div className="boss-w-48">
                  <SelectControl
                    label={__('Date', 'boss-seo')}
                    value={filters.dateRange}
                    options={dateRangeOptions}
                    onChange={(value) => onFilterChange('dateRange', value)}
                    className="boss-mb-0"
                  />
                </div>
              </>
            )}
            
            {/* Bouton pour afficher/masquer les filtres supplémentaires */}
            <Button
              isSecondary
              onClick={() => setIsFiltersExpanded(!isFiltersExpanded)}
              className="boss-flex boss-items-center"
            >
              {isFiltersExpanded ? (
                <>
                  <Dashicon icon="arrow-up-alt2" className="boss-mr-1" />
                  {__('Moins de filtres', 'boss-seo')}
                </>
              ) : (
                <>
                  <Dashicon icon="arrow-down-alt2" className="boss-mr-1" />
                  {__('Plus de filtres', 'boss-seo')}
                </>
              )}
            </Button>
          </div>
          
          {/* Menu pour personnaliser les colonnes */}
          <div>
            <Dropdown
              className="boss-ml-4"
              contentClassName="boss-min-w-[200px]"
              renderToggle={({ isOpen, onToggle }) => (
                <Button
                  isSecondary
                  onClick={onToggle}
                  aria-expanded={isOpen}
                  className="boss-flex boss-items-center"
                >
                  <Dashicon icon="admin-generic" className="boss-mr-1" />
                  {__('Colonnes', 'boss-seo')}
                </Button>
              )}
              renderContent={() => (
                <MenuGroup label={__('Colonnes visibles', 'boss-seo')}>
                  <div className="boss-p-3 boss-space-y-3">
                    {Object.entries(columnLabels).map(([key, label]) => (
                      <ToggleControl
                        key={key}
                        label={label}
                        checked={visibleColumns[key]}
                        onChange={() => onColumnToggle(key)}
                        disabled={key === 'title'} // Le titre est toujours visible
                      />
                    ))}
                  </div>
                </MenuGroup>
              )}
            />
          </div>
        </div>
        
        {/* Indicateurs de filtres actifs */}
        <div className="boss-flex boss-flex-wrap boss-items-center boss-mt-4 boss-gap-2">
          {filters.contentType !== 'all' && (
            <div className="boss-bg-gray-100 boss-rounded-full boss-px-3 boss-py-1 boss-text-sm boss-flex boss-items-center">
              <span className="boss-mr-1">{__('Type:', 'boss-seo')}</span>
              <span className="boss-font-medium">
                {contentTypeOptions.find(opt => opt.value === filters.contentType)?.label}
              </span>
              <Button
                isSmall
                icon="dismiss"
                onClick={() => onFilterChange('contentType', 'all')}
                className="boss-ml-1 boss-p-0 boss-h-auto boss-min-w-0"
              />
            </div>
          )}
          
          {filters.seoScore !== 'all' && (
            <div className="boss-bg-gray-100 boss-rounded-full boss-px-3 boss-py-1 boss-text-sm boss-flex boss-items-center">
              <span className="boss-mr-1">{__('Score:', 'boss-seo')}</span>
              <span className="boss-font-medium">
                {seoScoreOptions.find(opt => opt.value === filters.seoScore)?.label}
              </span>
              <Button
                isSmall
                icon="dismiss"
                onClick={() => onFilterChange('seoScore', 'all')}
                className="boss-ml-1 boss-p-0 boss-h-auto boss-min-w-0"
              />
            </div>
          )}
          
          {filters.status !== 'all' && (
            <div className="boss-bg-gray-100 boss-rounded-full boss-px-3 boss-py-1 boss-text-sm boss-flex boss-items-center">
              <span className="boss-mr-1">{__('Statut:', 'boss-seo')}</span>
              <span className="boss-font-medium">
                {statusOptions.find(opt => opt.value === filters.status)?.label}
              </span>
              <Button
                isSmall
                icon="dismiss"
                onClick={() => onFilterChange('status', 'all')}
                className="boss-ml-1 boss-p-0 boss-h-auto boss-min-w-0"
              />
            </div>
          )}
          
          {filters.author !== 'all' && (
            <div className="boss-bg-gray-100 boss-rounded-full boss-px-3 boss-py-1 boss-text-sm boss-flex boss-items-center">
              <span className="boss-mr-1">{__('Auteur:', 'boss-seo')}</span>
              <span className="boss-font-medium">{filters.author}</span>
              <Button
                isSmall
                icon="dismiss"
                onClick={() => onFilterChange('author', 'all')}
                className="boss-ml-1 boss-p-0 boss-h-auto boss-min-w-0"
              />
            </div>
          )}
          
          {filters.dateRange !== 'all' && (
            <div className="boss-bg-gray-100 boss-rounded-full boss-px-3 boss-py-1 boss-text-sm boss-flex boss-items-center">
              <span className="boss-mr-1">{__('Date:', 'boss-seo')}</span>
              <span className="boss-font-medium">
                {dateRangeOptions.find(opt => opt.value === filters.dateRange)?.label}
              </span>
              <Button
                isSmall
                icon="dismiss"
                onClick={() => onFilterChange('dateRange', 'all')}
                className="boss-ml-1 boss-p-0 boss-h-auto boss-min-w-0"
              />
            </div>
          )}
          
          {/* Bouton pour réinitialiser tous les filtres */}
          {(filters.contentType !== 'all' || 
            filters.seoScore !== 'all' || 
            filters.status !== 'all' || 
            filters.author !== 'all' || 
            filters.dateRange !== 'all') && (
            <Button
              isSmall
              isDestructive
              onClick={() => {
                onFilterChange('contentType', 'all');
                onFilterChange('seoScore', 'all');
                onFilterChange('status', 'all');
                onFilterChange('author', 'all');
                onFilterChange('dateRange', 'all');
              }}
              className="boss-ml-2"
            >
              {__('Réinitialiser tous les filtres', 'boss-seo')}
            </Button>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

export default FilterBar;
