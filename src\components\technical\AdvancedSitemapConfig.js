import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  ToggleControl,
  SelectControl,
  CheckboxControl,
  RangeControl,
  TextControl,
  TabPanel,
  Notice,
  Spinner,
  Dashicon
} from '@wordpress/components';

// Importer le service
import RobotsSitemapService from '../../services/RobotsSitemapService';

/**
 * Composant pour la configuration des sitemaps avancés
 */
const AdvancedSitemapConfig = ({
  sitemapSettings,
  setSitemapSettings,
  onSave,
  isSaving,
  contentTypes,
  taxonomies
}) => {
  const [activeTab, setActiveTab] = useState('general');
  const [generationHistory, setGenerationHistory] = useState([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [customUrls, setCustomUrls] = useState([]);
  const [newCustomUrl, setNewCustomUrl] = useState({
    loc: '',
    lastmod: '',
    changefreq: 'monthly',
    priority: '0.5'
  });
  const [isPinging, setPinging] = useState(false);
  const [pingResults, setPingResults] = useState(null);

  // Charger l'historique des générations
  useEffect(() => {
    const loadHistory = async () => {
      setIsLoadingHistory(true);
      try {
        const response = await RobotsSitemapService.getSitemapGenerationHistory();
        setGenerationHistory(response.history || []);
      } catch (error) {
        console.error('Erreur lors du chargement de l\'historique des générations:', error);
      } finally {
        setIsLoadingHistory(false);
      }
    };

    loadHistory();
  }, []);

  // Charger les URLs personnalisées
  useEffect(() => {
    const loadCustomUrls = async () => {
      try {
        const response = await RobotsSitemapService.getCustomUrls();
        setCustomUrls(response.urls || []);
      } catch (error) {
        console.error('Erreur lors du chargement des URLs personnalisées:', error);
      }
    };

    loadCustomUrls();
  }, []);

  // Fonction pour régénérer le sitemap
  const regenerateSitemap = async () => {
    try {
      const response = await RobotsSitemapService.regenerateSitemap();
      // Recharger l'historique après la régénération
      const historyResponse = await RobotsSitemapService.getSitemapGenerationHistory();
      setGenerationHistory(historyResponse.history || []);
      return response;
    } catch (error) {
      console.error('Erreur lors de la régénération du sitemap:', error);
      throw error;
    }
  };

  // Fonction pour ping les moteurs de recherche
  const pingSearchEngines = async () => {
    setPinging(true);
    try {
      const response = await RobotsSitemapService.pingSearchEngines();
      setPingResults(response.results);
      setTimeout(() => {
        setPingResults(null);
      }, 5000);
    } catch (error) {
      console.error('Erreur lors du ping des moteurs de recherche:', error);
    } finally {
      setPinging(false);
    }
  };

  // Fonction pour ajouter une URL personnalisée
  const addCustomUrl = () => {
    if (!newCustomUrl.loc) return;

    const updatedUrls = [...customUrls, newCustomUrl];
    setCustomUrls(updatedUrls);
    setNewCustomUrl({
      loc: '',
      lastmod: '',
      changefreq: 'monthly',
      priority: '0.5'
    });

    // Mettre à jour les paramètres du sitemap
    const updatedSettings = {
      ...sitemapSettings,
      customUrls: updatedUrls
    };
    setSitemapSettings(updatedSettings);
  };

  // Fonction pour supprimer une URL personnalisée
  const removeCustomUrl = (index) => {
    const updatedUrls = [...customUrls];
    updatedUrls.splice(index, 1);
    setCustomUrls(updatedUrls);

    // Mettre à jour les paramètres du sitemap
    const updatedSettings = {
      ...sitemapSettings,
      customUrls: updatedUrls
    };
    setSitemapSettings(updatedSettings);
  };

  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <div className="boss-flex boss-justify-between boss-items-center">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Configuration avancée du sitemap', 'boss-seo')}
          </h2>
          <div className="boss-flex boss-space-x-2">
            <Button
              isPrimary
              onClick={regenerateSitemap}
              isBusy={isSaving}
              disabled={isSaving}
            >
              {__('Régénérer tous les sitemaps', 'boss-seo')}
            </Button>
            <Button
              isSecondary
              onClick={pingSearchEngines}
              isBusy={isPinging}
              disabled={isPinging}
            >
              {__('Ping Google & Bing', 'boss-seo')}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardBody>
        {pingResults && (
          <Notice status="success" isDismissible={false} className="boss-mb-4">
            <p>{__('Résultats du ping:', 'boss-seo')}</p>
            <ul className="boss-list-disc boss-pl-5 boss-mt-2">
              <li>
                Google: {pingResults.google.success ? __('Succès', 'boss-seo') : __('Échec', 'boss-seo')}
                {pingResults.google.message && ` (${pingResults.google.message})`}
              </li>
              <li>
                Bing: {pingResults.bing.success ? __('Succès', 'boss-seo') : __('Échec', 'boss-seo')}
                {pingResults.bing.message && ` (${pingResults.bing.message})`}
              </li>
            </ul>
          </Notice>
        )}

        <div className="boss-p-4 boss-bg-blue-50 boss-rounded-lg boss-mb-6">
          <div className="boss-flex boss-items-start">
            <div className="boss-flex-shrink-0 boss-mr-3">
              <Dashicon icon="info" className="boss-text-blue-600" />
            </div>
            <div>
              <p className="boss-text-sm boss-text-boss-dark">
                {__('Votre sitemap XML principal est disponible à l\'adresse:', 'boss-seo')}
              </p>
              <a
                href={`${window.location.origin}/sitemap.xml`}
                target="_blank"
                rel="noopener noreferrer"
                className="boss-text-sm boss-text-blue-600 boss-font-medium boss-underline"
              >
                {`${window.location.origin}/sitemap.xml`}
              </a>
              <p className="boss-text-sm boss-text-boss-dark boss-mt-2">
                {__('Les sitemaps spécialisés sont accessibles via le sitemap principal.', 'boss-seo')}
              </p>
            </div>
          </div>
        </div>

        <TabPanel
          className="boss-mb-6"
          activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
          onSelect={(tabName) => setActiveTab(tabName)}
          tabs={[
            {
              name: 'general',
              title: __('Général', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'types',
              title: __('Types de sitemaps', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'custom',
              title: __('URLs personnalisées', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'history',
              title: __('Historique', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            }
          ]}
        >
          {(tab) => {
            if (tab.name === 'general') {
              return (
                <div className="boss-space-y-6">
                  <ToggleControl
                    label={__('Activer les sitemaps avancés', 'boss-seo')}
                    checked={sitemapSettings.enabled}
                    onChange={(value) => setSitemapSettings({ ...sitemapSettings, enabled: value })}
                    className="boss-mb-4"
                  />

                  {sitemapSettings.enabled && (
                    <>
                      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
                        <div>
                          <h3 className="boss-text-md boss-font-semibold boss-mb-4">
                            {__('Types de contenu à inclure', 'boss-seo')}
                          </h3>
                          <div className="boss-space-y-2">
                            {contentTypes.map((type) => (
                              <CheckboxControl
                                key={type.name}
                                label={type.label}
                                checked={sitemapSettings.includedPostTypes?.includes(type.name)}
                                onChange={(checked) => {
                                  const includedPostTypes = [...(sitemapSettings.includedPostTypes || [])];
                                  if (checked) {
                                    includedPostTypes.push(type.name);
                                  } else {
                                    const index = includedPostTypes.indexOf(type.name);
                                    if (index !== -1) {
                                      includedPostTypes.splice(index, 1);
                                    }
                                  }
                                  setSitemapSettings({ ...sitemapSettings, includedPostTypes });
                                }}
                              />
                            ))}
                          </div>
                        </div>

                        <div>
                          <h3 className="boss-text-md boss-font-semibold boss-mb-4">
                            {__('Taxonomies à inclure', 'boss-seo')}
                          </h3>
                          <div className="boss-space-y-2">
                            {taxonomies.map((tax) => (
                              <CheckboxControl
                                key={tax.name}
                                label={tax.label}
                                checked={sitemapSettings.includedTaxonomies?.includes(tax.name)}
                                onChange={(checked) => {
                                  const includedTaxonomies = [...(sitemapSettings.includedTaxonomies || [])];
                                  if (checked) {
                                    includedTaxonomies.push(tax.name);
                                  } else {
                                    const index = includedTaxonomies.indexOf(tax.name);
                                    if (index !== -1) {
                                      includedTaxonomies.splice(index, 1);
                                    }
                                  }
                                  setSitemapSettings({ ...sitemapSettings, includedTaxonomies });
                                }}
                              />
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
                        <div>
                          <SelectControl
                            label={__('Fréquence de changement par défaut', 'boss-seo')}
                            value={sitemapSettings.defaultChangeFreq || 'weekly'}
                            options={[
                              { label: __('Toujours', 'boss-seo'), value: 'always' },
                              { label: __('Horaire', 'boss-seo'), value: 'hourly' },
                              { label: __('Quotidien', 'boss-seo'), value: 'daily' },
                              { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                              { label: __('Mensuel', 'boss-seo'), value: 'monthly' },
                              { label: __('Annuel', 'boss-seo'), value: 'yearly' },
                              { label: __('Jamais', 'boss-seo'), value: 'never' }
                            ]}
                            onChange={(value) => setSitemapSettings({ ...sitemapSettings, defaultChangeFreq: value })}
                          />
                        </div>

                        <div>
                          <RangeControl
                            label={__('Priorité par défaut', 'boss-seo')}
                            value={parseFloat(sitemapSettings.defaultPriority || 0.7)}
                            onChange={(value) => setSitemapSettings({ ...sitemapSettings, defaultPriority: value })}
                            min={0.1}
                            max={1.0}
                            step={0.1}
                          />
                        </div>
                      </div>

                      <div className="boss-space-y-4">
                        <ToggleControl
                          label={__('Inclure les images', 'boss-seo')}
                          checked={sitemapSettings.includeImages}
                          onChange={(value) => setSitemapSettings({ ...sitemapSettings, includeImages: value })}
                        />

                        <ToggleControl
                          label={__('Inclure les dates de dernière modification', 'boss-seo')}
                          checked={sitemapSettings.includeLastMod}
                          onChange={(value) => setSitemapSettings({ ...sitemapSettings, includeLastMod: value })}
                        />

                        <ToggleControl
                          label={__('Mise à jour automatique des sitemaps', 'boss-seo')}
                          checked={sitemapSettings.enableAutoUpdate}
                          onChange={(value) => setSitemapSettings({ ...sitemapSettings, enableAutoUpdate: value })}
                        />

                        {sitemapSettings.enableAutoUpdate && (
                          <SelectControl
                            label={__('Fréquence de mise à jour', 'boss-seo')}
                            value={sitemapSettings.autoUpdateFrequency || 'daily'}
                            options={[
                              { label: __('Horaire', 'boss-seo'), value: 'hourly' },
                              { label: __('Quotidien', 'boss-seo'), value: 'daily' },
                              { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' }
                            ]}
                            onChange={(value) => setSitemapSettings({ ...sitemapSettings, autoUpdateFrequency: value })}
                          />
                        )}
                      </div>
                    </>
                  )}
                </div>
              );
            } else if (tab.name === 'types') {
              return (
                <div className="boss-space-y-6">
                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
                    <Card className="boss-border boss-border-gray-200">
                      <CardHeader className="boss-border-b boss-border-gray-200 boss-bg-gray-50">
                        <h3 className="boss-text-md boss-font-semibold">
                          {__('Sitemap d\'images', 'boss-seo')}
                        </h3>
                      </CardHeader>
                      <CardBody>
                        <ToggleControl
                          label={__('Activer le sitemap d\'images', 'boss-seo')}
                          checked={sitemapSettings.enableImageSitemap}
                          onChange={(value) => setSitemapSettings({ ...sitemapSettings, enableImageSitemap: value })}
                          className="boss-mb-4"
                        />
                        <p className="boss-text-sm boss-text-boss-gray">
                          {__('Inclut toutes les images de vos articles et pages dans un sitemap dédié.', 'boss-seo')}
                        </p>
                        {sitemapSettings.enableImageSitemap && (
                          <a
                            href={`${window.location.origin}/sitemap-image.xml`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="boss-text-sm boss-text-blue-600 boss-font-medium boss-underline boss-mt-2 boss-inline-block"
                          >
                            {__('Voir le sitemap d\'images', 'boss-seo')}
                          </a>
                        )}
                      </CardBody>
                    </Card>

                    <Card className="boss-border boss-border-gray-200">
                      <CardHeader className="boss-border-b boss-border-gray-200 boss-bg-gray-50">
                        <h3 className="boss-text-md boss-font-semibold">
                          {__('Sitemap de vidéos', 'boss-seo')}
                        </h3>
                      </CardHeader>
                      <CardBody>
                        <ToggleControl
                          label={__('Activer le sitemap de vidéos', 'boss-seo')}
                          checked={sitemapSettings.enableVideoSitemap}
                          onChange={(value) => setSitemapSettings({ ...sitemapSettings, enableVideoSitemap: value })}
                          className="boss-mb-4"
                        />
                        <p className="boss-text-sm boss-text-boss-gray">
                          {__('Inclut toutes les vidéos intégrées dans vos articles et pages.', 'boss-seo')}
                        </p>
                        {sitemapSettings.enableVideoSitemap && (
                          <a
                            href={`${window.location.origin}/sitemap-video.xml`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="boss-text-sm boss-text-blue-600 boss-font-medium boss-underline boss-mt-2 boss-inline-block"
                          >
                            {__('Voir le sitemap de vidéos', 'boss-seo')}
                          </a>
                        )}
                      </CardBody>
                    </Card>
                  </div>

                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
                    <Card className="boss-border boss-border-gray-200">
                      <CardHeader className="boss-border-b boss-border-gray-200 boss-bg-gray-50">
                        <h3 className="boss-text-md boss-font-semibold">
                          {__('Sitemap de Web Stories', 'boss-seo')}
                        </h3>
                      </CardHeader>
                      <CardBody>
                        <ToggleControl
                          label={__('Activer le sitemap de Web Stories', 'boss-seo')}
                          checked={sitemapSettings.enableStoriesSitemap}
                          onChange={(value) => setSitemapSettings({ ...sitemapSettings, enableStoriesSitemap: value })}
                          className="boss-mb-4"
                        />
                        <p className="boss-text-sm boss-text-boss-gray">
                          {__('Inclut toutes les Web Stories créées avec le plugin "Web Stories by Google".', 'boss-seo')}
                        </p>
                        {sitemapSettings.enableStoriesSitemap && (
                          <a
                            href={`${window.location.origin}/sitemap-stories.xml`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="boss-text-sm boss-text-blue-600 boss-font-medium boss-underline boss-mt-2 boss-inline-block"
                          >
                            {__('Voir le sitemap de Web Stories', 'boss-seo')}
                          </a>
                        )}
                      </CardBody>
                    </Card>

                    <Card className="boss-border boss-border-gray-200">
                      <CardHeader className="boss-border-b boss-border-gray-200 boss-bg-gray-50">
                        <h3 className="boss-text-md boss-font-semibold">
                          {__('Sitemap de news', 'boss-seo')}
                        </h3>
                      </CardHeader>
                      <CardBody>
                        <ToggleControl
                          label={__('Activer le sitemap de news', 'boss-seo')}
                          checked={sitemapSettings.enableNewsSitemap}
                          onChange={(value) => setSitemapSettings({ ...sitemapSettings, enableNewsSitemap: value })}
                          className="boss-mb-4"
                        />
                        <p className="boss-text-sm boss-text-boss-gray">
                          {__('Inclut les articles publiés au cours des dernières 48 heures.', 'boss-seo')}
                        </p>
                        {sitemapSettings.enableNewsSitemap && (
                          <>
                            <a
                              href={`${window.location.origin}/sitemap-news.xml`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="boss-text-sm boss-text-blue-600 boss-font-medium boss-underline boss-mt-2 boss-inline-block boss-mb-4"
                            >
                              {__('Voir le sitemap de news', 'boss-seo')}
                            </a>
                            <div>
                              <h4 className="boss-text-sm boss-font-semibold boss-mb-2">
                                {__('Types de contenu pour le sitemap de news', 'boss-seo')}
                              </h4>
                              <div className="boss-space-y-2">
                                {contentTypes.map((type) => (
                                  <CheckboxControl
                                    key={`news-${type.name}`}
                                    label={type.label}
                                    checked={sitemapSettings.newsPostTypes?.includes(type.name)}
                                    onChange={(checked) => {
                                      const newsPostTypes = [...(sitemapSettings.newsPostTypes || [])];
                                      if (checked) {
                                        newsPostTypes.push(type.name);
                                      } else {
                                        const index = newsPostTypes.indexOf(type.name);
                                        if (index !== -1) {
                                          newsPostTypes.splice(index, 1);
                                        }
                                      }
                                      setSitemapSettings({ ...sitemapSettings, newsPostTypes });
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                          </>
                        )}
                      </CardBody>
                    </Card>
                  </div>

                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
                    <Card className="boss-border boss-border-gray-200">
                      <CardHeader className="boss-border-b boss-border-gray-200 boss-bg-gray-50">
                        <h3 className="boss-text-md boss-font-semibold">
                          {__('Sitemap personnalisé', 'boss-seo')}
                        </h3>
                      </CardHeader>
                      <CardBody>
                        <ToggleControl
                          label={__('Activer le sitemap personnalisé', 'boss-seo')}
                          checked={sitemapSettings.enableCustomSitemap}
                          onChange={(value) => setSitemapSettings({ ...sitemapSettings, enableCustomSitemap: value })}
                          className="boss-mb-4"
                        />
                        <p className="boss-text-sm boss-text-boss-gray">
                          {__('Inclut les URLs personnalisées que vous avez ajoutées manuellement.', 'boss-seo')}
                        </p>
                        {sitemapSettings.enableCustomSitemap && (
                          <a
                            href={`${window.location.origin}/sitemap-custom.xml`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="boss-text-sm boss-text-blue-600 boss-font-medium boss-underline boss-mt-2 boss-inline-block"
                          >
                            {__('Voir le sitemap personnalisé', 'boss-seo')}
                          </a>
                        )}
                      </CardBody>
                    </Card>

                    <Card className="boss-border boss-border-gray-200">
                      <CardHeader className="boss-border-b boss-border-gray-200 boss-bg-gray-50">
                        <h3 className="boss-text-md boss-font-semibold">
                          {__('Sitemaps de taxonomies', 'boss-seo')}
                        </h3>
                      </CardHeader>
                      <CardBody>
                        <ToggleControl
                          label={__('Activer les sitemaps de taxonomies', 'boss-seo')}
                          checked={sitemapSettings.enableTaxonomySitemaps}
                          onChange={(value) => setSitemapSettings({ ...sitemapSettings, enableTaxonomySitemaps: value })}
                          className="boss-mb-4"
                        />
                        <p className="boss-text-sm boss-text-boss-gray">
                          {__('Crée un sitemap séparé pour chaque taxonomie (catégories, étiquettes, etc.).', 'boss-seo')}
                        </p>
                        {sitemapSettings.enableTaxonomySitemaps && taxonomies.length > 0 && (
                          <div className="boss-mt-2">
                            <p className="boss-text-sm boss-font-medium boss-mb-1">
                              {__('Sitemaps de taxonomies disponibles:', 'boss-seo')}
                            </p>
                            <ul className="boss-list-disc boss-pl-5 boss-space-y-1">
                              {taxonomies.map((tax) => (
                                <li key={tax.name}>
                                  <a
                                    href={`${window.location.origin}/sitemap-tax-${tax.name}.xml`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="boss-text-sm boss-text-blue-600 boss-underline"
                                  >
                                    {tax.label}
                                  </a>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </CardBody>
                    </Card>
                  </div>
                </div>
              );
            } else if (tab.name === 'custom') {
              return (
                <div className="boss-space-y-6">
                  <div className="boss-p-4 boss-bg-yellow-50 boss-rounded-lg boss-mb-4">
                    <div className="boss-flex boss-items-start">
                      <div className="boss-flex-shrink-0 boss-mr-3">
                        <Dashicon icon="info" className="boss-text-yellow-600" />
                      </div>
                      <div>
                        <p className="boss-text-sm boss-text-boss-dark">
                          {__('Ajoutez ici des URLs personnalisées qui ne sont pas automatiquement incluses dans les sitemaps générés.', 'boss-seo')}
                        </p>
                        <p className="boss-text-sm boss-text-boss-dark boss-mt-2">
                          {__('Assurez-vous que les URLs sont accessibles et appartiennent à votre domaine.', 'boss-seo')}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="boss-mb-6">
                    <h3 className="boss-text-md boss-font-semibold boss-mb-4">
                      {__('Ajouter une URL personnalisée', 'boss-seo')}
                    </h3>
                    <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mb-4">
                      <TextControl
                        label={__('URL', 'boss-seo')}
                        value={newCustomUrl.loc}
                        onChange={(value) => setNewCustomUrl({ ...newCustomUrl, loc: value })}
                        placeholder="https://example.com/page"
                      />
                      <TextControl
                        label={__('Date de dernière modification (YYYY-MM-DD)', 'boss-seo')}
                        value={newCustomUrl.lastmod}
                        onChange={(value) => setNewCustomUrl({ ...newCustomUrl, lastmod: value })}
                        placeholder="2023-01-01"
                      />
                    </div>
                    <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mb-4">
                      <SelectControl
                        label={__('Fréquence de changement', 'boss-seo')}
                        value={newCustomUrl.changefreq}
                        options={[
                          { label: __('Toujours', 'boss-seo'), value: 'always' },
                          { label: __('Horaire', 'boss-seo'), value: 'hourly' },
                          { label: __('Quotidien', 'boss-seo'), value: 'daily' },
                          { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                          { label: __('Mensuel', 'boss-seo'), value: 'monthly' },
                          { label: __('Annuel', 'boss-seo'), value: 'yearly' },
                          { label: __('Jamais', 'boss-seo'), value: 'never' }
                        ]}
                        onChange={(value) => setNewCustomUrl({ ...newCustomUrl, changefreq: value })}
                      />
                      <SelectControl
                        label={__('Priorité', 'boss-seo')}
                        value={newCustomUrl.priority}
                        options={[
                          { label: '0.1', value: '0.1' },
                          { label: '0.2', value: '0.2' },
                          { label: '0.3', value: '0.3' },
                          { label: '0.4', value: '0.4' },
                          { label: '0.5', value: '0.5' },
                          { label: '0.6', value: '0.6' },
                          { label: '0.7', value: '0.7' },
                          { label: '0.8', value: '0.8' },
                          { label: '0.9', value: '0.9' },
                          { label: '1.0', value: '1.0' }
                        ]}
                        onChange={(value) => setNewCustomUrl({ ...newCustomUrl, priority: value })}
                      />
                    </div>
                    <Button
                      isPrimary
                      onClick={addCustomUrl}
                      disabled={!newCustomUrl.loc}
                    >
                      {__('Ajouter l\'URL', 'boss-seo')}
                    </Button>
                  </div>

                  <div>
                    <h3 className="boss-text-md boss-font-semibold boss-mb-4">
                      {__('URLs personnalisées', 'boss-seo')}
                    </h3>
                    {customUrls.length === 0 ? (
                      <p className="boss-text-boss-gray boss-italic">
                        {__('Aucune URL personnalisée n\'a été ajoutée.', 'boss-seo')}
                      </p>
                    ) : (
                      <div className="boss-overflow-x-auto">
                        <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
                          <thead className="boss-bg-gray-50">
                            <tr>
                              <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                                {__('URL', 'boss-seo')}
                              </th>
                              <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                                {__('Dernière modification', 'boss-seo')}
                              </th>
                              <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                                {__('Fréquence', 'boss-seo')}
                              </th>
                              <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                                {__('Priorité', 'boss-seo')}
                              </th>
                              <th className="boss-px-6 boss-py-3 boss-text-right boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                                {__('Actions', 'boss-seo')}
                              </th>
                            </tr>
                          </thead>
                          <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                            {customUrls.map((url, index) => (
                              <tr key={index}>
                                <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-sm boss-text-boss-dark">
                                  <a
                                    href={url.loc}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="boss-text-blue-600 boss-underline"
                                  >
                                    {url.loc}
                                  </a>
                                </td>
                                <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-sm boss-text-boss-gray">
                                  {url.lastmod || '-'}
                                </td>
                                <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-sm boss-text-boss-gray">
                                  {url.changefreq || '-'}
                                </td>
                                <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-sm boss-text-boss-gray">
                                  {url.priority || '-'}
                                </td>
                                <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-right boss-text-sm boss-font-medium">
                                  <Button
                                    isDestructive
                                    isSmall
                                    onClick={() => removeCustomUrl(index)}
                                  >
                                    {__('Supprimer', 'boss-seo')}
                                  </Button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>
              );
            } else if (tab.name === 'history') {
              return (
                <div className="boss-space-y-6">
                  <div className="boss-p-4 boss-bg-blue-50 boss-rounded-lg boss-mb-4">
                    <div className="boss-flex boss-items-start">
                      <div className="boss-flex-shrink-0 boss-mr-3">
                        <Dashicon icon="info" className="boss-text-blue-600" />
                      </div>
                      <div>
                        <p className="boss-text-sm boss-text-boss-dark">
                          {__('L\'historique des générations de sitemaps vous permet de suivre les mises à jour et de détecter d\'éventuels problèmes.', 'boss-seo')}
                        </p>
                      </div>
                    </div>
                  </div>

                  {isLoadingHistory ? (
                    <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
                      <Spinner />
                    </div>
                  ) : (
                    <>
                      {generationHistory.length === 0 ? (
                        <p className="boss-text-boss-gray boss-italic">
                          {__('Aucun historique de génération disponible.', 'boss-seo')}
                        </p>
                      ) : (
                        <div className="boss-overflow-x-auto">
                          <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
                            <thead className="boss-bg-gray-50">
                              <tr>
                                <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                                  {__('Date', 'boss-seo')}
                                </th>
                                <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                                  {__('Type de sitemap', 'boss-seo')}
                                </th>
                                <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                                  {__('Statut', 'boss-seo')}
                                </th>
                                <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                                  {__('Message', 'boss-seo')}
                                </th>
                              </tr>
                            </thead>
                            <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                              {generationHistory.map((entry, index) => (
                                <tr key={index}>
                                  <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-sm boss-text-boss-dark">
                                    {entry.timestamp}
                                  </td>
                                  <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-sm boss-text-boss-gray">
                                    {entry.type === 'index' ? __('Sitemap principal', 'boss-seo') :
                                     entry.type === 'image' ? __('Sitemap d\'images', 'boss-seo') :
                                     entry.type === 'video' ? __('Sitemap de vidéos', 'boss-seo') :
                                     entry.type === 'stories' ? __('Sitemap de Web Stories', 'boss-seo') :
                                     entry.type === 'news' ? __('Sitemap de news', 'boss-seo') :
                                     entry.type === 'custom' ? __('Sitemap personnalisé', 'boss-seo') :
                                     entry.type.startsWith('tax-') ? __('Sitemap de taxonomie', 'boss-seo') + ': ' + entry.type.replace('tax-', '') :
                                     entry.type}
                                  </td>
                                  <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-sm">
                                    <span className={`boss-px-2 boss-py-1 boss-rounded-full boss-text-xs ${
                                      entry.status === 'success' ? 'boss-bg-green-100 boss-text-green-800' :
                                      entry.status === 'warning' ? 'boss-bg-yellow-100 boss-text-yellow-800' :
                                      'boss-bg-red-100 boss-text-red-800'
                                    }`}>
                                      {entry.status === 'success' ? __('Succès', 'boss-seo') :
                                       entry.status === 'warning' ? __('Avertissement', 'boss-seo') :
                                       __('Erreur', 'boss-seo')}
                                    </span>
                                  </td>
                                  <td className="boss-px-6 boss-py-4 boss-text-sm boss-text-boss-gray">
                                    {entry.message || '-'}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      )}
                    </>
                  )}
                </div>
              );
            }
            return null;
          }}
        </TabPanel>
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200 boss-flex boss-justify-end">
        <Button
          isPrimary
          onClick={() => onSave(sitemapSettings)}
          isBusy={isSaving}
          disabled={isSaving}
        >
          {__('Enregistrer', 'boss-seo')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default AdvancedSitemapConfig;
