<?php
/**
 * Test de l'interface Boss SEO
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🎯 TEST DE L'INTERFACE BOSS SEO\n";
echo "================================\n";

// Simuler WordPress complet
if (!defined('WPINC')) {
    define('WPINC', true);
}

// Simuler les fonctions WordPress
if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) {
        return dirname($file) . '/';
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return 'http://localhost/boss-seo/';
    }
}

if (!function_exists('update_option')) {
    function update_option($option, $value) {
        echo "✅ Option: $option = $value\n";
        return true;
    }
}

if (!function_exists('current_time')) {
    function current_time($format) {
        return date($format);
    }
}

if (!function_exists('set_transient')) {
    function set_transient($transient, $value, $expiration) {
        echo "✅ Transient créé: $transient\n";
        return true;
    }
}

if (!function_exists('get_transient')) {
    function get_transient($transient) {
        return true; // Simuler que le transient existe
    }
}

if (!function_exists('delete_transient')) {
    function delete_transient($transient) {
        echo "✅ Transient supprimé: $transient\n";
        return true;
    }
}

if (!function_exists('admin_url')) {
    function admin_url($path) {
        return 'http://localhost/wp-admin/' . $path;
    }
}

$menu_added = false;
$notice_added = false;

if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $accepted_args = 1) {
        global $menu_added, $notice_added;
        echo "✅ Hook ajouté: $hook\n";
        
        // Simuler l'exécution des hooks
        if ($hook === 'admin_menu' && is_callable($callback)) {
            echo "   Exécution du hook admin_menu...\n";
            call_user_func($callback);
            $menu_added = true;
        }
        
        if ($hook === 'admin_notices' && is_callable($callback)) {
            echo "   Exécution du hook admin_notices...\n";
            ob_start();
            call_user_func($callback);
            $output = ob_get_clean();
            if (!empty($output)) {
                echo "   Notice générée: " . strip_tags($output) . "\n";
                $notice_added = true;
            }
        }
    }
}

if (!function_exists('add_menu_page')) {
    function add_menu_page($page_title, $menu_title, $capability, $menu_slug, $function, $icon_url = '', $position = null) {
        echo "✅ Menu créé: $menu_title (slug: $menu_slug)\n";
        
        // Tester la fonction de page
        if (is_callable($function)) {
            echo "   Test de la page admin...\n";
            ob_start();
            call_user_func($function);
            $page_content = ob_get_clean();
            
            if (!empty($page_content)) {
                echo "   ✅ Page générée (" . strlen($page_content) . " caractères)\n";
                
                // Vérifier le contenu
                if (strpos($page_content, 'Boss SEO') !== false) {
                    echo "   ✅ Titre 'Boss SEO' trouvé\n";
                }
                if (strpos($page_content, 'activé avec succès') !== false) {
                    echo "   ✅ Message de succès trouvé\n";
                }
                if (strpos($page_content, 'Version:') !== false) {
                    echo "   ✅ Information de version trouvée\n";
                }
            } else {
                echo "   ❌ Aucun contenu généré\n";
            }
        }
    }
}

if (!function_exists('register_activation_hook')) {
    function register_activation_hook($file, $callback) {
        echo "✅ Hook d'activation enregistré\n";
        if (is_callable($callback)) {
            echo "   Exécution de l'activation...\n";
            call_user_func($callback);
        }
    }
}

if (!function_exists('register_deactivation_hook')) {
    function register_deactivation_hook($file, $callback) {
        echo "✅ Hook de désactivation enregistré\n";
    }
}

echo "1. Test du fichier principal...\n";
try {
    include 'boss-seo.php';
    echo "✅ Fichier principal chargé\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ Erreur fatale: " . $e->getMessage() . "\n";
}

echo "\n2. Vérification des résultats...\n";

if (defined('BOSS_SEO_VERSION')) {
    echo "✅ Version définie: " . BOSS_SEO_VERSION . "\n";
} else {
    echo "❌ Version non définie\n";
}

if (function_exists('boss_seo_add_admin_menu')) {
    echo "✅ Fonction menu existe\n";
} else {
    echo "❌ Fonction menu manquante\n";
}

if (function_exists('boss_seo_admin_page')) {
    echo "✅ Fonction page admin existe\n";
} else {
    echo "❌ Fonction page admin manquante\n";
}

if ($menu_added) {
    echo "✅ Menu admin ajouté avec succès\n";
} else {
    echo "❌ Menu admin non ajouté\n";
}

if ($notice_added) {
    echo "✅ Notice d'activation affichée\n";
} else {
    echo "❌ Notice d'activation non affichée\n";
}

echo "\n================================\n";
echo "🎯 RÉSULTAT FINAL: ";
if (defined('BOSS_SEO_VERSION') && function_exists('boss_seo_add_admin_menu') && $menu_added) {
    echo "INTERFACE FONCTIONNELLE ! ✅\n";
    echo "\nTu devrais maintenant voir :\n";
    echo "- Menu 'Boss SEO' dans l'admin WordPress\n";
    echo "- Notice de succès après activation\n";
    echo "- Page d'administration avec infos du plugin\n";
} else {
    echo "PROBLÈME DÉTECTÉ ❌\n";
}
echo "================================\n";
?>
