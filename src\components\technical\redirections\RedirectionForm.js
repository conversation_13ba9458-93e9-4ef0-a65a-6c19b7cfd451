import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  TextControl,
  SelectControl,
  ToggleControl,
  Notice
} from '@wordpress/components';

const RedirectionForm = ({ 
  editingRedirection, 
  onSave, 
  onCancel,
  notFoundUrls
}) => {
  // États
  const [formData, setFormData] = useState({
    source: '',
    target: '',
    type: '301',
    status: 'active',
    matchCase: false,
    regex: false,
    notes: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [selectedNotFoundUrl, setSelectedNotFoundUrl] = useState('');
  
  // Mettre à jour le formulaire lorsqu'une redirection est en cours d'édition
  useEffect(() => {
    if (editingRedirection) {
      setFormData({
        id: editingRedirection.id,
        source: editingRedirection.source,
        target: editingRedirection.target,
        type: editingRedirection.type,
        status: editingRedirection.status,
        matchCase: editingRedirection.matchCase || false,
        regex: editingRedirection.regex || false,
        notes: editingRedirection.notes || ''
      });
    } else {
      // Réinitialiser le formulaire
      setFormData({
        source: '',
        target: '',
        type: '301',
        status: 'active',
        matchCase: false,
        regex: false,
        notes: ''
      });
    }
    
    // Réinitialiser les erreurs
    setFormErrors({});
    setSelectedNotFoundUrl('');
  }, [editingRedirection]);
  
  // Fonction pour mettre à jour les données du formulaire
  const updateFormData = (key, value) => {
    setFormData({
      ...formData,
      [key]: value
    });
    
    // Effacer l'erreur pour ce champ
    if (formErrors[key]) {
      setFormErrors({
        ...formErrors,
        [key]: null
      });
    }
  };
  
  // Fonction pour valider le formulaire
  const validateForm = () => {
    const errors = {};
    
    if (!formData.source) {
      errors.source = __('L\'URL source est requise', 'boss-seo');
    }
    
    if (!formData.target && formData.type !== '410') {
      errors.target = __('L\'URL de destination est requise', 'boss-seo');
    }
    
    if (formData.regex) {
      try {
        new RegExp(formData.source);
      } catch (e) {
        errors.source = __('Expression régulière invalide', 'boss-seo');
      }
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  // Fonction pour gérer la soumission du formulaire
  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };
  
  // Fonction pour sélectionner une URL 404
  const handleSelectNotFoundUrl = () => {
    if (selectedNotFoundUrl) {
      updateFormData('source', selectedNotFoundUrl);
      setSelectedNotFoundUrl('');
    }
  };

  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {editingRedirection 
            ? __('Modifier la redirection', 'boss-seo') 
            : __('Ajouter une redirection', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6 boss-mb-6">
          <div>
            <TextControl
              label={__('URL source', 'boss-seo')}
              help={__('URL à rediriger (chemin relatif, ex: /ancienne-page)', 'boss-seo')}
              value={formData.source}
              onChange={(value) => updateFormData('source', value)}
              className="boss-mb-4"
            />
            {formErrors.source && (
              <Notice status="error" isDismissible={false} className="boss-mb-4">
                {formErrors.source}
              </Notice>
            )}
            
            {notFoundUrls && notFoundUrls.length > 0 && (
              <div className="boss-mb-4">
                <div className="boss-flex boss-space-x-2">
                  <SelectControl
                    label={__('URLs 404 détectées', 'boss-seo')}
                    value={selectedNotFoundUrl}
                    options={[
                      { label: __('-- Sélectionner une URL --', 'boss-seo'), value: '' },
                      ...notFoundUrls.map(url => ({
                        label: url,
                        value: url
                      }))
                    ]}
                    onChange={setSelectedNotFoundUrl}
                    className="boss-flex-1"
                  />
                  <div className="boss-flex boss-items-end boss-mb-2">
                    <Button
                      isSecondary
                      onClick={handleSelectNotFoundUrl}
                      disabled={!selectedNotFoundUrl}
                    >
                      {__('Utiliser', 'boss-seo')}
                    </Button>
                  </div>
                </div>
              </div>
            )}
            
            <TextControl
              label={__('URL de destination', 'boss-seo')}
              help={__('URL vers laquelle rediriger (chemin relatif ou URL complète)', 'boss-seo')}
              value={formData.target}
              onChange={(value) => updateFormData('target', value)}
              disabled={formData.type === '410'}
              className="boss-mb-4"
            />
            {formErrors.target && (
              <Notice status="error" isDismissible={false} className="boss-mb-4">
                {formErrors.target}
              </Notice>
            )}
            
            <SelectControl
              label={__('Type de redirection', 'boss-seo')}
              value={formData.type}
              options={[
                { label: __('301 - Permanente', 'boss-seo'), value: '301' },
                { label: __('302 - Temporaire', 'boss-seo'), value: '302' },
                { label: __('307 - Temporaire', 'boss-seo'), value: '307' },
                { label: __('410 - Contenu supprimé', 'boss-seo'), value: '410' },
                { label: __('451 - Indisponible pour raisons légales', 'boss-seo'), value: '451' }
              ]}
              onChange={(value) => updateFormData('type', value)}
              className="boss-mb-4"
            />
          </div>
          
          <div>
            <SelectControl
              label={__('Statut', 'boss-seo')}
              value={formData.status}
              options={[
                { label: __('Active', 'boss-seo'), value: 'active' },
                { label: __('Inactive', 'boss-seo'), value: 'inactive' }
              ]}
              onChange={(value) => updateFormData('status', value)}
              className="boss-mb-4"
            />
            
            <div className="boss-mb-4">
              <ToggleControl
                label={__('Sensible à la casse', 'boss-seo')}
                help={__('Respecter les majuscules et minuscules dans l\'URL source', 'boss-seo')}
                checked={formData.matchCase}
                onChange={(value) => updateFormData('matchCase', value)}
              />
            </div>
            
            <div className="boss-mb-4">
              <ToggleControl
                label={__('Utiliser une expression régulière', 'boss-seo')}
                help={__('Interpréter l\'URL source comme une expression régulière', 'boss-seo')}
                checked={formData.regex}
                onChange={(value) => updateFormData('regex', value)}
              />
            </div>
            
            <TextControl
              label={__('Notes', 'boss-seo')}
              help={__('Notes internes sur cette redirection (facultatif)', 'boss-seo')}
              value={formData.notes}
              onChange={(value) => updateFormData('notes', value)}
            />
          </div>
        </div>
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200">
        <div className="boss-flex boss-justify-end boss-space-x-3">
          <Button
            isSecondary
            onClick={onCancel}
          >
            {__('Annuler', 'boss-seo')}
          </Button>
          <Button
            isPrimary
            onClick={handleSubmit}
          >
            {editingRedirection ? __('Mettre à jour', 'boss-seo') : __('Ajouter', 'boss-seo')}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default RedirectionForm;
