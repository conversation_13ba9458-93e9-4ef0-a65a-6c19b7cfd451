<?php
/**
 * Script de validation des corrections du module Boss Optimizer.
 *
 * Ce script vérifie que toutes les corrections ont été appliquées correctement.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe de validation des corrections du module Boss Optimizer.
 */
class Boss_Optimizer_Fixes_Validator {

    /**
     * Résultats de la validation.
     *
     * @var array
     */
    private $results = array();

    /**
     * Exécute la validation complète.
     *
     * @return array Résultats de la validation.
     */
    public function run_validation() {
        $this->results = array(
            'endpoints' => $this->validate_api_endpoints(),
            'classes' => $this->validate_class_initialization(),
            'pagination' => $this->validate_pagination_fix(),
            'cache' => $this->validate_cache_system(),
            'permissions' => $this->validate_permissions(),
            'error_handling' => $this->validate_error_handling(),
            'performance' => $this->validate_performance_improvements()
        );

        return $this->results;
    }

    /**
     * Valide que tous les endpoints API existent.
     *
     * @return array Résultats de la validation des endpoints.
     */
    private function validate_api_endpoints() {
        $results = array(
            'status' => 'success',
            'message' => 'Tous les endpoints API sont présents',
            'details' => array()
        );

        $required_endpoints = array(
            '/boss-seo/v1/optimize/all',
            '/boss-seo/v1/analyze/all',
            '/boss-seo/v1/tags/add',
            '/boss-seo/v1/category/change',
            '/boss-seo/v1/author/change'
        );

        $api_file = plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-api.php';
        
        if ( ! file_exists( $api_file ) ) {
            $results['status'] = 'error';
            $results['message'] = 'Fichier API non trouvé';
            return $results;
        }

        $api_content = file_get_contents( $api_file );

        foreach ( $required_endpoints as $endpoint ) {
            $endpoint_name = str_replace( '/boss-seo/v1/', '', $endpoint );
            $endpoint_name = str_replace( '/', '_', $endpoint_name );
            
            if ( strpos( $api_content, $endpoint ) !== false ) {
                $results['details'][ $endpoint ] = 'Trouvé';
            } else {
                $results['details'][ $endpoint ] = 'Manquant';
                $results['status'] = 'error';
                $results['message'] = 'Certains endpoints sont manquants';
            }
        }

        return $results;
    }

    /**
     * Valide l'initialisation des classes.
     *
     * @return array Résultats de la validation des classes.
     */
    private function validate_class_initialization() {
        $results = array(
            'status' => 'success',
            'message' => 'Toutes les classes sont correctement initialisées',
            'details' => array()
        );

        $required_classes = array(
            'Boss_Optimizer_Content',
            'Boss_Optimizer_Analysis',
            'Boss_Optimizer_Recommendations',
            'Boss_Optimizer_AI',
            'Boss_Optimizer_API',
            'Boss_Optimizer_Cache'
        );

        foreach ( $required_classes as $class_name ) {
            $class_file = plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-' . strtolower( str_replace( '_', '-', $class_name ) ) . '.php';
            
            if ( file_exists( $class_file ) ) {
                $results['details'][ $class_name ] = 'Fichier trouvé';
            } else {
                $results['details'][ $class_name ] = 'Fichier manquant';
                $results['status'] = 'error';
                $results['message'] = 'Certaines classes sont manquantes';
            }
        }

        return $results;
    }

    /**
     * Valide la correction de la pagination.
     *
     * @return array Résultats de la validation de la pagination.
     */
    private function validate_pagination_fix() {
        $results = array(
            'status' => 'success',
            'message' => 'Correction de pagination appliquée',
            'details' => array()
        );

        $content_file = plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-content.php';
        
        if ( ! file_exists( $content_file ) ) {
            $results['status'] = 'error';
            $results['message'] = 'Fichier de contenu non trouvé';
            return $results;
        }

        $content = file_get_contents( $content_file );

        // Vérifier la présence de meta_query pour le filtrage par score SEO
        if ( strpos( $content, 'meta_query' ) !== false ) {
            $results['details']['meta_query'] = 'Trouvé';
        } else {
            $results['details']['meta_query'] = 'Manquant';
            $results['status'] = 'error';
            $results['message'] = 'Correction de pagination non appliquée';
        }

        // Vérifier la présence des requêtes groupées
        if ( strpos( $content, 'get_seo_scores_bulk' ) !== false ) {
            $results['details']['bulk_queries'] = 'Trouvé';
        } else {
            $results['details']['bulk_queries'] = 'Manquant';
            $results['status'] = 'warning';
            $results['message'] = 'Requêtes groupées partiellement implémentées';
        }

        return $results;
    }

    /**
     * Valide le système de cache.
     *
     * @return array Résultats de la validation du cache.
     */
    private function validate_cache_system() {
        $results = array(
            'status' => 'success',
            'message' => 'Système de cache implémenté',
            'details' => array()
        );

        $cache_file = plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-cache.php';
        
        if ( file_exists( $cache_file ) ) {
            $results['details']['cache_class'] = 'Trouvé';
            
            $cache_content = file_get_contents( $cache_file );
            
            // Vérifier les méthodes principales
            $required_methods = array(
                'set_seo_score',
                'get_seo_score',
                'set_recommendations',
                'get_recommendations',
                'invalidate_post_cache'
            );
            
            foreach ( $required_methods as $method ) {
                if ( strpos( $cache_content, $method ) !== false ) {
                    $results['details'][ $method ] = 'Trouvé';
                } else {
                    $results['details'][ $method ] = 'Manquant';
                    $results['status'] = 'error';
                    $results['message'] = 'Système de cache incomplet';
                }
            }
        } else {
            $results['status'] = 'error';
            $results['message'] = 'Classe de cache non trouvée';
            $results['details']['cache_class'] = 'Manquant';
        }

        return $results;
    }

    /**
     * Valide les permissions granulaires.
     *
     * @return array Résultats de la validation des permissions.
     */
    private function validate_permissions() {
        $results = array(
            'status' => 'success',
            'message' => 'Permissions granulaires implémentées',
            'details' => array()
        );

        $api_file = plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-api.php';
        
        if ( ! file_exists( $api_file ) ) {
            $results['status'] = 'error';
            $results['message'] = 'Fichier API non trouvé';
            return $results;
        }

        $api_content = file_get_contents( $api_file );

        // Vérifier la présence de vérifications de permissions granulaires
        if ( strpos( $api_content, 'current_user_can( \'edit_post\', $post_id )' ) !== false ) {
            $results['details']['granular_permissions'] = 'Trouvé';
        } else {
            $results['details']['granular_permissions'] = 'Manquant';
            $results['status'] = 'warning';
            $results['message'] = 'Permissions granulaires partiellement implémentées';
        }

        return $results;
    }

    /**
     * Valide la gestion d'erreurs améliorée.
     *
     * @return array Résultats de la validation de la gestion d'erreurs.
     */
    private function validate_error_handling() {
        $results = array(
            'status' => 'success',
            'message' => 'Gestion d\'erreurs améliorée',
            'details' => array()
        );

        $optimizer_file = plugin_dir_path( dirname( __FILE__ ) ) . 'src/pages/BossOptimizer.js';
        
        if ( file_exists( $optimizer_file ) ) {
            $optimizer_content = file_get_contents( $optimizer_file );
            
            // Vérifier la présence de la fonction de gestion d'erreurs
            if ( strpos( $optimizer_content, 'handleApiError' ) !== false ) {
                $results['details']['error_handler'] = 'Trouvé';
            } else {
                $results['details']['error_handler'] = 'Manquant';
                $results['status'] = 'warning';
                $results['message'] = 'Gestion d\'erreurs frontend non améliorée';
            }
        } else {
            $results['status'] = 'warning';
            $results['message'] = 'Fichier frontend non trouvé';
            $results['details']['frontend_file'] = 'Manquant';
        }

        return $results;
    }

    /**
     * Valide les améliorations de performance.
     *
     * @return array Résultats de la validation des performances.
     */
    private function validate_performance_improvements() {
        $results = array(
            'status' => 'success',
            'message' => 'Améliorations de performance appliquées',
            'details' => array()
        );

        $content_file = plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-content.php';
        
        if ( ! file_exists( $content_file ) ) {
            $results['status'] = 'error';
            $results['message'] = 'Fichier de contenu non trouvé';
            return $results;
        }

        $content = file_get_contents( $content_file );

        // Vérifier les requêtes groupées
        $performance_features = array(
            'get_seo_scores_bulk' => 'Requêtes groupées pour scores SEO',
            'get_recommendations_bulk' => 'Requêtes groupées pour recommandations',
            'fields.*ids' => 'Optimisation des requêtes WP_Query'
        );

        foreach ( $performance_features as $pattern => $description ) {
            if ( strpos( $content, str_replace( '.*', '', $pattern ) ) !== false ) {
                $results['details'][ $pattern ] = 'Trouvé';
            } else {
                $results['details'][ $pattern ] = 'Manquant';
                $results['status'] = 'warning';
                $results['message'] = 'Certaines améliorations de performance sont manquantes';
            }
        }

        return $results;
    }

    /**
     * Génère un rapport HTML des résultats.
     *
     * @return string Rapport HTML.
     */
    public function generate_html_report() {
        $html = '<div class="boss-optimizer-validation-report">';
        $html .= '<h2>🔧 Rapport de Validation - Corrections Boss Optimizer</h2>';

        foreach ( $this->results as $category => $result ) {
            $status_icon = $this->get_status_icon( $result['status'] );
            $status_class = 'validation-' . $result['status'];
            
            $html .= '<div class="validation-section ' . $status_class . '">';
            $html .= '<h3>' . $status_icon . ' ' . ucfirst( $category ) . '</h3>';
            $html .= '<p><strong>Statut:</strong> ' . $result['message'] . '</p>';
            
            if ( ! empty( $result['details'] ) ) {
                $html .= '<ul>';
                foreach ( $result['details'] as $item => $status ) {
                    $item_icon = ( $status === 'Trouvé' ) ? '✅' : '❌';
                    $html .= '<li>' . $item_icon . ' ' . $item . ': ' . $status . '</li>';
                }
                $html .= '</ul>';
            }
            
            $html .= '</div>';
        }

        $html .= '</div>';
        
        return $html;
    }

    /**
     * Récupère l'icône de statut.
     *
     * @param string $status Statut.
     * @return string Icône.
     */
    private function get_status_icon( $status ) {
        switch ( $status ) {
            case 'success':
                return '✅';
            case 'warning':
                return '⚠️';
            case 'error':
                return '❌';
            default:
                return '❓';
        }
    }
}

// Exécuter la validation si appelé directement
if ( isset( $_GET['run_validation'] ) && current_user_can( 'manage_options' ) ) {
    $validator = new Boss_Optimizer_Fixes_Validator();
    $results = $validator->run_validation();
    
    echo '<style>
        .boss-optimizer-validation-report { max-width: 800px; margin: 20px auto; font-family: Arial, sans-serif; }
        .validation-section { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .validation-success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .validation-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        .validation-error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .validation-section h3 { margin-top: 0; }
        .validation-section ul { margin: 10px 0; }
        .validation-section li { margin: 5px 0; }
    </style>';
    
    echo $validator->generate_html_report();
    exit;
}
