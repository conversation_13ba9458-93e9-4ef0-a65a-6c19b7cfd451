import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Dashicon,
  SelectControl,
  TextControl,
  ToggleControl,
  Tooltip,
  Modal
} from '@wordpress/components';

const OpportunityAnalysis = ({ data }) => {
  // États
  const [filterType, setFilterType] = useState('all');
  const [filterDifficulty, setFilterDifficulty] = useState('all');
  const [sortBy, setSortBy] = useState('potential');
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedOpportunity, setSelectedOpportunity] = useState(null);
  const [showModal, setShowModal] = useState(false);
  
  // Fonction pour filtrer les opportunités
  const getFilteredOpportunities = () => {
    if (!data) return [];
    
    let filtered = [...data];
    
    // Filtrer par type
    if (filterType !== 'all') {
      filtered = filtered.filter(opp => opp.type === filterType);
    }
    
    // Filtrer par difficulté
    if (filterDifficulty !== 'all') {
      filtered = filtered.filter(opp => opp.difficulty === filterDifficulty);
    }
    
    // Trier les résultats
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'potential':
          comparison = b.potential - a.potential;
          break;
        case 'difficulty':
          const difficultyOrder = { low: 1, medium: 2, high: 3 };
          comparison = difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
    
    return filtered;
  };
  
  // Obtenir les opportunités filtrées
  const filteredOpportunities = getFilteredOpportunities();
  
  // Fonction pour basculer l'ordre de tri
  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };
  
  // Fonction pour définir la colonne de tri
  const handleSort = (column) => {
    if (sortBy === column) {
      toggleSortOrder();
    } else {
      setSortBy(column);
      setSortOrder(column === 'potential' ? 'desc' : 'asc');
    }
  };
  
  // Fonction pour obtenir l'icône de tri
  const getSortIcon = (column) => {
    if (sortBy !== column) return null;
    return sortOrder === 'asc' ? 'arrow-up-alt2' : 'arrow-down-alt2';
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du potentiel
  const getPotentialColorClass = (potential) => {
    if (potential >= 80) return 'boss-text-green-600';
    if (potential >= 60) return 'boss-text-blue-600';
    if (potential >= 40) return 'boss-text-yellow-600';
    return 'boss-text-boss-gray';
  };
  
  // Fonction pour obtenir la classe de couleur en fonction de la difficulté
  const getDifficultyColorClass = (difficulty) => {
    if (difficulty === 'low') return 'boss-text-green-600';
    if (difficulty === 'medium') return 'boss-text-yellow-600';
    if (difficulty === 'high') return 'boss-text-red-500';
    return 'boss-text-boss-gray';
  };
  
  // Fonction pour obtenir l'icône en fonction du type d'opportunité
  const getOpportunityIcon = (type) => {
    switch (type) {
      case 'keyword':
        return 'search';
      case 'content':
        return 'edit';
      case 'technical':
        return 'admin-tools';
      case 'backlink':
        return 'admin-links';
      default:
        return 'star-filled';
    }
  };
  
  // Fonction pour obtenir le nom du type d'opportunité
  const getOpportunityTypeName = (type) => {
    switch (type) {
      case 'keyword':
        return __('Mot-clé', 'boss-seo');
      case 'content':
        return __('Contenu', 'boss-seo');
      case 'technical':
        return __('Technique', 'boss-seo');
      case 'backlink':
        return __('Backlink', 'boss-seo');
      default:
        return type;
    }
  };
  
  // Fonction pour obtenir le nom de la difficulté
  const getDifficultyName = (difficulty) => {
    switch (difficulty) {
      case 'low':
        return __('Facile', 'boss-seo');
      case 'medium':
        return __('Moyenne', 'boss-seo');
      case 'high':
        return __('Difficile', 'boss-seo');
      default:
        return difficulty;
    }
  };
  
  // Fonction pour afficher les détails d'une opportunité
  const showOpportunityDetails = (opportunity) => {
    setSelectedOpportunity(opportunity);
    setShowModal(true);
  };
  
  // Fonction pour fermer la modal
  const closeModal = () => {
    setShowModal(false);
    setSelectedOpportunity(null);
  };
  
  // Fonction pour obtenir le contenu détaillé d'une opportunité
  const getOpportunityDetails = (opportunity) => {
    if (!opportunity) return null;
    
    switch (opportunity.type) {
      case 'keyword':
        return (
          <div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Position actuelle', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">{opportunity.currentPosition}</p>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Volume de recherche estimé', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">1,200 {__('recherches/mois', 'boss-seo')}</p>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Concurrence', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">{opportunity.difficulty === 'low' ? __('Faible', 'boss-seo') : opportunity.difficulty === 'medium' ? __('Moyenne', 'boss-seo') : __('Élevée', 'boss-seo')}</p>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Recommandations', 'boss-seo')}</h3>
              <ul className="boss-list-disc boss-pl-5 boss-text-boss-gray">
                <li>{__('Créer du contenu optimisé pour ce mot-clé', 'boss-seo')}</li>
                <li>{__('Ajouter le mot-clé dans les balises title et meta description', 'boss-seo')}</li>
                <li>{__('Inclure le mot-clé dans les sous-titres (H2, H3)', 'boss-seo')}</li>
              </ul>
            </div>
          </div>
        );
        
      case 'content':
        return (
          <div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Page concernée', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">{opportunity.pageUrl}</p>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Problèmes identifiés', 'boss-seo')}</h3>
              <ul className="boss-list-disc boss-pl-5 boss-text-boss-gray">
                <li>{__('Contenu trop court (moins de 500 mots)', 'boss-seo')}</li>
                <li>{__('Manque de mots-clés pertinents', 'boss-seo')}</li>
                <li>{__('Structure des titres à améliorer', 'boss-seo')}</li>
              </ul>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Recommandations', 'boss-seo')}</h3>
              <ul className="boss-list-disc boss-pl-5 boss-text-boss-gray">
                <li>{__('Enrichir le contenu pour atteindre au moins 1000 mots', 'boss-seo')}</li>
                <li>{__('Ajouter des sous-sections avec des H2 et H3 pertinents', 'boss-seo')}</li>
                <li>{__('Inclure des images avec des attributs alt optimisés', 'boss-seo')}</li>
              </ul>
            </div>
          </div>
        );
        
      case 'technical':
        return (
          <div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Problème technique', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">{opportunity.title}</p>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Pages affectées', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">{opportunity.affectedPages} {__('pages', 'boss-seo')}</p>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Impact', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">{__('Ce problème peut affecter le classement de plusieurs pages et réduire la visibilité globale du site.', 'boss-seo')}</p>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Solution recommandée', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">{__('Utiliser l\'outil d\'audit technique de Boss SEO pour identifier et corriger les balises title dupliquées sur toutes les pages concernées.', 'boss-seo')}</p>
            </div>
          </div>
        );
        
      case 'backlink':
        return (
          <div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Site cible', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">{opportunity.title.split('sur ')[1]}</p>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Autorité de domaine', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">{opportunity.domainAuthority}/100</p>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Type d\'opportunité', 'boss-seo')}</h3>
              <p className="boss-text-boss-gray">{__('Mention de votre marque sans lien', 'boss-seo')}</p>
            </div>
            <div className="boss-mb-4">
              <h3 className="boss-font-medium boss-mb-1">{__('Actions recommandées', 'boss-seo')}</h3>
              <ul className="boss-list-disc boss-pl-5 boss-text-boss-gray">
                <li>{__('Contacter le webmaster du site', 'boss-seo')}</li>
                <li>{__('Demander l\'ajout d\'un lien vers votre site', 'boss-seo')}</li>
                <li>{__('Proposer du contenu complémentaire si nécessaire', 'boss-seo')}</li>
              </ul>
            </div>
          </div>
        );
        
      default:
        return (
          <div className="boss-text-boss-gray">
            {__('Aucun détail disponible pour cette opportunité.', 'boss-seo')}
          </div>
        );
    }
  };

  return (
    <div>
      {/* Filtres */}
      <div className="boss-flex boss-flex-col md:boss-flex-row boss-justify-between boss-items-start md:boss-items-center boss-gap-4 boss-mb-6">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Opportunités d\'amélioration SEO', 'boss-seo')}
        </h2>
        
        <div className="boss-flex boss-flex-col sm:boss-flex-row boss-gap-3">
          <SelectControl
            value={filterType}
            options={[
              { label: __('Tous les types', 'boss-seo'), value: 'all' },
              { label: __('Mots-clés', 'boss-seo'), value: 'keyword' },
              { label: __('Contenu', 'boss-seo'), value: 'content' },
              { label: __('Technique', 'boss-seo'), value: 'technical' },
              { label: __('Backlinks', 'boss-seo'), value: 'backlink' }
            ]}
            onChange={setFilterType}
          />
          
          <SelectControl
            value={filterDifficulty}
            options={[
              { label: __('Toutes les difficultés', 'boss-seo'), value: 'all' },
              { label: __('Facile', 'boss-seo'), value: 'low' },
              { label: __('Moyenne', 'boss-seo'), value: 'medium' },
              { label: __('Difficile', 'boss-seo'), value: 'high' }
            ]}
            onChange={setFilterDifficulty}
          />
        </div>
      </div>
      
      {/* Liste des opportunités */}
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-6">
        {filteredOpportunities.length === 0 ? (
          <div className="boss-col-span-full boss-text-center boss-py-12 boss-text-boss-gray">
            {__('Aucune opportunité trouvée.', 'boss-seo')}
          </div>
        ) : (
          filteredOpportunities.map((opportunity, index) => (
            <Card key={index} className="boss-card boss-transition-all boss-duration-300 boss-hover:boss-shadow-md">
              <CardBody>
                <div className="boss-flex boss-items-start boss-mb-4">
                  <div className={`boss-bg-${opportunity.type === 'keyword' ? 'blue' : opportunity.type === 'content' ? 'green' : opportunity.type === 'technical' ? 'yellow' : 'purple'}-100 boss-p-3 boss-rounded-lg boss-mr-4`}>
                    <Dashicon icon={getOpportunityIcon(opportunity.type)} className={`boss-text-${opportunity.type === 'keyword' ? 'blue' : opportunity.type === 'content' ? 'green' : opportunity.type === 'technical' ? 'yellow' : 'purple'}-600 boss-text-xl`} />
                  </div>
                  <div>
                    <div className="boss-flex boss-items-center boss-mb-1">
                      <span className="boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded-full boss-bg-gray-100 boss-text-boss-gray boss-mr-2">
                        {getOpportunityTypeName(opportunity.type)}
                      </span>
                      <span className={`boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded-full ${opportunity.difficulty === 'low' ? 'boss-bg-green-100 boss-text-green-600' : opportunity.difficulty === 'medium' ? 'boss-bg-yellow-100 boss-text-yellow-600' : 'boss-bg-red-100 boss-text-red-600'}`}>
                        {getDifficultyName(opportunity.difficulty)}
                      </span>
                    </div>
                    <h3 className="boss-text-lg boss-font-semibold boss-mb-2">{opportunity.title}</h3>
                  </div>
                </div>
                
                <div className="boss-mb-4">
                  <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                    <span className="boss-text-sm boss-text-boss-gray">{__('Potentiel d\'amélioration', 'boss-seo')}</span>
                    <span className={`boss-font-medium ${getPotentialColorClass(opportunity.potential)}`}>
                      {opportunity.potential}/100
                    </span>
                  </div>
                  <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2">
                    <div 
                      className={`boss-h-2 boss-rounded-full ${opportunity.potential >= 80 ? 'boss-bg-green-500' : opportunity.potential >= 60 ? 'boss-bg-blue-500' : opportunity.potential >= 40 ? 'boss-bg-yellow-500' : 'boss-bg-gray-400'}`}
                      style={{ width: `${opportunity.potential}%` }}
                    ></div>
                  </div>
                </div>
                
                <Button
                  isPrimary
                  className="boss-w-full"
                  onClick={() => showOpportunityDetails(opportunity)}
                >
                  {__('Voir les détails', 'boss-seo')}
                </Button>
              </CardBody>
            </Card>
          ))
        )}
      </div>
      
      {/* Modal de détails */}
      {showModal && selectedOpportunity && (
        <Modal
          title={selectedOpportunity.title}
          onRequestClose={closeModal}
          className="boss-opportunity-details-modal"
        >
          <div className="boss-p-6">
            <div className="boss-flex boss-items-center boss-mb-6">
              <div className={`boss-bg-${selectedOpportunity.type === 'keyword' ? 'blue' : selectedOpportunity.type === 'content' ? 'green' : selectedOpportunity.type === 'technical' ? 'yellow' : 'purple'}-100 boss-p-3 boss-rounded-lg boss-mr-4`}>
                <Dashicon icon={getOpportunityIcon(selectedOpportunity.type)} className={`boss-text-${selectedOpportunity.type === 'keyword' ? 'blue' : selectedOpportunity.type === 'content' ? 'green' : selectedOpportunity.type === 'technical' ? 'yellow' : 'purple'}-600 boss-text-xl`} />
              </div>
              <div>
                <div className="boss-flex boss-items-center boss-mb-1">
                  <span className="boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded-full boss-bg-gray-100 boss-text-boss-gray boss-mr-2">
                    {getOpportunityTypeName(selectedOpportunity.type)}
                  </span>
                  <span className={`boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded-full ${selectedOpportunity.difficulty === 'low' ? 'boss-bg-green-100 boss-text-green-600' : selectedOpportunity.difficulty === 'medium' ? 'boss-bg-yellow-100 boss-text-yellow-600' : 'boss-bg-red-100 boss-text-red-600'}`}>
                    {getDifficultyName(selectedOpportunity.difficulty)}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="boss-mb-6">
              <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                <span className="boss-text-sm boss-text-boss-gray">{__('Potentiel d\'amélioration', 'boss-seo')}</span>
                <span className={`boss-font-medium ${getPotentialColorClass(selectedOpportunity.potential)}`}>
                  {selectedOpportunity.potential}/100
                </span>
              </div>
              <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2">
                <div 
                  className={`boss-h-2 boss-rounded-full ${selectedOpportunity.potential >= 80 ? 'boss-bg-green-500' : selectedOpportunity.potential >= 60 ? 'boss-bg-blue-500' : selectedOpportunity.potential >= 40 ? 'boss-bg-yellow-500' : 'boss-bg-gray-400'}`}
                  style={{ width: `${selectedOpportunity.potential}%` }}
                ></div>
              </div>
            </div>
            
            <div className="boss-border-t boss-border-gray-200 boss-pt-6">
              {getOpportunityDetails(selectedOpportunity)}
            </div>
            
            <div className="boss-flex boss-justify-end boss-mt-6 boss-pt-6 boss-border-t boss-border-gray-200">
              <Button
                isPrimary
                onClick={closeModal}
              >
                {__('Fermer', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default OpportunityAnalysis;
