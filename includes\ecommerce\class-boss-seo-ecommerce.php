<?php
/**
 * Classe principale pour les fonctionnalités e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe principale pour les fonctionnalités e-commerce.
 *
 * Cette classe gère toutes les fonctionnalités liées à l'e-commerce.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Ecommerce {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * L'instance de la classe Boss_Product_Schema.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Product_Schema    $product_schema    L'instance de la classe Boss_Product_Schema.
     */
    protected $product_schema;

    /**
     * L'instance de la classe Boss_Review_Manager.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Review_Manager    $review_manager    L'instance de la classe Boss_Review_Manager.
     */
    protected $review_manager;

    /**
     * L'instance de la classe Boss_Rich_Snippets.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Rich_Snippets    $rich_snippets    L'instance de la classe Boss_Rich_Snippets.
     */
    protected $rich_snippets;

    /**
     * L'instance de la classe Boss_Seo_Analyzer.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Seo_Analyzer    $seo_analyzer    L'instance de la classe Boss_Seo_Analyzer.
     */
    protected $seo_analyzer;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->load_dependencies();
        $this->init_components();
    }

    /**
     * Charge les dépendances nécessaires pour ce module.
     *
     * @since    1.2.0
     * @access   private
     */
    private function load_dependencies() {
        /**
         * La classe qui gère les schémas de produits.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-product-schema.php';

        /**
         * La classe qui gère les avis clients.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-review-manager.php';

        /**
         * La classe qui gère les extraits enrichis.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-rich-snippets.php';

        /**
         * La classe qui gère l'analyse SEO des produits.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-seo-analyzer.php';
    }

    /**
     * Initialise les composants du module.
     *
     * @since    1.2.0
     * @access   private
     */
    private function init_components() {
        $this->product_schema = new Boss_Product_Schema( $this->plugin_name, $this->version );
        $this->review_manager = new Boss_Review_Manager( $this->plugin_name, $this->version );
        $this->rich_snippets = new Boss_Rich_Snippets( $this->plugin_name, $this->version );
        $this->seo_analyzer = new Boss_Seo_Analyzer( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Enregistrer les hooks pour chaque composant
        $this->product_schema->register_hooks();
        $this->review_manager->register_hooks();
        $this->rich_snippets->register_hooks();
        $this->seo_analyzer->register_hooks();

        // Ajouter les actions pour les assets
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_styles' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_public_styles' ) );
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_public_scripts' ) );

        // Ajouter les actions pour les menus
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );

        // Ajouter les actions pour les shortcodes
        add_action( 'init', array( $this, 'register_shortcodes' ) );

        // Ajouter les actions pour WooCommerce
        if ( $this->is_woocommerce_active() ) {
            $this->register_woocommerce_hooks();
        }
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Enregistrer les routes REST API pour chaque composant
        $this->product_schema->register_rest_routes();
        $this->review_manager->register_rest_routes();
        $this->rich_snippets->register_rest_routes();
        $this->seo_analyzer->register_rest_routes();
    }

    /**
     * Enregistre les styles pour l'administration.
     *
     * @since    1.2.0
     * @param    string    $hook_suffix    Le suffixe du hook.
     */
    public function enqueue_admin_styles( $hook_suffix ) {
        // Vérifier si nous sommes sur une page du plugin
        if ( strpos( $hook_suffix, 'boss-seo' ) !== false || $this->is_woocommerce_product_page( $hook_suffix ) ) {
            wp_enqueue_style( 'boss-seo-ecommerce-admin', plugin_dir_url( dirname( __FILE__ ) ) . 'admin/css/boss-seo-ecommerce-admin.css', array(), $this->version, 'all' );
        }
    }

    /**
     * Enregistre les scripts pour l'administration.
     *
     * @since    1.2.0
     * @param    string    $hook_suffix    Le suffixe du hook.
     */
    public function enqueue_admin_scripts( $hook_suffix ) {
        // Vérifier si nous sommes sur une page du plugin
        if ( strpos( $hook_suffix, 'boss-seo' ) !== false || $this->is_woocommerce_product_page( $hook_suffix ) ) {
            wp_enqueue_script( 'boss-seo-ecommerce-admin', plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/boss-seo-ecommerce-admin.js', array( 'jquery' ), $this->version, false );

            // Ajouter les variables locales
            wp_localize_script( 'boss-seo-ecommerce-admin', 'boss_seo_ecommerce', array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'boss_seo_ecommerce_nonce' ),
                'strings'  => array(
                    'confirm_delete' => __( 'Êtes-vous sûr de vouloir supprimer cet élément ?', 'boss-seo' ),
                    'error'          => __( 'Une erreur s\'est produite.', 'boss-seo' ),
                    'success'        => __( 'Opération réussie.', 'boss-seo' ),
                ),
            ) );
        }
    }

    /**
     * Enregistre les styles pour le front-end.
     *
     * @since    1.2.0
     */
    public function enqueue_public_styles() {
        wp_enqueue_style( 'boss-seo-ecommerce-public', plugin_dir_url( dirname( __FILE__ ) ) . 'public/css/boss-seo-ecommerce-public.css', array(), $this->version, 'all' );
    }

    /**
     * Enregistre les scripts pour le front-end.
     *
     * @since    1.2.0
     */
    public function enqueue_public_scripts() {
        wp_enqueue_script( 'boss-seo-ecommerce-public', plugin_dir_url( dirname( __FILE__ ) ) . 'public/js/boss-seo-ecommerce-public.js', array( 'jquery' ), $this->version, false );
    }

    /**
     * Ajoute les menus d'administration.
     *
     * @since    1.2.0
     */
    public function add_admin_menu() {
        // Nous n'ajoutons plus de sous-menus ici car ils sont gérés par l'interface à onglets
        // dans la page "SEO local & e-commerce"
    }

    /**
     * Affiche la page d'administration principale.
     *
     * @since    1.2.0
     */
    public function display_admin_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-seo-ecommerce-admin-display.php';
    }

    /**
     * Affiche la page des schémas de produits.
     *
     * @since    1.2.0
     */
    public function display_product_schema_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-seo-product-schema-display.php';
    }

    /**
     * Affiche la page des avis clients.
     *
     * @since    1.2.0
     */
    public function display_reviews_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-seo-reviews-display.php';
    }

    /**
     * Affiche la page des extraits enrichis.
     *
     * @since    1.2.0
     */
    public function display_rich_snippets_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-seo-rich-snippets-display.php';
    }

    /**
     * Affiche la page d'analyse SEO.
     *
     * @since    1.2.0
     */
    public function display_seo_analyzer_page() {
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/boss-seo-analyzer-display.php';
    }

    /**
     * Enregistre les shortcodes.
     *
     * @since    1.2.0
     */
    public function register_shortcodes() {
        add_shortcode( 'boss_product_rating', array( $this->review_manager, 'rating_shortcode' ) );
        add_shortcode( 'boss_product_reviews', array( $this->review_manager, 'reviews_shortcode' ) );
        add_shortcode( 'boss_product_schema', array( $this->product_schema, 'schema_shortcode' ) );
        add_shortcode( 'boss_rich_snippet', array( $this->rich_snippets, 'snippet_shortcode' ) );
    }

    /**
     * Vérifie si WooCommerce est actif.
     *
     * @since    1.2.0
     * @return   bool    True si WooCommerce est actif, false sinon.
     */
    private function is_woocommerce_active() {
        return in_array( 'woocommerce/woocommerce.php', apply_filters( 'active_plugins', get_option( 'active_plugins' ) ) );
    }

    /**
     * Vérifie si nous sommes sur une page de produit WooCommerce.
     *
     * @since    1.2.0
     * @param    string    $hook_suffix    Le suffixe du hook.
     * @return   bool                      True si nous sommes sur une page de produit WooCommerce, false sinon.
     */
    private function is_woocommerce_product_page( $hook_suffix ) {
        global $post_type;

        return ( $hook_suffix === 'post.php' || $hook_suffix === 'post-new.php' ) && $post_type === 'product';
    }

    /**
     * Enregistre les hooks pour WooCommerce.
     *
     * @since    1.2.0
     */
    private function register_woocommerce_hooks() {
        // Ajouter les métaboxes pour les produits
        add_action( 'add_meta_boxes', array( $this, 'add_product_meta_boxes' ) );

        // Enregistrer les métadonnées des produits
        add_action( 'woocommerce_process_product_meta', array( $this, 'save_product_meta' ) );

        // Ajouter les onglets dans les produits
        add_filter( 'woocommerce_product_data_tabs', array( $this, 'add_product_data_tabs' ) );
        add_action( 'woocommerce_product_data_panels', array( $this, 'add_product_data_panels' ) );

        // Ajouter les schémas structurés
        add_action( 'wp_head', array( $this->product_schema, 'insert_product_schema' ) );

        // Ajouter les extraits enrichis
        add_action( 'woocommerce_after_shop_loop_item', array( $this->rich_snippets, 'display_rich_snippets' ) );

        // Ajouter les avis clients
        add_action( 'woocommerce_review_before_comment_meta', array( $this->review_manager, 'display_review_rating' ) );
        add_action( 'comment_form_logged_in_after', array( $this->review_manager, 'add_review_rating_field' ) );
        add_action( 'comment_form_after_fields', array( $this->review_manager, 'add_review_rating_field' ) );
        add_action( 'comment_post', array( $this->review_manager, 'save_review_rating' ) );

        // Ajouter l'analyse SEO
        add_action( 'woocommerce_process_product_meta', array( $this->seo_analyzer, 'analyze_product' ) );
    }

    /**
     * Ajoute les métaboxes pour les produits.
     *
     * @since    1.2.0
     */
    public function add_product_meta_boxes() {
        add_meta_box(
            'boss_seo_product_schema',
            __( 'Boss SEO - Schéma de produit', 'boss-seo' ),
            array( $this->product_schema, 'render_meta_box' ),
            'product',
            'normal',
            'high'
        );

        add_meta_box(
            'boss_seo_rich_snippets',
            __( 'Boss SEO - Extraits enrichis', 'boss-seo' ),
            array( $this->rich_snippets, 'render_meta_box' ),
            'product',
            'normal',
            'high'
        );

        add_meta_box(
            'boss_seo_analyzer',
            __( 'Boss SEO - Analyse SEO', 'boss-seo' ),
            array( $this->seo_analyzer, 'render_meta_box' ),
            'product',
            'normal',
            'high'
        );
    }

    /**
     * Enregistre les métadonnées des produits.
     *
     * @since    1.2.0
     * @param    int    $post_id    L'ID du produit.
     */
    public function save_product_meta( $post_id ) {
        $this->product_schema->save_meta( $post_id );
        $this->rich_snippets->save_meta( $post_id );
        $this->seo_analyzer->save_meta( $post_id );
    }

    /**
     * Ajoute les onglets dans les produits.
     *
     * @since    1.2.0
     * @param    array    $tabs    Les onglets existants.
     * @return   array             Les onglets mis à jour.
     */
    public function add_product_data_tabs( $tabs ) {
        $tabs['boss_seo'] = array(
            'label'    => __( 'Boss SEO', 'boss-seo' ),
            'target'   => 'boss_seo_product_data',
            'class'    => array( 'show_if_simple', 'show_if_variable' ),
            'priority' => 90,
        );

        return $tabs;
    }

    /**
     * Ajoute les panneaux de données dans les produits.
     *
     * @since    1.2.0
     */
    public function add_product_data_panels() {
        ?>
        <div id="boss_seo_product_data" class="panel woocommerce_options_panel">
            <div class="options_group">
                <h3><?php esc_html_e( 'Schéma de produit', 'boss-seo' ); ?></h3>
                <?php $this->product_schema->render_product_data_panel(); ?>
            </div>

            <div class="options_group">
                <h3><?php esc_html_e( 'Extraits enrichis', 'boss-seo' ); ?></h3>
                <?php $this->rich_snippets->render_product_data_panel(); ?>
            </div>

            <div class="options_group">
                <h3><?php esc_html_e( 'Analyse SEO', 'boss-seo' ); ?></h3>
                <?php $this->seo_analyzer->render_product_data_panel(); ?>
            </div>
        </div>
        <?php
    }

    /**
     * Récupère l'instance de la classe Boss_Product_Schema.
     *
     * @since    1.2.0
     * @return   Boss_Product_Schema    L'instance de la classe Boss_Product_Schema.
     */
    public function get_product_schema() {
        return $this->product_schema;
    }

    /**
     * Récupère l'instance de la classe Boss_Review_Manager.
     *
     * @since    1.2.0
     * @return   Boss_Review_Manager    L'instance de la classe Boss_Review_Manager.
     */
    public function get_review_manager() {
        return $this->review_manager;
    }

    /**
     * Récupère l'instance de la classe Boss_Rich_Snippets.
     *
     * @since    1.2.0
     * @return   Boss_Rich_Snippets    L'instance de la classe Boss_Rich_Snippets.
     */
    public function get_rich_snippets() {
        return $this->rich_snippets;
    }

    /**
     * Récupère l'instance de la classe Boss_Seo_Analyzer.
     *
     * @since    1.2.0
     * @return   Boss_Seo_Analyzer    L'instance de la classe Boss_Seo_Analyzer.
     */
    public function get_seo_analyzer() {
        return $this->seo_analyzer;
    }
}
