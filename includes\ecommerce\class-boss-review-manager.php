<?php
/**
 * Classe pour la gestion des avis clients.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour la gestion des avis clients.
 *
 * Cette classe gère les avis clients pour les produits.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Review_Manager {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_review_manager_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_import_reviews', array( $this, 'ajax_import_reviews' ) );
        add_action( 'wp_ajax_boss_seo_export_reviews', array( $this, 'ajax_export_reviews' ) );
        add_action( 'wp_ajax_boss_seo_delete_review', array( $this, 'ajax_delete_review' ) );
        add_action( 'wp_ajax_boss_seo_update_review', array( $this, 'ajax_update_review' ) );

        // Ajouter les actions pour les avis
        add_action( 'comment_form_logged_in_after', array( $this, 'add_review_rating_field' ) );
        add_action( 'comment_form_after_fields', array( $this, 'add_review_rating_field' ) );
        add_action( 'comment_post', array( $this, 'save_review_rating' ) );
        add_action( 'woocommerce_review_before_comment_meta', array( $this, 'display_review_rating' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/reviews',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_reviews' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'add_review' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/reviews/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_review' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'PUT',
                    'callback'            => array( $this, 'update_review' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_review' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/reviews/import',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'import_reviews' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/reviews/export',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'export_reviews' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour importer des avis.
     *
     * @since    1.2.0
     */
    public function ajax_import_reviews() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_FILES['file'] ) || empty( $_FILES['file']['tmp_name'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Aucun fichier n\'a été téléchargé.', 'boss-seo' ) ) );
        }

        // Récupérer le fichier
        $file = $_FILES['file'];

        // Vérifier le type de fichier
        $file_type = wp_check_filetype( $file['name'] );
        if ( $file_type['ext'] !== 'csv' ) {
            wp_send_json_error( array( 'message' => __( 'Le fichier doit être au format CSV.', 'boss-seo' ) ) );
        }

        // Ouvrir le fichier
        $handle = fopen( $file['tmp_name'], 'r' );
        if ( ! $handle ) {
            wp_send_json_error( array( 'message' => __( 'Impossible d\'ouvrir le fichier.', 'boss-seo' ) ) );
        }

        // Lire l'en-tête
        $header = fgetcsv( $handle );

        // Vérifier les colonnes requises
        $required_columns = array( 'product_id', 'author', 'email', 'content', 'rating' );
        foreach ( $required_columns as $column ) {
            if ( ! in_array( $column, $header ) ) {
                fclose( $handle );
                wp_send_json_error( array( 'message' => sprintf( __( 'La colonne "%s" est requise.', 'boss-seo' ), $column ) ) );
            }
        }

        // Importer les avis
        $imported = 0;
        $errors = array();

        while ( ( $row = fgetcsv( $handle ) ) !== false ) {
            $data = array_combine( $header, $row );

            // Vérifier les données requises
            if ( empty( $data['product_id'] ) || empty( $data['author'] ) || empty( $data['email'] ) || empty( $data['content'] ) || empty( $data['rating'] ) ) {
                $errors[] = sprintf( __( 'Ligne %d : Données manquantes.', 'boss-seo' ), $imported + 1 );
                continue;
            }

            // Vérifier si le produit existe
            $product = wc_get_product( $data['product_id'] );
            if ( ! $product ) {
                $errors[] = sprintf( __( 'Ligne %d : Produit non trouvé.', 'boss-seo' ), $imported + 1 );
                continue;
            }

            // Créer l'avis
            $comment_data = array(
                'comment_post_ID'      => $data['product_id'],
                'comment_author'       => $data['author'],
                'comment_author_email' => $data['email'],
                'comment_content'      => $data['content'],
                'comment_type'         => 'review',
                'comment_parent'       => 0,
                'user_id'              => 0,
                'comment_approved'     => 1,
                'comment_meta'         => array(
                    'rating' => $data['rating'],
                ),
            );

            $comment_id = wp_insert_comment( $comment_data );

            if ( $comment_id ) {
                update_comment_meta( $comment_id, 'rating', $data['rating'] );
                $imported++;
            } else {
                $errors[] = sprintf( __( 'Ligne %d : Erreur lors de la création de l\'avis.', 'boss-seo' ), $imported + 1 );
            }
        }

        fclose( $handle );

        wp_send_json_success( array(
            'message' => sprintf( __( '%d avis importés avec succès.', 'boss-seo' ), $imported ),
            'imported' => $imported,
            'errors' => $errors,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour exporter des avis.
     *
     * @since    1.2.0
     */
    public function ajax_export_reviews() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $product_id = isset( $_POST['product_id'] ) ? absint( $_POST['product_id'] ) : 0;

        // Récupérer les avis
        $args = array(
            'post_id' => $product_id ? $product_id : '',
            'status' => 'approve',
            'type' => 'review',
        );

        $reviews = get_comments( $args );

        if ( empty( $reviews ) ) {
            wp_send_json_error( array( 'message' => __( 'Aucun avis trouvé.', 'boss-seo' ) ) );
        }

        // Créer le fichier CSV
        $csv = array();
        $csv[] = array( 'product_id', 'author', 'email', 'content', 'rating', 'date' );

        foreach ( $reviews as $review ) {
            $rating = get_comment_meta( $review->comment_ID, 'rating', true );

            $csv[] = array(
                $review->comment_post_ID,
                $review->comment_author,
                $review->comment_author_email,
                $review->comment_content,
                $rating,
                $review->comment_date,
            );
        }

        // Générer le contenu CSV
        $output = '';
        foreach ( $csv as $row ) {
            $output .= implode( ',', array_map( 'addslashes', $row ) ) . "\n";
        }

        wp_send_json_success( array(
            'file_content' => $output,
            'file_name' => 'boss-seo-reviews-' . date( 'Y-m-d' ) . '.csv',
            'file_type' => 'text/csv',
        ) );
    }

    /**
     * Gère les requêtes AJAX pour supprimer un avis.
     *
     * @since    1.2.0
     */
    public function ajax_delete_review() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['review_id'] ) || empty( $_POST['review_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de l\'avis est requis.', 'boss-seo' ) ) );
        }

        $review_id = absint( $_POST['review_id'] );

        // Supprimer l'avis
        $result = wp_delete_comment( $review_id, true );

        if ( $result ) {
            wp_send_json_success( array( 'message' => __( 'Avis supprimé avec succès.', 'boss-seo' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Erreur lors de la suppression de l\'avis.', 'boss-seo' ) ) );
        }
    }

    /**
     * Gère les requêtes AJAX pour mettre à jour un avis.
     *
     * @since    1.2.0
     */
    public function ajax_update_review() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['review_id'] ) || empty( $_POST['review_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de l\'avis est requis.', 'boss-seo' ) ) );
        }

        $review_id = absint( $_POST['review_id'] );
        $content = isset( $_POST['content'] ) ? sanitize_textarea_field( $_POST['content'] ) : '';
        $rating = isset( $_POST['rating'] ) ? absint( $_POST['rating'] ) : 0;

        // Mettre à jour l'avis
        $comment_data = array(
            'comment_ID' => $review_id,
            'comment_content' => $content,
        );

        $result = wp_update_comment( $comment_data );

        if ( $result ) {
            update_comment_meta( $review_id, 'rating', $rating );
            wp_send_json_success( array( 'message' => __( 'Avis mis à jour avec succès.', 'boss-seo' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Erreur lors de la mise à jour de l\'avis.', 'boss-seo' ) ) );
        }
    }

    /**
     * Récupère les avis via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_reviews( $request ) {
        $product_id = isset( $request['product_id'] ) ? absint( $request['product_id'] ) : 0;

        // Récupérer les avis
        $args = array(
            'post_id' => $product_id ? $product_id : '',
            'status' => 'approve',
            'type' => 'review',
        );

        $reviews = get_comments( $args );

        if ( empty( $reviews ) ) {
            return rest_ensure_response( array() );
        }

        $data = array();

        foreach ( $reviews as $review ) {
            $rating = get_comment_meta( $review->comment_ID, 'rating', true );

            $data[] = array(
                'id' => $review->comment_ID,
                'product_id' => $review->comment_post_ID,
                'author' => $review->comment_author,
                'email' => $review->comment_author_email,
                'content' => $review->comment_content,
                'rating' => $rating,
                'date' => $review->comment_date,
            );
        }

        return rest_ensure_response( $data );
    }

    /**
     * Récupère un avis via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_review( $request ) {
        $review_id = $request['id'];

        // Récupérer l'avis
        $review = get_comment( $review_id );

        if ( ! $review || $review->comment_type !== 'review' ) {
            return new WP_Error( 'review_not_found', __( 'Avis non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        $rating = get_comment_meta( $review_id, 'rating', true );

        $data = array(
            'id' => $review->comment_ID,
            'product_id' => $review->comment_post_ID,
            'author' => $review->comment_author,
            'email' => $review->comment_author_email,
            'content' => $review->comment_content,
            'rating' => $rating,
            'date' => $review->comment_date,
        );

        return rest_ensure_response( $data );
    }

    /**
     * Ajoute un avis via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function add_review( $request ) {
        $params = $request->get_params();

        // Vérifier les paramètres requis
        if ( ! isset( $params['product_id'] ) || empty( $params['product_id'] ) ) {
            return new WP_Error( 'missing_product_id', __( 'L\'ID du produit est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        if ( ! isset( $params['author'] ) || empty( $params['author'] ) ) {
            return new WP_Error( 'missing_author', __( 'L\'auteur est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        if ( ! isset( $params['email'] ) || empty( $params['email'] ) ) {
            return new WP_Error( 'missing_email', __( 'L\'email est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        if ( ! isset( $params['content'] ) || empty( $params['content'] ) ) {
            return new WP_Error( 'missing_content', __( 'Le contenu est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        if ( ! isset( $params['rating'] ) || empty( $params['rating'] ) ) {
            return new WP_Error( 'missing_rating', __( 'La note est requise.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Vérifier si le produit existe
        $product = wc_get_product( $params['product_id'] );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Créer l'avis
        $comment_data = array(
            'comment_post_ID'      => $params['product_id'],
            'comment_author'       => $params['author'],
            'comment_author_email' => $params['email'],
            'comment_content'      => $params['content'],
            'comment_type'         => 'review',
            'comment_parent'       => 0,
            'user_id'              => 0,
            'comment_approved'     => 1,
        );

        $comment_id = wp_insert_comment( $comment_data );

        if ( ! $comment_id ) {
            return new WP_Error( 'review_creation_failed', __( 'Erreur lors de la création de l\'avis.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        update_comment_meta( $comment_id, 'rating', $params['rating'] );

        $review = get_comment( $comment_id );

        $data = array(
            'id' => $review->comment_ID,
            'product_id' => $review->comment_post_ID,
            'author' => $review->comment_author,
            'email' => $review->comment_author_email,
            'content' => $review->comment_content,
            'rating' => get_comment_meta( $comment_id, 'rating', true ),
            'date' => $review->comment_date,
        );

        return rest_ensure_response( $data );
    }

    /**
     * Supprime un avis via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_review( $request ) {
        $review_id = $request['id'];

        // Récupérer l'avis
        $review = get_comment( $review_id );

        if ( ! $review || $review->comment_type !== 'review' ) {
            return new WP_Error( 'review_not_found', __( 'Avis non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Supprimer l'avis
        $result = wp_delete_comment( $review_id, true );

        if ( ! $result ) {
            return new WP_Error( 'review_deletion_failed', __( 'Erreur lors de la suppression de l\'avis.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array(
            'deleted' => true,
            'previous' => array(
                'id' => $review->comment_ID,
                'product_id' => $review->comment_post_ID,
                'author' => $review->comment_author,
                'email' => $review->comment_author_email,
                'content' => $review->comment_content,
                'rating' => get_comment_meta( $review_id, 'rating', true ),
                'date' => $review->comment_date,
            ),
        ) );
    }

    /**
     * Importe des avis via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function import_reviews( $request ) {
        $params = $request->get_params();

        // Vérifier les paramètres requis
        if ( ! isset( $params['reviews'] ) || ! is_array( $params['reviews'] ) ) {
            return new WP_Error( 'missing_reviews', __( 'Les avis sont requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $reviews = $params['reviews'];
        $imported = 0;
        $errors = array();

        foreach ( $reviews as $index => $review ) {
            // Vérifier les données requises
            if ( empty( $review['product_id'] ) || empty( $review['author'] ) || empty( $review['email'] ) || empty( $review['content'] ) || empty( $review['rating'] ) ) {
                $errors[] = sprintf( __( 'Avis %d : Données manquantes.', 'boss-seo' ), $index + 1 );
                continue;
            }

            // Vérifier si le produit existe
            $product = wc_get_product( $review['product_id'] );
            if ( ! $product ) {
                $errors[] = sprintf( __( 'Avis %d : Produit non trouvé.', 'boss-seo' ), $index + 1 );
                continue;
            }

            // Créer l'avis
            $comment_data = array(
                'comment_post_ID'      => $review['product_id'],
                'comment_author'       => $review['author'],
                'comment_author_email' => $review['email'],
                'comment_content'      => $review['content'],
                'comment_type'         => 'review',
                'comment_parent'       => 0,
                'user_id'              => 0,
                'comment_approved'     => 1,
            );

            $comment_id = wp_insert_comment( $comment_data );

            if ( $comment_id ) {
                update_comment_meta( $comment_id, 'rating', $review['rating'] );
                $imported++;
            } else {
                $errors[] = sprintf( __( 'Avis %d : Erreur lors de la création de l\'avis.', 'boss-seo' ), $index + 1 );
            }
        }

        return rest_ensure_response( array(
            'message' => sprintf( __( '%d avis importés avec succès.', 'boss-seo' ), $imported ),
            'imported' => $imported,
            'errors' => $errors,
        ) );
    }

    /**
     * Exporte des avis via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function export_reviews( $request ) {
        $product_id = isset( $request['product_id'] ) ? absint( $request['product_id'] ) : 0;
        $format = isset( $request['format'] ) ? sanitize_text_field( $request['format'] ) : 'csv';

        // Récupérer les avis
        $args = array(
            'post_id' => $product_id ? $product_id : '',
            'status' => 'approve',
            'type' => 'review',
        );

        $reviews = get_comments( $args );

        if ( empty( $reviews ) ) {
            return new WP_Error( 'no_reviews', __( 'Aucun avis trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        $data = array();

        foreach ( $reviews as $review ) {
            $rating = get_comment_meta( $review->comment_ID, 'rating', true );

            $data[] = array(
                'id' => $review->comment_ID,
                'product_id' => $review->comment_post_ID,
                'author' => $review->comment_author,
                'email' => $review->comment_author_email,
                'content' => $review->comment_content,
                'rating' => $rating,
                'date' => $review->comment_date,
            );
        }

        if ( $format === 'csv' ) {
            // Créer le fichier CSV
            $csv = array();
            $csv[] = array( 'product_id', 'author', 'email', 'content', 'rating', 'date' );

            foreach ( $data as $review ) {
                $csv[] = array(
                    $review['product_id'],
                    $review['author'],
                    $review['email'],
                    $review['content'],
                    $review['rating'],
                    $review['date'],
                );
            }

            // Générer le contenu CSV
            $output = '';
            foreach ( $csv as $row ) {
                $output .= implode( ',', array_map( 'addslashes', $row ) ) . "\n";
            }

            return rest_ensure_response( array(
                'file_content' => $output,
                'file_name' => 'boss-seo-reviews-' . date( 'Y-m-d' ) . '.csv',
                'file_type' => 'text/csv',
            ) );
        } else {
            return rest_ensure_response( $data );
        }
    }

    /**
     * Ajoute le champ de notation dans le formulaire de commentaire.
     *
     * @since    1.2.0
     */
    public function add_review_rating_field() {
        if ( ! is_product() ) {
            return;
        }

        ?>
        <div class="comment-form-rating">
            <label for="rating"><?php esc_html_e( 'Votre note', 'boss-seo' ); ?> <span class="required">*</span></label>
            <select name="rating" id="rating" required>
                <option value=""><?php esc_html_e( 'Note&hellip;', 'boss-seo' ); ?></option>
                <option value="5"><?php esc_html_e( 'Parfait', 'boss-seo' ); ?></option>
                <option value="4"><?php esc_html_e( 'Bien', 'boss-seo' ); ?></option>
                <option value="3"><?php esc_html_e( 'Moyen', 'boss-seo' ); ?></option>
                <option value="2"><?php esc_html_e( 'Pas terrible', 'boss-seo' ); ?></option>
                <option value="1"><?php esc_html_e( 'Très mauvais', 'boss-seo' ); ?></option>
            </select>
        </div>
        <?php
    }

    /**
     * Enregistre la notation de l'avis.
     *
     * @since    1.2.0
     * @param    int    $comment_id    L'ID du commentaire.
     */
    public function save_review_rating( $comment_id ) {
        if ( isset( $_POST['rating'] ) && is_product() ) {
            $rating = absint( $_POST['rating'] );
            if ( $rating > 0 && $rating <= 5 ) {
                update_comment_meta( $comment_id, 'rating', $rating );
            }
        }
    }

    /**
     * Affiche la notation de l'avis.
     *
     * @since    1.2.0
     * @param    WP_Comment    $comment    Le commentaire.
     */
    public function display_review_rating( $comment ) {
        $rating = get_comment_meta( $comment->comment_ID, 'rating', true );

        if ( $rating ) {
            echo '<div class="star-rating" title="' . sprintf( esc_attr__( 'Note %s sur 5', 'boss-seo' ), $rating ) . '">';
            echo '<span style="width:' . ( ( $rating / 5 ) * 100 ) . '%">';
            echo sprintf( esc_html__( 'Note %s sur 5', 'boss-seo' ), $rating );
            echo '</span>';
            echo '</div>';
        }
    }

    /**
     * Génère le shortcode pour afficher la notation d'un produit.
     *
     * @since    1.2.0
     * @param    array     $atts       Les attributs du shortcode.
     * @param    string    $content    Le contenu du shortcode.
     * @return   string                Le contenu généré.
     */
    public function rating_shortcode( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'product_id' => 0,
            'show_count' => 'yes',
        ), $atts, 'boss_product_rating' );

        $product_id = absint( $atts['product_id'] );
        $show_count = $atts['show_count'] === 'yes';

        if ( ! $product_id ) {
            return '';
        }

        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return '';
        }

        $rating_count = $product->get_rating_count();
        $average_rating = $product->get_average_rating();

        if ( $rating_count === 0 ) {
            return '';
        }

        $output = '<div class="boss-product-rating">';
        $output .= '<div class="star-rating" title="' . sprintf( esc_attr__( 'Note %s sur 5', 'boss-seo' ), $average_rating ) . '">';
        $output .= '<span style="width:' . ( ( $average_rating / 5 ) * 100 ) . '%">';
        $output .= sprintf( esc_html__( 'Note %s sur 5', 'boss-seo' ), $average_rating );
        $output .= '</span>';
        $output .= '</div>';

        if ( $show_count ) {
            $output .= '<span class="boss-product-rating-count">';
            $output .= sprintf( _n( '(%s avis)', '(%s avis)', $rating_count, 'boss-seo' ), $rating_count );
            $output .= '</span>';
        }

        $output .= '</div>';

        return $output;
    }

    /**
     * Génère le shortcode pour afficher les avis d'un produit.
     *
     * @since    1.2.0
     * @param    array     $atts       Les attributs du shortcode.
     * @param    string    $content    Le contenu du shortcode.
     * @return   string                Le contenu généré.
     */
    public function reviews_shortcode( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'product_id' => 0,
            'count' => 5,
            'show_rating' => 'yes',
            'show_date' => 'yes',
            'show_author' => 'yes',
        ), $atts, 'boss_product_reviews' );

        $product_id = absint( $atts['product_id'] );
        $count = absint( $atts['count'] );
        $show_rating = $atts['show_rating'] === 'yes';
        $show_date = $atts['show_date'] === 'yes';
        $show_author = $atts['show_author'] === 'yes';

        if ( ! $product_id ) {
            return '';
        }

        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return '';
        }

        $args = array(
            'post_id' => $product_id,
            'status' => 'approve',
            'type' => 'review',
            'number' => $count,
        );

        $reviews = get_comments( $args );

        if ( empty( $reviews ) ) {
            return '';
        }

        $output = '<div class="boss-product-reviews">';

        foreach ( $reviews as $review ) {
            $rating = get_comment_meta( $review->comment_ID, 'rating', true );

            $output .= '<div class="boss-product-review">';

            if ( $show_rating && $rating ) {
                $output .= '<div class="boss-product-review-rating">';
                $output .= '<div class="star-rating" title="' . sprintf( esc_attr__( 'Note %s sur 5', 'boss-seo' ), $rating ) . '">';
                $output .= '<span style="width:' . ( ( $rating / 5 ) * 100 ) . '%">';
                $output .= sprintf( esc_html__( 'Note %s sur 5', 'boss-seo' ), $rating );
                $output .= '</span>';
                $output .= '</div>';
                $output .= '</div>';
            }

            $output .= '<div class="boss-product-review-content">';
            $output .= wpautop( $review->comment_content );
            $output .= '</div>';

            if ( $show_author || $show_date ) {
                $output .= '<div class="boss-product-review-meta">';

                if ( $show_author ) {
                    $output .= '<span class="boss-product-review-author">';
                    $output .= sprintf( esc_html__( 'Par %s', 'boss-seo' ), $review->comment_author );
                    $output .= '</span>';
                }

                if ( $show_date ) {
                    $output .= '<span class="boss-product-review-date">';
                    $output .= sprintf( esc_html__( 'le %s', 'boss-seo' ), date_i18n( get_option( 'date_format' ), strtotime( $review->comment_date ) ) );
                    $output .= '</span>';
                }

                $output .= '</div>';
            }

            $output .= '</div>';
        }

        $output .= '</div>';

        return $output;
    }
}