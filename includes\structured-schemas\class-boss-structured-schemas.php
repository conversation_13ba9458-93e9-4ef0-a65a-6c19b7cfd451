<?php
/**
 * La classe principale du module Schémas structurés.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 */

/**
 * La classe principale du module Schémas structurés.
 *
 * Cette classe gère toutes les fonctionnalités du module Schémas structurés.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 * <AUTHOR> SEO Team
 */
class Boss_Structured_Schemas {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Instance de la classe de gestion des schémas.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Structured_Schemas_Manager    $schema_manager    Gère les schémas.
     */
    protected $schema_manager;

    /**
     * Instance de la classe de gestion des règles.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Structured_Schemas_Rules    $rules_manager    Gère les règles d'application.
     */
    protected $rules_manager;

    /**
     * Instance de la classe d'intégration IA.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Structured_Schemas_AI    $ai_manager    Gère l'intégration IA.
     */
    protected $ai_manager;

    /**
     * Instance de la classe API REST.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Structured_Schemas_API    $api    Gère l'API REST.
     */
    protected $api;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string                   $plugin_name    Le nom du plugin.
     * @param    string                   $version        La version du plugin.
     * @param    Boss_Optimizer_Settings  $settings       Instance de la classe de paramètres (peut être null).
     */
    public function __construct( $plugin_name, $version, $settings = null ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;

        $this->load_dependencies();
        $this->init_managers();
    }

    /**
     * Charge les dépendances nécessaires au module.
     *
     * @since    1.2.0
     * @access   private
     */
    private function load_dependencies() {
        /**
         * La classe qui gère les schémas.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'structured-schemas/class-boss-structured-schemas-manager.php';

        /**
         * La classe qui gère les règles d'application.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'structured-schemas/class-boss-structured-schemas-rules.php';

        /**
         * La classe qui gère l'intégration IA.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'structured-schemas/class-boss-structured-schemas-ai.php';

        /**
         * La classe qui gère l'API REST.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'structured-schemas/class-boss-structured-schemas-api.php';

        /**
         * La classe qui gère l'affichage des schémas.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'structured-schemas/class-boss-structured-schemas-output.php';
    }

    /**
     * Initialise les gestionnaires du module.
     *
     * @since    1.2.0
     * @access   private
     */
    private function init_managers() {
        $this->schema_manager = new Boss_Structured_Schemas_Manager( $this->plugin_name, $this->version, $this->settings );
        $this->rules_manager = new Boss_Structured_Schemas_Rules( $this->plugin_name, $this->version, $this->settings );
        $this->ai_manager = new Boss_Structured_Schemas_AI( $this->plugin_name, $this->version, $this->settings );

        // Initialiser l'API REST
        $this->api = new Boss_Structured_Schemas_API(
            $this->plugin_name,
            $this->version,
            $this->settings,
            $this->schema_manager,
            $this->rules_manager,
            $this->ai_manager
        );

        // Initialiser la sortie des schémas
        $output = new Boss_Structured_Schemas_Output(
            $this->plugin_name,
            $this->version,
            $this->settings,
            $this->schema_manager,
            $this->rules_manager
        );
    }

    /**
     * Enregistre les hooks WordPress nécessaires.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Hooks pour l'initialisation des tables personnalisées
        add_action( 'init', array( $this, 'register_post_types' ) );

        // Hooks pour l'ajout des schémas dans le frontend
        add_action( 'wp_head', array( $this, 'output_schemas' ), 10 );

        // Hooks pour l'admin
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );

        // Enregistrer les routes REST API
        add_action( 'rest_api_init', array( $this->api, 'register_api_routes' ) );
    }

    /**
     * Enregistre les types de posts personnalisés.
     *
     * @since    1.2.0
     */
    public function register_post_types() {
        // Enregistrer le type de post pour les schémas
        register_post_type( 'boss_schema', array(
            'labels' => array(
                'name' => __( 'Schémas', 'boss-seo' ),
                'singular_name' => __( 'Schéma', 'boss-seo' ),
            ),
            'public' => false,
            'show_ui' => false,
            'capability_type' => 'post',
            'capabilities' => array(
                'create_posts' => 'do_not_allow',
            ),
            'map_meta_cap' => true,
            'hierarchical' => false,
            'supports' => array( 'title', 'custom-fields' ),
            'has_archive' => false,
            'show_in_rest' => true,
            'rest_base' => 'boss_schemas',
            'rest_controller_class' => 'WP_REST_Posts_Controller',
        ) );

        // Enregistrer le type de post pour les règles
        register_post_type( 'boss_schema_rule', array(
            'labels' => array(
                'name' => __( 'Règles de schéma', 'boss-seo' ),
                'singular_name' => __( 'Règle de schéma', 'boss-seo' ),
            ),
            'public' => false,
            'show_ui' => false,
            'capability_type' => 'post',
            'capabilities' => array(
                'create_posts' => 'do_not_allow',
            ),
            'map_meta_cap' => true,
            'hierarchical' => false,
            'supports' => array( 'title', 'custom-fields' ),
            'has_archive' => false,
            'show_in_rest' => true,
            'rest_base' => 'boss_schema_rules',
            'rest_controller_class' => 'WP_REST_Posts_Controller',
        ) );
    }

    /**
     * Génère et affiche les schémas structurés dans le head.
     *
     * @since    1.2.0
     */
    public function output_schemas() {
        global $post;

        if ( ! is_singular() || ! $post ) {
            return;
        }

        // Obtenir les schémas applicables pour ce post
        $schemas = $this->schema_manager->get_schemas_for_post( $post->ID );

        if ( empty( $schemas ) ) {
            return;
        }

        foreach ( $schemas as $schema ) {
            echo "<!-- Boss SEO: Schéma structuré -->\n";
            echo "<script type=\"application/ld+json\">\n";
            echo json_encode( $schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE );
            echo "\n</script>\n";
        }
    }

    /**
     * Enregistre les scripts et styles pour l'admin.
     *
     * @since    1.2.0
     * @param    string    $hook    Le hook de la page actuelle.
     */
    public function enqueue_admin_scripts( $hook ) {
        // N'enregistrer les scripts que sur les pages du plugin
        if ( strpos( $hook, 'boss-seo' ) === false ) {
            return;
        }

        // Enregistrer les styles
        wp_enqueue_style( 'boss-structured-schemas', plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/css/boss-structured-schemas.css', array(), $this->version, 'all' );

        // Enregistrer les scripts
        wp_enqueue_script( 'boss-structured-schemas', plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/js/boss-structured-schemas.js', array( 'jquery', 'wp-api', 'wp-i18n' ), $this->version, false );

        // Localiser le script
        wp_localize_script( 'boss-structured-schemas', 'bossStructuredSchemas', array(
            'apiUrl' => rest_url( 'boss-seo/v1' ),
            'nonce' => wp_create_nonce( 'wp_rest' ),
            'schemaTypes' => $this->schema_manager->get_schema_types(),
            'i18n' => array(
                'saveSuccess' => __( 'Schéma enregistré avec succès.', 'boss-seo' ),
                'saveError' => __( 'Erreur lors de l\'enregistrement du schéma.', 'boss-seo' ),
                'deleteConfirm' => __( 'Êtes-vous sûr de vouloir supprimer ce schéma ?', 'boss-seo' ),
                'deleteSuccess' => __( 'Schéma supprimé avec succès.', 'boss-seo' ),
                'deleteError' => __( 'Erreur lors de la suppression du schéma.', 'boss-seo' ),
                'generateSuccess' => __( 'Schéma généré avec succès.', 'boss-seo' ),
                'generateError' => __( 'Erreur lors de la génération du schéma.', 'boss-seo' ),
                'testSuccess' => __( 'Le schéma est valide.', 'boss-seo' ),
                'testError' => __( 'Le schéma contient des erreurs.', 'boss-seo' ),
            )
        ) );
    }
}
