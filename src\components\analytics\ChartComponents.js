import { __ } from '@wordpress/i18n';
import { useState } from '@wordpress/element';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Couleurs pour les graphiques
const COLORS = {
  primary: '#4F46E5',
  secondary: '#6366F1',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  gray: '#9CA3AF',
  primaryLight: '#EEF2FF',
  secondaryLight: '#E0E7FF',
  successLight: '#D1FAE5',
  warningLight: '#FEF3C7',
  errorLight: '#FEE2E2',
  infoLight: '#DBEAFE'
};

// Composant pour le graphique de trafic
export const TrafficChart = ({ data, metrics = ['users', 'sessions', 'pageviews'] }) => {
  // État pour les métriques visibles
  const [visibleMetrics, setVisibleMetrics] = useState(metrics);
  
  // Fonction pour basculer la visibilité d'une métrique
  const toggleMetric = (metric) => {
    if (visibleMetrics.includes(metric)) {
      setVisibleMetrics(visibleMetrics.filter(m => m !== metric));
    } else {
      setVisibleMetrics([...visibleMetrics, metric]);
    }
  };
  
  // Formater les dates pour l'axe X
  const formatXAxis = (tickItem) => {
    const date = new Date(tickItem);
    return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' });
  };
  
  // Formater les valeurs pour le tooltip
  const formatTooltipValue = (value) => {
    return new Intl.NumberFormat('fr-FR').format(value);
  };
  
  // Définir les couleurs pour chaque métrique
  const metricColors = {
    users: COLORS.primary,
    sessions: COLORS.success,
    pageviews: COLORS.info,
    bounceRate: COLORS.warning
  };
  
  // Définir les noms d'affichage pour chaque métrique
  const metricNames = {
    users: __('Utilisateurs', 'boss-seo'),
    sessions: __('Sessions', 'boss-seo'),
    pageviews: __('Pages vues', 'boss-seo'),
    bounceRate: __('Taux de rebond (%)', 'boss-seo')
  };

  return (
    <div>
      <div className="boss-flex boss-flex-wrap boss-justify-end boss-mb-4 boss-space-x-2">
        {metrics.map(metric => (
          <button
            key={metric}
            className={`boss-px-3 boss-py-1 boss-rounded-full boss-text-xs boss-font-medium boss-transition-colors ${
              visibleMetrics.includes(metric)
                ? `boss-bg-${metricColors[metric]} boss-text-white`
                : 'boss-bg-gray-100 boss-text-gray-600'
            }`}
            onClick={() => toggleMetric(metric)}
          >
            {metricNames[metric]}
          </button>
        ))}
      </div>
      
      <ResponsiveContainer width="100%" height={300}>
        <LineChart
          data={data}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="date" 
            tickFormatter={formatXAxis} 
            tick={{ fontSize: 12 }}
          />
          <YAxis 
            yAxisId="left"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => new Intl.NumberFormat('fr-FR', { notation: 'compact' }).format(value)}
          />
          {visibleMetrics.includes('bounceRate') && (
            <YAxis 
              yAxisId="right"
              orientation="right"
              domain={[0, 100]}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => `${value}%`}
            />
          )}
          <Tooltip 
            formatter={(value, name) => [
              formatTooltipValue(value), 
              metricNames[name]
            ]}
            labelFormatter={(label) => new Date(label).toLocaleDateString('fr-FR', { 
              day: 'numeric', 
              month: 'long', 
              year: 'numeric' 
            })}
          />
          <Legend />
          
          {visibleMetrics.includes('users') && (
            <Line
              type="monotone"
              dataKey="users"
              name={metricNames.users}
              stroke={metricColors.users}
              activeDot={{ r: 8 }}
              strokeWidth={2}
              yAxisId="left"
            />
          )}
          
          {visibleMetrics.includes('sessions') && (
            <Line
              type="monotone"
              dataKey="sessions"
              name={metricNames.sessions}
              stroke={metricColors.sessions}
              activeDot={{ r: 8 }}
              strokeWidth={2}
              yAxisId="left"
            />
          )}
          
          {visibleMetrics.includes('pageviews') && (
            <Line
              type="monotone"
              dataKey="pageviews"
              name={metricNames.pageviews}
              stroke={metricColors.pageviews}
              activeDot={{ r: 8 }}
              strokeWidth={2}
              yAxisId="left"
            />
          )}
          
          {visibleMetrics.includes('bounceRate') && (
            <Line
              type="monotone"
              dataKey="bounceRate"
              name={metricNames.bounceRate}
              stroke={metricColors.bounceRate}
              activeDot={{ r: 8 }}
              strokeWidth={2}
              yAxisId="right"
            />
          )}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Composant pour le graphique de mots-clés
export const KeywordsChart = ({ data }) => {
  // Préparer les données pour le graphique
  const chartData = data.map(item => ({
    keyword: item.keyword,
    position: item.position,
    clicks: item.clicks,
    impressions: item.impressions,
    ctr: item.ctr
  }));
  
  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart
        data={chartData}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        layout="vertical"
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          type="number"
          tick={{ fontSize: 12 }}
        />
        <YAxis 
          dataKey="keyword" 
          type="category"
          width={150}
          tick={{ fontSize: 12 }}
        />
        <Tooltip 
          formatter={(value, name) => [
            name === 'position' 
              ? value.toFixed(1) 
              : new Intl.NumberFormat('fr-FR').format(value),
            name === 'position' 
              ? __('Position moyenne', 'boss-seo') 
              : name === 'clicks' 
                ? __('Clics', 'boss-seo') 
                : name === 'impressions' 
                  ? __('Impressions', 'boss-seo')
                  : __('CTR (%)', 'boss-seo')
          ]}
        />
        <Legend />
        <Bar 
          dataKey="clicks" 
          name={__('Clics', 'boss-seo')} 
          fill={COLORS.primary} 
          radius={[0, 4, 4, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

// Composant pour le graphique de positions
export const PositionsChart = ({ data }) => {
  // Préparer les données pour le graphique
  const chartData = data.map(item => ({
    keyword: item.keyword,
    position: item.position
  })).sort((a, b) => a.position - b.position);
  
  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart
        data={chartData}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        layout="vertical"
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          type="number"
          domain={[0, 'dataMax']}
          reversed
          tick={{ fontSize: 12 }}
        />
        <YAxis 
          dataKey="keyword" 
          type="category"
          width={150}
          tick={{ fontSize: 12 }}
        />
        <Tooltip 
          formatter={(value, name) => [
            value.toFixed(1),
            __('Position moyenne', 'boss-seo')
          ]}
        />
        <Legend />
        <Bar 
          dataKey="position" 
          name={__('Position', 'boss-seo')} 
          fill={COLORS.success} 
          radius={[0, 4, 4, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

// Composant pour le graphique de répartition des pages
export const PagesDistributionChart = ({ data }) => {
  // Préparer les données pour le graphique
  const total = data.reduce((sum, item) => sum + item.pageviews, 0);
  const chartData = data.map(item => ({
    name: item.title,
    value: item.pageviews,
    percentage: (item.pageviews / total) * 100
  }));
  
  const COLORS_ARRAY = [
    COLORS.primary,
    COLORS.success,
    COLORS.info,
    COLORS.warning,
    COLORS.error,
    COLORS.secondary,
    '#8B5CF6',
    '#EC4899',
    '#14B8A6',
    '#F97316'
  ];
  
  return (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          outerRadius={100}
          fill="#8884d8"
          dataKey="value"
          nameKey="name"
          label={({ name, percentage }) => `${name.substring(0, 15)}${name.length > 15 ? '...' : ''} (${percentage.toFixed(1)}%)`}
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS_ARRAY[index % COLORS_ARRAY.length]} />
          ))}
        </Pie>
        <Tooltip 
          formatter={(value, name, props) => [
            new Intl.NumberFormat('fr-FR').format(value),
            props.payload.name
          ]}
        />
      </PieChart>
    </ResponsiveContainer>
  );
};

// Composant pour le graphique de tendances
export const TrendsChart = ({ data, metric = 'traffic' }) => {
  // Préparer les données pour le graphique
  const chartData = data.map(item => ({
    date: item.date,
    value: item[metric]
  }));
  
  // Définir les couleurs pour chaque métrique
  const metricColors = {
    traffic: COLORS.primary,
    users: COLORS.success,
    sessions: COLORS.info,
    pageviews: COLORS.warning,
    bounceRate: COLORS.error
  };
  
  // Définir les noms d'affichage pour chaque métrique
  const metricNames = {
    traffic: __('Trafic', 'boss-seo'),
    users: __('Utilisateurs', 'boss-seo'),
    sessions: __('Sessions', 'boss-seo'),
    pageviews: __('Pages vues', 'boss-seo'),
    bounceRate: __('Taux de rebond', 'boss-seo')
  };
  
  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart
        data={chartData}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="date" 
          tickFormatter={(tickItem) => {
            const date = new Date(tickItem);
            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' });
          }}
          tick={{ fontSize: 12 }}
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          tickFormatter={(value) => new Intl.NumberFormat('fr-FR', { notation: 'compact' }).format(value)}
        />
        <Tooltip 
          formatter={(value, name) => [
            new Intl.NumberFormat('fr-FR').format(value),
            metricNames[metric]
          ]}
          labelFormatter={(label) => new Date(label).toLocaleDateString('fr-FR', { 
            day: 'numeric', 
            month: 'long', 
            year: 'numeric' 
          })}
        />
        <Area
          type="monotone"
          dataKey="value"
          name={metricNames[metric]}
          stroke={metricColors[metric]}
          fill={metricColors[metric] + '40'}
          strokeWidth={2}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

export default {
  TrafficChart,
  KeywordsChart,
  PositionsChart,
  PagesDistributionChart,
  TrendsChart
};
