import { useState, useRef, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { Popover, Button } from '@wordpress/components';

/**
 * Composant de popover d'aide avancé
 * 
 * @param {Object} props - Propriétés du composant
 * @param {string} props.title - Titre du popover
 * @param {string|React.ReactNode} props.content - Contenu du popover
 * @param {string} props.position - Position du popover (top, bottom, left, right)
 * @param {boolean} props.showIcon - Afficher l'icône d'aide
 * @param {string} props.iconType - Type d'icône (help, info, warning)
 * @param {string} props.buttonText - Texte du bouton (si pas d'icône)
 * @param {string} props.className - Classes CSS additionnelles
 * @param {boolean} props.hasArrow - Afficher une flèche sur le popover
 * @param {boolean} props.isDismissible - Popover peut être fermé
 * @param {React.ReactNode} props.children - Contenu enfant
 * @param {Function} props.onOpen - Callback à l'ouverture
 * @param {Function} props.onClose - Callback à la fermeture
 * @returns {React.ReactElement} Composant HelpPopover
 */
const HelpPopover = ({ 
  title,
  content, 
  position = 'bottom center', 
  showIcon = true,
  iconType = 'help',
  buttonText = __('Aide', 'boss-seo'),
  className = '',
  hasArrow = true,
  isDismissible = true,
  children,
  onOpen = () => {},
  onClose = () => {}
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const anchorRef = useRef(null);
  
  // Déterminer l'icône à afficher
  const getIconClass = () => {
    switch (iconType) {
      case 'info':
        return 'dashicons-info';
      case 'warning':
        return 'dashicons-warning';
      default:
        return 'dashicons-editor-help';
    }
  };
  
  // Gérer l'ouverture du popover
  const handleOpen = () => {
    setIsVisible(true);
    onOpen();
  };
  
  // Gérer la fermeture du popover
  const handleClose = () => {
    setIsVisible(false);
    onClose();
  };
  
  // Gérer le clic en dehors du popover pour le fermer
  useEffect(() => {
    if (!isDismissible) return;
    
    const handleClickOutside = (event) => {
      if (anchorRef.current && !anchorRef.current.contains(event.target)) {
        handleClose();
      }
    };
    
    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible, isDismissible]);
  
  return (
    <div className={`boss-inline-flex boss-items-center boss-relative ${className}`}>
      <div ref={anchorRef}>
        {children ? (
          <div onClick={handleOpen}>
            {children}
          </div>
        ) : showIcon ? (
          <span 
            className={`dashicons ${getIconClass()} boss-text-boss-blue-500 boss-cursor-pointer boss-transition-all boss-duration-200 boss-ease-in-out boss-hover:boss-text-boss-blue-700`}
            onClick={handleOpen}
            aria-label={buttonText}
          />
        ) : (
          <Button 
            isSecondary
            isSmall
            onClick={handleOpen}
          >
            {buttonText}
          </Button>
        )}
      </div>
      
      {isVisible && (
        <Popover
          position={position}
          onClose={handleClose}
          anchor={anchorRef.current}
          noArrow={!hasArrow}
          className="boss-z-50"
        >
          <div className="boss-p-4 boss-max-w-md">
            {title && (
              <div className="boss-font-medium boss-text-lg boss-mb-2 boss-text-boss-dark boss-border-b boss-border-gray-200 boss-pb-2">
                {title}
              </div>
            )}
            <div className="boss-text-sm boss-text-boss-gray">
              {content}
            </div>
            {isDismissible && (
              <div className="boss-mt-4 boss-flex boss-justify-end">
                <Button 
                  isSecondary
                  isSmall
                  onClick={handleClose}
                >
                  {__('Fermer', 'boss-seo')}
                </Button>
              </div>
            )}
          </div>
        </Popover>
      )}
    </div>
  );
};

export default HelpPopover;
