/**
 * Service pour gérer les paramètres du plugin Boss SEO.
 */

import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

/**
 * Service pour gérer les paramètres du plugin Boss SEO.
 */
class SettingsService {
  /**
   * Récupère les paramètres généraux.
   *
   * @return {Promise} Une promesse qui se résout avec les paramètres généraux.
   */
  async getGeneralSettings() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/settings/general',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres généraux:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres généraux.
   *
   * @param {Object} settings Les paramètres à enregistrer.
   * @return {Promise} Une promesse qui se résout avec la réponse du serveur.
   */
  async saveGeneralSettings(settings) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/settings/general',
        method: 'POST',
        data: settings,
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres généraux:', error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres avancés.
   *
   * @return {Promise} Une promesse qui se résout avec les paramètres avancés.
   */
  async getAdvancedSettings() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/settings/advanced',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres avancés:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres avancés.
   *
   * @param {Object} settings Les paramètres à enregistrer.
   * @return {Promise} Une promesse qui se résout avec la réponse du serveur.
   */
  async saveAdvancedSettings(settings) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/settings/advanced',
        method: 'POST',
        data: settings,
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres avancés:', error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres de sauvegarde.
   *
   * @return {Promise} Une promesse qui se résout avec les paramètres de sauvegarde.
   */
  async getBackupSettings() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/settings/backup',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres de sauvegarde:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres de sauvegarde.
   *
   * @param {Object} settings Les paramètres à enregistrer.
   * @return {Promise} Une promesse qui se résout avec la réponse du serveur.
   */
  async saveBackupSettings(settings) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/settings/backup',
        method: 'POST',
        data: settings,
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres de sauvegarde:', error);
      throw error;
    }
  }

  /**
   * Récupère la liste des sauvegardes.
   *
   * @return {Promise} Une promesse qui se résout avec la liste des sauvegardes.
   */
  async getBackups() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/backups',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des sauvegardes:', error);
      throw error;
    }
  }

  /**
   * Crée une sauvegarde.
   *
   * @param {string} name Le nom de la sauvegarde.
   * @param {boolean} includeSettings Si les paramètres doivent être inclus.
   * @param {boolean} includeData Si les données doivent être incluses.
   * @return {Promise} Une promesse qui se résout avec la réponse du serveur.
   */
  async createBackup(name, includeSettings = true, includeData = true) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/backups',
        method: 'POST',
        data: {
          name,
          include_settings: includeSettings,
          include_data: includeData,
        },
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la création de la sauvegarde:', error);
      throw error;
    }
  }

  /**
   * Restaure une sauvegarde.
   *
   * @param {string} name Le nom de la sauvegarde à restaurer.
   * @return {Promise} Une promesse qui se résout avec la réponse du serveur.
   */
  async restoreBackup(name) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/backups/${name}/restore`,
        method: 'POST',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la restauration de la sauvegarde:', error);
      throw error;
    }
  }

  /**
   * Supprime une sauvegarde.
   *
   * @param {string} name Le nom de la sauvegarde à supprimer.
   * @return {Promise} Une promesse qui se résout avec la réponse du serveur.
   */
  async deleteBackup(name) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/backups/${name}`,
        method: 'DELETE',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la suppression de la sauvegarde:', error);
      throw error;
    }
  }

  /**
   * Récupère les préférences utilisateur.
   *
   * @return {Promise} Une promesse qui se résout avec les préférences utilisateur.
   */
  async getUserPreferences() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/user-preferences',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des préférences utilisateur:', error);
      throw error;
    }
  }

  /**
   * Enregistre les préférences utilisateur.
   *
   * @param {Object} preferences Les préférences à enregistrer.
   * @return {Promise} Une promesse qui se résout avec la réponse du serveur.
   */
  async saveUserPreferences(preferences) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/user-preferences',
        method: 'POST',
        data: preferences,
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des préférences utilisateur:', error);
      throw error;
    }
  }

  /**
   * Réinitialise les préférences utilisateur.
   *
   * @return {Promise} Une promesse qui se résout avec la réponse du serveur.
   */
  async resetUserPreferences() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/user-preferences/reset',
        method: 'POST',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la réinitialisation des préférences utilisateur:', error);
      throw error;
    }
  }

  /**
   * Marque une notification comme lue.
   *
   * @param {string} id L'ID de la notification.
   * @return {Promise} Une promesse qui se résout avec la réponse du serveur.
   */
  async markNotificationRead(id) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/notifications/${id}/read`,
        method: 'POST',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du marquage de la notification comme lue:', error);
      throw error;
    }
  }
}

export default new SettingsService();
