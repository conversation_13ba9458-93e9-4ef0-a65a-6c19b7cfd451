import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { 
  <PERSON>, 
  Card<PERSON><PERSON>, 
  Card<PERSON>eader, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TabP<PERSON>l,
  <PERSON>dal
} from '@wordpress/components';

import HelpSearch from './HelpSearch';
import HelpTooltip from './HelpTooltip';

/**
 * Composant de base de connaissances
 * 
 * @param {Object} props - Propriétés du composant
 * @param {Array} props.articles - Articles de la base de connaissances
 * @param {Array} props.categories - Catégories d'articles
 * @param {boolean} props.isLoading - Indique si les données sont en cours de chargement
 * @returns {React.ReactElement} Composant KnowledgeBase
 */
const KnowledgeBase = ({ 
  articles = [], 
  categories = [],
  isLoading = false
}) => {
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedArticle, setSelectedArticle] = useState(null);
  const [showArticleModal, setShowArticleModal] = useState(false);
  const [activeCategory, setActiveCategory] = useState('all');
  
  // Filtrer les articles par catégorie
  const getFilteredArticles = () => {
    if (isSearching && searchResults.length > 0) {
      return searchResults;
    }
    
    if (activeCategory === 'all') {
      return articles;
    }
    
    return articles.filter(article => article.category === activeCategory);
  };
  
  // Gérer la recherche
  const handleSearch = (results) => {
    setSearchResults(results);
    setIsSearching(results.length > 0);
  };
  
  // Ouvrir un article
  const openArticle = (article) => {
    setSelectedArticle(article);
    setShowArticleModal(true);
  };
  
  // Fermer l'article
  const closeArticle = () => {
    setShowArticleModal(false);
  };
  
  // Obtenir les articles populaires
  const getPopularArticles = () => {
    return articles
      .filter(article => article.isPopular)
      .sort((a, b) => b.viewCount - a.viewCount)
      .slice(0, 5);
  };
  
  // Obtenir les articles récents
  const getRecentArticles = () => {
    return [...articles]
      .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
      .slice(0, 5);
  };
  
  // Formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };
  
  // Articles filtrés
  const filteredArticles = getFilteredArticles();
  
  return (
    <div>
      {/* Recherche */}
      <div className="boss-mb-6">
        <HelpSearch 
          articles={articles}
          onSearch={handleSearch}
          onResultClick={openArticle}
        />
      </div>
      
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-4 boss-gap-6">
          {/* Sidebar */}
          <div className="lg:boss-col-span-1">
            <Card className="boss-mb-6">
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Catégories', 'boss-seo')}
                </h3>
              </CardHeader>
              <CardBody className="boss-p-0">
                <ul className="boss-divide-y boss-divide-gray-200">
                  <li>
                    <button
                      className={`boss-w-full boss-text-left boss-px-4 boss-py-2 boss-transition-colors boss-duration-200 ${
                        activeCategory === 'all'
                          ? 'boss-bg-blue-50 boss-text-blue-700 boss-font-medium'
                          : 'boss-text-boss-gray boss-hover:boss-bg-gray-50'
                      }`}
                      onClick={() => setActiveCategory('all')}
                    >
                      {__('Tous les articles', 'boss-seo')}
                    </button>
                  </li>
                  {categories.map((category) => (
                    <li key={category.id}>
                      <button
                        className={`boss-w-full boss-text-left boss-px-4 boss-py-2 boss-transition-colors boss-duration-200 ${
                          activeCategory === category.id
                            ? 'boss-bg-blue-50 boss-text-blue-700 boss-font-medium'
                            : 'boss-text-boss-gray boss-hover:boss-bg-gray-50'
                        }`}
                        onClick={() => setActiveCategory(category.id)}
                      >
                        {category.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </CardBody>
            </Card>
            
            <Card>
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Articles populaires', 'boss-seo')}
                </h3>
              </CardHeader>
              <CardBody className="boss-p-0">
                <ul className="boss-divide-y boss-divide-gray-200">
                  {getPopularArticles().map((article) => (
                    <li key={article.id}>
                      <button
                        className="boss-w-full boss-text-left boss-px-4 boss-py-2 boss-text-boss-gray boss-hover:boss-bg-gray-50 boss-transition-colors boss-duration-200"
                        onClick={() => openArticle(article)}
                      >
                        {article.title}
                      </button>
                    </li>
                  ))}
                </ul>
              </CardBody>
            </Card>
          </div>
          
          {/* Contenu principal */}
          <div className="lg:boss-col-span-3">
            <Card>
              <CardHeader className="boss-border-b boss-border-gray-200">
                <div className="boss-flex boss-justify-between boss-items-center">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {isSearching 
                      ? __('Résultats de recherche', 'boss-seo') 
                      : activeCategory === 'all' 
                        ? __('Tous les articles', 'boss-seo')
                        : categories.find(c => c.id === activeCategory)?.name || __('Articles', 'boss-seo')}
                  </h2>
                  <div className="boss-text-sm boss-text-boss-gray">
                    {filteredArticles.length} {__('articles', 'boss-seo')}
                  </div>
                </div>
              </CardHeader>
              <CardBody>
                {filteredArticles.length === 0 ? (
                  <div className="boss-text-center boss-py-8 boss-text-boss-gray">
                    <div className="boss-text-5xl boss-mb-4">
                      <span className="dashicons dashicons-info"></span>
                    </div>
                    <h3 className="boss-text-lg boss-font-medium boss-mb-2">
                      {isSearching 
                        ? __('Aucun résultat trouvé', 'boss-seo') 
                        : __('Aucun article dans cette catégorie', 'boss-seo')}
                    </h3>
                    <p className="boss-mb-4">
                      {isSearching 
                        ? __('Essayez d\'autres termes de recherche ou parcourez les catégories.', 'boss-seo') 
                        : __('Des articles seront ajoutés prochainement.', 'boss-seo')}
                    </p>
                  </div>
                ) : (
                  <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                    {filteredArticles.map((article) => (
                      <Card key={article.id} className="boss-transition-all boss-duration-300 boss-hover:boss-shadow-md">
                        <CardBody>
                          <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark boss-mb-2">
                            {article.title}
                          </h3>
                          <p className="boss-text-sm boss-text-boss-gray boss-mb-4 boss-line-clamp-2">
                            {article.excerpt || article.content.substring(0, 120) + '...'}
                          </p>
                          <div className="boss-flex boss-justify-between boss-items-center">
                            <div>
                              {article.category && (
                                <span className="boss-inline-block boss-px-2 boss-py-1 boss-text-xs boss-font-medium boss-rounded-full boss-bg-blue-100 boss-text-blue-800">
                                  {article.category}
                                </span>
                              )}
                            </div>
                            <div className="boss-text-xs boss-text-boss-gray">
                              {article.updatedAt && __('Mis à jour le', 'boss-seo') + ' ' + formatDate(article.updatedAt)}
                            </div>
                          </div>
                        </CardBody>
                        <CardFooter className="boss-border-t boss-border-gray-200">
                          <Button
                            isSecondary
                            onClick={() => openArticle(article)}
                            className="boss-w-full"
                          >
                            {__('Lire l\'article', 'boss-seo')}
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                )}
              </CardBody>
            </Card>
          </div>
        </div>
      )}
      
      {/* Modal d'article */}
      {showArticleModal && selectedArticle && (
        <Modal
          title={selectedArticle.title}
          onRequestClose={closeArticle}
          className="boss-article-modal"
        >
          <div className="boss-p-6">
            <div className="boss-mb-4">
              <div className="boss-flex boss-justify-between boss-items-center boss-mb-4">
                <div>
                  {selectedArticle.category && (
                    <span className="boss-inline-block boss-px-2 boss-py-1 boss-text-xs boss-font-medium boss-rounded-full boss-bg-blue-100 boss-text-blue-800">
                      {selectedArticle.category}
                    </span>
                  )}
                </div>
                <div className="boss-text-sm boss-text-boss-gray">
                  {selectedArticle.updatedAt && __('Mis à jour le', 'boss-seo') + ' ' + formatDate(selectedArticle.updatedAt)}
                </div>
              </div>
              
              <div className="boss-prose boss-max-w-none boss-mb-6">
                <div dangerouslySetInnerHTML={{ __html: selectedArticle.content }} />
              </div>
              
              {selectedArticle.relatedArticles && selectedArticle.relatedArticles.length > 0 && (
                <div className="boss-mt-8 boss-pt-4 boss-border-t boss-border-gray-200">
                  <h3 className="boss-text-lg boss-font-medium boss-text-boss-dark boss-mb-3">
                    {__('Articles connexes', 'boss-seo')}
                  </h3>
                  <ul className="boss-space-y-2">
                    {selectedArticle.relatedArticles.map((relatedId) => {
                      const related = articles.find(a => a.id === relatedId);
                      if (!related) return null;
                      
                      return (
                        <li key={related.id}>
                          <button
                            className="boss-text-blue-600 boss-hover:boss-text-blue-800 boss-hover:boss-underline boss-text-left"
                            onClick={() => openArticle(related)}
                          >
                            {related.title}
                          </button>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              )}
              
              {selectedArticle.tags && selectedArticle.tags.length > 0 && (
                <div className="boss-mt-4 boss-flex boss-flex-wrap boss-gap-2">
                  {selectedArticle.tags.map((tag, index) => (
                    <span 
                      key={index} 
                      className="boss-px-2 boss-py-1 boss-text-xs boss-rounded-full boss-bg-gray-100 boss-text-boss-gray"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
            
            <div className="boss-flex boss-justify-between boss-mt-6">
              <Button
                isSecondary
                onClick={closeArticle}
              >
                {__('Fermer', 'boss-seo')}
              </Button>
              
              <div className="boss-flex boss-space-x-2">
                <Button
                  isSecondary
                  href={selectedArticle.externalUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  disabled={!selectedArticle.externalUrl}
                >
                  <span className="dashicons dashicons-external boss-mr-1"></span>
                  {__('Documentation complète', 'boss-seo')}
                </Button>
                
                <Button
                  isPrimary
                  onClick={() => {
                    // Simuler l'impression
                    window.print();
                  }}
                >
                  <span className="dashicons dashicons-printer boss-mr-1"></span>
                  {__('Imprimer', 'boss-seo')}
                </Button>
              </div>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default KnowledgeBase;
