<?php
/**
 * Script de debug temporaire pour tester l'affichage des métadonnées
 * À placer dans le dossier du plugin Boss SEO
 * 
 * Usage: wp-admin/admin.php?page=debug-metadata&post_id=123
 */

// Sécurité WordPress
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Vérifier les permissions
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'Accès refusé' );
}

// Récupérer l'ID du post depuis l'URL
$post_id = isset( $_GET['post_id'] ) ? intval( $_GET['post_id'] ) : 0;

if ( ! $post_id ) {
    echo '<div class="notice notice-error"><p>Veuillez spécifier un post_id dans l\'URL. Exemple: ?page=debug-metadata&post_id=123</p></div>';
    return;
}

$post = get_post( $post_id );
if ( ! $post ) {
    echo '<div class="notice notice-error"><p>Post non trouvé avec l\'ID: ' . $post_id . '</p></div>';
    return;
}

echo '<div class="wrap">';
echo '<h1>🔍 Debug Boss SEO - Métadonnées</h1>';
echo '<h2>Post: "' . esc_html( $post->post_title ) . '" (ID: ' . $post_id . ')</h2>';

// Récupérer toutes les métadonnées Boss SEO
$meta_keys = array(
    '_boss_seo_title' => 'Titre SEO',
    '_boss_seo_meta_description' => 'Meta Description',
    '_boss_seo_focus_keyword' => 'Mot-clé Principal',
    '_boss_seo_secondary_keywords' => 'Mots-clés Secondaires',
    '_boss_seo_canonical_url' => 'URL Canonique',
    '_boss_seo_robots_index' => 'Robots Index',
    '_boss_seo_robots_follow' => 'Robots Follow',
    '_boss_seo_og_title' => 'Titre Open Graph',
    '_boss_seo_og_description' => 'Description Open Graph',
    '_boss_seo_og_image' => 'Image Open Graph',
    '_boss_seo_twitter_title' => 'Titre Twitter',
    '_boss_seo_twitter_description' => 'Description Twitter',
    '_boss_seo_twitter_image' => 'Image Twitter',
    '_boss_seo_score' => 'Score SEO',
    '_boss_seo_analysis_date' => 'Date d\'Analyse',
    '_boss_seo_last_optimized' => 'Dernière Optimisation'
);

echo '<table class="wp-list-table widefat fixed striped">';
echo '<thead><tr><th>Clé Meta</th><th>Nom</th><th>Valeur</th><th>Type</th><th>Longueur</th></tr></thead>';
echo '<tbody>';

$has_data = false;

foreach ( $meta_keys as $meta_key => $label ) {
    $value = get_post_meta( $post_id, $meta_key, true );
    $type = gettype( $value );
    $length = is_string( $value ) ? strlen( $value ) : ( is_array( $value ) ? count( $value ) : 'N/A' );
    
    if ( ! empty( $value ) ) {
        $has_data = true;
    }
    
    $display_value = '';
    if ( is_array( $value ) ) {
        $display_value = '<pre>' . esc_html( print_r( $value, true ) ) . '</pre>';
    } elseif ( is_string( $value ) ) {
        $display_value = esc_html( $value );
        if ( strlen( $value ) > 100 ) {
            $display_value = esc_html( substr( $value, 0, 100 ) ) . '... <em>(tronqué)</em>';
        }
    } else {
        $display_value = esc_html( (string) $value );
    }
    
    $row_class = empty( $value ) ? 'style="background-color: #ffeeee;"' : '';
    
    echo '<tr ' . $row_class . '>';
    echo '<td><code>' . esc_html( $meta_key ) . '</code></td>';
    echo '<td><strong>' . esc_html( $label ) . '</strong></td>';
    echo '<td>' . $display_value . '</td>';
    echo '<td>' . esc_html( $type ) . '</td>';
    echo '<td>' . esc_html( $length ) . '</td>';
    echo '</tr>';
}

echo '</tbody></table>';

if ( ! $has_data ) {
    echo '<div class="notice notice-warning"><p><strong>⚠️ Aucune métadonnée Boss SEO trouvée pour ce post !</strong></p>';
    echo '<p>Cela signifie que :</p>';
    echo '<ul>';
    echo '<li>Le post n\'a jamais été optimisé avec Boss SEO</li>';
    echo '<li>L\'optimisation a échoué</li>';
    echo '<li>Les métadonnées ont été supprimées</li>';
    echo '</ul></div>';
} else {
    echo '<div class="notice notice-success"><p><strong>✅ Métadonnées Boss SEO trouvées !</strong></p></div>';
}

// Test de simulation JavaScript
echo '<h3>🧪 Test de Simulation JavaScript</h3>';
echo '<div id="test-keywords-container">';
echo '<p>Simulation du chargement des mots-clés :</p>';

$focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );
$secondary_keywords = get_post_meta( $post_id, '_boss_seo_secondary_keywords', true );

echo '<div style="background: #f0f0f0; padding: 15px; border: 1px solid #ccc; margin: 10px 0;">';
echo '<strong>Données récupérées :</strong><br>';
echo 'Focus Keyword: <code>' . esc_html( $focus_keyword ) . '</code><br>';
echo 'Secondary Keywords: <code>' . esc_html( $secondary_keywords ) . '</code><br>';

if ( $secondary_keywords ) {
    $keywords_array = explode( ',', $secondary_keywords );
    echo '<br><strong>Mots-clés secondaires séparés :</strong><br>';
    foreach ( $keywords_array as $i => $keyword ) {
        echo ($i + 1) . '. "<code>' . esc_html( trim( $keyword ) ) . '</code>"<br>';
    }
}
echo '</div>';

echo '<div id="simulated-tags" style="background: #e7f3ff; padding: 15px; border: 1px solid #0073aa; margin: 10px 0;">';
echo '<strong>🏷️ Tags simulés (comme dans l\'interface) :</strong><br>';

if ( $focus_keyword ) {
    echo '<span style="background: #0073aa; color: white; padding: 5px 10px; margin: 3px; border-radius: 3px; display: inline-block;">';
    echo '⭐ ' . esc_html( $focus_keyword ) . ' <span style="cursor: pointer;">✖</span>';
    echo '</span>';
}

if ( $secondary_keywords ) {
    $keywords_array = explode( ',', $secondary_keywords );
    foreach ( $keywords_array as $keyword ) {
        $keyword = trim( $keyword );
        if ( $keyword ) {
            echo '<span style="background: #666; color: white; padding: 5px 10px; margin: 3px; border-radius: 3px; display: inline-block;">';
            echo esc_html( $keyword ) . ' <span style="cursor: pointer;">✖</span>';
            echo '</span>';
        }
    }
}

if ( ! $focus_keyword && ! $secondary_keywords ) {
    echo '<em style="color: #666;">Aucun mot-clé défini</em>';
}

echo '</div>';
echo '</div>';

// Actions de test
echo '<h3>🔧 Actions de Test</h3>';
echo '<p>';
echo '<a href="' . admin_url( 'post.php?post=' . $post_id . '&action=edit' ) . '" class="button button-primary">✏️ Éditer ce Post</a> ';
echo '<a href="' . admin_url( 'admin.php?page=debug-metadata&post_id=' . $post_id ) . '" class="button">🔄 Actualiser</a> ';
echo '</p>';

echo '</div>';

// CSS pour améliorer l'affichage
echo '<style>
.wp-list-table td { vertical-align: top; }
.wp-list-table code { background: #f1f1f1; padding: 2px 4px; border-radius: 2px; }
</style>';
?>
