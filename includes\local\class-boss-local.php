<?php
/**
 * Classe principale pour le module SEO Local.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe principale pour le module SEO Local.
 *
 * Cette classe gère toutes les fonctionnalités du module SEO Local.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_local_';

    /**
     * Instance du tableau de bord local.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Dashboard    $dashboard    Instance du tableau de bord local.
     */
    protected $dashboard;

    /**
     * Instance de la gestion des emplacements.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Locations    $locations    Instance de la gestion des emplacements.
     */
    protected $locations;

    /**
     * Instance des informations d'entreprise.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Business_Info    $business_info    Instance des informations d'entreprise.
     */
    protected $business_info;

    /**
     * Instance du générateur de pages locales.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Page_Generator    $page_generator    Instance du générateur de pages locales.
     */
    protected $page_generator;

    /**
     * Instance des schémas structurés.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Schema    $schema    Instance des schémas structurés.
     */
    protected $schema;

    /**
     * Instance du suivi des positions locales.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Rankings    $rankings    Instance du suivi des positions locales.
     */
    protected $rankings;

    /**
     * Instance de l'analyse SEO locale.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Analyzer    $analyzer    Instance de l'analyse SEO locale.
     */
    protected $analyzer;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->load_dependencies();
        $this->init_modules();
    }

    /**
     * Charge les dépendances du module.
     *
     * @since    1.2.0
     */
    private function load_dependencies() {
        /**
         * La classe qui gère le tableau de bord local.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'local/class-boss-local-dashboard.php';

        /**
         * La classe qui gère les emplacements.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'local/class-boss-local-locations.php';

        /**
         * La classe qui gère les informations d'entreprise.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'local/class-boss-local-business-info.php';

        /**
         * La classe qui gère le générateur de pages locales.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'local/class-boss-local-page-generator.php';

        /**
         * La classe qui gère les schémas structurés.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'local/class-boss-local-schema.php';

        /**
         * La classe qui gère le suivi des positions locales.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'local/class-boss-local-rankings.php';

        /**
         * La classe qui gère l'analyse SEO locale.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'local/class-boss-local-analyzer.php';
    }

    /**
     * Initialise les modules.
     *
     * @since    1.2.0
     */
    private function init_modules() {
        $this->dashboard = new Boss_Local_Dashboard( $this->plugin_name, $this->version );
        $this->locations = new Boss_Local_Locations( $this->plugin_name, $this->version );
        $this->business_info = new Boss_Local_Business_Info( $this->plugin_name, $this->version );
        $this->page_generator = new Boss_Local_Page_Generator( $this->plugin_name, $this->version );
        $this->schema = new Boss_Local_Schema( $this->plugin_name, $this->version );
        $this->rankings = new Boss_Local_Rankings( $this->plugin_name, $this->version );
        $this->analyzer = new Boss_Local_Analyzer( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Enregistrer les hooks des modules
        $this->dashboard->register_hooks();
        $this->locations->register_hooks();
        $this->business_info->register_hooks();
        $this->page_generator->register_hooks();
        $this->schema->register_hooks();
        $this->rankings->register_hooks();
        $this->analyzer->register_hooks();

        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_local_settings', array( $this, 'ajax_get_local_settings' ) );
        add_action( 'wp_ajax_boss_seo_save_local_settings', array( $this, 'ajax_save_local_settings' ) );

        // Ajouter les actions pour l'interface d'administration
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );

        // Ajouter les actions pour l'initialisation
        add_action( 'init', array( $this, 'register_post_types' ) );
        add_action( 'init', array( $this, 'register_taxonomies' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Enregistrer les routes REST API des modules
        $this->dashboard->register_rest_routes();
        $this->locations->register_rest_routes();
        $this->business_info->register_rest_routes();
        $this->page_generator->register_rest_routes();
        $this->schema->register_rest_routes();
        $this->rankings->register_rest_routes();
        $this->analyzer->register_rest_routes();

        register_rest_route(
            'boss-seo/v1',
            '/local/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_local_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_local_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Enregistre les types de publication personnalisés.
     *
     * @since    1.2.0
     */
    public function register_post_types() {
        // Enregistrer le type de publication pour les emplacements
        register_post_type( 'boss_local_location', array(
            'labels'              => array(
                'name'               => __( 'Emplacements', 'boss-seo' ),
                'singular_name'      => __( 'Emplacement', 'boss-seo' ),
                'menu_name'          => __( 'Emplacements', 'boss-seo' ),
                'name_admin_bar'     => __( 'Emplacement', 'boss-seo' ),
                'add_new'            => __( 'Ajouter', 'boss-seo' ),
                'add_new_item'       => __( 'Ajouter un emplacement', 'boss-seo' ),
                'new_item'           => __( 'Nouvel emplacement', 'boss-seo' ),
                'edit_item'          => __( 'Modifier l\'emplacement', 'boss-seo' ),
                'view_item'          => __( 'Voir l\'emplacement', 'boss-seo' ),
                'all_items'          => __( 'Tous les emplacements', 'boss-seo' ),
                'search_items'       => __( 'Rechercher des emplacements', 'boss-seo' ),
                'parent_item_colon'  => __( 'Emplacement parent :', 'boss-seo' ),
                'not_found'          => __( 'Aucun emplacement trouvé.', 'boss-seo' ),
                'not_found_in_trash' => __( 'Aucun emplacement trouvé dans la corbeille.', 'boss-seo' ),
            ),
            'public'              => true,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'show_ui'             => true,
            'show_in_menu'        => false,
            'query_var'           => true,
            'rewrite'             => array( 'slug' => 'emplacement' ),
            'capability_type'     => 'post',
            'has_archive'         => true,
            'hierarchical'        => false,
            'menu_position'       => null,
            'supports'            => array( 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'        => true,
        ) );

        // Enregistrer le type de publication pour les pages locales
        register_post_type( 'boss_local_page', array(
            'labels'              => array(
                'name'               => __( 'Pages locales', 'boss-seo' ),
                'singular_name'      => __( 'Page locale', 'boss-seo' ),
                'menu_name'          => __( 'Pages locales', 'boss-seo' ),
                'name_admin_bar'     => __( 'Page locale', 'boss-seo' ),
                'add_new'            => __( 'Ajouter', 'boss-seo' ),
                'add_new_item'       => __( 'Ajouter une page locale', 'boss-seo' ),
                'new_item'           => __( 'Nouvelle page locale', 'boss-seo' ),
                'edit_item'          => __( 'Modifier la page locale', 'boss-seo' ),
                'view_item'          => __( 'Voir la page locale', 'boss-seo' ),
                'all_items'          => __( 'Toutes les pages locales', 'boss-seo' ),
                'search_items'       => __( 'Rechercher des pages locales', 'boss-seo' ),
                'parent_item_colon'  => __( 'Page locale parente :', 'boss-seo' ),
                'not_found'          => __( 'Aucune page locale trouvée.', 'boss-seo' ),
                'not_found_in_trash' => __( 'Aucune page locale trouvée dans la corbeille.', 'boss-seo' ),
            ),
            'public'              => true,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'show_ui'             => true,
            'show_in_menu'        => false,
            'query_var'           => true,
            'rewrite'             => array( 'slug' => 'page-locale' ),
            'capability_type'     => 'post',
            'has_archive'         => true,
            'hierarchical'        => false,
            'menu_position'       => null,
            'supports'            => array( 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'        => true,
        ) );
    }

    /**
     * Enregistre les taxonomies personnalisées.
     *
     * @since    1.2.0
     */
    public function register_taxonomies() {
        // Enregistrer la taxonomie pour les types d'emplacements
        register_taxonomy( 'boss_local_location_type', 'boss_local_location', array(
            'labels'            => array(
                'name'              => __( 'Types d\'emplacements', 'boss-seo' ),
                'singular_name'     => __( 'Type d\'emplacement', 'boss-seo' ),
                'search_items'      => __( 'Rechercher des types d\'emplacements', 'boss-seo' ),
                'all_items'         => __( 'Tous les types d\'emplacements', 'boss-seo' ),
                'parent_item'       => __( 'Type d\'emplacement parent', 'boss-seo' ),
                'parent_item_colon' => __( 'Type d\'emplacement parent :', 'boss-seo' ),
                'edit_item'         => __( 'Modifier le type d\'emplacement', 'boss-seo' ),
                'update_item'       => __( 'Mettre à jour le type d\'emplacement', 'boss-seo' ),
                'add_new_item'      => __( 'Ajouter un type d\'emplacement', 'boss-seo' ),
                'new_item_name'     => __( 'Nouveau type d\'emplacement', 'boss-seo' ),
                'menu_name'         => __( 'Types d\'emplacements', 'boss-seo' ),
            ),
            'hierarchical'      => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'query_var'         => true,
            'rewrite'           => array( 'slug' => 'type-emplacement' ),
            'show_in_rest'      => true,
        ) );
    }

    /**
     * Ajoute les éléments de menu pour l'administration.
     *
     * @since    1.2.0
     */
    public function add_admin_menu() {
        // Ajouter le menu principal
        add_submenu_page(
            'boss-seo',
            __( 'SEO Local', 'boss-seo' ),
            __( 'SEO Local', 'boss-seo' ),
            'manage_options',
            'boss-seo-local',
            array( $this, 'render_admin_page' )
        );
    }

    /**
     * Enregistre les scripts et styles pour l'administration.
     *
     * @since    1.2.0
     * @param    string    $hook_suffix    Le suffixe du hook.
     */
    public function enqueue_admin_scripts( $hook_suffix ) {
        // Vérifier si nous sommes sur la page d'administration du module
        if ( 'boss-seo_page_boss-seo-local' !== $hook_suffix ) {
            return;
        }

        // Enregistrer les styles
        wp_enqueue_style( 'boss-seo-local', plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/css/boss-seo-local.css', array(), $this->version, 'all' );

        // Enregistrer les scripts
        wp_enqueue_script( 'boss-seo-local', plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/js/boss-seo-local.js', array( 'jquery' ), $this->version, false );

        // Localiser le script
        wp_localize_script( 'boss-seo-local', 'boss_seo_local', array(
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'nonce'    => wp_create_nonce( 'boss_seo_local_nonce' ),
        ) );

        // Enregistrer les scripts pour la carte
        wp_enqueue_script( 'google-maps', 'https://maps.googleapis.com/maps/api/js?key=' . $this->get_google_maps_api_key() . '&libraries=places', array(), null, true );
    }

    /**
     * Affiche la page d'administration.
     *
     * @since    1.2.0
     */
    public function render_admin_page() {
        include plugin_dir_path( dirname( dirname( __FILE__ ) ) ) . 'admin/partials/boss-seo-local-admin-display.php';
    }

    /**
     * Gère les requêtes AJAX pour récupérer les paramètres du module.
     *
     * @since    1.2.0
     */
    public function ajax_get_local_settings() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $settings = $this->get_settings();

        wp_send_json_success( array(
            'message'  => __( 'Paramètres récupérés avec succès.', 'boss-seo' ),
            'settings' => $settings,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour enregistrer les paramètres du module.
     *
     * @since    1.2.0
     */
    public function ajax_save_local_settings() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['settings'] ) || ! is_array( $_POST['settings'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Paramètres invalides.', 'boss-seo' ) ) );
        }

        $settings = $_POST['settings'];

        // Enregistrer les paramètres
        $result = $this->save_settings( $settings );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Paramètres enregistrés avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les paramètres du module via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_local_settings( $request ) {
        $settings = $this->get_settings();

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres du module via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_local_settings( $request ) {
        $params = $request->get_params();

        if ( ! isset( $params['settings'] ) || ! is_array( $params['settings'] ) ) {
            return new WP_Error( 'invalid_settings', __( 'Paramètres invalides.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $result = $this->save_settings( $params['settings'] );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Paramètres enregistrés avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les paramètres du module.
     *
     * @since    1.2.0
     * @return   array    Les paramètres.
     */
    private function get_settings() {
        $default_settings = array(
            'enable_dashboard'      => true,
            'enable_locations'      => true,
            'enable_business_info'  => true,
            'enable_page_generator' => true,
            'enable_schema'         => true,
            'enable_rankings'       => true,
            'enable_analyzer'       => true,
            'google_maps_api_key'   => '',
            'google_search_console_api_key' => '',
            'default_country'       => 'FR',
            'default_language'      => 'fr',
            'default_currency'      => 'EUR',
        );

        $settings = get_option( $this->option_prefix . 'settings', $default_settings );

        return $settings;
    }

    /**
     * Enregistre les paramètres du module.
     *
     * @since    1.2.0
     * @param    array     $settings    Les paramètres à enregistrer.
     * @return   bool|WP_Error          True en cas de succès, WP_Error en cas d'erreur.
     */
    private function save_settings( $settings ) {
        // Valider les paramètres
        $valid_settings = array();

        // Valider les paramètres booléens
        $valid_settings['enable_dashboard'] = isset( $settings['enable_dashboard'] ) && $settings['enable_dashboard'];
        $valid_settings['enable_locations'] = isset( $settings['enable_locations'] ) && $settings['enable_locations'];
        $valid_settings['enable_business_info'] = isset( $settings['enable_business_info'] ) && $settings['enable_business_info'];
        $valid_settings['enable_page_generator'] = isset( $settings['enable_page_generator'] ) && $settings['enable_page_generator'];
        $valid_settings['enable_schema'] = isset( $settings['enable_schema'] ) && $settings['enable_schema'];
        $valid_settings['enable_rankings'] = isset( $settings['enable_rankings'] ) && $settings['enable_rankings'];
        $valid_settings['enable_analyzer'] = isset( $settings['enable_analyzer'] ) && $settings['enable_analyzer'];

        // Valider les clés API
        if ( isset( $settings['google_maps_api_key'] ) ) {
            $valid_settings['google_maps_api_key'] = sanitize_text_field( $settings['google_maps_api_key'] );
        }

        if ( isset( $settings['google_search_console_api_key'] ) ) {
            $valid_settings['google_search_console_api_key'] = sanitize_text_field( $settings['google_search_console_api_key'] );
        }

        // Valider les paramètres de localisation
        if ( isset( $settings['default_country'] ) ) {
            $valid_settings['default_country'] = sanitize_text_field( $settings['default_country'] );
        }

        if ( isset( $settings['default_language'] ) ) {
            $valid_settings['default_language'] = sanitize_text_field( $settings['default_language'] );
        }

        if ( isset( $settings['default_currency'] ) ) {
            $valid_settings['default_currency'] = sanitize_text_field( $settings['default_currency'] );
        }

        // Enregistrer les paramètres
        $result = update_option( $this->option_prefix . 'settings', $valid_settings );

        if ( ! $result ) {
            return new WP_Error( 'save_failed', __( 'L\'enregistrement des paramètres a échoué.', 'boss-seo' ) );
        }

        return true;
    }

    /**
     * Récupère la clé API Google Maps.
     *
     * @since    1.2.0
     * @return   string    La clé API Google Maps.
     */
    private function get_google_maps_api_key() {
        $settings = $this->get_settings();

        return isset( $settings['google_maps_api_key'] ) ? $settings['google_maps_api_key'] : '';
    }
}
