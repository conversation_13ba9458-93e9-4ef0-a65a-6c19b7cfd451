<?php
/**
 * Classe pour gérer les emplacements locaux.
 *
 * Cette classe gère les emplacements locaux pour le module SEO local.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour gérer les emplacements locaux.
 *
 * Cette classe gère les emplacements locaux pour le module SEO local.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_Locations_Fix {

    /**
     * Récupère les emplacements avec gestion d'erreurs améliorée.
     *
     * @since    1.2.0
     * @param    int       $page       Le numéro de page.
     * @param    int       $per_page   Le nombre d'éléments par page.
     * @param    string    $search     La recherche.
     * @return   array                 Les emplacements.
     */
    public function get_locations_data( $page, $per_page, $search ) {
        try {
            // Vérifier si le type de post existe
            if (!post_type_exists('boss_local_location')) {
                // Retourner des données fictives pour le développement
                return $this->get_mock_locations_data( $page, $per_page );
            }

            // Paramètres de la requête
            $args = array(
                'post_type'      => 'boss_local_location',
                'posts_per_page' => $per_page,
                'paged'          => $page,
                'post_status'    => array( 'publish', 'draft' ),
            );

            // Ajouter la recherche
            if ( ! empty( $search ) ) {
                $args['s'] = $search;
            }

            // Exécuter la requête
            $query = new WP_Query( $args );

            // Préparer les résultats
            $locations = array();

            if ($query->have_posts()) {
                foreach ( $query->posts as $post ) {
                    $location_data = $this->get_location_data( $post->ID );
                    if ($location_data) {
                        $locations[] = $location_data;
                    }
                }
            }

            return array(
                'success'   => true,
                'locations' => $locations,
                'total'     => $query->found_posts,
                'pages'     => ceil( $query->found_posts / $per_page ),
                'current_page' => $page,
            );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Local Locations Error: ' . $e->getMessage() );

            // Retourner des données fictives en cas d'erreur
            return $this->get_mock_locations_data( $page, $per_page );
        }
    }

    /**
     * Récupère un emplacement avec gestion d'erreurs améliorée.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array|false               L'emplacement ou false si non trouvé.
     */
    public function get_location_data( $location_id ) {
        $post = get_post( $location_id );

        if ( ! $post || $post->post_type !== 'boss_local_location' ) {
            return false;
        }

        // Récupérer les métadonnées avec les bonnes clés (compatibilité avec la classe principale)
        $meta_fields = array(
            'address'       => '_boss_local_location_address',
            'city'          => '_boss_local_location_city',
            'state'         => '_boss_local_location_state',
            'postal_code'   => '_boss_local_location_postal_code',
            'country'       => '_boss_local_location_country',
            'phone'         => '_boss_local_location_phone',
            'email'         => '_boss_local_location_email',
            'website'       => '_boss_local_location_website',
            'latitude'      => '_boss_local_location_latitude',
            'longitude'     => '_boss_local_location_longitude',
        );

        $meta_data = array();
        foreach ( $meta_fields as $field => $meta_key ) {
            $value = get_post_meta( $location_id, $meta_key, true );

            // Fallback vers les anciennes clés si les nouvelles n'existent pas
            if ( empty( $value ) ) {
                $old_meta_key = '_boss_local_' . $field;
                $value = get_post_meta( $location_id, $old_meta_key, true );
            }

            $meta_data[ $field ] = $value;
        }

        // Récupérer les horaires
        $hours = get_post_meta( $location_id, '_boss_local_location_hours', true );
        if ( empty( $hours ) ) {
            $hours = get_post_meta( $location_id, '_boss_local_hours', true );
        }

        // Récupérer les horaires spéciaux
        $special_hours = get_post_meta( $location_id, '_boss_local_location_special_hours', true );

        // Récupérer les types d'emplacement
        $types = wp_get_post_terms( $location_id, 'boss_local_location_type', array( 'fields' => 'names' ) );
        if ( is_wp_error( $types ) || empty( $types ) ) {
            // Fallback vers les métadonnées
            $types = get_post_meta( $location_id, '_boss_local_types', true );
            if ( ! is_array( $types ) ) {
                $types = array();
            }
        }

        // Calculer un score SEO
        $seo_score = $this->calculate_seo_score( $post, $meta_data );

        // Préparer les données
        return array(
            'id'            => $location_id,
            'title'         => $post->post_title,
            'content'       => $post->post_content,
            'excerpt'       => $post->post_excerpt,
            'status'        => $post->post_status,
            'address'       => $meta_data['address'] ?: '',
            'city'          => $meta_data['city'] ?: '',
            'state'         => $meta_data['state'] ?: '',
            'postal_code'   => $meta_data['postal_code'] ?: '',
            'country'       => $meta_data['country'] ?: 'FR',
            'phone'         => $meta_data['phone'] ?: '',
            'email'         => $meta_data['email'] ?: '',
            'website'       => $meta_data['website'] ?: '',
            'latitude'      => (float) ( $meta_data['latitude'] ?: 0 ),
            'longitude'     => (float) ( $meta_data['longitude'] ?: 0 ),
            'hours'         => is_array( $hours ) ? $hours : array(),
            'special_hours' => is_array( $special_hours ) ? $special_hours : array(),
            'types'         => $types,
            'seo_score'     => $seo_score,
            'last_updated'  => $post->post_modified,
        );
    }

    /**
     * Calcule un score SEO pour un emplacement
     *
     * @param WP_Post $post Post de l'emplacement
     * @param array $meta_data Métadonnées de l'emplacement
     * @return int Score SEO sur 100
     */
    private function calculate_seo_score( $post, $meta_data ) {
        $score = 0;

        // Titre (20 points)
        if ( ! empty( $post->post_title ) && strlen( $post->post_title ) >= 10 ) {
            $score += 20;
        } elseif ( ! empty( $post->post_title ) ) {
            $score += 10;
        }

        // Description (15 points)
        if ( ! empty( $post->post_content ) && strlen( $post->post_content ) >= 100 ) {
            $score += 15;
        } elseif ( ! empty( $post->post_content ) ) {
            $score += 8;
        }

        // Adresse complète (25 points)
        $address_fields = array( 'address', 'city', 'postal_code' );
        $address_complete = 0;
        foreach ( $address_fields as $field ) {
            if ( ! empty( $meta_data[ $field ] ) ) {
                $address_complete++;
            }
        }
        $score += ( $address_complete / count( $address_fields ) ) * 25;

        // Informations de contact (20 points)
        $contact_score = 0;
        if ( ! empty( $meta_data['phone'] ) ) {
            $contact_score += 10;
        }
        if ( ! empty( $meta_data['email'] ) ) {
            $contact_score += 5;
        }
        if ( ! empty( $meta_data['website'] ) ) {
            $contact_score += 5;
        }
        $score += $contact_score;

        // Coordonnées GPS (10 points)
        if ( $meta_data['latitude'] != 0 && $meta_data['longitude'] != 0 ) {
            $score += 10;
        }

        // Horaires (10 points)
        $hours = get_post_meta( $post->ID, '_boss_local_location_hours', true );
        if ( is_array( $hours ) && ! empty( $hours ) ) {
            $score += 10;
        }

        return min( 100, max( 0, $score ) );
    }

    /**
     * Retourne des données fictives pour le développement
     *
     * @param int $page Numéro de page
     * @param int $per_page Nombre d'éléments par page
     * @return array Données fictives
     */
    private function get_mock_locations_data( $page = 1, $per_page = 10 ) {
        $mock_locations = array(
            array(
                'id'            => 1,
                'title'         => 'Restaurant Le Gourmet',
                'content'       => 'Restaurant français traditionnel au cœur de Paris.',
                'excerpt'       => 'Cuisine française authentique',
                'status'        => 'publish',
                'address'       => '123 Rue de la Paix',
                'city'          => 'Paris',
                'state'         => 'Île-de-France',
                'postal_code'   => '75001',
                'country'       => 'FR',
                'phone'         => '+33 1 42 86 87 88',
                'email'         => '<EMAIL>',
                'website'       => 'https://legourmet.fr',
                'latitude'      => 48.8566,
                'longitude'     => 2.3522,
                'hours'         => array(
                    'monday'    => array( 'open' => '12:00', 'close' => '14:00' ),
                    'tuesday'   => array( 'open' => '12:00', 'close' => '14:00' ),
                ),
                'special_hours' => array(),
                'types'         => array( 'Restaurant' ),
                'seo_score'     => 85,
                'last_updated'  => date( 'Y-m-d H:i:s' ),
            ),
            array(
                'id'            => 2,
                'title'         => 'Boutique Mode Élégance',
                'content'       => 'Boutique de mode féminine haut de gamme.',
                'excerpt'       => 'Mode féminine élégante',
                'status'        => 'publish',
                'address'       => '456 Avenue des Champs-Élysées',
                'city'          => 'Paris',
                'state'         => 'Île-de-France',
                'postal_code'   => '75008',
                'country'       => 'FR',
                'phone'         => '+33 1 45 62 33 44',
                'email'         => '<EMAIL>',
                'website'       => 'https://elegance.fr',
                'latitude'      => 48.8698,
                'longitude'     => 2.3076,
                'hours'         => array(
                    'monday'    => array( 'open' => '10:00', 'close' => '19:00' ),
                    'tuesday'   => array( 'open' => '10:00', 'close' => '19:00' ),
                ),
                'special_hours' => array(),
                'types'         => array( 'Boutique' ),
                'seo_score'     => 92,
                'last_updated'  => date( 'Y-m-d H:i:s' ),
            ),
        );

        // Simuler la pagination
        $start = ( $page - 1 ) * $per_page;
        $locations = array_slice( $mock_locations, $start, $per_page );

        return array(
            'success'      => true,
            'locations'    => $locations,
            'total'        => count( $mock_locations ),
            'pages'        => ceil( count( $mock_locations ) / $per_page ),
            'current_page' => $page,
            'is_mock'      => true,
        );
    }
}
