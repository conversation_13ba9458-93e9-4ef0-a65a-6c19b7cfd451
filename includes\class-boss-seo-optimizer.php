<?php
/**
 * Optimiseur SEO intelligent pour Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe d'optimisation SEO intelligente.
 *
 * Cette classe fournit toutes les méthodes d'optimisation automatique,
 * de validation et de génération de schémas.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Optimizer {

    /**
     * Instance du service IA.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_AI    $ai    Instance du service IA.
     */
    private $ai;

    /**
     * Instance des paramètres.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_Settings    $settings    Instance des paramètres.
     */
    private $settings;

    /**
     * Constructeur.
     *
     * @since    1.2.0
     */
    public function __construct() {
        // Charger les dépendances
        if ( ! class_exists( 'Boss_Optimizer_AI' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-ai.php';
        }
        if ( ! class_exists( 'Boss_Optimizer_Settings' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-settings.php';
        }

        $this->settings = new Boss_Optimizer_Settings( 'boss-seo' );
        $this->ai = new Boss_Optimizer_AI( $this->settings );
    }

    /**
     * Effectue l'optimisation automatique du contenu avec IA.
     *
     * @since    1.2.0
     * @param    int       $post_id    ID du post.
     * @param    string    $content    Contenu à optimiser.
     * @param    array     $keywords   Mots-clés à utiliser.
     * @return   array                 Suggestions d'optimisation.
     */
    public function perform_content_optimization( $post_id, $content, $keywords ) {
        $optimizations = array();

        $primary_keyword = ! empty( $keywords ) ? $keywords[0] : '';

        // Optimiser le titre
        $title_optimization = $this->optimize_title( $post_id, $primary_keyword );
        if ( $title_optimization ) {
            $optimizations['title'] = $title_optimization;
        }

        // Optimiser la meta description
        $desc_optimization = $this->optimize_meta_description( $post_id, $content, $primary_keyword );
        if ( $desc_optimization ) {
            $optimizations['meta_description'] = $desc_optimization;
        }

        // Optimiser l'URL (slug)
        $slug_optimization = $this->optimize_slug( $post_id, $primary_keyword );
        if ( $slug_optimization ) {
            $optimizations['slug'] = $slug_optimization;
        }

        // Suggestions pour le contenu
        $content_suggestions = $this->generate_content_suggestions( $content, $keywords );
        if ( ! empty( $content_suggestions ) ) {
            $optimizations['content_suggestions'] = $content_suggestions;
        }

        // Suggestions pour les images
        $image_suggestions = $this->generate_image_suggestions( $post_id, $primary_keyword );
        if ( ! empty( $image_suggestions ) ) {
            $optimizations['image_suggestions'] = $image_suggestions;
        }

        return array(
            'optimizations' => $optimizations,
            'primary_keyword' => $primary_keyword,
            'timestamp' => current_time( 'mysql' )
        );
    }

    /**
     * Optimise le titre SEO avec IA.
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   string|false               Titre optimisé ou false.
     */
    private function optimize_title( $post_id, $primary_keyword ) {
        $current_title = get_post_meta( $post_id, '_boss_seo_title', true );
        $post_title = get_the_title( $post_id );

        $title_to_optimize = ! empty( $current_title ) ? $current_title : $post_title;

        if ( empty( $primary_keyword ) ) {
            return false;
        }

        // Vérifier si le mot-clé est déjà présent
        if ( stripos( $title_to_optimize, $primary_keyword ) !== false ) {
            return false; // Déjà optimisé
        }

        // Vérifier si l'IA est configurée et activée pour les titres
        if ( $this->settings->is_ai_configured() && $this->settings->get( 'ai', 'use_ai_for_titles', true ) ) {
            return $this->generate_ai_optimized_title( $title_to_optimize, $primary_keyword, $post_id );
        }

        // Fallback : génération avec templates
        return $this->generate_template_title( $title_to_optimize, $primary_keyword );
    }

    /**
     * Génère un titre optimisé avec IA.
     *
     * @since    1.2.0
     * @param    string    $original_title  Titre original.
     * @param    string    $keyword         Mot-clé principal.
     * @param    int       $post_id         ID du post.
     * @return   string|false               Titre optimisé ou false.
     */
    private function generate_ai_optimized_title( $original_title, $keyword, $post_id ) {
        // Récupérer le contenu pour le contexte
        $post = get_post( $post_id );
        $content_excerpt = wp_trim_words( wp_strip_all_tags( $post->post_content ), 30 );

        // Construire le prompt pour l'IA
        $prompt = sprintf(
            'Optimise ce titre pour le SEO en incluant le mot-clé principal :

Titre actuel : "%s"
Mot-clé principal : "%s"
Contexte du contenu : "%s"

Critères d\'optimisation :
- Inclure le mot-clé principal naturellement
- Maximum 60 caractères
- Attractif et engageant
- Optimisé pour les moteurs de recherche
- En français

Génère UN SEUL titre optimisé (sans guillemets) :',
            $original_title,
            $keyword,
            $content_excerpt
        );

        // Générer avec l'IA
        $ai_result = $this->ai->generate_content( $prompt, array(
            'temperature' => 0.7,
            'max_tokens' => 100
        ) );

        if ( $ai_result['success'] && ! empty( $ai_result['content'] ) ) {
            // Nettoyer le titre généré
            $optimized_title = trim( $ai_result['content'] );
            $optimized_title = trim( $optimized_title, '"\'' );

            // Limiter à 60 caractères
            if ( mb_strlen( $optimized_title ) > 60 ) {
                $optimized_title = mb_substr( $optimized_title, 0, 57 ) . '...';
            }

            return $optimized_title;
        }

        // Fallback si l'IA échoue
        return $this->generate_template_title( $original_title, $keyword );
    }

    /**
     * Génère un titre optimisé avec templates (fallback).
     *
     * @since    1.2.0
     * @param    string    $original_title  Titre original.
     * @param    string    $keyword         Mot-clé à intégrer.
     * @return   string                     Titre optimisé.
     */
    private function generate_template_title( $original_title, $keyword ) {
        $templates = array(
            '%keyword% : %title%',
            '%title% - %keyword%',
            '%keyword% | %title%',
            'Guide %keyword% : %title%',
            '%title% (%keyword%)',
        );

        $template = $templates[ array_rand( $templates ) ];
        $optimized = str_replace(
            array( '%keyword%', '%title%' ),
            array( ucfirst( $keyword ), $original_title ),
            $template
        );

        // Limiter à 60 caractères
        if ( mb_strlen( $optimized ) > 60 ) {
            $optimized = mb_substr( $optimized, 0, 57 ) . '...';
        }

        return $optimized;
    }

    /**
     * Optimise la meta description avec IA.
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $content         Contenu du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   string|false               Meta description optimisée ou false.
     */
    private function optimize_meta_description( $post_id, $content, $primary_keyword ) {
        $current_desc = get_post_meta( $post_id, '_boss_seo_meta_description', true );

        if ( ! empty( $current_desc ) && stripos( $current_desc, $primary_keyword ) !== false ) {
            return false; // Déjà optimisée
        }

        if ( empty( $primary_keyword ) ) {
            return false;
        }

        // Vérifier si l'IA est configurée et activée pour les descriptions
        if ( $this->settings->is_ai_configured() && $this->settings->get( 'ai', 'use_ai_for_descriptions', true ) ) {
            return $this->generate_ai_meta_description( $post_id, $content, $primary_keyword );
        }

        // Fallback : génération avec templates
        return $this->generate_template_meta_description( $content, $primary_keyword );
    }

    /**
     * Génère une meta description optimisée avec IA.
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $content         Contenu du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   string|false               Meta description optimisée ou false.
     */
    private function generate_ai_meta_description( $post_id, $content, $primary_keyword ) {
        // Récupérer le titre et un extrait du contenu
        $title = get_the_title( $post_id );
        $clean_content = wp_strip_all_tags( $content );
        $excerpt = wp_trim_words( $clean_content, 50 );

        // Construire le prompt pour l'IA
        $prompt = sprintf(
            'Rédige une meta description SEO optimisée pour cette page :

Titre : "%s"
Mot-clé principal : "%s"
Extrait du contenu : "%s"

Critères :
- Inclure le mot-clé principal naturellement
- Entre 120 et 160 caractères
- Inciter au clic (call-to-action)
- Résumer le contenu de façon attrayante
- En français
- Sans guillemets

Meta description :',
            $title,
            $primary_keyword,
            $excerpt
        );

        // Générer avec l'IA
        $ai_result = $this->ai->generate_content( $prompt, array(
            'temperature' => 0.8,
            'max_tokens' => 150
        ) );

        if ( $ai_result['success'] && ! empty( $ai_result['content'] ) ) {
            // Nettoyer la description générée
            $optimized_desc = trim( $ai_result['content'] );
            $optimized_desc = trim( $optimized_desc, '"\'' );

            // Limiter à 160 caractères
            if ( mb_strlen( $optimized_desc ) > 160 ) {
                $optimized_desc = mb_substr( $optimized_desc, 0, 157 ) . '...';
            }

            return $optimized_desc;
        }

        // Fallback si l'IA échoue
        return $this->generate_template_meta_description( $content, $primary_keyword );
    }

    /**
     * Génère une meta description avec templates (fallback).
     *
     * @since    1.2.0
     * @param    string    $content         Contenu du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   string                     Meta description optimisée.
     */
    private function generate_template_meta_description( $content, $primary_keyword ) {
        // Extraire un extrait du contenu
        $clean_content = wp_strip_all_tags( $content );
        $excerpt = wp_trim_words( $clean_content, 20 );

        // Générer une meta description optimisée
        $templates = array(
            'Découvrez tout sur %keyword%. %excerpt%',
            'Guide complet sur %keyword% : %excerpt%',
            '%excerpt% Apprenez-en plus sur %keyword%.',
            'Tout ce que vous devez savoir sur %keyword%. %excerpt%',
        );

        $template = $templates[ array_rand( $templates ) ];
        $optimized = str_replace(
            array( '%keyword%', '%excerpt%' ),
            array( $primary_keyword, $excerpt ),
            $template
        );

        // Limiter à 160 caractères
        if ( mb_strlen( $optimized ) > 160 ) {
            $optimized = mb_substr( $optimized, 0, 157 ) . '...';
        }

        return $optimized;
    }

    /**
     * Optimise le slug (URL).
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   string|false               Slug optimisé ou false.
     */
    private function optimize_slug( $post_id, $primary_keyword ) {
        if ( empty( $primary_keyword ) ) {
            return false;
        }

        $post = get_post( $post_id );
        $current_slug = $post->post_name;

        // Vérifier si le mot-clé est déjà dans le slug
        if ( strpos( $current_slug, sanitize_title( $primary_keyword ) ) !== false ) {
            return false; // Déjà optimisé
        }

        // Générer un slug optimisé
        $keyword_slug = sanitize_title( $primary_keyword );
        $optimized_slug = $keyword_slug . '-' . $current_slug;

        // Vérifier l'unicité
        $existing = get_page_by_path( $optimized_slug, OBJECT, $post->post_type );
        if ( $existing && $existing->ID !== $post_id ) {
            $optimized_slug = $keyword_slug . '-' . $current_slug . '-' . $post_id;
        }

        return $optimized_slug;
    }

    /**
     * Génère des suggestions pour le contenu.
     *
     * @since    1.2.0
     * @param    string    $content    Contenu actuel.
     * @param    array     $keywords   Mots-clés.
     * @return   array                 Suggestions de contenu.
     */
    private function generate_content_suggestions( $content, $keywords ) {
        $suggestions = array();

        if ( empty( $keywords ) ) {
            return $suggestions;
        }

        $primary_keyword = $keywords[0];
        $clean_content = wp_strip_all_tags( $content );
        $word_count = str_word_count( $clean_content );

        // Suggestions basées sur la longueur
        if ( $word_count < 300 ) {
            $suggestions[] = array(
                'type' => 'content_length',
                'text' => 'Développez votre contenu pour atteindre au moins 300 mots.',
                'action' => 'expand_content'
            );
        }

        // Suggestions pour les mots-clés
        $keyword_density = $word_count > 0 ? ( substr_count( strtolower( $clean_content ), strtolower( $primary_keyword ) ) / $word_count ) * 100 : 0;

        if ( $keyword_density < 0.5 ) {
            $suggestions[] = array(
                'type' => 'keyword_density',
                'text' => sprintf( 'Utilisez plus souvent le mot-clé "%s" dans votre contenu.', $primary_keyword ),
                'action' => 'increase_keyword_density'
            );
        }

        // Suggestions pour la structure
        if ( strpos( $content, '<h2' ) === false && $word_count > 500 ) {
            $suggestions[] = array(
                'type' => 'structure',
                'text' => 'Ajoutez des sous-titres H2 pour améliorer la structure.',
                'action' => 'add_headings'
            );
        }

        return $suggestions;
    }

    /**
     * Génère des suggestions pour les images.
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   array                      Suggestions d'images.
     */
    private function generate_image_suggestions( $post_id, $primary_keyword ) {
        $suggestions = array();

        // Récupérer les images attachées
        $images = get_attached_media( 'image', $post_id );

        if ( empty( $images ) ) {
            $suggestions[] = array(
                'type' => 'add_images',
                'text' => 'Ajoutez des images pour enrichir votre contenu.',
                'action' => 'add_images'
            );
        } else {
            // Vérifier les attributs alt
            foreach ( $images as $image ) {
                $alt_text = get_post_meta( $image->ID, '_wp_attachment_image_alt', true );
                if ( empty( $alt_text ) ) {
                    $suggestions[] = array(
                        'type' => 'image_alt',
                        'text' => sprintf( 'Ajoutez un attribut alt à l\'image "%s".', $image->post_title ),
                        'action' => 'add_alt_text',
                        'image_id' => $image->ID
                    );
                } elseif ( ! empty( $primary_keyword ) && stripos( $alt_text, $primary_keyword ) === false ) {
                    $suggestions[] = array(
                        'type' => 'image_alt_keyword',
                        'text' => sprintf( 'Incluez le mot-clé "%s" dans l\'attribut alt de l\'image "%s".', $primary_keyword, $image->post_title ),
                        'action' => 'optimize_alt_text',
                        'image_id' => $image->ID
                    );
                }
            }
        }

        return $suggestions;
    }

    /**
     * Valide les métadonnées.
     *
     * @since    1.2.0
     * @param    array    $metadata    Métadonnées à valider.
     * @return   array                 Métadonnées validées.
     */
    public function validate_metadata_fields( $metadata ) {
        $validated = array();

        $fields = array(
            'boss_seo_title' => 'sanitize_text_field',
            'boss_seo_meta_description' => 'sanitize_textarea_field',
            'boss_seo_focus_keyword' => 'sanitize_text_field',
            'boss_seo_secondary_keywords' => 'sanitize_text_field',
            'boss_seo_canonical_url' => 'esc_url_raw',
            'boss_seo_robots_index' => array( $this, 'validate_robots_index' ),
            'boss_seo_robots_follow' => array( $this, 'validate_robots_follow' ),
            'boss_seo_og_title' => 'sanitize_text_field',
            'boss_seo_og_description' => 'sanitize_textarea_field',
            'boss_seo_og_image' => 'esc_url_raw',
            'boss_seo_twitter_card_type' => array( $this, 'validate_twitter_card_type' ),
            'boss_seo_twitter_title' => 'sanitize_text_field',
            'boss_seo_twitter_description' => 'sanitize_textarea_field',
            'boss_seo_twitter_image' => 'esc_url_raw',
        );

        foreach ( $fields as $field => $sanitizer ) {
            if ( isset( $metadata[ $field ] ) ) {
                if ( is_callable( $sanitizer ) ) {
                    $validated[ $field ] = call_user_func( $sanitizer, $metadata[ $field ] );
                } else {
                    $validated[ $field ] = $metadata[ $field ];
                }
            }
        }

        return $validated;
    }

    /**
     * Valide la directive robots index.
     *
     * @since    1.2.0
     * @param    string    $value    Valeur à valider.
     * @return   string              Valeur validée.
     */
    public function validate_robots_index( $value ) {
        return in_array( $value, array( 'index', 'noindex' ) ) ? $value : 'index';
    }

    /**
     * Valide la directive robots follow.
     *
     * @since    1.2.0
     * @param    string    $value    Valeur à valider.
     * @return   string              Valeur validée.
     */
    public function validate_robots_follow( $value ) {
        return in_array( $value, array( 'follow', 'nofollow' ) ) ? $value : 'follow';
    }

    /**
     * Valide le type de carte Twitter.
     *
     * @since    1.2.0
     * @param    string    $value    Valeur à valider.
     * @return   string              Valeur validée.
     */
    public function validate_twitter_card_type( $value ) {
        return in_array( $value, array( 'summary', 'summary_large_image' ) ) ? $value : 'summary_large_image';
    }

    /**
     * Valide un champ individuel.
     *
     * @since    1.2.0
     * @param    string    $field_name     Nom du champ.
     * @param    string    $field_value    Valeur du champ.
     * @return   array                     Résultat de la validation.
     */
    public function validate_single_field( $field_name, $field_value ) {
        $result = array(
            'valid' => true,
            'message' => '',
            'suggestions' => array()
        );

        switch ( $field_name ) {
            case 'boss_seo_title':
                $length = mb_strlen( $field_value );
                if ( $length > 60 ) {
                    $result['valid'] = false;
                    $result['message'] = 'Titre trop long (max 60 caractères)';
                } elseif ( $length < 30 && $length > 0 ) {
                    $result['message'] = 'Titre court, idéalement 50-60 caractères';
                }
                break;

            case 'boss_seo_meta_description':
                $length = mb_strlen( $field_value );
                if ( $length > 160 ) {
                    $result['valid'] = false;
                    $result['message'] = 'Description trop longue (max 160 caractères)';
                } elseif ( $length < 120 && $length > 0 ) {
                    $result['message'] = 'Description courte, idéalement 120-160 caractères';
                }
                break;

            case 'boss_seo_canonical_url':
                if ( ! empty( $field_value ) && ! filter_var( $field_value, FILTER_VALIDATE_URL ) ) {
                    $result['valid'] = false;
                    $result['message'] = 'URL invalide';
                }
                break;
        }

        return $result;
    }
}
