/**
 * Service pour le module E-commerce
 *
 * Gère les communications avec l'API pour les fonctionnalités e-commerce
 */

import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

class EcommerceService {
  constructor() {
    // Configuration de base pour apiFetch
    this.apiNamespace = 'boss-seo/v1/ecommerce';
  }

  /**
   * Gère les erreurs API de manière centralisée.
   *
   * @param {Error} error - L'erreur à traiter
   * @returns {Object} - Objet d'erreur formaté
   */
  handleApiError(error) {
    let errorMessage = 'Une erreur inattendue s\'est produite.';
    let errorCode = 'unknown_error';

    if (error.code) {
      switch (error.code) {
        case 'woocommerce_not_available':
          errorMessage = 'WooCommerce n\'est pas installé ou activé.';
          errorCode = 'woocommerce_missing';
          break;
        case 'no_products_available':
          errorMessage = 'Aucun produit n\'est disponible dans WooCommerce.';
          errorCode = 'no_products';
          break;
        case 'product_not_found':
          errorMessage = 'Produit non trouvé.';
          errorCode = 'product_not_found';
          break;
        case 'ai_not_available':
          errorMessage = 'Les services d\'IA ne sont pas configurés.';
          errorCode = 'ai_not_configured';
          break;
        case 'rest_forbidden':
          errorMessage = 'Vous n\'avez pas les permissions nécessaires.';
          errorCode = 'forbidden';
          break;
        default:
          errorMessage = error.message || errorMessage;
          errorCode = error.code;
      }
    } else if (error.message) {
      errorMessage = error.message;
    }

    console.error('Erreur API E-commerce:', error);

    return {
      message: errorMessage,
      code: errorCode,
      originalError: error
    };
  }

  /**
   * Effectue une requête API avec gestion d'erreurs.
   *
   * @param {string} path - Chemin de l'API
   * @param {Object} options - Options pour apiFetch
   * @returns {Promise} - Promesse avec gestion d'erreurs
   */
  async apiRequest(path, options = {}) {
    try {
      const response = await apiFetch({
        path: `${this.apiNamespace}${path}`,
        ...options
      });
      return response;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }
  /**
   * Récupère les données du tableau de bord e-commerce
   *
   * @returns {Promise} Promesse contenant les données du tableau de bord
   */
  async getDashboardData() {
    return this.apiRequest('/dashboard');
  }

  /**
   * Récupère les statistiques du tableau de bord e-commerce
   *
   * @param {string} period - Période (7days, 30days, 90days, 1year)
   * @returns {Promise} Promesse contenant les statistiques du tableau de bord
   */
  async getDashboardStats(period = '30days') {
    return this.apiRequest(addQueryArgs('/dashboard/stats', { period }));
  }

  /**
   * Récupère les produits les plus performants
   *
   * @param {string} period - Période (7days, 30days, 90days, 1year)
   * @returns {Promise} Promesse contenant les produits les plus performants
   */
  async getTopProducts(period = '30days') {
    return this.apiRequest(addQueryArgs('/dashboard/top-products', { period }));
  }

  /**
   * Récupère les catégories les plus performantes
   *
   * @param {string} period - Période (7days, 30days, 90days, 1year)
   * @returns {Promise} Promesse contenant les catégories les plus performantes
   */
  async getTopCategories(period = '30days') {
    return this.apiRequest(addQueryArgs('/dashboard/top-categories', { period }));
  }

  /**
   * Récupère la liste des produits
   *
   * @param {Object} filters - Filtres pour la requête
   * @returns {Promise} Promesse contenant la liste des produits ou une erreur
   */
  async getProducts(filters = {}) {
    return this.apiRequest(addQueryArgs('/products', filters));
  }

  /**
   * Récupère un produit spécifique
   *
   * @param {number} id - ID du produit
   * @returns {Promise} Promesse contenant les détails du produit
   */
  async getProduct(id) {
    return this.apiRequest(`/products/${id}`);
  }

  /**
   * Met à jour un produit
   *
   * @param {number} id - ID du produit
   * @param {Object} productData - Données du produit
   * @returns {Promise} Promesse contenant les détails du produit mis à jour
   */
  async updateProduct(id, productData) {
    return this.apiRequest(`/products/${id}`, {
      method: 'POST',
      data: { product: productData }
    });
  }

  /**
   * Optimise un produit
   *
   * @param {number} id - ID du produit
   * @param {Object} optimizationSettings - Paramètres d'optimisation
   * @returns {Promise} Promesse contenant les détails du produit optimisé
   */
  async optimizeProduct(id, optimizationSettings) {
    return this.apiRequest('/optimize-product', {
      method: 'POST',
      data: {
        product_id: id,
        settings: optimizationSettings
      }
    });
  }

  /**
   * Optimise plusieurs produits en masse
   *
   * @param {Array} ids - IDs des produits
   * @param {Object} optimizationSettings - Paramètres d'optimisation
   * @returns {Promise} Promesse contenant les détails des produits optimisés
   */
  async bulkOptimizeProducts(ids, optimizationSettings) {
    return this.apiRequest('/bulk-optimize-products', {
      method: 'POST',
      data: {
        product_ids: ids,
        settings: optimizationSettings
      }
    });
  }

  /**
   * Analyse un produit
   *
   * @param {number} id - ID du produit
   * @returns {Promise} Promesse contenant les résultats de l'analyse
   */
  async analyzeProduct(id) {
    return this.apiRequest('/analyze-product', {
      method: 'POST',
      data: { product_id: id }
    });
  }

  /**
   * Récupère l'analyse d'un produit
   *
   * @param {number} id - ID du produit
   * @returns {Promise} Promesse contenant l'analyse du produit
   */
  async getProductAnalysis(id) {
    return this.apiRequest(`/product-analysis/${id}`);
  }

  /**
   * Génère une description de produit avec l'IA
   *
   * @param {number} id - ID du produit
   * @param {Object} generationSettings - Paramètres de génération
   * @returns {Promise} Promesse contenant la description générée
   */
  async generateProductDescription(id, generationSettings) {
    return this.apiRequest('/generate-description', {
      method: 'POST',
      data: {
        product_id: id,
        settings: generationSettings
      }
    });
  }

  /**
   * Applique la description générée à un produit
   *
   * @param {number} id - ID du produit
   * @param {Object} content - Contenu généré (titre, description, etc.)
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async applyGeneratedContent(id, content) {
    try {
      const path = '/boss-seo/v1/ecommerce/apply-generated-content';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          product_id: id,
          content
        }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'application du contenu généré au produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Génère un schéma pour un produit
   *
   * @param {number} id - ID du produit
   * @param {Object} schemaSettings - Paramètres du schéma
   * @returns {Promise} Promesse contenant le schéma généré
   */
  async generateProductSchema(id, schemaSettings) {
    try {
      const path = '/boss-seo/v1/ecommerce/generate-schema';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          product_id: id,
          settings: schemaSettings
        }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la génération du schéma pour le produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Enregistre un schéma pour un produit
   *
   * @param {number} id - ID du produit
   * @param {Object} schema - Schéma à enregistrer
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async saveProductSchema(id, schema) {
    try {
      const path = '/boss-seo/v1/ecommerce/save-schema';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          product_id: id,
          schema
        }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'enregistrement du schéma pour le produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les flux Google Shopping
   *
   * @returns {Promise} Promesse contenant la liste des flux
   */
  async getShoppingFeeds() {
    try {
      const path = '/boss-seo/v1/ecommerce/shopping-feeds';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des flux Google Shopping:', error);
      throw error;
    }
  }

  /**
   * Crée un nouveau flux Google Shopping
   *
   * @param {Object} feedData - Données du flux
   * @returns {Promise} Promesse contenant les détails du flux créé
   */
  async createShoppingFeed(feedData) {
    try {
      const path = '/boss-seo/v1/ecommerce/shopping-feeds';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { feed: feedData }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la création du flux Google Shopping:', error);
      throw error;
    }
  }

  /**
   * Met à jour un flux Google Shopping
   *
   * @param {number} id - ID du flux
   * @param {Object} feedData - Données du flux
   * @returns {Promise} Promesse contenant les détails du flux mis à jour
   */
  async updateShoppingFeed(id, feedData) {
    try {
      const path = `/boss-seo/v1/ecommerce/shopping-feeds/${id}`;
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { feed: feedData }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du flux Google Shopping ${id}:`, error);
      throw error;
    }
  }

  /**
   * Supprime un flux Google Shopping
   *
   * @param {number} id - ID du flux
   * @returns {Promise} Promesse contenant le résultat de la suppression
   */
  async deleteShoppingFeed(id) {
    try {
      const path = `/boss-seo/v1/ecommerce/shopping-feeds/${id}`;
      const response = await apiFetch({
        path,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la suppression du flux Google Shopping ${id}:`, error);
      throw error;
    }
  }

  /**
   * Génère un flux Google Shopping
   *
   * @param {number} id - ID du flux
   * @returns {Promise} Promesse contenant le résultat de la génération
   */
  async generateShoppingFeed(id) {
    try {
      const path = '/boss-seo/v1/ecommerce/generate-shopping-feed';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { feed_id: id }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la génération du flux Google Shopping ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres du module e-commerce
   *
   * @returns {Promise} Promesse contenant les paramètres du module
   */
  async getEcommerceSettings() {
    try {
      const path = '/boss-seo/v1/ecommerce/settings';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres du module e-commerce:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres du module e-commerce
   *
   * @param {Object} settings - Paramètres du module
   * @returns {Promise} Promesse contenant le résultat de l'enregistrement
   */
  async saveEcommerceSettings(settings) {
    try {
      const path = '/boss-seo/v1/ecommerce/settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: settings
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres du module e-commerce:', error);
      throw error;
    }
  }
}

export default EcommerceService;
