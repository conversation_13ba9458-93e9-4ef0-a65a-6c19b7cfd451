/**
 * JavaScript sécurisé et optimisé pour la Meta Box Boss SEO
 * Version avec accessibilité, performance et UX améliorées
 */

// Attendre que jQuery soit disponible
function initBossSeo() {
    if (typeof jQuery === 'undefined') {
        console.log('⏳ Boss SEO: Attente de jQuery...');
        setTimeout(initBossSeo, 100);
        return;
    }

    console.log('✅ Boss SEO: jQuery trouvé, initialisation...');

    (function($) {
        'use strict';

    // Configuration et constantes
    const BOSS_SEO_CONFIG = {
        debounceDelay: 300,
        maxKeywords: 8,
        titleOptimalLength: { min: 50, max: 60 },
        descriptionOptimalLength: { min: 120, max: 160 },
        ajaxTimeout: 30000,
        retryAttempts: 3
    };

    // Messages traduits (à charger depuis PHP)
    const BOSS_SEO_MESSAGES = window.bossSeoMessages || {
        analyzing: 'Analyse en cours...',
        optimizing: 'Optimisation en cours...',
        error: 'Une erreur est survenue',
        success: 'Opération réussie',
        networkError: 'Erreur de connexion',
        timeout: 'Délai d\'attente dépassé',
        keywordExists: 'Ce mot-clé existe déjà',
        keywordTooLong: 'Mot-clé trop long (max 50 caractères)',
        keywordTooShort: 'Mot-clé trop court (min 3 caractères)',
        primaryKeywordSet: 'Mot-clé principal défini',
        keywordAdded: 'Mot-clé ajouté',
        keywordRemoved: 'Mot-clé supprimé'
    };

    /**
     * Classe principale pour la gestion de la Meta Box sécurisée avec onglets
     */
    class BossSeoMetaboxSecure {
        constructor() {
            this.container = $('.boss-seo-metabox-secure, .boss-seo-metabox-tabbed');

            // Récupérer le post ID avec plusieurs méthodes de fallback
            this.postId = this.container.data('post-id') ||
                         $('#post_ID').val() ||
                         (window.bossSeoTabbed && window.bossSeoTabbed.postId) ||
                         0;

            this.keywords = new Set();
            this.primaryKeyword = '';
            this.debounceTimers = new Map();
            this.retryCount = new Map();
            this.currentTab = 'keywords';
            this.mediaUploader = null;

            console.log('Boss SEO Debug:', {
                containerFound: this.container.length,
                postId: this.postId,
                containerClass: this.container.attr('class')
            });

            if (this.container.length && this.postId) {
                console.log('✅ Boss SEO Meta Box trouvée, initialisation...');
                this.init();
            } else {
                console.log('❌ Boss SEO Meta Box non trouvée ou post ID manquant');
                console.log('Container:', this.container.length);
                console.log('Post ID:', this.postId);
            }
        }

        /**
         * Initialise la Meta Box
         */
        init() {
            this.bindEvents();
            this.initializeTabs();
            this.initializeKeywords();
            this.initializeCounters();
            this.initializeValidation();
            this.initializeMediaUploader();
            this.setupAccessibility();

            // Charger les données existantes
            this.loadExistingData();

            // Initialiser l'aperçu SERP
            this.initializeSerpPreview();

            console.log('Boss SEO Meta Box sécurisée avec onglets initialisée');
        }

        /**
         * Lie tous les événements
         */
        bindEvents() {
            // Gestion des onglets
            this.container.on('click', '.boss-seo-tab-button', this.handleTabClick.bind(this));

            // Gestion des mots-clés
            this.container.on('keypress', '#boss_seo_keywords_input', this.handleKeywordInput.bind(this));
            this.container.on('click', '.boss-seo-add-keyword-btn', this.handleAddKeywordClick.bind(this));
            this.container.on('click', '.boss-seo-suggestion', this.handleSuggestionClick.bind(this));
            this.container.on('click', '.boss-seo-keyword-tag', this.handleKeywordTagClick.bind(this));
            this.container.on('click', '.boss-seo-refresh-suggestions', this.handleRefreshSuggestions.bind(this));

            // Validation en temps réel
            this.container.on('input', '.boss-seo-input, .boss-seo-textarea', this.handleFieldInput.bind(this));

            // Actions principales
            this.container.on('click', '#boss_seo_analyze_button', this.handleAnalyze.bind(this));
            this.container.on('click', '#boss_seo_optimize_button', this.handleOptimize.bind(this));

            // Gestion des médias
            this.container.on('click', '.boss-seo-media-button', this.handleMediaUpload.bind(this));

            // Sauvegarde automatique (debounced)
            this.container.on('input change', 'input, textarea, select', this.debouncedAutoSave.bind(this));

            // Gestion des erreurs globales
            $(document).on('ajaxError', this.handleAjaxError.bind(this));
        }

        /**
         * Gère la saisie de mots-clés
         */
        handleKeywordInput(e) {
            if (e.which === 13) { // Entrée
                e.preventDefault();
                const input = $(e.target);
                const keyword = input.val().trim();

                if (this.validateKeyword(keyword)) {
                    this.addKeyword(keyword);
                    input.val('');
                    this.announceToScreenReader(BOSS_SEO_MESSAGES.keywordAdded);
                }
            }
        }

        /**
         * Valide un mot-clé
         */
        validateKeyword(keyword) {
            if (!keyword) return false;

            if (keyword.length < 3) {
                this.showNotification(BOSS_SEO_MESSAGES.keywordTooShort, 'warning');
                return false;
            }

            if (keyword.length > 50) {
                this.showNotification(BOSS_SEO_MESSAGES.keywordTooLong, 'warning');
                return false;
            }

            if (this.keywords.has(keyword.toLowerCase())) {
                this.showNotification(BOSS_SEO_MESSAGES.keywordExists, 'warning');
                return false;
            }

            if (this.keywords.size >= BOSS_SEO_CONFIG.maxKeywords) {
                this.showNotification(`Maximum ${BOSS_SEO_CONFIG.maxKeywords} mots-clés autorisés`, 'warning');
                return false;
            }

            return true;
        }

        /**
         * Ajoute un mot-clé
         */
        addKeyword(keyword, isPrimary = false) {
            const normalizedKeyword = keyword.toLowerCase();

            if (this.keywords.has(normalizedKeyword)) {
                return;
            }

            this.keywords.add(normalizedKeyword);

            if (isPrimary || this.keywords.size === 1) {
                this.primaryKeyword = normalizedKeyword;
                this.announceToScreenReader(BOSS_SEO_MESSAGES.primaryKeywordSet);
            }

            this.renderKeywordTags();
            this.updateHiddenFields();
            this.validateContent();
        }

        /**
         * Supprime un mot-clé
         */
        removeKeyword(keyword) {
            const normalizedKeyword = keyword.toLowerCase();

            if (!this.keywords.has(normalizedKeyword)) {
                return;
            }

            this.keywords.delete(normalizedKeyword);

            if (this.primaryKeyword === normalizedKeyword) {
                this.primaryKeyword = this.keywords.size > 0 ? Array.from(this.keywords)[0] : '';
            }

            this.renderKeywordTags();
            this.updateHiddenFields();
            this.validateContent();
            this.announceToScreenReader(BOSS_SEO_MESSAGES.keywordRemoved);
        }

        /**
         * Affiche les tags de mots-clés
         */
        renderKeywordTags() {
            console.log('🎨 renderKeywordTags() appelée');
            const container = this.container.find('.boss-seo-keywords-tags');
            console.log('📦 Conteneur trouvé:', container.length);
            console.log('🏷️ Nombre de mots-clés à afficher:', this.keywords.size);
            console.log('📋 Mots-clés:', Array.from(this.keywords));

            if (container.length === 0) {
                console.error('❌ ERREUR: Conteneur .boss-seo-keywords-tags introuvable !');
                // Essayer d'autres sélecteurs
                const altContainers = this.container.find('.keywords-tags, .boss-keywords-tags, .seo-keywords-tags');
                console.log('🔍 Conteneurs alternatifs trouvés:', altContainers.length);
                return;
            }

            container.empty();

            if (this.keywords.size === 0) {
                console.log('📝 Affichage du placeholder (aucun mot-clé)');
                container.append('<div class="boss-seo-keywords-placeholder">Aucun mot-clé défini</div>');
                return;
            }

            Array.from(this.keywords).forEach(keyword => {
                const isPrimary = keyword === this.primaryKeyword;
                const tag = $(`
                    <div class="boss-seo-keyword-tag ${isPrimary ? 'primary' : ''}"
                         data-keyword="${this.escapeHtml(keyword)}"
                         role="listitem"
                         tabindex="0"
                         aria-label="${isPrimary ? 'Mot-clé principal' : 'Mot-clé secondaire'}: ${this.escapeHtml(keyword)}">
                        ${isPrimary ? '<span class="dashicons dashicons-star-filled" aria-hidden="true"></span>' : ''}
                        <span class="keyword-text">${this.escapeHtml(keyword)}</span>
                        <span class="dashicons dashicons-no-alt" aria-label="Supprimer" role="button"></span>
                    </div>
                `);

                container.append(tag);
            });
        }

        /**
         * Met à jour les champs cachés
         */
        updateHiddenFields() {
            $('#boss_seo_focus_keyword').val(this.primaryKeyword);

            const secondaryKeywords = Array.from(this.keywords)
                .filter(keyword => keyword !== this.primaryKeyword)
                .join(', ');

            $('#boss_seo_secondary_keywords').val(secondaryKeywords);
        }

        /**
         * Gère les clics sur les tags de mots-clés
         */
        handleKeywordTagClick(e) {
            const tag = $(e.currentTarget);
            const keyword = tag.data('keyword');
            const clickedElement = $(e.target);

            if (clickedElement.hasClass('dashicons-no-alt')) {
                // Supprimer le mot-clé
                this.removeKeyword(keyword);
            } else if (!tag.hasClass('primary')) {
                // Définir comme mot-clé principal
                this.primaryKeyword = keyword.toLowerCase();
                this.renderKeywordTags();
                this.updateHiddenFields();
                this.announceToScreenReader(BOSS_SEO_MESSAGES.primaryKeywordSet);
            }
        }

        /**
         * Gère les clics sur les suggestions
         */
        handleSuggestionClick(e) {
            e.preventDefault();
            const button = $(e.currentTarget);
            const keyword = button.data('keyword');

            if (this.validateKeyword(keyword)) {
                this.addKeyword(keyword);
                button.fadeOut(200);
            }
        }

        /**
         * Gère les clics sur le bouton d'ajout de mot-clé
         */
        handleAddKeywordClick(e) {
            e.preventDefault();
            const input = $('#boss_seo_keywords_input');
            const keyword = input.val().trim();

            if (this.validateKeyword(keyword)) {
                this.addKeyword(keyword);
                input.val('');
                this.announceToScreenReader(BOSS_SEO_MESSAGES.keywordAdded);
            }
        }

        /**
         * Gère la saisie dans les champs
         */
        handleFieldInput(e) {
            // Validation en temps réel si nécessaire
            const field = $(e.target);
            const fieldName = field.attr('name') || field.attr('id');

            // Mettre à jour l'aperçu SERP si c'est le titre ou la description
            if (fieldName === 'boss_seo_title' || fieldName === 'boss_seo_meta_description') {
                this.updateSerpPreview();
            }
        }

        /**
         * Gère l'actualisation des suggestions
         */
        handleRefreshSuggestions(e) {
            e.preventDefault();
            const button = $(e.currentTarget);

            button.addClass('spinning');

            // Simuler l'actualisation (à remplacer par un appel AJAX réel)
            setTimeout(() => {
                button.removeClass('spinning');
                this.showNotification('Suggestions actualisées', 'success');
            }, 1000);
        }

        /**
         * Gère l'upload de médias
         */
        handleMediaUpload(e) {
            e.preventDefault();
            const button = $(e.currentTarget);
            const target = button.data('target');

            // Ouvrir le sélecteur de médias WordPress
            if (typeof wp !== 'undefined' && wp.media) {
                const mediaUploader = wp.media({
                    title: 'Choisir une image',
                    button: { text: 'Utiliser cette image' },
                    multiple: false
                });

                mediaUploader.on('select', function() {
                    const attachment = mediaUploader.state().get('selection').first().toJSON();
                    $('#' + target).val(attachment.url);
                });

                mediaUploader.open();
            }
        }

        /**
         * Gère les erreurs AJAX globales
         */
        handleAjaxError(event, jqXHR, ajaxSettings, thrownError) {
            console.error('Erreur AJAX Boss SEO:', {
                url: ajaxSettings.url,
                error: thrownError,
                status: jqXHR.status
            });
        }

        /**
         * Initialise les compteurs de caractères
         */
        initializeCounters() {
            const fields = [
                { input: '#boss_seo_title', counter: '#boss_seo_title_counter', optimal: BOSS_SEO_CONFIG.titleOptimalLength },
                { input: '#boss_seo_meta_description', counter: '#boss_seo_meta_description_counter', optimal: BOSS_SEO_CONFIG.descriptionOptimalLength }
            ];

            fields.forEach(field => {
                const input = this.container.find(field.input);
                const counter = this.container.find(field.counter);

                if (input.length && counter.length) {
                    this.updateCounter(input, counter, field.optimal);

                    input.on('input', () => {
                        this.updateCounter(input, counter, field.optimal);
                    });
                }
            });
        }

        /**
         * Met à jour un compteur de caractères
         */
        updateCounter(input, counter, optimal) {
            const length = input.val().length;
            const currentSpan = counter.find('.boss-seo-counter-current');

            currentSpan.text(length);

            // Supprimer les classes précédentes
            currentSpan.removeClass('too-short too-long good');

            // Ajouter la classe appropriée
            if (length === 0) {
                // Pas de classe pour les champs vides
            } else if (length < optimal.min) {
                currentSpan.addClass('too-short');
            } else if (length > optimal.max) {
                currentSpan.addClass('too-long');
            } else {
                currentSpan.addClass('good');
            }
        }

        /**
         * Gère l'analyse du contenu
         */
        async handleAnalyze(e) {
            e.preventDefault();
            const button = $(e.currentTarget);

            if (button.hasClass('analyzing')) {
                return;
            }

            try {
                this.setButtonLoading(button, 'analyzing', BOSS_SEO_MESSAGES.analyzing);

                const result = await this.makeSecureAjaxRequest('boss_seo_analyze_content_secure', {
                    post_id: this.postId,
                    content: this.getContentForAnalysis()
                });

                if (result.success) {
                    this.updateScoreDisplay(result.data.score);
                    this.updateRecommendations(result.data.recommendations);
                    this.showNotification(BOSS_SEO_MESSAGES.success, 'success');
                } else {
                    throw new Error(result.data?.message || BOSS_SEO_MESSAGES.error);
                }

            } catch (error) {
                console.error('Erreur lors de l\'analyse:', error);
                this.showNotification(error.message || BOSS_SEO_MESSAGES.error, 'error');
            } finally {
                this.setButtonLoading(button, false);
            }
        }

        /**
         * Gère l'optimisation avec l'IA
         */
        async handleOptimize(e) {
            e.preventDefault();
            const button = $(e.currentTarget);

            if (button.hasClass('optimizing')) {
                return;
            }

            try {
                this.setButtonLoading(button, 'optimizing', BOSS_SEO_MESSAGES.optimizing);

                const result = await this.makeSecureAjaxRequest('boss_seo_optimize_content_secure', {
                    post_id: this.postId,
                    content: this.getContentForAnalysis(),
                    keywords: Array.from(this.keywords)
                });

                if (result.success) {
                    this.applyOptimizations(result.data);
                    // Message spécifique pour la sauvegarde automatique
                    const message = result.data.auto_saved
                        ? 'Métadonnées optimisées et sauvegardées automatiquement ✅'
                        : BOSS_SEO_MESSAGES.success;
                    this.showNotification(message, 'success');
                } else {
                    throw new Error(result.data?.message || BOSS_SEO_MESSAGES.error);
                }

            } catch (error) {
                console.error('Erreur lors de l\'optimisation:', error);
                this.showNotification(error.message || BOSS_SEO_MESSAGES.error, 'error');
            } finally {
                this.setButtonLoading(button, false);
            }
        }

        /**
         * Effectue une requête AJAX sécurisée avec retry
         */
        async makeSecureAjaxRequest(action, data, attempt = 1) {
            // Utiliser les variables localisées ou les variables globales en fallback
            const nonce = (window.bossSeoTabbed && window.bossSeoTabbed.nonce) || window.bossSeoNonce || '';
            const ajaxUrl = (window.bossSeoTabbed && window.bossSeoTabbed.ajaxUrl) || window.ajaxurl || '';

            if (!nonce) {
                console.error('Boss SEO: Nonce manquant');
                throw new Error('Erreur de sécurité: nonce manquant');
            }

            if (!ajaxUrl) {
                console.error('Boss SEO: URL AJAX manquante');
                throw new Error('Erreur de configuration: URL AJAX manquante');
            }

            const requestData = {
                action: action,
                nonce: nonce,
                ...data
            };

            console.log('Boss SEO AJAX Request:', { action, nonce: nonce.substring(0, 10) + '...', data });

            try {
                const response = await $.ajax({
                    url: ajaxUrl,
                    type: 'POST',
                    data: requestData,
                    timeout: BOSS_SEO_CONFIG.ajaxTimeout,
                    dataType: 'json'
                });

                console.log('Boss SEO AJAX Response:', response);
                return response;

            } catch (error) {
                if (attempt < BOSS_SEO_CONFIG.retryAttempts && this.shouldRetry(error)) {
                    console.warn(`Tentative ${attempt} échouée, retry...`);
                    await this.delay(1000 * attempt); // Délai progressif
                    return this.makeSecureAjaxRequest(action, data, attempt + 1);
                }

                throw this.normalizeError(error);
            }
        }

        /**
         * Détermine si une requête doit être retentée
         */
        shouldRetry(error) {
            const retryableStatuses = [0, 500, 502, 503, 504];
            return retryableStatuses.includes(error.status) || error.statusText === 'timeout';
        }

        /**
         * Normalise les erreurs
         */
        normalizeError(error) {
            if (error.statusText === 'timeout') {
                return new Error(BOSS_SEO_MESSAGES.timeout);
            }

            if (error.status === 0) {
                return new Error(BOSS_SEO_MESSAGES.networkError);
            }

            if (error.responseJSON?.data?.message) {
                return new Error(error.responseJSON.data.message);
            }

            return new Error(BOSS_SEO_MESSAGES.error);
        }

        /**
         * Délai asynchrone
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * Configure l'état de chargement d'un bouton
         */
        setButtonLoading(button, state, text = '') {
            if (state) {
                button.addClass(state)
                      .prop('disabled', true)
                      .data('original-text', button.text())
                      .html(`<span class="boss-seo-spinner"></span> ${text}`);
            } else {
                button.removeClass('analyzing optimizing')
                      .prop('disabled', false)
                      .text(button.data('original-text') || button.text().replace(/^.*?\s/, ''));
            }
        }

        /**
         * Affiche une notification
         */
        showNotification(message, type = 'info') {
            // Créer ou mettre à jour la notification
            let notification = $('.boss-seo-notification');

            if (!notification.length) {
                notification = $(`
                    <div class="boss-seo-notification" role="alert" aria-live="polite">
                        <span class="notification-text"></span>
                        <button type="button" class="notification-close" aria-label="Fermer">
                            <span class="dashicons dashicons-no-alt"></span>
                        </button>
                    </div>
                `);

                this.container.prepend(notification);

                notification.on('click', '.notification-close', () => {
                    notification.fadeOut(200);
                });
            }

            notification.removeClass('success warning error info')
                       .addClass(type)
                       .find('.notification-text')
                       .text(message);

            notification.fadeIn(200);

            // Auto-hide après 5 secondes sauf pour les erreurs
            if (type !== 'error') {
                setTimeout(() => {
                    notification.fadeOut(200);
                }, 5000);
            }
        }

        /**
         * Annonce un message aux lecteurs d'écran
         */
        announceToScreenReader(message) {
            const announcement = $('<div class="screen-reader-text" aria-live="polite"></div>');
            announcement.text(message);
            $('body').append(announcement);

            setTimeout(() => {
                announcement.remove();
            }, 1000);
        }

        /**
         * Échappe le HTML
         */
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        /**
         * Sauvegarde automatique avec debounce
         */
        debouncedAutoSave() {
            const key = 'autosave';

            if (this.debounceTimers.has(key)) {
                clearTimeout(this.debounceTimers.get(key));
            }

            this.debounceTimers.set(key, setTimeout(() => {
                this.autoSave();
            }, BOSS_SEO_CONFIG.debounceDelay));
        }

        /**
         * Sauvegarde automatique
         */
        async autoSave() {
            try {
                const data = this.collectFormData();

                await this.makeSecureAjaxRequest('boss_seo_auto_save', {
                    post_id: this.postId,
                    metadata: data
                });

                console.log('Sauvegarde automatique réussie');

            } catch (error) {
                console.warn('Erreur lors de la sauvegarde automatique:', error);
            }
        }

        /**
         * Collecte les données du formulaire
         */
        collectFormData() {
            const data = {};

            this.container.find('input, textarea, select').each((index, element) => {
                const $element = $(element);
                const name = $element.attr('name');

                if (name && name.startsWith('boss_seo_')) {
                    data[name] = $element.val();
                }
            });

            return data;
        }

        /**
         * Configure l'accessibilité
         */
        setupAccessibility() {
            // Navigation au clavier pour les tags de mots-clés
            this.container.on('keydown', '.boss-seo-keyword-tag', (e) => {
                if (e.which === 13 || e.which === 32) { // Entrée ou Espace
                    e.preventDefault();
                    $(e.currentTarget).click();
                }
            });

            // Navigation au clavier pour les suggestions
            this.container.on('keydown', '.boss-seo-suggestion', (e) => {
                if (e.which === 13 || e.which === 32) { // Entrée ou Espace
                    e.preventDefault();
                    $(e.currentTarget).click();
                }
            });

            // Améliorer les labels ARIA
            this.container.find('input, textarea, select').each((index, element) => {
                const $element = $(element);
                const id = $element.attr('id');

                if (id) {
                    const label = this.container.find(`label[for="${id}"]`);
                    if (label.length) {
                        $element.attr('aria-labelledby', id + '_label');
                        label.attr('id', id + '_label');
                    }
                }
            });
        }

        /**
         * Charge les données existantes
         */
        loadExistingData() {
            // Charger les mots-clés existants
            const focusKeyword = $('#boss_seo_focus_keyword').val();
            const secondaryKeywords = $('#boss_seo_secondary_keywords').val();

            console.log('Boss SEO - Chargement des données existantes:');
            console.log('Mot-clé principal:', focusKeyword ? `"${focusKeyword}"` : 'VIDE');
            console.log('Mots-clés secondaires:', secondaryKeywords ? `"${secondaryKeywords}"` : 'VIDE');

            // Réinitialiser les mots-clés
            this.keywords.clear();
            this.primaryKeyword = '';

            if (focusKeyword && focusKeyword.trim()) {
                this.addKeyword(focusKeyword.trim(), true);
                console.log('Mot-clé principal ajouté:', focusKeyword.trim());
            }

            if (secondaryKeywords && secondaryKeywords.trim()) {
                // Gérer les différents formats possibles
                let keywordsArray = [];

                if (secondaryKeywords.includes(',')) {
                    // Format: "mot1, mot2, mot3"
                    keywordsArray = secondaryKeywords.split(',');
                } else if (secondaryKeywords.includes(';')) {
                    // Format alternatif: "mot1; mot2; mot3"
                    keywordsArray = secondaryKeywords.split(';');
                } else {
                    // Un seul mot-clé
                    keywordsArray = [secondaryKeywords];
                }

                keywordsArray.forEach((keyword, index) => {
                    const trimmed = keyword.trim();
                    if (trimmed && trimmed !== focusKeyword) {
                        this.addKeyword(trimmed);
                        console.log(`Mot-clé secondaire ${index + 1} ajouté:`, trimmed);
                    }
                });
            }

            console.log('Mots-clés chargés au total:', this.keywords.size);
            console.log('Liste des mots-clés:', Array.from(this.keywords));
        }

        /**
         * Initialise la validation en temps réel
         */
        initializeValidation() {
            this.container.on('input', 'input, textarea', this.debounce((e) => {
                this.validateField($(e.target));
            }, 300));
        }

        /**
         * Valide un champ
         */
        validateField($field) {
            const value = $field.val();
            const fieldName = $field.attr('name');

            // Supprimer les messages d'erreur précédents
            $field.removeClass('error warning success');
            $field.siblings('.field-validation').remove();

            // Validation spécifique par champ
            switch (fieldName) {
                case 'boss_seo_canonical_url':
                    if (value && !this.isValidUrl(value)) {
                        this.addFieldValidation($field, 'URL invalide', 'error');
                    }
                    break;

                case 'boss_seo_title':
                    if (value.length > 0 && value.length < 30) {
                        this.addFieldValidation($field, 'Titre trop court pour un bon SEO', 'warning');
                    } else if (value.length > 60) {
                        this.addFieldValidation($field, 'Titre trop long, sera tronqué dans les résultats', 'warning');
                    }
                    break;

                case 'boss_seo_meta_description':
                    if (value.length > 0 && value.length < 120) {
                        this.addFieldValidation($field, 'Description trop courte', 'warning');
                    } else if (value.length > 160) {
                        this.addFieldValidation($field, 'Description trop longue, sera tronquée', 'warning');
                    }
                    break;
            }
        }

        /**
         * Ajoute un message de validation à un champ
         */
        addFieldValidation($field, message, type) {
            $field.addClass(type);

            const validation = $(`
                <div class="field-validation field-validation-${type}">
                    <span class="dashicons dashicons-${type === 'error' ? 'warning' : 'info'}"></span>
                    ${message}
                </div>
            `);

            $field.after(validation);
        }

        /**
         * Valide une URL
         */
        isValidUrl(string) {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        }

        /**
         * Fonction debounce utilitaire
         */
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        /**
         * Gère les erreurs AJAX globales
         */
        handleAjaxError(event, jqXHR, ajaxSettings, thrownError) {
            if (ajaxSettings.url === window.ajaxurl &&
                ajaxSettings.data &&
                ajaxSettings.data.action &&
                ajaxSettings.data.action.startsWith('boss_seo_')) {

                console.error('Erreur AJAX Boss SEO:', {
                    status: jqXHR.status,
                    statusText: jqXHR.statusText,
                    responseText: jqXHR.responseText,
                    thrownError: thrownError
                });
            }
        }

        /**
         * Initialise le système d'onglets
         */
        initializeTabs() {
            // Vérifier si l'interface a des onglets
            const tabsNav = this.container.find('.boss-seo-tabs-nav');
            if (!tabsNav.length) {
                console.log('Pas d\'onglets trouvés, interface simple');
                return;
            }

            console.log('Onglets trouvés, initialisation...');

            // Restaurer l'onglet actif depuis le localStorage
            const savedTab = localStorage.getItem('boss_seo_active_tab');
            if (savedTab && this.container.find(`[data-tab="${savedTab}"]`).length) {
                this.switchTab(savedTab);
            } else {
                // Activer le premier onglet par défaut
                this.switchTab('keywords');
            }

            // Afficher tous les onglets (au cas où ils seraient cachés)
            this.container.find('.boss-seo-tab-panel').show();
        }

        /**
         * Gère les clics sur les onglets
         */
        handleTabClick(e) {
            e.preventDefault();
            const button = $(e.currentTarget);
            const tabName = button.data('tab');

            if (tabName && tabName !== this.currentTab) {
                this.switchTab(tabName);
            }
        }

        /**
         * Change d'onglet
         */
        switchTab(tabName) {
            console.log('Changement vers l\'onglet:', tabName);

            // Mettre à jour les boutons d'onglets
            this.container.find('.boss-seo-tab-button').removeClass('active').attr('aria-selected', 'false');
            const activeButton = this.container.find(`[data-tab="${tabName}"]`);
            activeButton.addClass('active').attr('aria-selected', 'true');

            // Mettre à jour les panneaux
            this.container.find('.boss-seo-tab-panel').removeClass('active').hide();
            const activePanel = this.container.find(`#boss-seo-tab-${tabName}`);
            activePanel.addClass('active').show();

            console.log('Onglet activé:', activePanel.length ? 'trouvé' : 'non trouvé');

            // Sauvegarder l'onglet actif
            this.currentTab = tabName;
            localStorage.setItem('boss_seo_active_tab', tabName);

            // Déclencher des actions spécifiques selon l'onglet
            this.onTabSwitch(tabName);
        }

        /**
         * Actions à effectuer lors du changement d'onglet
         */
        onTabSwitch(tabName) {
            switch (tabName) {
                case 'analysis':
                    // Mettre à jour l'aperçu SERP si nécessaire
                    this.updateSerpPreview();
                    break;
                case 'keywords':
                    // Focus sur le champ de mots-clés
                    setTimeout(() => {
                        this.container.find('#boss_seo_keywords_input').focus();
                    }, 100);
                    break;
            }
        }

        /**
         * Gère le clic sur le bouton d'ajout de mot-clé
         */
        handleAddKeywordClick(e) {
            e.preventDefault();
            const input = this.container.find('#boss_seo_keywords_input');
            const keyword = input.val().trim();

            if (this.validateKeyword(keyword)) {
                this.addKeyword(keyword);
                input.val('');
                this.announceToScreenReader(BOSS_SEO_MESSAGES.keywordAdded);
            }
        }

        /**
         * Gère l'actualisation des suggestions
         */
        async handleRefreshSuggestions(e) {
            e.preventDefault();
            const button = $(e.currentTarget);

            if (button.hasClass('loading')) {
                return;
            }

            try {
                button.addClass('loading').prop('disabled', true);
                button.find('.dashicons').addClass('boss-seo-spinner');

                const result = await this.makeSecureAjaxRequest('boss_seo_refresh_suggestions', {
                    post_id: this.postId,
                    content: this.getContentForAnalysis()
                });

                if (result.success && result.data.suggestions) {
                    this.updateSuggestions(result.data.suggestions);
                    this.showNotification('Suggestions mises à jour', 'success');
                }

            } catch (error) {
                console.error('Erreur lors de l\'actualisation des suggestions:', error);
                this.showNotification(error.message || BOSS_SEO_MESSAGES.error, 'error');
            } finally {
                button.removeClass('loading').prop('disabled', false);
                button.find('.dashicons').removeClass('boss-seo-spinner');
            }
        }

        /**
         * Met à jour les suggestions de mots-clés
         */
        updateSuggestions(suggestions) {
            const container = this.container.find('.boss-seo-suggestions-list');
            container.empty();

            suggestions.forEach(suggestion => {
                const button = $(`
                    <button type="button" class="boss-seo-suggestion" data-keyword="${this.escapeHtml(suggestion)}" role="listitem">
                        <span class="dashicons dashicons-plus-alt2" aria-hidden="true"></span>
                        <span class="suggestion-text">${this.escapeHtml(suggestion)}</span>
                    </button>
                `);
                container.append(button);
            });
        }

        /**
         * Initialise l'uploader de médias WordPress
         */
        initializeMediaUploader() {
            if (typeof wp === 'undefined' || !wp.media) {
                return;
            }

            this.mediaUploader = wp.media({
                title: 'Choisir une image',
                button: {
                    text: 'Utiliser cette image'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });
        }

        /**
         * Gère l'upload de médias
         */
        handleMediaUpload(e) {
            e.preventDefault();

            if (!this.mediaUploader) {
                this.showNotification('Uploader de médias non disponible', 'error');
                return;
            }

            const button = $(e.currentTarget);
            const targetField = button.data('target');

            this.mediaUploader.off('select').on('select', () => {
                const attachment = this.mediaUploader.state().get('selection').first().toJSON();
                this.container.find(`#${targetField}`).val(attachment.url).trigger('input');
                this.showNotification('Image sélectionnée', 'success');
            });

            this.mediaUploader.open();
        }

        /**
         * Initialise l'aperçu SERP
         */
        initializeSerpPreview() {
            // Mettre à jour l'aperçu en temps réel
            this.container.on('input', '#boss_seo_title, #boss_seo_meta_description',
                this.debounce(() => this.updateSerpPreview(), 500)
            );

            // Mise à jour initiale
            this.updateSerpPreview();
        }

        /**
         * Met à jour l'aperçu SERP
         */
        updateSerpPreview() {
            const titleField = this.container.find('#boss_seo_title');
            const descriptionField = this.container.find('#boss_seo_meta_description');

            const title = titleField.val() || $('h1.entry-title, h1.post-title, #title').first().val() || document.title;
            const description = descriptionField.val() || this.generateAutoDescription();

            this.container.find('#serp-title-preview').text(title);
            this.container.find('#serp-description-preview').text(description);
        }

        /**
         * Génère une description automatique
         */
        generateAutoDescription() {
            // Essayer d'extraire du contenu de l'éditeur
            let content = '';

            if (typeof tinymce !== 'undefined' && tinymce.get('content')) {
                content = tinymce.get('content').getContent({ format: 'text' });
            } else if ($('#content').length) {
                content = $('#content').val();
            }

            // Nettoyer et tronquer
            content = content.replace(/\s+/g, ' ').trim();
            return content.length > 160 ? content.substring(0, 157) + '...' : content;
        }

        /**
         * Affiche l'indicateur de sauvegarde
         */
        showSaveIndicator() {
            const indicator = this.container.find('.boss-seo-save-indicator');
            indicator.addClass('visible');

            setTimeout(() => {
                indicator.removeClass('visible');
            }, 2000);
        }

        /**
         * Récupère le contenu pour l'analyse
         */
        getContentForAnalysis() {
            let content = '';

            // Essayer d'obtenir le contenu de l'éditeur WordPress
            if (typeof tinymce !== 'undefined' && tinymce.get('content')) {
                content = tinymce.get('content').getContent({ format: 'text' });
            } else if ($('#content').length) {
                content = $('#content').val();
            }

            // Nettoyer le contenu
            content = content.replace(/\s+/g, ' ').trim();

            // Limiter la longueur pour l'analyse
            return content.length > 2000 ? content.substring(0, 2000) + '...' : content;
        }

        /**
         * Applique les optimisations reçues
         */
        applyOptimizations(data) {
            if (!data.optimizations) {
                console.warn('Aucune optimisation reçue');
                return;
            }

            const optimizations = data.optimizations;

            // Appliquer le titre optimisé
            if (optimizations.title) {
                $('#boss_seo_title').val(optimizations.title).trigger('input');
                console.log('Titre optimisé appliqué:', optimizations.title);
            }

            // Appliquer la meta description
            if (optimizations.meta_description) {
                $('#boss_seo_meta_description').val(optimizations.meta_description).trigger('input');
                console.log('Meta description appliquée:', optimizations.meta_description);
            }

            // Appliquer le mot-clé principal
            if (optimizations.focus_keyword) {
                console.log('🔧 Ajout du mot-clé principal:', optimizations.focus_keyword);
                this.addKeyword(optimizations.focus_keyword, true);
                console.log('✅ Mot-clé principal appliqué. Total mots-clés:', this.keywords.size);
                console.log('📋 Liste actuelle:', Array.from(this.keywords));
            }

            // Appliquer les mots-clés secondaires
            if (optimizations.secondary_keywords && Array.isArray(optimizations.secondary_keywords)) {
                console.log('🔧 Ajout des mots-clés secondaires:', optimizations.secondary_keywords);
                optimizations.secondary_keywords.forEach((keyword, index) => {
                    if (keyword && keyword.trim()) {
                        console.log(`  Ajout mot-clé ${index + 1}:`, keyword.trim());
                        this.addKeyword(keyword.trim());
                    }
                });
                console.log('✅ Mots-clés secondaires appliqués. Total mots-clés:', this.keywords.size);
                console.log('📋 Liste finale:', Array.from(this.keywords));
            }

            // Forcer le rendu des tags
            console.log('🎨 Forçage du rendu des tags...');
            this.renderKeywordTags();

            // Mettre à jour l'aperçu SERP
            this.updateSerpPreview();

            // Afficher le score SEO si disponible
            if (data.seo_score) {
                this.updateScoreDisplay(data.seo_score);
            }
        }

        /**
         * Met à jour l'affichage du score SEO
         */
        updateScoreDisplay(score) {
            const scoreElement = this.container.find('.boss-seo-score-value');
            const scoreBar = this.container.find('.boss-seo-score-bar');

            if (scoreElement.length) {
                scoreElement.text(score + '%');
            }

            if (scoreBar.length) {
                scoreBar.css('width', score + '%');

                // Changer la couleur selon le score
                scoreBar.removeClass('score-low score-medium score-high');
                if (score < 50) {
                    scoreBar.addClass('score-low');
                } else if (score < 80) {
                    scoreBar.addClass('score-medium');
                } else {
                    scoreBar.addClass('score-high');
                }
            }
        }

        /**
         * Met à jour les recommandations
         */
        updateRecommendations(recommendations) {
            const container = this.container.find('.boss-seo-recommendations');

            if (!container.length || !recommendations) {
                return;
            }

            container.empty();

            if (recommendations.length === 0) {
                container.append('<p class="no-recommendations">Aucune recommandation pour le moment.</p>');
                return;
            }

            const list = $('<ul class="recommendations-list"></ul>');

            recommendations.forEach(recommendation => {
                const item = $(`
                    <li class="recommendation-item recommendation-${recommendation.type}">
                        <span class="dashicons dashicons-${this.getRecommendationIcon(recommendation.type)}"></span>
                        <span class="recommendation-text">${this.escapeHtml(recommendation.message)}</span>
                    </li>
                `);
                list.append(item);
            });

            container.append(list);
        }

        /**
         * Récupère l'icône pour un type de recommandation
         */
        getRecommendationIcon(type) {
            const icons = {
                'error': 'warning',
                'warning': 'info',
                'success': 'yes-alt',
                'info': 'lightbulb'
            };

            return icons[type] || 'info';
        }

        /**
         * Valide le contenu en temps réel
         */
        validateContent() {
            // Cette méthode peut être étendue pour valider le contenu
            // en fonction des mots-clés définis
            console.log('Validation du contenu...');
        }


    }

    // Initialisation quand le DOM est prêt
    $(document).ready(() => {
        new BossSeoMetaboxSecure();
    });

    })(jQuery);
}

// Démarrer l'initialisation
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initBossSeo);
} else {
    initBossSeo();
}
