<?php
/**
 * Classe pour les schémas de produits.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour les schémas de produits.
 *
 * Cette classe gère les schémas structurés pour les produits.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Product_Schema {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_product_schema_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_generate_product_schema', array( $this, 'ajax_generate_product_schema' ) );
        add_action( 'wp_ajax_boss_seo_test_product_schema', array( $this, 'ajax_test_product_schema' ) );

        // Ajouter les schémas structurés
        add_action( 'wp_head', array( $this, 'insert_product_schema' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/product-schema/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_product_schema' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'update_product_schema' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/product-schema/(?P<id>\d+)/test',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'test_product_schema' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour générer un schéma de produit.
     *
     * @since    1.2.0
     */
    public function ajax_generate_product_schema() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );

        // Générer le schéma
        $schema = $this->generate_product_schema( $product_id );

        if ( is_wp_error( $schema ) ) {
            wp_send_json_error( array( 'message' => $schema->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Schéma généré avec succès.', 'boss-seo' ),
            'schema'  => $schema,
            'schema_json' => wp_json_encode( $schema, JSON_PRETTY_PRINT ),
        ) );
    }

    /**
     * Gère les requêtes AJAX pour tester un schéma de produit.
     *
     * @since    1.2.0
     */
    public function ajax_test_product_schema() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );

        // Générer le schéma
        $schema = $this->generate_product_schema( $product_id );

        if ( is_wp_error( $schema ) ) {
            wp_send_json_error( array( 'message' => $schema->get_error_message() ) );
        }

        // Tester le schéma
        $result = $this->test_schema_with_google( $schema );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Schéma testé avec succès.', 'boss-seo' ),
            'result'  => $result,
        ) );
    }

    /**
     * Récupère le schéma d'un produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_product_schema( $request ) {
        $product_id = $request['id'];

        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Récupérer le schéma
        $schema = $this->generate_product_schema( $product_id );

        if ( is_wp_error( $schema ) ) {
            return $schema;
        }

        return rest_ensure_response( array(
            'schema'      => $schema,
            'schema_json' => wp_json_encode( $schema, JSON_PRETTY_PRINT ),
        ) );
    }

    /**
     * Met à jour le schéma d'un produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_product_schema( $request ) {
        $product_id = $request['id'];
        $params = $request->get_params();

        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Mettre à jour les options du schéma
        $schema_type = isset( $params['schema_type'] ) ? sanitize_text_field( $params['schema_type'] ) : 'Product';
        $schema_options = isset( $params['schema_options'] ) && is_array( $params['schema_options'] ) ? $params['schema_options'] : array();

        update_post_meta( $product_id, '_boss_product_schema_type', $schema_type );
        update_post_meta( $product_id, '_boss_product_schema_options', $schema_options );

        // Générer le schéma mis à jour
        $schema = $this->generate_product_schema( $product_id );

        if ( is_wp_error( $schema ) ) {
            return $schema;
        }

        return rest_ensure_response( array(
            'message'     => __( 'Schéma mis à jour avec succès.', 'boss-seo' ),
            'schema'      => $schema,
            'schema_json' => wp_json_encode( $schema, JSON_PRETTY_PRINT ),
        ) );
    }

    /**
     * Teste le schéma d'un produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function test_product_schema( $request ) {
        $product_id = $request['id'];

        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Générer le schéma
        $schema = $this->generate_product_schema( $product_id );

        if ( is_wp_error( $schema ) ) {
            return $schema;
        }

        // Tester le schéma
        $result = $this->test_schema_with_google( $schema );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Schéma testé avec succès.', 'boss-seo' ),
            'result'  => $result,
        ) );
    }

    /**
     * Insère les schémas structurés dans le head du site.
     *
     * @since    1.2.0
     */
    public function insert_product_schema() {
        if ( is_product() ) {
            $product_id = get_the_ID();
            $schema = $this->generate_product_schema( $product_id );

            if ( ! is_wp_error( $schema ) ) {
                echo '<script type="application/ld+json">' . wp_json_encode( $schema ) . '</script>' . "\n";
            }
        }
    }

    /**
     * Affiche la métabox pour les schémas de produits.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post.
     */
    public function render_meta_box( $post ) {
        // Récupérer les données du produit
        $product_id = $post->ID;
        $schema_type = get_post_meta( $product_id, '_boss_product_schema_type', true );
        $schema_options = get_post_meta( $product_id, '_boss_product_schema_options', true );

        if ( empty( $schema_type ) ) {
            $schema_type = 'Product';
        }

        if ( empty( $schema_options ) || ! is_array( $schema_options ) ) {
            $schema_options = array(
                'include_brand' => true,
                'include_reviews' => true,
                'include_offers' => true,
                'include_aggregate_rating' => true,
                'include_sku' => true,
                'include_mpn' => true,
                'include_gtin' => true,
                'include_description' => true,
                'include_image' => true,
            );
        }

        // Afficher le formulaire
        wp_nonce_field( 'boss_seo_product_schema_meta_box', 'boss_seo_product_schema_meta_box_nonce' );
        ?>
        <div class="boss-seo-product-schema-meta-box">
            <p>
                <label for="boss_product_schema_type"><?php esc_html_e( 'Type de schéma :', 'boss-seo' ); ?></label>
                <select id="boss_product_schema_type" name="boss_product_schema_type">
                    <option value="Product" <?php selected( $schema_type, 'Product' ); ?>><?php esc_html_e( 'Produit', 'boss-seo' ); ?></option>
                    <option value="IndividualProduct" <?php selected( $schema_type, 'IndividualProduct' ); ?>><?php esc_html_e( 'Produit individuel', 'boss-seo' ); ?></option>
                    <option value="ProductModel" <?php selected( $schema_type, 'ProductModel' ); ?>><?php esc_html_e( 'Modèle de produit', 'boss-seo' ); ?></option>
                    <option value="SomeProducts" <?php selected( $schema_type, 'SomeProducts' ); ?>><?php esc_html_e( 'Certains produits', 'boss-seo' ); ?></option>
                    <option value="Vehicle" <?php selected( $schema_type, 'Vehicle' ); ?>><?php esc_html_e( 'Véhicule', 'boss-seo' ); ?></option>
                    <option value="Book" <?php selected( $schema_type, 'Book' ); ?>><?php esc_html_e( 'Livre', 'boss-seo' ); ?></option>
                    <option value="Movie" <?php selected( $schema_type, 'Movie' ); ?>><?php esc_html_e( 'Film', 'boss-seo' ); ?></option>
                    <option value="MusicAlbum" <?php selected( $schema_type, 'MusicAlbum' ); ?>><?php esc_html_e( 'Album de musique', 'boss-seo' ); ?></option>
                    <option value="SoftwareApplication" <?php selected( $schema_type, 'SoftwareApplication' ); ?>><?php esc_html_e( 'Application logicielle', 'boss-seo' ); ?></option>
                </select>
            </p>

            <p><?php esc_html_e( 'Options du schéma :', 'boss-seo' ); ?></p>
            <ul>
                <li>
                    <label>
                        <input type="checkbox" name="boss_product_schema_options[include_brand]" value="1" <?php checked( isset( $schema_options['include_brand'] ) ? $schema_options['include_brand'] : true ); ?>>
                        <?php esc_html_e( 'Inclure la marque', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_product_schema_options[include_reviews]" value="1" <?php checked( isset( $schema_options['include_reviews'] ) ? $schema_options['include_reviews'] : true ); ?>>
                        <?php esc_html_e( 'Inclure les avis', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_product_schema_options[include_offers]" value="1" <?php checked( isset( $schema_options['include_offers'] ) ? $schema_options['include_offers'] : true ); ?>>
                        <?php esc_html_e( 'Inclure les offres', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_product_schema_options[include_aggregate_rating]" value="1" <?php checked( isset( $schema_options['include_aggregate_rating'] ) ? $schema_options['include_aggregate_rating'] : true ); ?>>
                        <?php esc_html_e( 'Inclure la note moyenne', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_product_schema_options[include_sku]" value="1" <?php checked( isset( $schema_options['include_sku'] ) ? $schema_options['include_sku'] : true ); ?>>
                        <?php esc_html_e( 'Inclure le SKU', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_product_schema_options[include_mpn]" value="1" <?php checked( isset( $schema_options['include_mpn'] ) ? $schema_options['include_mpn'] : true ); ?>>
                        <?php esc_html_e( 'Inclure le MPN', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_product_schema_options[include_gtin]" value="1" <?php checked( isset( $schema_options['include_gtin'] ) ? $schema_options['include_gtin'] : true ); ?>>
                        <?php esc_html_e( 'Inclure le GTIN', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_product_schema_options[include_description]" value="1" <?php checked( isset( $schema_options['include_description'] ) ? $schema_options['include_description'] : true ); ?>>
                        <?php esc_html_e( 'Inclure la description', 'boss-seo' ); ?>
                    </label>
                </li>
                <li>
                    <label>
                        <input type="checkbox" name="boss_product_schema_options[include_image]" value="1" <?php checked( isset( $schema_options['include_image'] ) ? $schema_options['include_image'] : true ); ?>>
                        <?php esc_html_e( 'Inclure l\'image', 'boss-seo' ); ?>
                    </label>
                </li>
            </ul>

            <div class="boss-seo-product-schema-actions">
                <button type="button" class="button button-secondary" id="boss-seo-generate-product-schema" data-product-id="<?php echo esc_attr( $product_id ); ?>"><?php esc_html_e( 'Générer le schéma', 'boss-seo' ); ?></button>
                <button type="button" class="button button-secondary" id="boss-seo-test-product-schema" data-product-id="<?php echo esc_attr( $product_id ); ?>"><?php esc_html_e( 'Tester le schéma', 'boss-seo' ); ?></button>
            </div>

            <div class="boss-seo-product-schema-result" style="display: none;">
                <h4><?php esc_html_e( 'Schéma généré :', 'boss-seo' ); ?></h4>
                <pre id="boss-seo-product-schema-json"></pre>
            </div>

            <div class="boss-seo-product-schema-test-result" style="display: none;">
                <h4><?php esc_html_e( 'Résultat du test :', 'boss-seo' ); ?></h4>
                <div id="boss-seo-product-schema-test-result"></div>
            </div>
        </div>
        <?php
    }

    /**
     * Affiche le panneau de données pour les schémas de produits.
     *
     * @since    1.2.0
     */
    public function render_product_data_panel() {
        global $post;

        // Récupérer les données du produit
        $product_id = $post->ID;
        $schema_type = get_post_meta( $product_id, '_boss_product_schema_type', true );
        $schema_options = get_post_meta( $product_id, '_boss_product_schema_options', true );

        if ( empty( $schema_type ) ) {
            $schema_type = 'Product';
        }

        if ( empty( $schema_options ) || ! is_array( $schema_options ) ) {
            $schema_options = array(
                'include_brand' => true,
                'include_reviews' => true,
                'include_offers' => true,
                'include_aggregate_rating' => true,
                'include_sku' => true,
                'include_mpn' => true,
                'include_gtin' => true,
                'include_description' => true,
                'include_image' => true,
            );
        }

        // Afficher le formulaire
        woocommerce_wp_select( array(
            'id'          => 'boss_product_schema_type',
            'label'       => __( 'Type de schéma', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Sélectionnez le type de schéma pour ce produit.', 'boss-seo' ),
            'options'     => array(
                'Product'             => __( 'Produit', 'boss-seo' ),
                'IndividualProduct'   => __( 'Produit individuel', 'boss-seo' ),
                'ProductModel'        => __( 'Modèle de produit', 'boss-seo' ),
                'SomeProducts'        => __( 'Certains produits', 'boss-seo' ),
                'Vehicle'             => __( 'Véhicule', 'boss-seo' ),
                'Book'                => __( 'Livre', 'boss-seo' ),
                'Movie'               => __( 'Film', 'boss-seo' ),
                'MusicAlbum'          => __( 'Album de musique', 'boss-seo' ),
                'SoftwareApplication' => __( 'Application logicielle', 'boss-seo' ),
            ),
            'value'       => $schema_type,
        ) );

        echo '<p>' . esc_html__( 'Options du schéma :', 'boss-seo' ) . '</p>';

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_product_schema_options_include_brand',
            'label'       => __( 'Inclure la marque', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Inclure la marque dans le schéma.', 'boss-seo' ),
            'value'       => isset( $schema_options['include_brand'] ) ? $schema_options['include_brand'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_product_schema_options_include_reviews',
            'label'       => __( 'Inclure les avis', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Inclure les avis dans le schéma.', 'boss-seo' ),
            'value'       => isset( $schema_options['include_reviews'] ) ? $schema_options['include_reviews'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_product_schema_options_include_offers',
            'label'       => __( 'Inclure les offres', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Inclure les offres dans le schéma.', 'boss-seo' ),
            'value'       => isset( $schema_options['include_offers'] ) ? $schema_options['include_offers'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_product_schema_options_include_aggregate_rating',
            'label'       => __( 'Inclure la note moyenne', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Inclure la note moyenne dans le schéma.', 'boss-seo' ),
            'value'       => isset( $schema_options['include_aggregate_rating'] ) ? $schema_options['include_aggregate_rating'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_product_schema_options_include_sku',
            'label'       => __( 'Inclure le SKU', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Inclure le SKU dans le schéma.', 'boss-seo' ),
            'value'       => isset( $schema_options['include_sku'] ) ? $schema_options['include_sku'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_product_schema_options_include_mpn',
            'label'       => __( 'Inclure le MPN', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Inclure le MPN dans le schéma.', 'boss-seo' ),
            'value'       => isset( $schema_options['include_mpn'] ) ? $schema_options['include_mpn'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_product_schema_options_include_gtin',
            'label'       => __( 'Inclure le GTIN', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Inclure le GTIN dans le schéma.', 'boss-seo' ),
            'value'       => isset( $schema_options['include_gtin'] ) ? $schema_options['include_gtin'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_product_schema_options_include_description',
            'label'       => __( 'Inclure la description', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Inclure la description dans le schéma.', 'boss-seo' ),
            'value'       => isset( $schema_options['include_description'] ) ? $schema_options['include_description'] : 'yes',
        ) );

        woocommerce_wp_checkbox( array(
            'id'          => 'boss_product_schema_options_include_image',
            'label'       => __( 'Inclure l\'image', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Inclure l\'image dans le schéma.', 'boss-seo' ),
            'value'       => isset( $schema_options['include_image'] ) ? $schema_options['include_image'] : 'yes',
        ) );

        echo '<div class="boss-seo-product-schema-actions">';
        echo '<button type="button" class="button button-secondary" id="boss-seo-generate-product-schema" data-product-id="' . esc_attr( $product_id ) . '">' . esc_html__( 'Générer le schéma', 'boss-seo' ) . '</button>';
        echo '<button type="button" class="button button-secondary" id="boss-seo-test-product-schema" data-product-id="' . esc_attr( $product_id ) . '">' . esc_html__( 'Tester le schéma', 'boss-seo' ) . '</button>';
        echo '</div>';

        echo '<div class="boss-seo-product-schema-result" style="display: none;">';
        echo '<h4>' . esc_html__( 'Schéma généré :', 'boss-seo' ) . '</h4>';
        echo '<pre id="boss-seo-product-schema-json"></pre>';
        echo '</div>';

        echo '<div class="boss-seo-product-schema-test-result" style="display: none;">';
        echo '<h4>' . esc_html__( 'Résultat du test :', 'boss-seo' ) . '</h4>';
        echo '<div id="boss-seo-product-schema-test-result"></div>';
        echo '</div>';
    }

    /**
     * Enregistre les métadonnées du produit.
     *
     * @since    1.2.0
     * @param    int    $post_id    L'ID du produit.
     */
    public function save_meta( $post_id ) {
        // Vérifier le nonce
        if ( ! isset( $_POST['boss_seo_product_schema_meta_box_nonce'] ) || ! wp_verify_nonce( $_POST['boss_seo_product_schema_meta_box_nonce'], 'boss_seo_product_schema_meta_box' ) ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Enregistrer le type de schéma
        if ( isset( $_POST['boss_product_schema_type'] ) ) {
            update_post_meta( $post_id, '_boss_product_schema_type', sanitize_text_field( $_POST['boss_product_schema_type'] ) );
        }

        // Enregistrer les options du schéma
        $schema_options = array(
            'include_brand'           => isset( $_POST['boss_product_schema_options_include_brand'] ) && $_POST['boss_product_schema_options_include_brand'] === 'yes',
            'include_reviews'         => isset( $_POST['boss_product_schema_options_include_reviews'] ) && $_POST['boss_product_schema_options_include_reviews'] === 'yes',
            'include_offers'          => isset( $_POST['boss_product_schema_options_include_offers'] ) && $_POST['boss_product_schema_options_include_offers'] === 'yes',
            'include_aggregate_rating' => isset( $_POST['boss_product_schema_options_include_aggregate_rating'] ) && $_POST['boss_product_schema_options_include_aggregate_rating'] === 'yes',
            'include_sku'             => isset( $_POST['boss_product_schema_options_include_sku'] ) && $_POST['boss_product_schema_options_include_sku'] === 'yes',
            'include_mpn'             => isset( $_POST['boss_product_schema_options_include_mpn'] ) && $_POST['boss_product_schema_options_include_mpn'] === 'yes',
            'include_gtin'            => isset( $_POST['boss_product_schema_options_include_gtin'] ) && $_POST['boss_product_schema_options_include_gtin'] === 'yes',
            'include_description'     => isset( $_POST['boss_product_schema_options_include_description'] ) && $_POST['boss_product_schema_options_include_description'] === 'yes',
            'include_image'           => isset( $_POST['boss_product_schema_options_include_image'] ) && $_POST['boss_product_schema_options_include_image'] === 'yes',
        );

        update_post_meta( $post_id, '_boss_product_schema_options', $schema_options );
    }

    /**
     * Génère le shortcode pour les schémas de produits.
     *
     * @since    1.2.0
     * @param    array     $atts       Les attributs du shortcode.
     * @param    string    $content    Le contenu du shortcode.
     * @return   string                Le contenu généré.
     */
    public function schema_shortcode( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'product_id' => 0,
        ), $atts, 'boss_product_schema' );

        $product_id = absint( $atts['product_id'] );

        if ( ! $product_id ) {
            return '';
        }

        $schema = $this->generate_product_schema( $product_id );

        if ( is_wp_error( $schema ) ) {
            return '';
        }

        return '<script type="application/ld+json">' . wp_json_encode( $schema ) . '</script>';
    }

    /**
     * Génère le schéma structuré pour un produit.
     *
     * @since    1.2.0
     * @param    int       $product_id    L'ID du produit.
     * @return   array|WP_Error           Le schéma structuré ou une erreur.
     */
    public function generate_product_schema( $product_id ) {
        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ) );
        }

        // Récupérer les options du schéma
        $schema_type = get_post_meta( $product_id, '_boss_product_schema_type', true );
        $schema_options = get_post_meta( $product_id, '_boss_product_schema_options', true );

        if ( empty( $schema_type ) ) {
            $schema_type = 'Product';
        }

        if ( empty( $schema_options ) || ! is_array( $schema_options ) ) {
            $schema_options = array(
                'include_brand' => true,
                'include_reviews' => true,
                'include_offers' => true,
                'include_aggregate_rating' => true,
                'include_sku' => true,
                'include_mpn' => true,
                'include_gtin' => true,
                'include_description' => true,
                'include_image' => true,
            );
        }

        // Construire le schéma
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => $schema_type,
            'name' => $product->get_name(),
            'url' => get_permalink( $product_id ),
        );

        // Ajouter la description
        if ( isset( $schema_options['include_description'] ) && $schema_options['include_description'] ) {
            $schema['description'] = $product->get_description() ? $product->get_description() : $product->get_short_description();
        }

        // Ajouter l'image
        if ( isset( $schema_options['include_image'] ) && $schema_options['include_image'] ) {
            $image_id = $product->get_image_id();
            if ( $image_id ) {
                $image_url = wp_get_attachment_url( $image_id );
                if ( $image_url ) {
                    $schema['image'] = $image_url;
                }
            }
        }

        // Ajouter le SKU
        if ( isset( $schema_options['include_sku'] ) && $schema_options['include_sku'] ) {
            $sku = $product->get_sku();
            if ( $sku ) {
                $schema['sku'] = $sku;
            }
        }

        // Ajouter le MPN
        if ( isset( $schema_options['include_mpn'] ) && $schema_options['include_mpn'] ) {
            $mpn = get_post_meta( $product_id, '_boss_product_mpn', true );
            if ( $mpn ) {
                $schema['mpn'] = $mpn;
            }
        }

        // Ajouter le GTIN
        if ( isset( $schema_options['include_gtin'] ) && $schema_options['include_gtin'] ) {
            $gtin = get_post_meta( $product_id, '_boss_product_gtin', true );
            if ( $gtin ) {
                $schema['gtin'] = $gtin;
            }
        }

        // Ajouter la marque
        if ( isset( $schema_options['include_brand'] ) && $schema_options['include_brand'] ) {
            $brand = get_post_meta( $product_id, '_boss_product_brand', true );
            if ( $brand ) {
                $schema['brand'] = array(
                    '@type' => 'Brand',
                    'name' => $brand,
                );
            }
        }

        // Ajouter les offres
        if ( isset( $schema_options['include_offers'] ) && $schema_options['include_offers'] ) {
            $schema['offers'] = array(
                '@type' => 'Offer',
                'url' => get_permalink( $product_id ),
                'price' => $product->get_price(),
                'priceCurrency' => get_woocommerce_currency(),
                'availability' => $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
                'priceValidUntil' => date( 'Y-m-d', strtotime( '+1 year' ) ),
            );
        }

        // Ajouter les avis
        if ( isset( $schema_options['include_reviews'] ) && $schema_options['include_reviews'] ) {
            $reviews = get_comments( array(
                'post_id' => $product_id,
                'status' => 'approve',
                'type' => 'review',
            ) );

            if ( $reviews ) {
                $schema_reviews = array();

                foreach ( $reviews as $review ) {
                    $rating = get_comment_meta( $review->comment_ID, 'rating', true );

                    if ( $rating ) {
                        $schema_reviews[] = array(
                            '@type' => 'Review',
                            'reviewRating' => array(
                                '@type' => 'Rating',
                                'ratingValue' => $rating,
                                'bestRating' => '5',
                            ),
                            'author' => array(
                                '@type' => 'Person',
                                'name' => $review->comment_author,
                            ),
                            'reviewBody' => $review->comment_content,
                            'datePublished' => $review->comment_date,
                        );
                    }
                }

                if ( ! empty( $schema_reviews ) ) {
                    $schema['review'] = $schema_reviews;
                }
            }
        }

        // Ajouter la note moyenne
        if ( isset( $schema_options['include_aggregate_rating'] ) && $schema_options['include_aggregate_rating'] ) {
            $rating_count = $product->get_rating_count();
            $average_rating = $product->get_average_rating();

            if ( $rating_count > 0 ) {
                $schema['aggregateRating'] = array(
                    '@type' => 'AggregateRating',
                    'ratingValue' => $average_rating,
                    'reviewCount' => $rating_count,
                    'bestRating' => '5',
                    'worstRating' => '1',
                );
            }
        }

        // Ajouter des propriétés spécifiques selon le type de schéma
        switch ( $schema_type ) {
            case 'Book':
                $schema['author'] = array(
                    '@type' => 'Person',
                    'name' => get_post_meta( $product_id, '_boss_product_book_author', true ),
                );
                $schema['isbn'] = get_post_meta( $product_id, '_boss_product_book_isbn', true );
                $schema['numberOfPages'] = get_post_meta( $product_id, '_boss_product_book_pages', true );
                $schema['publisher'] = array(
                    '@type' => 'Organization',
                    'name' => get_post_meta( $product_id, '_boss_product_book_publisher', true ),
                );
                break;

            case 'Movie':
                $schema['director'] = array(
                    '@type' => 'Person',
                    'name' => get_post_meta( $product_id, '_boss_product_movie_director', true ),
                );
                $schema['actor'] = array(
                    '@type' => 'Person',
                    'name' => get_post_meta( $product_id, '_boss_product_movie_actor', true ),
                );
                $schema['duration'] = get_post_meta( $product_id, '_boss_product_movie_duration', true );
                break;

            case 'MusicAlbum':
                $schema['byArtist'] = array(
                    '@type' => 'MusicGroup',
                    'name' => get_post_meta( $product_id, '_boss_product_music_artist', true ),
                );
                $schema['numTracks'] = get_post_meta( $product_id, '_boss_product_music_tracks', true );
                break;

            case 'SoftwareApplication':
                $schema['applicationCategory'] = get_post_meta( $product_id, '_boss_product_software_category', true );
                $schema['operatingSystem'] = get_post_meta( $product_id, '_boss_product_software_os', true );
                $schema['softwareVersion'] = get_post_meta( $product_id, '_boss_product_software_version', true );
                break;

            case 'Vehicle':
                $schema['vehicleIdentificationNumber'] = get_post_meta( $product_id, '_boss_product_vehicle_vin', true );
                $schema['vehicleEngine'] = array(
                    '@type' => 'EngineSpecification',
                    'name' => get_post_meta( $product_id, '_boss_product_vehicle_engine', true ),
                );
                $schema['fuelType'] = get_post_meta( $product_id, '_boss_product_vehicle_fuel', true );
                $schema['vehicleTransmission'] = get_post_meta( $product_id, '_boss_product_vehicle_transmission', true );
                break;
        }

        return $schema;
    }

    /**
     * Teste un schéma structuré avec l'outil de test de Google.
     *
     * @since    1.2.0
     * @param    array     $schema    Le schéma à tester.
     * @return   array|WP_Error       Les résultats du test ou une erreur.
     */
    private function test_schema_with_google( $schema ) {
        // URL de l'API de test de schéma de Google
        $api_url = 'https://search.google.com/test/rich-results/perform';

        // Préparer les données
        $data = array(
            'url' => home_url(),
            'html' => '<html><head><script type="application/ld+json">' . wp_json_encode( $schema ) . '</script></head><body></body></html>',
        );

        // Effectuer la requête
        $response = wp_remote_post( $api_url, array(
            'body' => $data,
            'timeout' => 30,
        ) );

        // Vérifier la réponse
        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $body = wp_remote_retrieve_body( $response );
        $result = json_decode( $body, true );

        if ( ! $result ) {
            return new WP_Error( 'invalid_response', __( 'Réponse invalide de l\'API de test de Google.', 'boss-seo' ) );
        }

        return $result;
    }
}