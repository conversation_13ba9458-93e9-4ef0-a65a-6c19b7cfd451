<?php
/**
 * Classe pour le tableau de bord e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour le tableau de bord e-commerce.
 *
 * Cette classe gère le tableau de bord e-commerce.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Ecommerce_Dashboard {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_ecommerce_dashboard_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_ecommerce_stats', array( $this, 'ajax_get_ecommerce_stats' ) );
        add_action( 'wp_ajax_boss_seo_get_product_distribution', array( $this, 'ajax_get_product_distribution' ) );
        add_action( 'wp_ajax_boss_seo_get_category_stats', array( $this, 'ajax_get_category_stats' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/dashboard',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_dashboard_data' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_ecommerce_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/product-distribution',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_product_distribution' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/category-stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_category_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les statistiques e-commerce.
     *
     * @since    1.2.0
     */
    public function ajax_get_ecommerce_stats() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les statistiques
        $stats = $this->get_ecommerce_stats_data();

        wp_send_json_success( array(
            'message' => __( 'Statistiques récupérées avec succès.', 'boss-seo' ),
            'stats'   => $stats,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer la distribution des produits.
     *
     * @since    1.2.0
     */
    public function ajax_get_product_distribution() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer la distribution des produits
        $distribution = $this->get_product_distribution_data();

        wp_send_json_success( array(
            'message'      => __( 'Distribution des produits récupérée avec succès.', 'boss-seo' ),
            'distribution' => $distribution,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les statistiques par catégorie.
     *
     * @since    1.2.0
     */
    public function ajax_get_category_stats() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les statistiques par catégorie
        $category_stats = $this->get_category_stats_data();

        wp_send_json_success( array(
            'message'        => __( 'Statistiques par catégorie récupérées avec succès.', 'boss-seo' ),
            'category_stats' => $category_stats,
        ) );
    }

    /**
     * Récupère les statistiques e-commerce via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_ecommerce_stats( $request ) {
        $stats = $this->get_ecommerce_stats_data();

        return rest_ensure_response( $stats );
    }

    /**
     * Récupère la distribution des produits via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_product_distribution( $request ) {
        $distribution = $this->get_product_distribution_data();

        return rest_ensure_response( $distribution );
    }

    /**
     * Récupère les statistiques par catégorie via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_category_stats( $request ) {
        $category_stats = $this->get_category_stats_data();

        return rest_ensure_response( $category_stats );
    }

    /**
     * Récupère les données du tableau de bord e-commerce via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_dashboard_data( $request ) {
        try {
            // Vérifier si WooCommerce est installé et activé
            if ( ! post_type_exists( 'product' ) || ! function_exists( 'wc_get_product' ) ) {
                // Si WooCommerce n'est pas disponible, retourner des données fictives
                return rest_ensure_response( array(
                    'stats' => array(
                        'total_products'        => 33,
                        'optimized_products'    => 17,
                        'critical_products'     => 4,
                        'to_improve_products'   => 12,
                        'optimized_percentage'  => 52,
                        'critical_percentage'   => 12,
                        'to_improve_percentage' => 36,
                    ),
                    'distribution' => array(
                        'excellent'    => 5,
                        'good'         => 12,
                        'average'      => 8,
                        'poor'         => 3,
                        'critical'     => 1,
                        'not_analyzed' => 4,
                    ),
                    'category_stats' => array(
                        array(
                            'id'               => 1,
                            'name'             => __( 'Catégorie exemple 1', 'boss-seo' ),
                            'total_products'   => 5,
                            'analyzed_products' => 3,
                            'average_score'    => 75,
                        ),
                        array(
                            'id'               => 2,
                            'name'             => __( 'Catégorie exemple 2', 'boss-seo' ),
                            'total_products'   => 8,
                            'analyzed_products' => 6,
                            'average_score'    => 82,
                        ),
                        array(
                            'id'               => 3,
                            'name'             => __( 'Catégorie exemple 3', 'boss-seo' ),
                            'total_products'   => 3,
                            'analyzed_products' => 2,
                            'average_score'    => 65,
                        ),
                    ),
                    'recent_products' => array(
                        array(
                            'id'          => 1,
                            'title'       => __( 'Exemple de produit 1', 'boss-seo' ),
                            'price'       => '19.99',
                            'regular_price' => '24.99',
                            'sale_price'  => '19.99',
                            'sku'         => 'PROD001',
                            'stock_status' => 'instock',
                            'categories'  => array( __( 'Catégorie exemple', 'boss-seo' ) ),
                            'image'       => '',
                            'permalink'   => '#',
                            'date'        => date( 'Y-m-d' ),
                        ),
                        array(
                            'id'          => 2,
                            'title'       => __( 'Exemple de produit 2', 'boss-seo' ),
                            'price'       => '29.99',
                            'regular_price' => '29.99',
                            'sale_price'  => '',
                            'sku'         => 'PROD002',
                            'stock_status' => 'instock',
                            'categories'  => array( __( 'Catégorie exemple', 'boss-seo' ) ),
                            'image'       => '',
                            'permalink'   => '#',
                            'date'        => date( 'Y-m-d' ),
                        ),
                    ),
                    'tasks' => $this->get_tasks(),
                ) );
            }

            // Si WooCommerce est disponible, récupérer les données réelles
            // Récupérer les statistiques e-commerce
            $stats = $this->get_ecommerce_stats_data();

            // Récupérer la distribution des produits
            $distribution = $this->get_product_distribution_data();

            // Récupérer les statistiques par catégorie
            $category_stats = $this->get_category_stats_data();

            // Récupérer les produits récents
            $recent_products = $this->get_recent_products();

            // Récupérer les tâches
            $tasks = $this->get_tasks();

            return rest_ensure_response( array(
                'stats'            => $stats,
                'distribution'     => $distribution,
                'category_stats'   => $category_stats,
                'recent_products'  => $recent_products,
                'tasks'            => $tasks,
            ) );
        } catch (Exception $e) {
            // Log l'erreur
            error_log('Erreur dans get_dashboard_data: ' . $e->getMessage());

            // Retourner une réponse d'erreur
            return new WP_Error(
                'boss_seo_dashboard_error',
                __('Une erreur est survenue lors de la récupération des données du tableau de bord.', 'boss-seo'),
                array('status' => 500, 'error' => $e->getMessage())
            );
        }
    }

    /**
     * Récupère les statistiques e-commerce.
     *
     * @since    1.2.0
     * @return   array    Les statistiques e-commerce.
     */
    private function get_ecommerce_stats_data() {
        // Vérifier si WooCommerce est installé et activé
        if ( ! post_type_exists( 'product' ) ) {
            // Si WooCommerce n'est pas disponible, retourner des données fictives
            return array(
                'total_products'        => 33,
                'optimized_products'    => 17,
                'critical_products'     => 4,
                'to_improve_products'   => 12,
                'optimized_percentage'  => 52,
                'critical_percentage'   => 12,
                'to_improve_percentage' => 36,
            );
        }

        // Récupérer tous les produits
        $products = $this->get_all_products();

        // Initialiser les compteurs
        $total_products = count( $products );
        $optimized_products = 0;
        $critical_products = 0;
        $to_improve_products = 0;

        // Analyser chaque produit
        foreach ( $products as $product ) {
            $analysis = get_post_meta( $product->ID, '_boss_seo_analysis', true );

            if ( ! empty( $analysis ) && isset( $analysis['score'] ) ) {
                $score = $analysis['score'];

                if ( $score >= 80 ) {
                    $optimized_products++;
                } elseif ( $score < 50 ) {
                    $critical_products++;
                } else {
                    $to_improve_products++;
                }
            } else {
                // Si le produit n'a pas été analysé, le considérer comme à améliorer
                $to_improve_products++;
            }
        }

        // Calculer les pourcentages
        $optimized_percentage = $total_products > 0 ? round( ( $optimized_products / $total_products ) * 100 ) : 0;
        $critical_percentage = $total_products > 0 ? round( ( $critical_products / $total_products ) * 100 ) : 0;
        $to_improve_percentage = $total_products > 0 ? round( ( $to_improve_products / $total_products ) * 100 ) : 0;

        return array(
            'total_products'        => $total_products,
            'optimized_products'    => $optimized_products,
            'critical_products'     => $critical_products,
            'to_improve_products'   => $to_improve_products,
            'optimized_percentage'  => $optimized_percentage,
            'critical_percentage'   => $critical_percentage,
            'to_improve_percentage' => $to_improve_percentage,
        );
    }

    /**
     * Récupère la distribution des produits.
     *
     * @since    1.2.0
     * @return   array    La distribution des produits.
     */
    private function get_product_distribution_data() {
        // Vérifier si WooCommerce est installé et activé
        if ( ! post_type_exists( 'product' ) ) {
            // Si WooCommerce n'est pas disponible, retourner des données fictives
            return array(
                'excellent'    => 5,
                'good'         => 12,
                'average'      => 8,
                'poor'         => 3,
                'critical'     => 1,
                'not_analyzed' => 4,
            );
        }

        // Récupérer tous les produits
        $products = $this->get_all_products();

        // Initialiser les compteurs
        $distribution = array(
            'excellent' => 0,
            'good'      => 0,
            'average'   => 0,
            'poor'      => 0,
            'critical'  => 0,
            'not_analyzed' => 0,
        );

        // Analyser chaque produit
        foreach ( $products as $product ) {
            $analysis = get_post_meta( $product->ID, '_boss_seo_analysis', true );

            if ( ! empty( $analysis ) && isset( $analysis['score'] ) ) {
                $score = $analysis['score'];

                if ( $score >= 90 ) {
                    $distribution['excellent']++;
                } elseif ( $score >= 80 ) {
                    $distribution['good']++;
                } elseif ( $score >= 60 ) {
                    $distribution['average']++;
                } elseif ( $score >= 40 ) {
                    $distribution['poor']++;
                } else {
                    $distribution['critical']++;
                }
            } else {
                $distribution['not_analyzed']++;
            }
        }

        return $distribution;
    }

    /**
     * Récupère les statistiques par catégorie.
     *
     * @since    1.2.0
     * @return   array    Les statistiques par catégorie.
     */
    private function get_category_stats_data() {
        // Vérifier si WooCommerce est installé et activé
        if ( ! taxonomy_exists( 'product_cat' ) ) {
            // Si WooCommerce n'est pas disponible, retourner des données fictives
            return array(
                array(
                    'id'               => 1,
                    'name'             => __( 'Catégorie exemple 1', 'boss-seo' ),
                    'total_products'   => 5,
                    'analyzed_products' => 3,
                    'average_score'    => 75,
                ),
                array(
                    'id'               => 2,
                    'name'             => __( 'Catégorie exemple 2', 'boss-seo' ),
                    'total_products'   => 8,
                    'analyzed_products' => 6,
                    'average_score'    => 82,
                ),
                array(
                    'id'               => 3,
                    'name'             => __( 'Catégorie exemple 3', 'boss-seo' ),
                    'total_products'   => 3,
                    'analyzed_products' => 2,
                    'average_score'    => 65,
                ),
            );
        }

        // Récupérer toutes les catégories de produits
        $categories = get_terms( array(
            'taxonomy'   => 'product_cat',
            'hide_empty' => false,
        ) );

        if ( is_wp_error( $categories ) ) {
            return array();
        }

        $category_stats = array();

        foreach ( $categories as $category ) {
            // Récupérer les produits de cette catégorie
            $products = get_posts( array(
                'post_type'      => 'product',
                'posts_per_page' => -1,
                'tax_query'      => array(
                    array(
                        'taxonomy' => 'product_cat',
                        'field'    => 'term_id',
                        'terms'    => $category->term_id,
                    ),
                ),
            ) );

            // Initialiser les compteurs
            $total_products = count( $products );
            $total_score = 0;
            $analyzed_products = 0;

            // Analyser chaque produit
            foreach ( $products as $product ) {
                $analysis = get_post_meta( $product->ID, '_boss_seo_analysis', true );

                if ( ! empty( $analysis ) && isset( $analysis['score'] ) ) {
                    $total_score += $analysis['score'];
                    $analyzed_products++;
                }
            }

            // Calculer le score moyen
            $average_score = $analyzed_products > 0 ? round( $total_score / $analyzed_products ) : 0;

            $category_stats[] = array(
                'id'             => $category->term_id,
                'name'           => $category->name,
                'total_products' => $total_products,
                'analyzed_products' => $analyzed_products,
                'average_score'  => $average_score,
            );
        }

        return $category_stats;
    }

    /**
     * Récupère tous les produits.
     *
     * @since    1.2.0
     * @return   array    Les produits.
     */
    private function get_all_products() {
        // Vérifier si WooCommerce est installé et activé
        if ( ! post_type_exists( 'product' ) ) {
            // Si WooCommerce n'est pas disponible, retourner un tableau vide
            return array();
        }

        return get_posts( array(
            'post_type'      => 'product',
            'posts_per_page' => -1,
        ) );
    }

    /**
     * Récupère les produits récents.
     *
     * @since    1.2.0
     * @return   array    Les produits récents.
     */
    private function get_recent_products() {
        $products = array();

        // Vérifier si WooCommerce est installé et activé
        if ( ! function_exists( 'wc_get_product' ) ) {
            // Si WooCommerce n'est pas disponible, retourner un tableau vide ou des données fictives
            return array(
                array(
                    'id'          => 1,
                    'title'       => __( 'Exemple de produit 1', 'boss-seo' ),
                    'price'       => '19.99',
                    'regular_price' => '24.99',
                    'sale_price'  => '19.99',
                    'sku'         => 'PROD001',
                    'stock_status' => 'instock',
                    'categories'  => array( __( 'Catégorie exemple', 'boss-seo' ) ),
                    'image'       => '',
                    'permalink'   => '#',
                    'date'        => date( 'Y-m-d' ),
                ),
                array(
                    'id'          => 2,
                    'title'       => __( 'Exemple de produit 2', 'boss-seo' ),
                    'price'       => '29.99',
                    'regular_price' => '29.99',
                    'sale_price'  => '',
                    'sku'         => 'PROD002',
                    'stock_status' => 'instock',
                    'categories'  => array( __( 'Catégorie exemple', 'boss-seo' ) ),
                    'image'       => '',
                    'permalink'   => '#',
                    'date'        => date( 'Y-m-d' ),
                ),
            );
        }

        $args = array(
            'post_type'      => 'product',
            'post_status'    => 'publish',
            'posts_per_page' => 5,
            'orderby'        => 'date',
            'order'          => 'DESC',
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();
                $product_id = get_the_ID();
                $product = wc_get_product( $product_id );

                if ( $product ) {
                    $products[] = array(
                        'id'          => $product_id,
                        'title'       => get_the_title(),
                        'price'       => $product->get_price(),
                        'regular_price' => $product->get_regular_price(),
                        'sale_price'  => $product->get_sale_price(),
                        'sku'         => $product->get_sku(),
                        'stock_status' => $product->get_stock_status(),
                        'categories'  => wp_get_post_terms( $product_id, 'product_cat', array( 'fields' => 'names' ) ),
                        'image'       => get_the_post_thumbnail_url( $product_id, 'thumbnail' ),
                        'permalink'   => get_permalink( $product_id ),
                        'date'        => get_the_date( 'Y-m-d' ),
                    );
                }
            }
        }

        wp_reset_postdata();

        return $products;
    }

    /**
     * Récupère les tâches.
     *
     * @since    1.2.0
     * @return   array    Les tâches.
     */
    private function get_tasks() {
        return array(
            array(
                'id'          => 1,
                'title'       => __( 'Optimiser les titres des produits', 'boss-seo' ),
                'description' => __( 'Assurez-vous que tous les titres de produits sont optimisés pour le référencement.', 'boss-seo' ),
                'priority'    => 'high',
                'completed'   => false,
            ),
            array(
                'id'          => 2,
                'title'       => __( 'Ajouter des descriptions détaillées', 'boss-seo' ),
                'description' => __( 'Ajoutez des descriptions détaillées à tous vos produits.', 'boss-seo' ),
                'priority'    => 'medium',
                'completed'   => false,
            ),
            array(
                'id'          => 3,
                'title'       => __( 'Configurer les schémas de produits', 'boss-seo' ),
                'description' => __( 'Configurez les schémas de produits pour améliorer votre référencement.', 'boss-seo' ),
                'priority'    => 'medium',
                'completed'   => false,
            ),
            array(
                'id'          => 4,
                'title'       => __( 'Optimiser les images des produits', 'boss-seo' ),
                'description' => __( 'Optimisez les images de vos produits pour améliorer les performances.', 'boss-seo' ),
                'priority'    => 'low',
                'completed'   => false,
            ),
            array(
                'id'          => 5,
                'title'       => __( 'Configurer Google Shopping', 'boss-seo' ),
                'description' => __( 'Configurez Google Shopping pour promouvoir vos produits.', 'boss-seo' ),
                'priority'    => 'low',
                'completed'   => false,
            ),
        );
    }
}
