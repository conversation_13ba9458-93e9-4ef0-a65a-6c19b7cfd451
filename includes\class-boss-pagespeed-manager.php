<?php
/**
 * Gestionnaire PageSpeed Insights pour Boss SEO
 *
 * @package Boss_SEO
 * @subpackage Technical_Analysis
 * @since 1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour gérer les interactions avec l'API PageSpeed Insights
 */
class Boss_PageSpeed_Manager {

    /**
     * Clé API PageSpeed Insights
     *
     * @var string
     */
    private $api_key;

    /**
     * Durée de cache en secondes (1 heure par défaut)
     *
     * @var int
     */
    private $cache_duration = 3600;

    /**
     * Nombre de tentatives en cas d'échec
     *
     * @var int
     */
    private $retry_attempts = 3;

    /**
     * Timeout pour les requêtes API
     *
     * @var int
     */
    private $timeout = 45;

    /**
     * Constructeur
     *
     * @throws Exception Si la clé API n'est pas configurée
     */
    public function __construct() {
        $this->api_key = $this->get_validated_api_key();
    }

    /**
     * Récupère et valide la clé API
     *
     * @return string Clé API validée
     * @throws Exception Si la clé API n'est pas trouvée
     */
    private function get_validated_api_key() {
        // Essayer d'abord la nouvelle structure
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $api_key = isset( $external_services['google_pagespeed']['api_key'] ) ? $external_services['google_pagespeed']['api_key'] : '';

        // Fallback vers l'ancienne structure
        if ( empty( $api_key ) ) {
            $api_key = get_option( 'boss_optimizer_pagespeed_api_key', '' );
        }

        if ( empty( $api_key ) ) {
            throw new Exception( __( 'Clé API PageSpeed Insights manquante. Veuillez la configurer dans les paramètres.', 'boss-seo' ) );
        }

        return $api_key;
    }

    /**
     * Analyse une URL avec PageSpeed Insights
     *
     * @param string $url URL à analyser
     * @param string $strategy Stratégie (mobile ou desktop)
     * @param array $categories Catégories à analyser
     * @return array|false Résultats de l'analyse ou false en cas d'erreur
     */
    public function analyze_url( $url, $strategy = 'mobile', $categories = array( 'performance', 'seo', 'accessibility', 'best-practices' ) ) {
        // Vérifier le cache
        $cache_key = $this->get_cache_key( $url, $strategy, $categories );
        $cached_result = get_transient( $cache_key );

        if ( $cached_result !== false ) {
            return $cached_result;
        }

        // Effectuer l'analyse avec retry
        $result = $this->perform_analysis_with_retry( $url, $strategy, $categories );

        if ( $result !== false ) {
            // Mettre en cache le résultat
            set_transient( $cache_key, $result, $this->cache_duration );
        }

        return $result;
    }

    /**
     * Génère une clé de cache unique
     *
     * @param string $url URL
     * @param string $strategy Stratégie
     * @param array $categories Catégories
     * @return string Clé de cache
     */
    private function get_cache_key( $url, $strategy, $categories ) {
        $key_data = array(
            'url' => $url,
            'strategy' => $strategy,
            'categories' => $categories,
            'version' => '1.2.0' // Version pour invalider le cache si nécessaire
        );

        return 'boss_pagespeed_' . md5( serialize( $key_data ) );
    }

    /**
     * Effectue l'analyse avec système de retry
     *
     * @param string $url URL à analyser
     * @param string $strategy Stratégie
     * @param array $categories Catégories
     * @return array|false Résultats ou false
     */
    private function perform_analysis_with_retry( $url, $strategy, $categories ) {
        $last_error = null;

        for ( $attempt = 1; $attempt <= $this->retry_attempts; $attempt++ ) {
            try {
                $result = $this->call_pagespeed_api( $url, $strategy, $categories );

                if ( $result !== false ) {
                    return $result;
                }
            } catch ( Exception $e ) {
                $last_error = $e->getMessage();
                error_log( sprintf( 'Boss SEO PageSpeed API attempt %d failed: %s', $attempt, $last_error ) );
            }

            // Attendre avant la prochaine tentative (backoff exponentiel)
            if ( $attempt < $this->retry_attempts ) {
                sleep( pow( 2, $attempt - 1 ) );
            }
        }

        // Enregistrer l'erreur finale
        if ( $last_error ) {
            error_log( sprintf( 'Boss SEO PageSpeed API failed after %d attempts. Last error: %s', $this->retry_attempts, $last_error ) );
        }

        return false;
    }

    /**
     * Appelle l'API PageSpeed Insights
     *
     * @param string $url URL à analyser
     * @param string $strategy Stratégie
     * @param array $categories Catégories
     * @return array|false Résultats ou false
     * @throws Exception En cas d'erreur API
     */
    private function call_pagespeed_api( $url, $strategy, $categories ) {
        // Valider les paramètres
        if ( empty( $url ) ) {
            throw new Exception( __( 'URL manquante pour l\'analyse PageSpeed', 'boss-seo' ) );
        }

        if ( empty( $this->api_key ) ) {
            throw new Exception( __( 'Clé API PageSpeed Insights manquante', 'boss-seo' ) );
        }

        // Construire l'URL de l'API
        $api_url = add_query_arg(
            array(
                'url' => urlencode( $url ),
                'key' => $this->api_key,
                'strategy' => $strategy,
                'category' => implode( ',', $categories ),
                'locale' => 'fr_FR',
            ),
            'https://www.googleapis.com/pagespeedonline/v5/runPagespeed'
        );

        // Log de debug (seulement si WP_DEBUG est activé)
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( sprintf(
                'Boss SEO PageSpeed API Call: URL=%s, Strategy=%s, Categories=%s',
                $url,
                $strategy,
                implode( ',', $categories )
            ) );
        }

        // Effectuer la requête avec retry
        $attempts = 0;
        $last_error = '';

        while ( $attempts < $this->retry_attempts ) {
            $attempts++;

            $response = wp_remote_get( $api_url, array(
                'timeout' => $this->timeout,
                'headers' => array(
                    'User-Agent' => 'Boss SEO WordPress Plugin/1.2.0'
                )
            ) );

            // Vérifier les erreurs de réseau
            if ( is_wp_error( $response ) ) {
                $last_error = sprintf(
                    __( 'Tentative %d/%d - Erreur réseau: %s', 'boss-seo' ),
                    $attempts,
                    $this->retry_attempts,
                    $response->get_error_message()
                );

                if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                    error_log( 'Boss SEO PageSpeed Error: ' . $last_error );
                }

                if ( $attempts < $this->retry_attempts ) {
                    sleep( 2 ); // Attendre 2 secondes avant de réessayer
                    continue;
                }

                throw new Exception( $last_error );
            }

            // Vérifier le code de statut HTTP
            $status_code = wp_remote_retrieve_response_code( $response );
            $body = wp_remote_retrieve_body( $response );

            if ( $status_code !== 200 ) {
                $error_data = json_decode( $body, true );
                $error_message = isset( $error_data['error']['message'] ) ? $error_data['error']['message'] : sprintf( __( 'Erreur HTTP %d', 'boss-seo' ), $status_code );

                // Erreurs spécifiques qui ne nécessitent pas de retry
                if ( $status_code === 400 || $status_code === 403 ) {
                    throw new Exception( $error_message );
                }

                $last_error = sprintf(
                    __( 'Tentative %d/%d - %s', 'boss-seo' ),
                    $attempts,
                    $this->retry_attempts,
                    $error_message
                );

                if ( $attempts < $this->retry_attempts ) {
                    sleep( 2 );
                    continue;
                }

                throw new Exception( $last_error );
            }

            // Analyser la réponse JSON
            $data = json_decode( $body, true );

            if ( json_last_error() !== JSON_ERROR_NONE ) {
                $last_error = sprintf(
                    __( 'Tentative %d/%d - Erreur JSON: %s', 'boss-seo' ),
                    $attempts,
                    $this->retry_attempts,
                    json_last_error_msg()
                );

                if ( $attempts < $this->retry_attempts ) {
                    sleep( 2 );
                    continue;
                }

                throw new Exception( $last_error );
            }

            // Vérifier la structure de la réponse
            if ( empty( $data ) || ! isset( $data['lighthouseResult'] ) ) {
                $last_error = sprintf(
                    __( 'Tentative %d/%d - Réponse API PageSpeed invalide ou incomplète', 'boss-seo' ),
                    $attempts,
                    $this->retry_attempts
                );

                if ( $attempts < $this->retry_attempts ) {
                    sleep( 2 );
                    continue;
                }

                throw new Exception( $last_error );
            }

            // Succès ! Traiter et retourner les données
            if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                error_log( sprintf(
                    'Boss SEO PageSpeed Success: URL=%s, Attempts=%d, Performance Score=%s',
                    $url,
                    $attempts,
                    isset( $data['lighthouseResult']['categories']['performance']['score'] ) ?
                        round( $data['lighthouseResult']['categories']['performance']['score'] * 100 ) : 'N/A'
                ) );
            }

            return $this->process_pagespeed_response( $data, $url, $strategy );
        }

        // Si on arrive ici, toutes les tentatives ont échoué
        throw new Exception( $last_error ?: __( 'Échec de toutes les tentatives d\'appel API PageSpeed', 'boss-seo' ) );
    }

    /**
     * Traite la réponse de l'API PageSpeed
     *
     * @param array $data Données de l'API
     * @param string $url URL analysée
     * @param string $strategy Stratégie utilisée
     * @return array Données traitées
     */
    private function process_pagespeed_response( $data, $url, $strategy ) {
        $lighthouse_result = $data['lighthouseResult'];
        $audits = $lighthouse_result['audits'];
        $categories = isset( $lighthouse_result['categories'] ) ? $lighthouse_result['categories'] : array();

        // Extraire les scores par catégorie
        $scores = array();
        foreach ( $categories as $category_id => $category ) {
            $scores[ $category_id ] = array(
                'title' => $category['title'],
                'score' => round( $category['score'] * 100 ),
                'description' => isset( $category['description'] ) ? $category['description'] : '',
            );
        }

        // Extraire les métriques Core Web Vitals améliorées
        $core_web_vitals = $this->get_enhanced_core_web_vitals( $audits );

        // Extraire les opportunités d'amélioration
        $opportunities = $this->extract_opportunities( $audits );

        // Extraire les diagnostics
        $diagnostics = $this->extract_diagnostics( $audits );

        // Extraire les audits réussis
        $passed_audits = $this->extract_passed_audits( $audits );

        return array(
            'url' => $url,
            'strategy' => $strategy,
            'date' => current_time( 'mysql' ),
            'scores' => $scores,
            'core_web_vitals' => $core_web_vitals,
            'opportunities' => $opportunities,
            'diagnostics' => $diagnostics,
            'passed_audits' => $passed_audits,
            'raw_data' => $lighthouse_result, // Garder les données brutes pour analyse avancée
        );
    }

    /**
     * Extrait les métriques Core Web Vitals améliorées
     *
     * @param array $audits Audits Lighthouse
     * @return array Métriques formatées
     */
    private function get_enhanced_core_web_vitals( $audits ) {
        $metrics = array();

        // LCP - Largest Contentful Paint
        if ( isset( $audits['largest-contentful-paint'] ) ) {
            $lcp_value = $audits['largest-contentful-paint']['numericValue'] / 1000;
            $metrics['lcp'] = array(
                'name' => 'LCP',
                'value' => round( $lcp_value, 1 ),
                'unit' => 's',
                'status' => $this->get_metric_status( 'lcp', $lcp_value ),
                'description' => __( 'Largest Contentful Paint', 'boss-seo' ),
                'threshold' => array( 'good' => 2.5, 'poor' => 4.0 ),
                'score' => isset( $audits['largest-contentful-paint']['score'] ) ? round( $audits['largest-contentful-paint']['score'] * 100 ) : 0,
            );
        }

        // INP - Interaction to Next Paint (remplace FID)
        if ( isset( $audits['interaction-to-next-paint'] ) ) {
            $inp_value = $audits['interaction-to-next-paint']['numericValue'];
            $metrics['inp'] = array(
                'name' => 'INP',
                'value' => round( $inp_value ),
                'unit' => 'ms',
                'status' => $this->get_metric_status( 'inp', $inp_value ),
                'description' => __( 'Interaction to Next Paint', 'boss-seo' ),
                'threshold' => array( 'good' => 200, 'poor' => 500 ),
                'score' => isset( $audits['interaction-to-next-paint']['score'] ) ? round( $audits['interaction-to-next-paint']['score'] * 100 ) : 0,
            );
        } else {
            // Fallback vers FID si INP n'est pas disponible
            if ( isset( $audits['max-potential-fid'] ) ) {
                $fid_value = $audits['max-potential-fid']['numericValue'];
                $metrics['fid'] = array(
                    'name' => 'FID',
                    'value' => round( $fid_value ),
                    'unit' => 'ms',
                    'status' => $this->get_metric_status( 'fid', $fid_value ),
                    'description' => __( 'First Input Delay (Max Potential)', 'boss-seo' ),
                    'threshold' => array( 'good' => 100, 'poor' => 300 ),
                    'score' => isset( $audits['max-potential-fid']['score'] ) ? round( $audits['max-potential-fid']['score'] * 100 ) : 0,
                );
            }
        }

        // CLS - Cumulative Layout Shift
        if ( isset( $audits['cumulative-layout-shift'] ) ) {
            $cls_value = $audits['cumulative-layout-shift']['numericValue'];
            $metrics['cls'] = array(
                'name' => 'CLS',
                'value' => round( $cls_value, 3 ),
                'unit' => '',
                'status' => $this->get_metric_status( 'cls', $cls_value ),
                'description' => __( 'Cumulative Layout Shift', 'boss-seo' ),
                'threshold' => array( 'good' => 0.1, 'poor' => 0.25 ),
                'score' => isset( $audits['cumulative-layout-shift']['score'] ) ? round( $audits['cumulative-layout-shift']['score'] * 100 ) : 0,
            );
        }

        // TTFB - Time to First Byte
        if ( isset( $audits['server-response-time'] ) ) {
            $ttfb_value = $audits['server-response-time']['numericValue'];
            $metrics['ttfb'] = array(
                'name' => 'TTFB',
                'value' => round( $ttfb_value ),
                'unit' => 'ms',
                'status' => $this->get_metric_status( 'ttfb', $ttfb_value ),
                'description' => __( 'Time to First Byte', 'boss-seo' ),
                'threshold' => array( 'good' => 800, 'poor' => 1800 ),
                'score' => isset( $audits['server-response-time']['score'] ) ? round( $audits['server-response-time']['score'] * 100 ) : 0,
            );
        }

        // FCP - First Contentful Paint
        if ( isset( $audits['first-contentful-paint'] ) ) {
            $fcp_value = $audits['first-contentful-paint']['numericValue'] / 1000;
            $metrics['fcp'] = array(
                'name' => 'FCP',
                'value' => round( $fcp_value, 1 ),
                'unit' => 's',
                'status' => $this->get_metric_status( 'fcp', $fcp_value ),
                'description' => __( 'First Contentful Paint', 'boss-seo' ),
                'threshold' => array( 'good' => 1.8, 'poor' => 3.0 ),
                'score' => isset( $audits['first-contentful-paint']['score'] ) ? round( $audits['first-contentful-paint']['score'] * 100 ) : 0,
            );
        }

        return $metrics;
    }

    /**
     * Détermine le statut d'une métrique avec seuils mis à jour
     *
     * @param string $metric Nom de la métrique
     * @param float $value Valeur de la métrique
     * @return string Statut (good, needs-improvement, poor)
     */
    private function get_metric_status( $metric, $value ) {
        $thresholds = array(
            'lcp' => array( 'good' => 2.5, 'poor' => 4.0 ),
            'inp' => array( 'good' => 200, 'poor' => 500 ),
            'fid' => array( 'good' => 100, 'poor' => 300 ),
            'cls' => array( 'good' => 0.1, 'poor' => 0.25 ),
            'ttfb' => array( 'good' => 800, 'poor' => 1800 ),
            'fcp' => array( 'good' => 1.8, 'poor' => 3.0 ),
        );

        if ( ! isset( $thresholds[ $metric ] ) ) {
            return 'unknown';
        }

        $threshold = $thresholds[ $metric ];

        if ( $value <= $threshold['good'] ) {
            return 'good';
        } elseif ( $value <= $threshold['poor'] ) {
            return 'needs-improvement';
        } else {
            return 'poor';
        }
    }

    /**
     * Extrait les opportunités d'amélioration
     *
     * @param array $audits Audits Lighthouse
     * @return array Opportunités formatées
     */
    private function extract_opportunities( $audits ) {
        $opportunities = array();

        foreach ( $audits as $audit_id => $audit ) {
            if ( isset( $audit['details'] ) &&
                 isset( $audit['details']['type'] ) &&
                 $audit['details']['type'] === 'opportunity' &&
                 isset( $audit['score'] ) &&
                 $audit['score'] < 1 ) {

                $opportunities[ $audit_id ] = array(
                    'title' => $audit['title'],
                    'description' => $audit['description'],
                    'score' => round( $audit['score'] * 100 ),
                    'display_value' => isset( $audit['displayValue'] ) ? $audit['displayValue'] : '',
                    'savings_ms' => isset( $audit['details']['overallSavingsMs'] ) ? $audit['details']['overallSavingsMs'] : 0,
                    'savings_bytes' => isset( $audit['details']['overallSavingsBytes'] ) ? $audit['details']['overallSavingsBytes'] : 0,
                    'items' => isset( $audit['details']['items'] ) ? array_slice( $audit['details']['items'], 0, 5 ) : array(),
                    'impact' => $this->calculate_opportunity_impact( $audit ),
                    'difficulty' => $this->get_opportunity_difficulty( $audit_id ),
                );
            }
        }

        // Trier par impact (économies potentielles)
        uasort( $opportunities, function( $a, $b ) {
            return $b['savings_ms'] - $a['savings_ms'];
        });

        return $opportunities;
    }

    /**
     * Calcule l'impact d'une opportunité
     *
     * @param array $audit Audit Lighthouse
     * @return string Impact (high, medium, low)
     */
    private function calculate_opportunity_impact( $audit ) {
        $savings_ms = isset( $audit['details']['overallSavingsMs'] ) ? $audit['details']['overallSavingsMs'] : 0;
        $savings_bytes = isset( $audit['details']['overallSavingsBytes'] ) ? $audit['details']['overallSavingsBytes'] : 0;
        $score = isset( $audit['score'] ) ? $audit['score'] : 1;

        if ( $savings_ms > 1000 || $savings_bytes > 500000 || $score < 0.3 ) {
            return 'high';
        } elseif ( $savings_ms > 500 || $savings_bytes > 100000 || $score < 0.6 ) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Détermine la difficulté d'implémentation d'une opportunité
     *
     * @param string $audit_id ID de l'audit
     * @return string Difficulté (low, medium, high)
     */
    private function get_opportunity_difficulty( $audit_id ) {
        $easy_fixes = array(
            'uses-responsive-images',
            'offscreen-images',
            'uses-rel-preconnect',
            'uses-rel-preload',
            'efficient-animated-content',
            'uses-optimized-images',
            'uses-webp-images',
            'uses-text-compression',
        );

        $medium_fixes = array(
            'render-blocking-resources',
            'unminified-css',
            'unminified-javascript',
            'unused-css-rules',
            'legacy-javascript',
            'uses-long-cache-ttl',
            'total-byte-weight',
        );

        $hard_fixes = array(
            'unused-javascript',
            'third-party-summary',
            'third-party-facades',
            'server-response-time',
            'redirects',
            'critical-request-chains',
            'dom-size',
            'bootup-time',
        );

        if ( in_array( $audit_id, $easy_fixes ) ) {
            return 'low';
        } elseif ( in_array( $audit_id, $medium_fixes ) ) {
            return 'medium';
        } elseif ( in_array( $audit_id, $hard_fixes ) ) {
            return 'high';
        } else {
            return 'medium';
        }
    }

    /**
     * Extrait les diagnostics
     *
     * @param array $audits Audits Lighthouse
     * @return array Diagnostics formatés
     */
    private function extract_diagnostics( $audits ) {
        $diagnostics = array();

        foreach ( $audits as $audit_id => $audit ) {
            if ( isset( $audit['details'] ) &&
                 isset( $audit['details']['type'] ) &&
                 $audit['details']['type'] === 'diagnostic' ) {

                $diagnostics[ $audit_id ] = array(
                    'title' => $audit['title'],
                    'description' => $audit['description'],
                    'score' => isset( $audit['score'] ) ? round( $audit['score'] * 100 ) : null,
                    'display_value' => isset( $audit['displayValue'] ) ? $audit['displayValue'] : '',
                    'items' => isset( $audit['details']['items'] ) ? array_slice( $audit['details']['items'], 0, 3 ) : array(),
                );
            }
        }

        return $diagnostics;
    }

    /**
     * Extrait les audits réussis
     *
     * @param array $audits Audits Lighthouse
     * @return array Audits réussis
     */
    private function extract_passed_audits( $audits ) {
        $passed = array();

        foreach ( $audits as $audit_id => $audit ) {
            if ( isset( $audit['score'] ) && $audit['score'] === 1 ) {
                $passed[ $audit_id ] = array(
                    'title' => $audit['title'],
                    'description' => $audit['description'],
                );
            }
        }

        return $passed;
    }

    /**
     * Vide le cache PageSpeed
     *
     * @param string $url URL spécifique (optionnel)
     */
    public function clear_cache( $url = null ) {
        global $wpdb;

        if ( $url ) {
            // Supprimer le cache pour une URL spécifique
            $pattern = 'boss_pagespeed_' . md5( $url ) . '%';
            $wpdb->query( $wpdb->prepare( "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s", $pattern ) );
        } else {
            // Supprimer tout le cache PageSpeed
            $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE 'boss_pagespeed_%'" );
        }
    }

    /**
     * Teste la validité de la clé API
     *
     * @return array Résultat du test
     */
    public function test_api_key() {
        try {
            // Tester avec une URL publique d'abord
            $test_urls = array(
                'https://www.google.com',  // URL publique garantie
                home_url(),                // URL du site
            );

            $last_error = '';

            foreach ( $test_urls as $test_url ) {
                try {
                    $result = $this->call_pagespeed_api( $test_url, 'mobile', array( 'performance' ) );

                    if ( $result ) {
                        return array(
                            'success' => true,
                            'message' => sprintf(
                                __( 'Clé API PageSpeed Insights valide (testée avec %s)', 'boss-seo' ),
                                $test_url
                            ),
                            'data' => $result,
                            'debug' => array(
                                'test_url' => $test_url,
                                'api_key_length' => strlen( $this->api_key ),
                                'cache_duration' => $this->cache_duration,
                                'timeout' => $this->timeout
                            )
                        );
                    }
                } catch ( Exception $e ) {
                    $last_error = sprintf(
                        __( 'Erreur avec %s: %s', 'boss-seo' ),
                        $test_url,
                        $e->getMessage()
                    );
                    continue;
                }
            }

            // Aucune URL n'a fonctionné
            throw new Exception( $last_error ?: __( 'Impossible de tester la clé API', 'boss-seo' ) );

        } catch ( Exception $e ) {
            return array(
                'success' => false,
                'message' => $e->getMessage(),
                'debug' => array(
                    'api_key_configured' => ! empty( $this->api_key ),
                    'api_key_length' => strlen( $this->api_key ),
                    'tested_urls' => isset( $test_urls ) ? $test_urls : array(),
                    'error_details' => $e->getMessage()
                )
            );
        }
    }
}
