<?php
/**
 * Classe principale pour gérer les paramètres du plugin Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.0.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe principale pour gérer les paramètres du plugin Boss SEO.
 *
 * Cette classe gère l'initialisation des différents modules de paramètres,
 * l'enregistrement des points de terminaison REST API, et l'intégration
 * avec l'interface utilisateur.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Settings {

    /**
     * Le nom du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de paramètres généraux.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Boss_General_Settings    $general_settings    Instance de la classe de paramètres généraux.
     */
    protected $general_settings;

    /**
     * Instance de la classe de paramètres avancés.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Boss_Advanced_Settings    $advanced_settings    Instance de la classe de paramètres avancés.
     */
    protected $advanced_settings;

    /**
     * Instance de la classe de gestion des sauvegardes.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Boss_Backup_Manager    $backup_manager    Instance de la classe de gestion des sauvegardes.
     */
    protected $backup_manager;

    /**
     * Instance de la classe de préférences utilisateur.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Boss_User_Preferences    $user_preferences    Instance de la classe de préférences utilisateur.
     */
    protected $user_preferences;

    /**
     * Instance de la classe de paramètres de l'optimiseur.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $optimizer_settings    Instance de la classe de paramètres de l'optimiseur.
     */
    protected $optimizer_settings;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->load_dependencies();
        $this->init_modules();
    }

    /**
     * Charge les dépendances nécessaires.
     *
     * @since    1.0.0
     * @access   private
     */
    private function load_dependencies() {
        /**
         * Les classes pour gérer les différents modules de paramètres.
         */
        require_once plugin_dir_path( __FILE__ ) . 'settings/class-boss-general-settings.php';
        require_once plugin_dir_path( __FILE__ ) . 'settings/class-boss-advanced-settings.php';
        require_once plugin_dir_path( __FILE__ ) . 'settings/class-boss-backup-manager.php';
        require_once plugin_dir_path( __FILE__ ) . 'settings/class-boss-user-preferences.php';

        /**
         * La classe pour gérer les paramètres de l'optimiseur (pour l'IA).
         */
        require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-settings.php';

        /**
         * La classe pour gérer les points de terminaison REST API.
         */
        require_once plugin_dir_path( __FILE__ ) . 'api/class-boss-settings-api.php';
    }

    /**
     * Initialise les différents modules de paramètres.
     *
     * @since    1.0.0
     * @access   private
     */
    private function init_modules() {
        $this->general_settings = new Boss_General_Settings( $this->plugin_name, $this->version );
        $this->advanced_settings = new Boss_Advanced_Settings( $this->plugin_name, $this->version );
        $this->backup_manager = new Boss_Backup_Manager( $this->plugin_name, $this->version );
        $this->user_preferences = new Boss_User_Preferences( $this->plugin_name, $this->version );
        $this->optimizer_settings = new Boss_Optimizer_Settings( $this->plugin_name );
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.0.0
     */
    public function register_hooks() {
        // Enregistrer les hooks pour les différents modules
        $this->general_settings->register_hooks();
        $this->advanced_settings->register_hooks();
        $this->backup_manager->register_hooks();
        $this->user_preferences->register_hooks();

        // Enregistrer les points de terminaison REST API
        add_action( 'rest_api_init', array( $this, 'register_rest_routes' ) );
    }

    /**
     * Enregistre les points de terminaison REST API.
     *
     * @since    1.0.0
     */
    public function register_rest_routes() {
        $settings_api = new Boss_Settings_API( $this->plugin_name, $this->version, $this );
        $settings_api->register_routes();
    }

    /**
     * Récupère l'instance de la classe de paramètres généraux.
     *
     * @since    1.0.0
     * @return   Boss_General_Settings    L'instance de la classe de paramètres généraux.
     */
    public function get_general_settings() {
        return $this->general_settings;
    }

    /**
     * Récupère l'instance de la classe de paramètres avancés.
     *
     * @since    1.0.0
     * @return   Boss_Advanced_Settings    L'instance de la classe de paramètres avancés.
     */
    public function get_advanced_settings() {
        return $this->advanced_settings;
    }

    /**
     * Récupère l'instance de la classe de gestion des sauvegardes.
     *
     * @since    1.0.0
     * @return   Boss_Backup_Manager    L'instance de la classe de gestion des sauvegardes.
     */
    public function get_backup_manager() {
        return $this->backup_manager;
    }

    /**
     * Récupère l'instance de la classe de préférences utilisateur.
     *
     * @since    1.0.0
     * @return   Boss_User_Preferences    L'instance de la classe de préférences utilisateur.
     */
    public function get_user_preferences() {
        return $this->user_preferences;
    }

    /**
     * Récupère l'instance de la classe de paramètres de l'optimiseur.
     *
     * @since    1.1.0
     * @return   Boss_Optimizer_Settings    L'instance de la classe de paramètres de l'optimiseur.
     */
    public function get_optimizer_settings() {
        return $this->optimizer_settings;
    }

    /**
     * Vérifie si l'IA est configurée et disponible.
     * Méthode proxy vers Boss_Optimizer_Settings.
     *
     * @since    1.1.0
     * @return   boolean    True si l'IA est configurée, false sinon.
     */
    public function is_ai_configured() {
        return $this->optimizer_settings ? $this->optimizer_settings->is_ai_configured() : false;
    }

    /**
     * Récupère le fournisseur d'IA actuel.
     * Méthode proxy vers Boss_Optimizer_Settings.
     *
     * @since    1.1.0
     * @return   string    Le fournisseur d'IA actuel.
     */
    public function get_ai_provider() {
        return $this->optimizer_settings ? $this->optimizer_settings->get_ai_provider() : 'openai';
    }

    /**
     * Récupère la clé API pour le fournisseur d'IA actuel.
     * Méthode proxy vers Boss_Optimizer_Settings.
     *
     * @since    1.1.0
     * @return   string    La clé API.
     */
    public function get_ai_api_key() {
        return $this->optimizer_settings ? $this->optimizer_settings->get_ai_api_key() : '';
    }

    /**
     * Récupère le modèle d'IA actuel.
     * Méthode proxy vers Boss_Optimizer_Settings.
     *
     * @since    1.1.0
     * @return   string    Le modèle d'IA actuel.
     */
    public function get_ai_model() {
        return $this->optimizer_settings ? $this->optimizer_settings->get_ai_model() : '';
    }
}
