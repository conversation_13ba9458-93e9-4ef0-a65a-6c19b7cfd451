import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  SelectControl,
  TextControl,
  ToggleControl,
  Modal,
  Notice
} from '@wordpress/components';

const ReportManager = ({ analyticsData }) => {
  // États
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [newReport, setNewReport] = useState({
    name: '',
    description: '',
    type: 'complete',
    sections: ['traffic', 'keywords', 'pages', 'opportunities'],
    format: 'pdf',
    schedule: 'none'
  });
  
  // Rapports prédéfinis
  const [reports, setReports] = useState([
    {
      id: 1,
      name: __('Rapport SEO mensuel', 'boss-seo'),
      description: __('Rapport complet sur les performances SEO du site', 'boss-seo'),
      type: 'complete',
      sections: ['traffic', 'keywords', 'pages', 'opportunities'],
      format: 'pdf',
      schedule: 'monthly',
      lastGenerated: '2023-06-01',
      recipients: ['<EMAIL>']
    },
    {
      id: 2,
      name: __('Rapport de mots-clés', 'boss-seo'),
      description: __('Analyse détaillée des performances des mots-clés', 'boss-seo'),
      type: 'keywords',
      sections: ['keywords'],
      format: 'pdf',
      schedule: 'weekly',
      lastGenerated: '2023-06-15',
      recipients: ['<EMAIL>', '<EMAIL>']
    },
    {
      id: 3,
      name: __('Rapport de trafic', 'boss-seo'),
      description: __('Analyse du trafic et des sources', 'boss-seo'),
      type: 'traffic',
      sections: ['traffic'],
      format: 'csv',
      schedule: 'none',
      lastGenerated: '2023-06-10',
      recipients: []
    }
  ]);
  
  // Fonction pour créer un nouveau rapport
  const handleCreateReport = () => {
    const newReportWithId = {
      ...newReport,
      id: reports.length + 1,
      lastGenerated: new Date().toISOString().split('T')[0],
      recipients: []
    };
    
    setReports([...reports, newReportWithId]);
    setShowCreateModal(false);
    setNewReport({
      name: '',
      description: '',
      type: 'complete',
      sections: ['traffic', 'keywords', 'pages', 'opportunities'],
      format: 'pdf',
      schedule: 'none'
    });
  };
  
  // Fonction pour générer un rapport
  const handleGenerateReport = (report) => {
    // Simuler la génération d'un rapport
    console.log('Génération du rapport:', report);
    
    // Mettre à jour la date de dernière génération
    const updatedReports = reports.map(r => {
      if (r.id === report.id) {
        return {
          ...r,
          lastGenerated: new Date().toISOString().split('T')[0]
        };
      }
      return r;
    });
    
    setReports(updatedReports);
    
    // Afficher une notification de succès (à implémenter)
  };
  
  // Fonction pour supprimer un rapport
  const handleDeleteReport = (reportId) => {
    setReports(reports.filter(report => report.id !== reportId));
  };
  
  // Fonction pour ouvrir la modal de planification
  const openScheduleModal = (report) => {
    setSelectedReport(report);
    setShowScheduleModal(true);
  };
  
  // Fonction pour mettre à jour la planification d'un rapport
  const handleUpdateSchedule = () => {
    if (!selectedReport) return;
    
    const updatedReports = reports.map(r => {
      if (r.id === selectedReport.id) {
        return selectedReport;
      }
      return r;
    });
    
    setReports(updatedReports);
    setShowScheduleModal(false);
    setSelectedReport(null);
  };
  
  // Fonction pour mettre à jour les destinataires
  const handleUpdateRecipients = (recipients) => {
    if (!selectedReport) return;
    
    setSelectedReport({
      ...selectedReport,
      recipients: recipients.split(',').map(email => email.trim()).filter(email => email)
    });
  };
  
  // Fonction pour obtenir le nom du type de rapport
  const getReportTypeName = (type) => {
    switch (type) {
      case 'complete':
        return __('Rapport complet', 'boss-seo');
      case 'traffic':
        return __('Rapport de trafic', 'boss-seo');
      case 'keywords':
        return __('Rapport de mots-clés', 'boss-seo');
      case 'pages':
        return __('Rapport de pages', 'boss-seo');
      case 'opportunities':
        return __('Rapport d\'opportunités', 'boss-seo');
      default:
        return type;
    }
  };
  
  // Fonction pour obtenir le nom de la planification
  const getScheduleName = (schedule) => {
    switch (schedule) {
      case 'none':
        return __('Aucune', 'boss-seo');
      case 'daily':
        return __('Quotidienne', 'boss-seo');
      case 'weekly':
        return __('Hebdomadaire', 'boss-seo');
      case 'monthly':
        return __('Mensuelle', 'boss-seo');
      default:
        return schedule;
    }
  };
  
  // Fonction pour obtenir l'icône du format
  const getFormatIcon = (format) => {
    switch (format) {
      case 'pdf':
        return 'media-document';
      case 'csv':
        return 'media-spreadsheet';
      case 'html':
        return 'media-code';
      default:
        return 'media-default';
    }
  };

  return (
    <div>
      <div className="boss-flex boss-justify-between boss-items-center boss-mb-6">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Gestionnaire de rapports', 'boss-seo')}
        </h2>
        
        <Button
          isPrimary
          onClick={() => setShowCreateModal(true)}
        >
          <Dashicon icon="plus" className="boss-mr-1" />
          {__('Créer un rapport', 'boss-seo')}
        </Button>
      </div>
      
      {/* Liste des rapports */}
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-6">
        {reports.map(report => (
          <Card key={report.id} className="boss-card">
            <CardHeader className="boss-border-b boss-border-gray-200">
              <div className="boss-flex boss-justify-between boss-items-start">
                <div>
                  <h3 className="boss-text-lg boss-font-semibold boss-mb-1">{report.name}</h3>
                  <div className="boss-flex boss-items-center">
                    <span className="boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded-full boss-bg-gray-100 boss-text-boss-gray boss-mr-2">
                      {getReportTypeName(report.type)}
                    </span>
                    <span className="boss-text-xs boss-font-medium boss-px-2 boss-py-1 boss-rounded-full boss-bg-blue-100 boss-text-blue-600">
                      {report.format.toUpperCase()}
                    </span>
                  </div>
                </div>
                <div className={`boss-p-2 boss-rounded-lg boss-bg-${report.format === 'pdf' ? 'red' : report.format === 'csv' ? 'green' : 'purple'}-100`}>
                  <Dashicon icon={getFormatIcon(report.format)} className={`boss-text-${report.format === 'pdf' ? 'red' : report.format === 'csv' ? 'green' : 'purple'}-600 boss-text-xl`} />
                </div>
              </div>
            </CardHeader>
            <CardBody>
              <p className="boss-text-boss-gray boss-text-sm boss-mb-4">{report.description}</p>
              
              <div className="boss-space-y-2 boss-mb-4">
                <div className="boss-flex boss-justify-between boss-items-center">
                  <span className="boss-text-sm boss-text-boss-gray">{__('Sections', 'boss-seo')}</span>
                  <span className="boss-text-sm">{report.sections.length}</span>
                </div>
                <div className="boss-flex boss-justify-between boss-items-center">
                  <span className="boss-text-sm boss-text-boss-gray">{__('Planification', 'boss-seo')}</span>
                  <span className="boss-text-sm">{getScheduleName(report.schedule)}</span>
                </div>
                <div className="boss-flex boss-justify-between boss-items-center">
                  <span className="boss-text-sm boss-text-boss-gray">{__('Dernière génération', 'boss-seo')}</span>
                  <span className="boss-text-sm">{report.lastGenerated}</span>
                </div>
              </div>
            </CardBody>
            <CardFooter className="boss-border-t boss-border-gray-200">
              <div className="boss-flex boss-justify-between boss-items-center">
                <Button
                  isDestructive
                  isSmall
                  onClick={() => handleDeleteReport(report.id)}
                >
                  {__('Supprimer', 'boss-seo')}
                </Button>
                <div className="boss-flex boss-space-x-2">
                  <Button
                    isSecondary
                    isSmall
                    onClick={() => openScheduleModal(report)}
                  >
                    <Dashicon icon="calendar-alt" className="boss-mr-1" />
                    {__('Planifier', 'boss-seo')}
                  </Button>
                  <Button
                    isPrimary
                    isSmall
                    onClick={() => handleGenerateReport(report)}
                  >
                    <Dashicon icon="media-document" className="boss-mr-1" />
                    {__('Générer', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
      
      {/* Modal de création de rapport */}
      {showCreateModal && (
        <Modal
          title={__('Créer un nouveau rapport', 'boss-seo')}
          onRequestClose={() => setShowCreateModal(false)}
          className="boss-create-report-modal"
        >
          <div className="boss-p-6">
            <div className="boss-space-y-4 boss-mb-6">
              <TextControl
                label={__('Nom du rapport', 'boss-seo')}
                value={newReport.name}
                onChange={(value) => setNewReport({ ...newReport, name: value })}
              />
              
              <TextControl
                label={__('Description', 'boss-seo')}
                value={newReport.description}
                onChange={(value) => setNewReport({ ...newReport, description: value })}
              />
              
              <SelectControl
                label={__('Type de rapport', 'boss-seo')}
                value={newReport.type}
                options={[
                  { label: __('Rapport complet', 'boss-seo'), value: 'complete' },
                  { label: __('Rapport de trafic', 'boss-seo'), value: 'traffic' },
                  { label: __('Rapport de mots-clés', 'boss-seo'), value: 'keywords' },
                  { label: __('Rapport de pages', 'boss-seo'), value: 'pages' },
                  { label: __('Rapport d\'opportunités', 'boss-seo'), value: 'opportunities' }
                ]}
                onChange={(value) => {
                  let sections = [];
                  switch (value) {
                    case 'complete':
                      sections = ['traffic', 'keywords', 'pages', 'opportunities'];
                      break;
                    case 'traffic':
                      sections = ['traffic'];
                      break;
                    case 'keywords':
                      sections = ['keywords'];
                      break;
                    case 'pages':
                      sections = ['pages'];
                      break;
                    case 'opportunities':
                      sections = ['opportunities'];
                      break;
                  }
                  setNewReport({ ...newReport, type: value, sections });
                }}
              />
              
              <div>
                <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium">
                  {__('Sections à inclure', 'boss-seo')}
                </label>
                <div className="boss-space-y-2">
                  <ToggleControl
                    label={__('Trafic et engagement', 'boss-seo')}
                    checked={newReport.sections.includes('traffic')}
                    onChange={(checked) => {
                      const sections = checked 
                        ? [...newReport.sections, 'traffic'] 
                        : newReport.sections.filter(s => s !== 'traffic');
                      setNewReport({ ...newReport, sections });
                    }}
                  />
                  <ToggleControl
                    label={__('Performance des mots-clés', 'boss-seo')}
                    checked={newReport.sections.includes('keywords')}
                    onChange={(checked) => {
                      const sections = checked 
                        ? [...newReport.sections, 'keywords'] 
                        : newReport.sections.filter(s => s !== 'keywords');
                      setNewReport({ ...newReport, sections });
                    }}
                  />
                  <ToggleControl
                    label={__('Pages populaires', 'boss-seo')}
                    checked={newReport.sections.includes('pages')}
                    onChange={(checked) => {
                      const sections = checked 
                        ? [...newReport.sections, 'pages'] 
                        : newReport.sections.filter(s => s !== 'pages');
                      setNewReport({ ...newReport, sections });
                    }}
                  />
                  <ToggleControl
                    label={__('Opportunités d\'amélioration', 'boss-seo')}
                    checked={newReport.sections.includes('opportunities')}
                    onChange={(checked) => {
                      const sections = checked 
                        ? [...newReport.sections, 'opportunities'] 
                        : newReport.sections.filter(s => s !== 'opportunities');
                      setNewReport({ ...newReport, sections });
                    }}
                  />
                </div>
              </div>
              
              <SelectControl
                label={__('Format', 'boss-seo')}
                value={newReport.format}
                options={[
                  { label: 'PDF', value: 'pdf' },
                  { label: 'CSV', value: 'csv' },
                  { label: 'HTML', value: 'html' }
                ]}
                onChange={(value) => setNewReport({ ...newReport, format: value })}
              />
              
              <SelectControl
                label={__('Planification', 'boss-seo')}
                value={newReport.schedule}
                options={[
                  { label: __('Aucune', 'boss-seo'), value: 'none' },
                  { label: __('Quotidienne', 'boss-seo'), value: 'daily' },
                  { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                  { label: __('Mensuelle', 'boss-seo'), value: 'monthly' }
                ]}
                onChange={(value) => setNewReport({ ...newReport, schedule: value })}
              />
            </div>
            
            <div className="boss-flex boss-justify-end boss-space-x-3">
              <Button
                isSecondary
                onClick={() => setShowCreateModal(false)}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                onClick={handleCreateReport}
                disabled={!newReport.name}
              >
                {__('Créer', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
      
      {/* Modal de planification */}
      {showScheduleModal && selectedReport && (
        <Modal
          title={__('Planifier le rapport', 'boss-seo')}
          onRequestClose={() => setShowScheduleModal(false)}
          className="boss-schedule-report-modal"
        >
          <div className="boss-p-6">
            <div className="boss-space-y-4 boss-mb-6">
              <SelectControl
                label={__('Fréquence', 'boss-seo')}
                value={selectedReport.schedule}
                options={[
                  { label: __('Aucune', 'boss-seo'), value: 'none' },
                  { label: __('Quotidienne', 'boss-seo'), value: 'daily' },
                  { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                  { label: __('Mensuelle', 'boss-seo'), value: 'monthly' }
                ]}
                onChange={(value) => setSelectedReport({ ...selectedReport, schedule: value })}
              />
              
              {selectedReport.schedule !== 'none' && (
                <div>
                  <TextControl
                    label={__('Destinataires (séparés par des virgules)', 'boss-seo')}
                    value={selectedReport.recipients.join(', ')}
                    onChange={handleUpdateRecipients}
                  />
                  
                  <Notice status="info" isDismissible={false} className="boss-mt-4">
                    <p>
                      {__('Le rapport sera envoyé automatiquement par email aux destinataires selon la fréquence sélectionnée.', 'boss-seo')}
                    </p>
                  </Notice>
                </div>
              )}
            </div>
            
            <div className="boss-flex boss-justify-end boss-space-x-3">
              <Button
                isSecondary
                onClick={() => setShowScheduleModal(false)}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                onClick={handleUpdateSchedule}
              >
                {__('Enregistrer', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ReportManager;
