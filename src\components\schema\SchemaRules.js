import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  SelectControl,
  CheckboxControl,
  RadioControl,
  ToggleControl,
  RangeControl,
  Notice,
  Panel,
  PanelBody,
  PanelRow
} from '@wordpress/components';

const SchemaRules = ({ schema, schemas, onSave }) => {
  // États
  const [currentSchema, setCurrentSchema] = useState(schema || {});
  const [postTypes, setPostTypes] = useState([]);
  const [taxonomies, setTaxonomies] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [selectedSchema, setSelectedSchema] = useState(null);

  // Effet pour charger les types de contenu et taxonomies
  useEffect(() => {
    setIsLoading(true);
    
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour les types de contenu
      const mockPostTypes = [
        { id: 'post', name: __('Articles', 'boss-seo') },
        { id: 'page', name: __('Pages', 'boss-seo') },
        { id: 'product', name: __('Produits', 'boss-seo') },
        { id: 'portfolio', name: __('Portfolio', 'boss-seo') },
        { id: 'event', name: __('Événements', 'boss-seo') }
      ];
      
      // Données fictives pour les taxonomies
      const mockTaxonomies = [
        { id: 'category', name: __('Catégories', 'boss-seo'), terms: [
          { id: 'cat1', name: __('Actualités', 'boss-seo') },
          { id: 'cat2', name: __('Tutoriels', 'boss-seo') },
          { id: 'cat3', name: __('Ressources', 'boss-seo') }
        ]},
        { id: 'post_tag', name: __('Étiquettes', 'boss-seo'), terms: [
          { id: 'tag1', name: __('SEO', 'boss-seo') },
          { id: 'tag2', name: __('Marketing', 'boss-seo') },
          { id: 'tag3', name: __('Développement', 'boss-seo') }
        ]},
        { id: 'product_cat', name: __('Catégories de produits', 'boss-seo'), terms: [
          { id: 'pcat1', name: __('Électronique', 'boss-seo') },
          { id: 'pcat2', name: __('Vêtements', 'boss-seo') },
          { id: 'pcat3', name: __('Livres', 'boss-seo') }
        ]}
      ];
      
      setPostTypes(mockPostTypes);
      setTaxonomies(mockTaxonomies);
      
      // Si un schéma est sélectionné, l'utiliser
      if (schema) {
        setSelectedSchema(schema);
        setCurrentSchema(schema);
      } else if (schemas.length > 0) {
        // Sinon, utiliser le premier schéma de la liste
        setSelectedSchema(schemas[0]);
        setCurrentSchema(schemas[0]);
      }
      
      setIsLoading(false);
    }, 500);
  }, [schema, schemas]);

  // Fonction pour mettre à jour les règles du schéma
  const updateSchemaRules = (key, value) => {
    setCurrentSchema({
      ...currentSchema,
      rules: {
        ...currentSchema.rules,
        [key]: value
      }
    });
  };

  // Fonction pour gérer la sélection d'un type de contenu
  const handlePostTypeChange = (postTypeId, isChecked) => {
    const currentPostTypes = currentSchema.rules?.postTypes || [];
    
    if (isChecked) {
      updateSchemaRules('postTypes', [...currentPostTypes, postTypeId]);
    } else {
      updateSchemaRules('postTypes', currentPostTypes.filter(id => id !== postTypeId));
    }
  };

  // Fonction pour gérer la sélection d'une taxonomie
  const handleTaxonomyChange = (taxonomyId, termId, isChecked) => {
    const currentTaxonomies = currentSchema.rules?.taxonomies || {};
    
    if (isChecked) {
      updateSchemaRules('taxonomies', {
        ...currentTaxonomies,
        [taxonomyId]: [...(currentTaxonomies[taxonomyId] || []), termId]
      });
    } else {
      updateSchemaRules('taxonomies', {
        ...currentTaxonomies,
        [taxonomyId]: (currentTaxonomies[taxonomyId] || []).filter(id => id !== termId)
      });
    }
  };

  // Fonction pour sauvegarder les règles
  const handleSave = () => {
    onSave(currentSchema);
  };

  // Fonction pour changer le schéma sélectionné
  const handleSchemaChange = (schemaId) => {
    const selected = schemas.find(s => s.id === parseInt(schemaId));
    setSelectedSchema(selected);
    setCurrentSchema(selected);
  };

  return (
    <div>
      {/* Sélecteur de schéma */}
      <Card className="boss-mb-6">
        <CardBody>
          <SelectControl
            label={__('Schéma à configurer', 'boss-seo')}
            value={selectedSchema?.id || ''}
            options={[
              { label: __('-- Sélectionner un schéma --', 'boss-seo'), value: '' },
              ...schemas.map(s => ({
                label: s.name,
                value: s.id
              }))
            ]}
            onChange={handleSchemaChange}
          />
        </CardBody>
      </Card>
      
      {selectedSchema && (
        <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
          {/* Panneau principal */}
          <div className="lg:boss-col-span-2">
            <Card className="boss-mb-6">
              <CardHeader className="boss-border-b boss-border-gray-200">
                <div className="boss-flex boss-justify-between boss-items-center">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Règles d\'application', 'boss-seo')} - {currentSchema.name}
                  </h2>
                  <Button
                    isPrimary
                    onClick={handleSave}
                  >
                    {__('Enregistrer les règles', 'boss-seo')}
                  </Button>
                </div>
              </CardHeader>
              <CardBody>
                {isLoading ? (
                  <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
                    <Dashicon icon="update" className="boss-animate-spin boss-text-boss-primary boss-text-2xl" />
                  </div>
                ) : (
                  <div>
                    <div className="boss-mb-6">
                      <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                        {__('Types de contenu', 'boss-seo')}
                      </h3>
                      <p className="boss-text-sm boss-text-boss-gray boss-mb-4">
                        {__('Sélectionnez les types de contenu auxquels ce schéma sera appliqué.', 'boss-seo')}
                      </p>
                      
                      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-3">
                        {postTypes.map(postType => (
                          <CheckboxControl
                            key={postType.id}
                            label={postType.name}
                            checked={(currentSchema.rules?.postTypes || []).includes(postType.id)}
                            onChange={(isChecked) => handlePostTypeChange(postType.id, isChecked)}
                          />
                        ))}
                      </div>
                    </div>
                    
                    <div className="boss-mb-6">
                      <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                        {__('Taxonomies', 'boss-seo')}
                      </h3>
                      <p className="boss-text-sm boss-text-boss-gray boss-mb-4">
                        {__('Sélectionnez les taxonomies auxquelles ce schéma sera appliqué.', 'boss-seo')}
                      </p>
                      
                      {taxonomies.map(taxonomy => (
                        <Panel key={taxonomy.id} className="boss-mb-4">
                          <PanelBody
                            title={taxonomy.name}
                            initialOpen={false}
                          >
                            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-3 boss-mt-3">
                              {taxonomy.terms.map(term => (
                                <CheckboxControl
                                  key={term.id}
                                  label={term.name}
                                  checked={(currentSchema.rules?.taxonomies?.[taxonomy.id] || []).includes(term.id)}
                                  onChange={(isChecked) => handleTaxonomyChange(taxonomy.id, term.id, isChecked)}
                                />
                              ))}
                            </div>
                          </PanelBody>
                        </Panel>
                      ))}
                    </div>
                    
                    <div className="boss-mb-6">
                      <Button
                        isSecondary
                        onClick={() => setShowAdvanced(!showAdvanced)}
                      >
                        {showAdvanced ? __('Masquer les options avancées', 'boss-seo') : __('Afficher les options avancées', 'boss-seo')}
                      </Button>
                    </div>
                    
                    {showAdvanced && (
                      <div>
                        <div className="boss-mb-6">
                          <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                            {__('Priorité', 'boss-seo')}
                          </h3>
                          <p className="boss-text-sm boss-text-boss-gray boss-mb-4">
                            {__('Définissez la priorité de ce schéma par rapport aux autres schémas.', 'boss-seo')}
                          </p>
                          
                          <RangeControl
                            value={currentSchema.rules?.priority || 10}
                            onChange={(value) => updateSchemaRules('priority', value)}
                            min={1}
                            max={100}
                            step={1}
                          />
                        </div>
                        
                        <div className="boss-mb-6">
                          <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                            {__('Planning', 'boss-seo')}
                          </h3>
                          <p className="boss-text-sm boss-text-boss-gray boss-mb-4">
                            {__('Définissez une période pendant laquelle ce schéma sera actif.', 'boss-seo')}
                          </p>
                          
                          <ToggleControl
                            label={__('Activer le planning', 'boss-seo')}
                            checked={currentSchema.rules?.scheduling?.enabled || false}
                            onChange={(value) => updateSchemaRules('scheduling', {
                              ...(currentSchema.rules?.scheduling || {}),
                              enabled: value
                            })}
                            className="boss-mb-4"
                          />
                          
                          {currentSchema.rules?.scheduling?.enabled && (
                            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                              <div>
                                <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium">
                                  {__('Date de début', 'boss-seo')}
                                </label>
                                <input
                                  type="date"
                                  value={currentSchema.rules?.scheduling?.startDate || ''}
                                  onChange={(e) => updateSchemaRules('scheduling', {
                                    ...(currentSchema.rules?.scheduling || {}),
                                    startDate: e.target.value
                                  })}
                                  className="boss-w-full boss-p-2 boss-border boss-border-gray-300 boss-rounded"
                                />
                              </div>
                              <div>
                                <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium">
                                  {__('Date de fin', 'boss-seo')}
                                </label>
                                <input
                                  type="date"
                                  value={currentSchema.rules?.scheduling?.endDate || ''}
                                  onChange={(e) => updateSchemaRules('scheduling', {
                                    ...(currentSchema.rules?.scheduling || {}),
                                    endDate: e.target.value
                                  })}
                                  className="boss-w-full boss-p-2 boss-border boss-border-gray-300 boss-rounded"
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardBody>
            </Card>
          </div>
          
          {/* Panneau d'aide */}
          <div>
            <Card>
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Aide', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-space-y-4">
                  <div>
                    <h3 className="boss-font-medium boss-mb-2">
                      {__('Types de contenu', 'boss-seo')}
                    </h3>
                    <p className="boss-text-sm boss-text-boss-gray">
                      {__('Sélectionnez les types de contenu (articles, pages, produits, etc.) auxquels ce schéma sera appliqué.', 'boss-seo')}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="boss-font-medium boss-mb-2">
                      {__('Taxonomies', 'boss-seo')}
                    </h3>
                    <p className="boss-text-sm boss-text-boss-gray">
                      {__('Sélectionnez les catégories, étiquettes ou autres taxonomies auxquelles ce schéma sera appliqué.', 'boss-seo')}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="boss-font-medium boss-mb-2">
                      {__('Priorité', 'boss-seo')}
                    </h3>
                    <p className="boss-text-sm boss-text-boss-gray">
                      {__('Si plusieurs schémas correspondent à une page, celui avec la priorité la plus élevée sera utilisé.', 'boss-seo')}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="boss-font-medium boss-mb-2">
                      {__('Planning', 'boss-seo')}
                    </h3>
                    <p className="boss-text-sm boss-text-boss-gray">
                      {__('Définissez une période pendant laquelle ce schéma sera actif, utile pour les événements saisonniers ou les promotions.', 'boss-seo')}
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};

export default SchemaRules;
