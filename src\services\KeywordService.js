/**
 * Service pour la recherche de mots-clés
 * 
 * Gère les communications avec l'API pour les fonctionnalités de recherche de mots-clés
 */

import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

class KeywordService {
  /**
   * Recherche des mots-clés via SerpAPI
   * 
   * @param {string} query - Requête de recherche
   * @param {string} language - Langue (fr, en, etc.)
   * @param {string} country - Pays (fr, us, etc.)
   * @returns {Promise} - Promesse contenant les résultats de la recherche
   */
  async searchKeywords(query, language = 'fr', country = 'fr') {
    try {
      const path = addQueryArgs('/boss-seo/v1/keywords/search', {
        query,
        language,
        country
      });
      
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la recherche de mots-clés:', error);
      throw error;
    }
  }
  
  /**
   * Recherche des mots-clés connexes
   * 
   * @param {string} keyword - Mot-clé principal
   * @param {string} language - Langue (fr, en, etc.)
   * @param {string} country - Pays (fr, us, etc.)
   * @returns {Promise} - Promesse contenant les mots-clés connexes
   */
  async getRelatedKeywords(keyword, language = 'fr', country = 'fr') {
    try {
      const path = addQueryArgs('/boss-seo/v1/keywords/related', {
        keyword,
        language,
        country
      });
      
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des mots-clés connexes:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les données de volume de recherche pour un mot-clé
   * 
   * @param {string} keyword - Mot-clé
   * @param {string} language - Langue (fr, en, etc.)
   * @param {string} country - Pays (fr, us, etc.)
   * @returns {Promise} - Promesse contenant les données de volume
   */
  async getKeywordVolume(keyword, language = 'fr', country = 'fr') {
    try {
      const path = addQueryArgs('/boss-seo/v1/keywords/volume', {
        keyword,
        language,
        country
      });
      
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération du volume de recherche:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les mots-clés à longue traîne
   * 
   * @param {string} keyword - Mot-clé principal
   * @param {string} language - Langue (fr, en, etc.)
   * @param {string} country - Pays (fr, us, etc.)
   * @returns {Promise} - Promesse contenant les mots-clés à longue traîne
   */
  async getLongTailKeywords(keyword, language = 'fr', country = 'fr') {
    try {
      const path = addQueryArgs('/boss-seo/v1/keywords/longtail', {
        keyword,
        language,
        country
      });
      
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des mots-clés à longue traîne:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les mots-clés suggérés par l'IA
   * 
   * @param {string} topic - Sujet ou thème
   * @param {string} contentType - Type de contenu (article, product, etc.)
   * @returns {Promise} - Promesse contenant les mots-clés suggérés
   */
  async getAiSuggestedKeywords(topic, contentType) {
    try {
      const path = '/boss-seo/v1/keywords/ai-suggestions';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          topic,
          content_type: contentType
        }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des suggestions de mots-clés par l\'IA:', error);
      throw error;
    }
  }
  
  /**
   * Sauvegarde un mot-clé dans les favoris
   * 
   * @param {Object} keyword - Mot-clé à sauvegarder
   * @returns {Promise} - Promesse contenant le résultat de l'opération
   */
  async saveKeyword(keyword) {
    try {
      const path = '/boss-seo/v1/keywords/save';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { keyword }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du mot-clé:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les mots-clés sauvegardés
   * 
   * @returns {Promise} - Promesse contenant les mots-clés sauvegardés
   */
  async getSavedKeywords() {
    try {
      const path = '/boss-seo/v1/keywords/saved';
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des mots-clés sauvegardés:', error);
      throw error;
    }
  }
}

export default new KeywordService();
