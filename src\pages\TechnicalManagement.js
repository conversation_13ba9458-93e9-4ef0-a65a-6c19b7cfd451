import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  TabPanel,
  Spinner
} from '@wordpress/components';

// Importer les composants
import Redirections from '../components/technical/Redirections';
import RobotsSitemapManager from '../components/technical/RobotsSitemapManager';
import MediaOptimization from '../components/technical/MediaOptimization';
import HttpHeaders from '../components/technical/HttpHeaders';

const TechnicalManagement = () => {
  // États
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('redirections');

  // Fonction pour gérer le changement d'onglet
  const handleTabChange = (tabName) => {
    setActiveTab(tabName);
  };

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Gestion technique', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Gérez les aspects techniques du SEO : redirections, robots.txt, sitemaps, médias et en-têtes HTTP.', 'boss-seo')}
          </p>
        </div>

        <TabPanel
          className="boss-mb-6"
          activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
          onSelect={handleTabChange}
          tabs={[
            {
              name: 'redirections',
              title: __('Redirections', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'robots-sitemap',
              title: __('Robots.txt & Sitemap', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'media-optimization',
              title: __('Optimisation médias', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'http-headers',
              title: __('En-têtes HTTP', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            }
          ]}
        >
          {(tab) => {
            if (isLoading) {
              return (
                <Card>
                  <CardBody>
                    <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
                      <Spinner />
                    </div>
                  </CardBody>
                </Card>
              );
            }

            switch (tab.name) {
              case 'redirections':
                return <Redirections />;
              case 'robots-sitemap':
                return <RobotsSitemapManager />;
              case 'media-optimization':
                return <MediaOptimization />;
              case 'http-headers':
                return <HttpHeaders />;
              default:
                return <Redirections />;
            }
          }}
        </TabPanel>

        {/* Informations contextuelles */}
        <Card>
          <CardBody>
            <div className="boss-flex boss-items-start boss-gap-4">
              <div className="boss-flex-shrink-0 boss-w-8 boss-h-8 boss-bg-blue-100 boss-rounded-full boss-flex boss-items-center boss-justify-center">
                <span className="dashicons dashicons-info boss-text-blue-600"></span>
              </div>
              <div>
                <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-2">
                  {activeTab === 'redirections' && __('À propos des redirections', 'boss-seo')}
                  {activeTab === 'robots-sitemap' && __('À propos des robots.txt et sitemaps', 'boss-seo')}
                  {activeTab === 'media-optimization' && __('À propos de l\'optimisation des médias', 'boss-seo')}
                  {activeTab === 'http-headers' && __('À propos des en-têtes HTTP', 'boss-seo')}
                </h3>
                <div className="boss-text-boss-gray">
                  {activeTab === 'redirections' && (
                    <p>
                      {__('Les redirections permettent de rediriger les utilisateurs et les moteurs de recherche d\'une URL à une autre. Elles sont essentielles pour gérer les changements d\'URL, les pages supprimées ou les erreurs 404. Une bonne gestion des redirections améliore l\'expérience utilisateur et préserve le référencement.', 'boss-seo')}
                    </p>
                  )}
                  {activeTab === 'robots-sitemap' && (
                    <p>
                      {__('Le fichier robots.txt indique aux moteurs de recherche quelles parties de votre site ils peuvent explorer. Les sitemaps XML aident les moteurs de recherche à comprendre la structure de votre site et à découvrir toutes vos pages. Ensemble, ils optimisent l\'indexation de votre site.', 'boss-seo')}
                    </p>
                  )}
                  {activeTab === 'media-optimization' && (
                    <p>
                      {__('L\'optimisation des médias améliore la vitesse de chargement de votre site, un facteur important pour le SEO. La compression des images, l\'ajout de textes alternatifs et le chargement différé (lazy loading) contribuent à une meilleure expérience utilisateur et à un meilleur référencement.', 'boss-seo')}
                    </p>
                  )}
                  {activeTab === 'http-headers' && (
                    <p>
                      {__('Les en-têtes HTTP fournissent des instructions aux navigateurs et aux moteurs de recherche sur la façon de traiter votre contenu. Ils peuvent améliorer la sécurité, les performances et l\'expérience utilisateur de votre site. Une configuration optimale des en-têtes peut avoir un impact positif sur votre SEO.', 'boss-seo')}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default TechnicalManagement;
