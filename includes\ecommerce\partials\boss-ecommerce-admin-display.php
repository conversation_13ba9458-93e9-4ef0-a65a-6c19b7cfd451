<?php
/**
 * Template pour l'affichage de la page d'administration du module e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce/partials
 */

// Si ce fichier est appelé directement, abandonner.
if ( ! defined( 'WPINC' ) ) {
    die;
}
?>

<div class="wrap">
    <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
    
    <div class="boss-seo-ecommerce-tabs">
        <div class="nav-tab-wrapper">
            <a href="#rich-snippets" class="nav-tab nav-tab-active"><?php esc_html_e( 'Extraits enrichis', 'boss-seo' ); ?></a>
            <a href="#seo-analyzer" class="nav-tab"><?php esc_html_e( 'Analyse SEO', 'boss-seo' ); ?></a>
            <a href="#product-brands" class="nav-tab"><?php esc_html_e( 'Marques de produits', 'boss-seo' ); ?></a>
            <a href="#settings" class="nav-tab"><?php esc_html_e( 'Paramètres', 'boss-seo' ); ?></a>
        </div>
        
        <div id="rich-snippets" class="tab-content active">
            <h2><?php esc_html_e( 'Extraits enrichis', 'boss-seo' ); ?></h2>
            <p><?php esc_html_e( 'Gérez les extraits enrichis pour vos produits.', 'boss-seo' ); ?></p>
            
            <div class="boss-seo-ecommerce-section">
                <h3><?php esc_html_e( 'Types d\'extraits enrichis', 'boss-seo' ); ?></h3>
                <p><?php esc_html_e( 'Sélectionnez les types d\'extraits enrichis à activer pour vos produits.', 'boss-seo' ); ?></p>
                
                <form id="boss-seo-rich-snippets-form">
                    <table class="form-table">
                        <tbody>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Produit', 'boss-seo' ); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="rich_snippets_types[]" value="product" checked="checked">
                                        <?php esc_html_e( 'Activer les extraits enrichis de type produit', 'boss-seo' ); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Offre', 'boss-seo' ); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="rich_snippets_types[]" value="offer" checked="checked">
                                        <?php esc_html_e( 'Activer les extraits enrichis de type offre', 'boss-seo' ); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Avis', 'boss-seo' ); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="rich_snippets_types[]" value="review" checked="checked">
                                        <?php esc_html_e( 'Activer les extraits enrichis de type avis', 'boss-seo' ); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Note moyenne', 'boss-seo' ); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="rich_snippets_types[]" value="aggregate_rating" checked="checked">
                                        <?php esc_html_e( 'Activer les extraits enrichis de type note moyenne', 'boss-seo' ); ?>
                                    </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <p class="submit">
                        <button type="submit" class="button button-primary"><?php esc_html_e( 'Enregistrer les paramètres', 'boss-seo' ); ?></button>
                    </p>
                </form>
            </div>
        </div>
        
        <div id="seo-analyzer" class="tab-content">
            <h2><?php esc_html_e( 'Analyse SEO', 'boss-seo' ); ?></h2>
            <p><?php esc_html_e( 'Analysez vos produits pour améliorer leur référencement.', 'boss-seo' ); ?></p>
            
            <div class="boss-seo-ecommerce-section">
                <h3><?php esc_html_e( 'Paramètres d\'analyse', 'boss-seo' ); ?></h3>
                <p><?php esc_html_e( 'Configurez les paramètres d\'analyse SEO pour vos produits.', 'boss-seo' ); ?></p>
                
                <form id="boss-seo-analyzer-form">
                    <table class="form-table">
                        <tbody>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Analyse automatique', 'boss-seo' ); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="auto_analyze" value="1" checked="checked">
                                        <?php esc_html_e( 'Analyser automatiquement les produits lors de leur publication ou mise à jour', 'boss-seo' ); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Éléments à analyser', 'boss-seo' ); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="analyze_elements[]" value="title" checked="checked">
                                        <?php esc_html_e( 'Titre', 'boss-seo' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="analyze_elements[]" value="description" checked="checked">
                                        <?php esc_html_e( 'Description', 'boss-seo' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="analyze_elements[]" value="images" checked="checked">
                                        <?php esc_html_e( 'Images', 'boss-seo' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="analyze_elements[]" value="categories_tags" checked="checked">
                                        <?php esc_html_e( 'Catégories et tags', 'boss-seo' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="analyze_elements[]" value="attributes" checked="checked">
                                        <?php esc_html_e( 'Attributs', 'boss-seo' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="analyze_elements[]" value="price" checked="checked">
                                        <?php esc_html_e( 'Prix', 'boss-seo' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="analyze_elements[]" value="stock" checked="checked">
                                        <?php esc_html_e( 'Stock', 'boss-seo' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="analyze_elements[]" value="reviews" checked="checked">
                                        <?php esc_html_e( 'Avis', 'boss-seo' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="analyze_elements[]" value="sku" checked="checked">
                                        <?php esc_html_e( 'SKU', 'boss-seo' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="analyze_elements[]" value="permalink" checked="checked">
                                        <?php esc_html_e( 'Permalien', 'boss-seo' ); ?>
                                    </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <p class="submit">
                        <button type="submit" class="button button-primary"><?php esc_html_e( 'Enregistrer les paramètres', 'boss-seo' ); ?></button>
                    </p>
                </form>
            </div>
        </div>
        
        <div id="product-brands" class="tab-content">
            <h2><?php esc_html_e( 'Marques de produits', 'boss-seo' ); ?></h2>
            <p><?php esc_html_e( 'Gérez les marques de vos produits.', 'boss-seo' ); ?></p>
            
            <div class="boss-seo-ecommerce-section">
                <h3><?php esc_html_e( 'Ajouter une marque', 'boss-seo' ); ?></h3>
                
                <form id="boss-seo-add-brand-form">
                    <table class="form-table">
                        <tbody>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Nom de la marque', 'boss-seo' ); ?></th>
                                <td>
                                    <input type="text" name="brand_name" class="regular-text" required>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Description', 'boss-seo' ); ?></th>
                                <td>
                                    <textarea name="brand_description" class="large-text" rows="5"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Logo', 'boss-seo' ); ?></th>
                                <td>
                                    <div class="brand-logo-preview"></div>
                                    <input type="hidden" name="brand_logo" value="">
                                    <button type="button" class="button brand-logo-upload"><?php esc_html_e( 'Téléverser un logo', 'boss-seo' ); ?></button>
                                    <button type="button" class="button brand-logo-remove" style="display: none;"><?php esc_html_e( 'Supprimer le logo', 'boss-seo' ); ?></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <p class="submit">
                        <button type="submit" class="button button-primary"><?php esc_html_e( 'Ajouter la marque', 'boss-seo' ); ?></button>
                    </p>
                </form>
            </div>
            
            <div class="boss-seo-ecommerce-section">
                <h3><?php esc_html_e( 'Marques existantes', 'boss-seo' ); ?></h3>
                
                <div id="boss-seo-brands-list">
                    <div class="boss-seo-loading"><?php esc_html_e( 'Chargement des marques...', 'boss-seo' ); ?></div>
                </div>
            </div>
        </div>
        
        <div id="settings" class="tab-content">
            <h2><?php esc_html_e( 'Paramètres', 'boss-seo' ); ?></h2>
            <p><?php esc_html_e( 'Configurez les paramètres généraux du module e-commerce.', 'boss-seo' ); ?></p>
            
            <div class="boss-seo-ecommerce-section">
                <form id="boss-seo-ecommerce-settings-form">
                    <table class="form-table">
                        <tbody>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Activer les extraits enrichis', 'boss-seo' ); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="enable_rich_snippets" value="1" checked="checked">
                                        <?php esc_html_e( 'Activer les extraits enrichis pour les produits', 'boss-seo' ); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Activer l\'analyse SEO', 'boss-seo' ); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="enable_seo_analyzer" value="1" checked="checked">
                                        <?php esc_html_e( 'Activer l\'analyse SEO pour les produits', 'boss-seo' ); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e( 'Activer les marques de produits', 'boss-seo' ); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="enable_product_brands" value="1" checked="checked">
                                        <?php esc_html_e( 'Activer les marques de produits', 'boss-seo' ); ?>
                                    </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <p class="submit">
                        <button type="submit" class="button button-primary"><?php esc_html_e( 'Enregistrer les paramètres', 'boss-seo' ); ?></button>
                    </p>
                </form>
            </div>
        </div>
    </div>
</div>
