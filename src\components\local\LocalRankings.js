import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Dashicon,
  SelectControl,
  TextControl,
  Spinner
} from '@wordpress/components';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

const LocalRankings = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [locations, setLocations] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedKeyword, setSelectedKeyword] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30days');
  const [rankingData, setRankingData] = useState({
    keywords: [],
    history: []
  });
  
  // Charger les données
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour les emplacements
      const mockLocations = [
        {
          id: 1,
          name: 'Paris - Siège social',
          address: '123 Avenue des Champs-Élysées, 75008 Paris'
        },
        {
          id: 2,
          name: 'Lyon - Succursale',
          address: '45 Rue de la République, 69002 Lyon'
        },
        {
          id: 3,
          name: 'Marseille - Boutique',
          address: '78 La Canebière, 13001 Marseille'
        }
      ];
      
      setLocations(mockLocations);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  // Effet pour charger les données de classement lorsqu'un emplacement est sélectionné
  useEffect(() => {
    if (selectedLocation) {
      setIsLoading(true);
      
      // Simuler le chargement des données de classement
      setTimeout(() => {
        // Données fictives pour les mots-clés
        const mockKeywords = [
          {
            id: 1,
            keyword: `magasin ${selectedLocation.name.split(' - ')[0].toLowerCase()}`,
            position: 3,
            change: -1,
            volume: 1200,
            lastUpdated: '2023-06-15'
          },
          {
            id: 2,
            keyword: `boutique ${selectedLocation.name.split(' - ')[0].toLowerCase()}`,
            position: 5,
            change: 2,
            volume: 880,
            lastUpdated: '2023-06-15'
          },
          {
            id: 3,
            keyword: `${selectedLocation.name.split(' - ')[1].toLowerCase()} ${selectedLocation.name.split(' - ')[0].toLowerCase()}`,
            position: 1,
            change: 0,
            volume: 650,
            lastUpdated: '2023-06-15'
          },
          {
            id: 4,
            keyword: `${selectedLocation.name.split(' - ')[1].toLowerCase()} près de moi`,
            position: 8,
            change: -3,
            volume: 1500,
            lastUpdated: '2023-06-15'
          },
          {
            id: 5,
            keyword: `meilleur ${selectedLocation.name.split(' - ')[1].toLowerCase()} ${selectedLocation.name.split(' - ')[0].toLowerCase()}`,
            position: 12,
            change: 4,
            volume: 320,
            lastUpdated: '2023-06-15'
          }
        ];
        
        // Générer des données d'historique fictives
        const mockHistory = [];
        const today = new Date();
        
        // Déterminer le nombre de jours en fonction de la période sélectionnée
        let days = 30;
        if (selectedPeriod === '7days') days = 7;
        if (selectedPeriod === '90days') days = 90;
        
        // Générer des données pour chaque jour
        for (let i = days; i >= 0; i--) {
          const date = new Date(today);
          date.setDate(date.getDate() - i);
          
          const entry = {
            date: date.toISOString().split('T')[0],
            'magasin': Math.floor(Math.random() * 5) + 1,
            'boutique': Math.floor(Math.random() * 7) + 3,
            'siège social': Math.floor(Math.random() * 3) + 1,
            'près de moi': Math.floor(Math.random() * 10) + 5,
            'meilleur': Math.floor(Math.random() * 15) + 10
          };
          
          mockHistory.push(entry);
        }
        
        setRankingData({
          keywords: mockKeywords,
          history: mockHistory
        });
        
        // Sélectionner le premier mot-clé par défaut
        if (mockKeywords.length > 0) {
          setSelectedKeyword(mockKeywords[0]);
        }
        
        setIsLoading(false);
      }, 1000);
    }
  }, [selectedLocation, selectedPeriod]);
  
  // Fonction pour obtenir la classe de couleur en fonction de la position
  const getPositionColorClass = (position) => {
    if (position <= 3) return 'boss-text-green-600';
    if (position <= 10) return 'boss-text-blue-600';
    if (position <= 20) return 'boss-text-yellow-600';
    return 'boss-text-red-600';
  };
  
  // Fonction pour obtenir l'icône de changement de position
  const getPositionChangeIcon = (change) => {
    if (change < 0) return 'arrow-up-alt';
    if (change > 0) return 'arrow-down-alt';
    return 'minus';
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du changement de position
  const getPositionChangeColorClass = (change) => {
    if (change < 0) return 'boss-text-green-600';
    if (change > 0) return 'boss-text-red-600';
    return 'boss-text-gray-500';
  };
  
  // Fonction pour formater les dates pour l'axe X
  const formatXAxis = (tickItem) => {
    const date = new Date(tickItem);
    return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' });
  };
  
  // Fonction pour formater les valeurs pour le tooltip
  const formatTooltipValue = (value, name) => {
    return [value, name];
  };
  
  // Fonction pour obtenir les données d'historique pour le mot-clé sélectionné
  const getKeywordHistoryData = () => {
    if (!selectedKeyword || !rankingData.history.length) return [];
    
    // Extraire le premier mot du mot-clé pour correspondre aux clés dans l'historique
    const keywordKey = selectedKeyword.keyword.split(' ')[0];
    
    return rankingData.history.map(entry => ({
      date: entry.date,
      position: entry[keywordKey] || 0
    }));
  };
  
  // Obtenir les données d'historique pour le mot-clé sélectionné
  const keywordHistoryData = getKeywordHistoryData();

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {/* Sélecteurs */}
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4 boss-mb-6">
            <SelectControl
              label={__('Emplacement', 'boss-seo')}
              value={selectedLocation ? selectedLocation.id : ''}
              options={[
                { label: __('-- Sélectionner un emplacement --', 'boss-seo'), value: '' },
                ...locations.map(location => ({
                  label: location.name,
                  value: location.id
                }))
              ]}
              onChange={(value) => {
                const location = locations.find(loc => loc.id === parseInt(value));
                setSelectedLocation(location);
              }}
            />
            
            <SelectControl
              label={__('Période', 'boss-seo')}
              value={selectedPeriod}
              options={[
                { label: __('7 derniers jours', 'boss-seo'), value: '7days' },
                { label: __('30 derniers jours', 'boss-seo'), value: '30days' },
                { label: __('90 derniers jours', 'boss-seo'), value: '90days' }
              ]}
              onChange={setSelectedPeriod}
              disabled={!selectedLocation}
            />
            
            <div className="boss-flex boss-items-end">
              <Button
                isPrimary
                className="boss-h-10"
                onClick={() => {
                  // Simuler une actualisation des données
                  setIsLoading(true);
                  setTimeout(() => {
                    setIsLoading(false);
                  }, 1000);
                }}
                disabled={!selectedLocation}
              >
                <Dashicon icon="update" className="boss-mr-1" />
                {__('Actualiser les données', 'boss-seo')}
              </Button>
            </div>
          </div>
          
          {selectedLocation ? (
            <div>
              {/* Statistiques principales */}
              <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-4 boss-gap-4 boss-mb-6">
                <Card>
                  <CardBody className="boss-p-4">
                    <div className="boss-text-center">
                      <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                        {__('Mots-clés suivis', 'boss-seo')}
                      </h3>
                      <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
                        {rankingData.keywords.length}
                      </div>
                    </div>
                  </CardBody>
                </Card>
                
                <Card>
                  <CardBody className="boss-p-4">
                    <div className="boss-text-center">
                      <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                        {__('Top 3', 'boss-seo')}
                      </h3>
                      <div className="boss-text-2xl boss-font-bold boss-text-green-600">
                        {rankingData.keywords.filter(kw => kw.position <= 3).length}
                      </div>
                    </div>
                  </CardBody>
                </Card>
                
                <Card>
                  <CardBody className="boss-p-4">
                    <div className="boss-text-center">
                      <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                        {__('Position moyenne', 'boss-seo')}
                      </h3>
                      <div className="boss-text-2xl boss-font-bold boss-text-boss-primary">
                        {rankingData.keywords.length > 0
                          ? (rankingData.keywords.reduce((sum, kw) => sum + kw.position, 0) / rankingData.keywords.length).toFixed(1)
                          : '-'
                        }
                      </div>
                    </div>
                  </CardBody>
                </Card>
                
                <Card>
                  <CardBody className="boss-p-4">
                    <div className="boss-text-center">
                      <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                        {__('Progression', 'boss-seo')}
                      </h3>
                      <div className="boss-text-2xl boss-font-bold boss-text-boss-success">
                        {rankingData.keywords.length > 0
                          ? (() => {
                              const totalChange = rankingData.keywords.reduce((sum, kw) => sum - kw.change, 0);
                              return totalChange > 0 ? `+${totalChange}` : totalChange;
                            })()
                          : '-'
                        }
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
              
              {/* Graphique d'évolution */}
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <div className="boss-flex boss-justify-between boss-items-center">
                    <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                      {__('Évolution des positions', 'boss-seo')}
                    </h2>
                    <SelectControl
                      value={selectedKeyword ? selectedKeyword.id : ''}
                      options={rankingData.keywords.map(kw => ({
                        label: kw.keyword,
                        value: kw.id
                      }))}
                      onChange={(value) => {
                        const keyword = rankingData.keywords.find(kw => kw.id === parseInt(value));
                        setSelectedKeyword(keyword);
                      }}
                    />
                  </div>
                </CardHeader>
                <CardBody>
                  {keywordHistoryData.length > 0 ? (
                    <div className="boss-h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={keywordHistoryData}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                          <XAxis 
                            dataKey="date" 
                            tickFormatter={formatXAxis} 
                            tick={{ fontSize: 12 }}
                          />
                          <YAxis 
                            reversed
                            domain={[1, 'dataMax']}
                            tick={{ fontSize: 12 }}
                          />
                          <Tooltip 
                            formatter={formatTooltipValue}
                            labelFormatter={(label) => new Date(label).toLocaleDateString('fr-FR', { 
                              day: 'numeric', 
                              month: 'long', 
                              year: 'numeric' 
                            })}
                          />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="position"
                            name={selectedKeyword ? selectedKeyword.keyword : __('Position', 'boss-seo')}
                            stroke="#4F46E5"
                            activeDot={{ r: 8 }}
                            strokeWidth={2}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  ) : (
                    <div className="boss-text-center boss-py-8 boss-text-boss-gray">
                      {__('Aucune donnée disponible pour ce mot-clé.', 'boss-seo')}
                    </div>
                  )}
                </CardBody>
              </Card>
              
              {/* Liste des mots-clés */}
              <Card>
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Mots-clés locaux', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody className="boss-p-0">
                  <div className="boss-overflow-x-auto">
                    <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
                      <thead className="boss-bg-gray-50">
                        <tr>
                          <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                            {__('Mot-clé', 'boss-seo')}
                          </th>
                          <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                            {__('Position', 'boss-seo')}
                          </th>
                          <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                            {__('Évolution', 'boss-seo')}
                          </th>
                          <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                            {__('Volume', 'boss-seo')}
                          </th>
                          <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                            {__('Dernière mise à jour', 'boss-seo')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                        {rankingData.keywords.map(keyword => (
                          <tr 
                            key={keyword.id} 
                            className={`boss-hover:boss-bg-gray-50 ${
                              selectedKeyword && selectedKeyword.id === keyword.id
                                ? 'boss-bg-blue-50'
                                : ''
                            }`}
                            onClick={() => setSelectedKeyword(keyword)}
                          >
                            <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                              <div className="boss-font-medium boss-text-boss-dark">{keyword.keyword}</div>
                            </td>
                            <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                              <div className={`boss-font-medium ${getPositionColorClass(keyword.position)}`}>
                                {keyword.position}
                              </div>
                            </td>
                            <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                              <div className={`boss-flex boss-items-center ${getPositionChangeColorClass(keyword.change)}`}>
                                <Dashicon icon={getPositionChangeIcon(keyword.change)} className="boss-mr-1" />
                                <span>{Math.abs(keyword.change)}</span>
                              </div>
                            </td>
                            <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                              <div className="boss-text-boss-gray">{keyword.volume}</div>
                            </td>
                            <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                              <div className="boss-text-boss-gray">{keyword.lastUpdated}</div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardBody>
              </Card>
            </div>
          ) : (
            <Card>
              <CardBody>
                <div className="boss-text-center boss-py-8">
                  <div className="boss-text-5xl boss-text-boss-gray boss-mb-4">
                    <Dashicon icon="location" />
                  </div>
                  <h2 className="boss-text-xl boss-font-bold boss-mb-4">
                    {__('Sélectionnez un emplacement', 'boss-seo')}
                  </h2>
                  <p className="boss-text-boss-gray boss-mb-6">
                    {__('Veuillez sélectionner un emplacement pour afficher les données de classement local.', 'boss-seo')}
                  </p>
                </div>
              </CardBody>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

export default LocalRankings;
