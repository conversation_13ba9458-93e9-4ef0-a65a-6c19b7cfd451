import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  SelectControl,
  TextControl,
  Modal,
  Spinner
} from '@wordpress/components';

const ReportsLibrary = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [reports, setReports] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  
  // Charger les données
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour les catégories
      const mockCategories = [
        { id: 'performance', name: __('Performance', 'boss-seo') },
        { id: 'keywords', name: __('Mots-clés', 'boss-seo') },
        { id: 'content', name: __('Contenu', 'boss-seo') },
        { id: 'technical', name: __('Technique', 'boss-seo') },
        { id: 'local', name: __('SEO Local', 'boss-seo') },
        { id: 'ecommerce', name: __('E-commerce', 'boss-seo') }
      ];
      
      // Données fictives pour les rapports
      const mockReports = [
        {
          id: 1,
          title: __('Rapport de performance SEO', 'boss-seo'),
          description: __('Vue d\'ensemble des performances SEO avec métriques clés et tendances.', 'boss-seo'),
          category: 'performance',
          thumbnail: 'https://example.com/thumbnails/performance.jpg',
          metrics: ['Visibilité', 'Classement moyen', 'Trafic organique', 'CTR'],
          frequency: 'weekly',
          lastGenerated: '2023-06-15'
        },
        {
          id: 2,
          title: __('Analyse des mots-clés', 'boss-seo'),
          description: __('Analyse détaillée des performances des mots-clés et opportunités.', 'boss-seo'),
          category: 'keywords',
          thumbnail: 'https://example.com/thumbnails/keywords.jpg',
          metrics: ['Position', 'Volume de recherche', 'Difficulté', 'Opportunités'],
          frequency: 'monthly',
          lastGenerated: '2023-06-01'
        },
        {
          id: 3,
          title: __('Audit de contenu', 'boss-seo'),
          description: __('Analyse qualitative et quantitative du contenu du site.', 'boss-seo'),
          category: 'content',
          thumbnail: 'https://example.com/thumbnails/content.jpg',
          metrics: ['Qualité', 'Lisibilité', 'Mots-clés', 'Engagement'],
          frequency: 'monthly',
          lastGenerated: null
        },
        {
          id: 4,
          title: __('Audit technique', 'boss-seo'),
          description: __('Analyse technique complète du site avec recommandations.', 'boss-seo'),
          category: 'technical',
          thumbnail: 'https://example.com/thumbnails/technical.jpg',
          metrics: ['Erreurs', 'Vitesse', 'Mobile', 'Structure'],
          frequency: 'quarterly',
          lastGenerated: '2023-05-10'
        },
        {
          id: 5,
          title: __('Performance SEO local', 'boss-seo'),
          description: __('Analyse des performances dans les résultats de recherche locaux.', 'boss-seo'),
          category: 'local',
          thumbnail: 'https://example.com/thumbnails/local.jpg',
          metrics: ['Visibilité locale', 'GMB', 'Avis', 'Citations'],
          frequency: 'monthly',
          lastGenerated: null
        },
        {
          id: 6,
          title: __('Analyse e-commerce', 'boss-seo'),
          description: __('Performances SEO des pages produits et catégories.', 'boss-seo'),
          category: 'ecommerce',
          thumbnail: 'https://example.com/thumbnails/ecommerce.jpg',
          metrics: ['Visibilité produits', 'CTR', 'Conversions', 'Revenus'],
          frequency: 'weekly',
          lastGenerated: '2023-06-10'
        },
        {
          id: 7,
          title: __('Rapport Core Web Vitals', 'boss-seo'),
          description: __('Analyse détaillée des métriques Core Web Vitals et recommandations.', 'boss-seo'),
          category: 'performance',
          thumbnail: 'https://example.com/thumbnails/cwv.jpg',
          metrics: ['LCP', 'FID', 'CLS', 'TTFB'],
          frequency: 'monthly',
          lastGenerated: '2023-06-05'
        },
        {
          id: 8,
          title: __('Analyse de la concurrence', 'boss-seo'),
          description: __('Comparaison avec les principaux concurrents sur les métriques SEO clés.', 'boss-seo'),
          category: 'keywords',
          thumbnail: 'https://example.com/thumbnails/competitors.jpg',
          metrics: ['Gap analysis', 'Mots-clés communs', 'Opportunités', 'Backlinks'],
          frequency: 'quarterly',
          lastGenerated: null
        }
      ];
      
      setCategories(mockCategories);
      setReports(mockReports);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  // Fonction pour filtrer les rapports
  const getFilteredReports = () => {
    return reports.filter(report => {
      // Filtrer par catégorie
      const matchesCategory = selectedCategory === 'all' || report.category === selectedCategory;
      
      // Filtrer par recherche
      const matchesSearch = searchQuery === '' || 
        report.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        report.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      return matchesCategory && matchesSearch;
    });
  };
  
  // Obtenir les rapports filtrés
  const filteredReports = getFilteredReports();
  
  // Fonction pour prévisualiser un rapport
  const handlePreview = (report) => {
    setSelectedReport(report);
    setShowPreview(true);
  };
  
  // Fonction pour générer un rapport
  const handleGenerate = (report) => {
    setIsGenerating(true);
    
    // Simuler la génération
    setTimeout(() => {
      setIsGenerating(false);
      
      // Mettre à jour la date de dernière génération
      const updatedReports = reports.map(r => {
        if (r.id === report.id) {
          return {
            ...r,
            lastGenerated: new Date().toISOString().split('T')[0]
          };
        }
        return r;
      });
      
      setReports(updatedReports);
      
      // Si on est dans la prévisualisation, mettre à jour le rapport sélectionné
      if (selectedReport && selectedReport.id === report.id) {
        setSelectedReport({
          ...selectedReport,
          lastGenerated: new Date().toISOString().split('T')[0]
        });
      }
      
      // Rediriger vers la page du rapport (simulation)
      alert(__('Rapport généré avec succès ! Vous allez être redirigé vers la page du rapport.', 'boss-seo'));
    }, 2000);
  };
  
  // Fonction pour obtenir le texte de la fréquence
  const getFrequencyText = (frequency) => {
    switch (frequency) {
      case 'daily':
        return __('Quotidien', 'boss-seo');
      case 'weekly':
        return __('Hebdomadaire', 'boss-seo');
      case 'monthly':
        return __('Mensuel', 'boss-seo');
      case 'quarterly':
        return __('Trimestriel', 'boss-seo');
      default:
        return frequency;
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          <Card className="boss-mb-6">
            <CardBody>
              <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4">
                <TextControl
                  placeholder={__('Rechercher un rapport...', 'boss-seo')}
                  value={searchQuery}
                  onChange={setSearchQuery}
                />
                
                <SelectControl
                  label=""
                  value={selectedCategory}
                  options={[
                    { label: __('Toutes les catégories', 'boss-seo'), value: 'all' },
                    ...categories.map(category => ({
                      label: category.name,
                      value: category.id
                    }))
                  ]}
                  onChange={setSelectedCategory}
                />
                
                <div className="boss-flex boss-items-end">
                  <Button
                    isPrimary
                    href="#report-creator"
                    className="boss-w-full"
                  >
                    {__('Créer un rapport personnalisé', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
          
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-6">
            {filteredReports.length === 0 ? (
              <div className="boss-col-span-3 boss-text-center boss-py-12 boss-text-boss-gray">
                {__('Aucun rapport trouvé.', 'boss-seo')}
              </div>
            ) : (
              filteredReports.map(report => (
                <Card key={report.id} className="boss-transition-all boss-duration-300 boss-hover:boss-shadow-lg">
                  <div className="boss-relative boss-h-40 boss-bg-gray-100 boss-rounded-t-lg boss-overflow-hidden">
                    {report.thumbnail ? (
                      <img 
                        src={report.thumbnail} 
                        alt={report.title} 
                        className="boss-w-full boss-h-full boss-object-cover"
                      />
                    ) : (
                      <div className="boss-flex boss-justify-center boss-items-center boss-h-full boss-bg-gray-200">
                        <span className="dashicons dashicons-chart-bar boss-text-5xl boss-text-gray-400"></span>
                      </div>
                    )}
                    <div className="boss-absolute boss-top-2 boss-right-2">
                      <span className="boss-px-2 boss-py-1 boss-text-xs boss-font-medium boss-rounded-full boss-bg-blue-100 boss-text-blue-800">
                        {categories.find(c => c.id === report.category)?.name || report.category}
                      </span>
                    </div>
                  </div>
                  <CardBody>
                    <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-2">
                      {report.title}
                    </h3>
                    <p className="boss-text-sm boss-text-boss-gray boss-mb-4">
                      {report.description}
                    </p>
                    <div className="boss-flex boss-flex-wrap boss-gap-2 boss-mb-4">
                      {report.metrics.map((metric, index) => (
                        <span 
                          key={index} 
                          className="boss-px-2 boss-py-1 boss-text-xs boss-rounded-full boss-bg-gray-100 boss-text-boss-gray"
                        >
                          {metric}
                        </span>
                      ))}
                    </div>
                    <div className="boss-flex boss-justify-between boss-items-center boss-text-sm boss-text-boss-gray boss-mb-4">
                      <span>
                        {getFrequencyText(report.frequency)}
                      </span>
                      <span>
                        {report.lastGenerated 
                          ? __('Généré le:', 'boss-seo') + ' ' + report.lastGenerated 
                          : __('Jamais généré', 'boss-seo')}
                      </span>
                    </div>
                  </CardBody>
                  <CardFooter className="boss-border-t boss-border-gray-200">
                    <div className="boss-flex boss-justify-between boss-space-x-2">
                      <Button
                        isSecondary
                        onClick={() => handlePreview(report)}
                      >
                        {__('Aperçu', 'boss-seo')}
                      </Button>
                      <Button
                        isPrimary
                        onClick={() => handleGenerate(report)}
                        isBusy={isGenerating && selectedReport?.id === report.id}
                        disabled={isGenerating}
                      >
                        {__('Générer', 'boss-seo')}
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))
            )}
          </div>
          
          {/* Modal de prévisualisation */}
          {showPreview && selectedReport && (
            <Modal
              title={selectedReport.title}
              onRequestClose={() => setShowPreview(false)}
              className="boss-report-preview-modal"
            >
              <div className="boss-p-6">
                <div className="boss-mb-6">
                  <div className="boss-h-60 boss-bg-gray-100 boss-rounded-lg boss-overflow-hidden boss-mb-4">
                    {selectedReport.thumbnail ? (
                      <img 
                        src={selectedReport.thumbnail} 
                        alt={selectedReport.title} 
                        className="boss-w-full boss-h-full boss-object-cover"
                      />
                    ) : (
                      <div className="boss-flex boss-justify-center boss-items-center boss-h-full boss-bg-gray-200">
                        <span className="dashicons dashicons-chart-bar boss-text-6xl boss-text-gray-400"></span>
                      </div>
                    )}
                  </div>
                  
                  <h2 className="boss-text-xl boss-font-bold boss-text-boss-dark boss-mb-2">
                    {selectedReport.title}
                  </h2>
                  
                  <p className="boss-text-boss-gray boss-mb-4">
                    {selectedReport.description}
                  </p>
                  
                  <div className="boss-grid boss-grid-cols-2 boss-gap-4 boss-mb-6">
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Catégorie', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {categories.find(c => c.id === selectedReport.category)?.name || selectedReport.category}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Fréquence recommandée', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {getFrequencyText(selectedReport.frequency)}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Dernière génération', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        {selectedReport.lastGenerated || __('Jamais généré', 'boss-seo')}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Format', 'boss-seo')}
                      </h3>
                      <p className="boss-text-boss-gray">
                        PDF, CSV, HTML
                      </p>
                    </div>
                  </div>
                  
                  <div className="boss-mb-6">
                    <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                      {__('Métriques incluses', 'boss-seo')}
                    </h3>
                    
                    <div className="boss-flex boss-flex-wrap boss-gap-2">
                      {selectedReport.metrics.map((metric, index) => (
                        <span 
                          key={index} 
                          className="boss-px-3 boss-py-1 boss-text-sm boss-rounded-full boss-bg-blue-50 boss-text-blue-700"
                        >
                          {metric}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="boss-p-4 boss-bg-gray-50 boss-rounded-lg boss-mb-6">
                    <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                      {__('À propos de ce rapport', 'boss-seo')}
                    </h3>
                    
                    <p className="boss-text-sm boss-text-boss-gray boss-mb-2">
                      {__('Ce rapport vous fournit une analyse détaillée de vos performances SEO, avec des métriques clés et des recommandations d\'amélioration.', 'boss-seo')}
                    </p>
                    
                    <p className="boss-text-sm boss-text-boss-gray">
                      {__('Utilisez-le pour suivre vos progrès au fil du temps et identifier les opportunités d\'optimisation.', 'boss-seo')}
                    </p>
                  </div>
                </div>
                
                <div className="boss-flex boss-justify-between">
                  <Button
                    isSecondary
                    onClick={() => setShowPreview(false)}
                  >
                    {__('Fermer', 'boss-seo')}
                  </Button>
                  
                  <Button
                    isPrimary
                    onClick={() => {
                      handleGenerate(selectedReport);
                      setShowPreview(false);
                    }}
                    isBusy={isGenerating}
                    disabled={isGenerating}
                  >
                    {__('Générer ce rapport', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </Modal>
          )}
        </div>
      )}
    </div>
  );
};

export default ReportsLibrary;
