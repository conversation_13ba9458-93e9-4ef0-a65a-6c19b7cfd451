<?php
/**
 * Classe pour gérer les variables d'environnement.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe pour gérer les variables d'environnement.
 *
 * Cette classe permet de charger et d'accéder aux variables d'environnement
 * stockées dans un fichier .env à la racine du plugin.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Env {

    /**
     * Les variables d'environnement.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $env    Les variables d'environnement.
     */
    private static $env = array();

    /**
     * Indique si les variables d'environnement ont été chargées.
     *
     * @since    1.2.0
     * @access   private
     * @var      bool    $loaded    Indique si les variables d'environnement ont été chargées.
     */
    private static $loaded = false;

    /**
     * Charge les variables d'environnement depuis le fichier .env.
     *
     * @since    1.2.0
     * @return   bool    True si le fichier a été chargé, false sinon.
     */
    public static function load() {
        if ( self::$loaded ) {
            return true;
        }

        $env_file = plugin_dir_path( dirname( __FILE__ ) ) . '.env';

        if ( ! file_exists( $env_file ) ) {
            return false;
        }

        $env_content = file_get_contents( $env_file );
        $lines = explode( "\n", $env_content );

        foreach ( $lines as $line ) {
            $line = trim( $line );

            // Ignorer les commentaires et les lignes vides
            if ( empty( $line ) || strpos( $line, '#' ) === 0 ) {
                continue;
            }

            // Extraire la clé et la valeur
            list( $key, $value ) = explode( '=', $line, 2 );
            $key = trim( $key );
            $value = trim( $value );

            // Supprimer les guillemets si nécessaire
            if ( strpos( $value, '"' ) === 0 && strrpos( $value, '"' ) === strlen( $value ) - 1 ) {
                $value = substr( $value, 1, -1 );
            } elseif ( strpos( $value, "'" ) === 0 && strrpos( $value, "'" ) === strlen( $value ) - 1 ) {
                $value = substr( $value, 1, -1 );
            }

            self::$env[ $key ] = $value;
        }

        self::$loaded = true;
        return true;
    }

    /**
     * Récupère une variable d'environnement.
     *
     * @since    1.2.0
     * @param    string    $key         La clé de la variable.
     * @param    mixed     $default     La valeur par défaut si la variable n'existe pas.
     * @return   mixed                  La valeur de la variable ou la valeur par défaut.
     */
    public static function get( $key, $default = null ) {
        if ( ! self::$loaded ) {
            self::load();
        }

        return isset( self::$env[ $key ] ) ? self::$env[ $key ] : $default;
    }

    /**
     * Vérifie si une variable d'environnement existe.
     *
     * @since    1.2.0
     * @param    string    $key    La clé de la variable.
     * @return   bool              True si la variable existe, false sinon.
     */
    public static function has( $key ) {
        if ( ! self::$loaded ) {
            self::load();
        }

        return isset( self::$env[ $key ] );
    }
}
