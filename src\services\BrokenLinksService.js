/**
 * Service pour la gestion des liens cassés
 */
import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

class BrokenLinksService {
  /**
   * Récupère la liste des liens cassés
   * 
   * @param {Object} filters - Filtres à appliquer
   * @returns {Promise} - Promesse contenant les liens cassés
   */
  async getBrokenLinks(filters = {}) {
    try {
      const path = addQueryArgs('/boss-seo/v1/technical/broken-links', filters);
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des liens cassés:', error);
      throw error;
    }
  }

  /**
   * Récupère les statistiques des liens cassés
   * 
   * @returns {Promise} - Promesse contenant les statistiques
   */
  async getStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/technical/broken-links/stats'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  /**
   * Lance un scan des liens cassés
   * 
   * @returns {Promise} - Promesse du scan
   */
  async startScan() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/technical/broken-links/scan',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du lancement du scan:', error);
      throw error;
    }
  }

  /**
   * Revérifie un lien spécifique
   * 
   * @param {number} linkId - ID du lien à revérifier
   * @returns {Promise} - Promesse de la revérification
   */
  async recheckLink(linkId) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/technical/broken-links/${linkId}/recheck`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la revérification du lien:', error);
      throw error;
    }
  }

  /**
   * Ignore un lien cassé
   * 
   * @param {number} linkId - ID du lien à ignorer
   * @returns {Promise} - Promesse de l'action
   */
  async ignoreLink(linkId) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/technical/broken-links/${linkId}/ignore`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'ajout du lien à la liste d\'ignorés:', error);
      throw error;
    }
  }

  /**
   * Supprime un lien de la liste d'ignorés
   * 
   * @param {number} linkId - ID du lien à ne plus ignorer
   * @returns {Promise} - Promesse de l'action
   */
  async unignoreLink(linkId) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/technical/broken-links/${linkId}/unignore`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la suppression du lien de la liste d\'ignorés:', error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres de détection des liens cassés
   * 
   * @returns {Promise} - Promesse contenant les paramètres
   */
  async getSettings() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/technical/broken-links/settings'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres:', error);
      throw error;
    }
  }

  /**
   * Sauvegarde les paramètres de détection des liens cassés
   * 
   * @param {Object} settings - Paramètres à sauvegarder
   * @returns {Promise} - Promesse de la sauvegarde
   */
  async saveSettings(settings) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/technical/broken-links/settings',
        method: 'POST',
        data: settings
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des paramètres:', error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des scans
   * 
   * @returns {Promise} - Promesse contenant l'historique
   */
  async getScanHistory() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/technical/broken-links/history'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      throw error;
    }
  }

  /**
   * Exporte les liens cassés au format CSV
   * 
   * @returns {Promise} - Promesse contenant les données CSV
   */
  async exportToCsv() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/technical/broken-links/export',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'export CSV:', error);
      throw error;
    }
  }

  /**
   * Supprime tous les liens cassés résolus
   * 
   * @returns {Promise} - Promesse de la suppression
   */
  async cleanupResolvedLinks() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/technical/broken-links/cleanup',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du nettoyage:', error);
      throw error;
    }
  }

  /**
   * Récupère les suggestions de correction pour un lien cassé
   * 
   * @param {number} linkId - ID du lien
   * @returns {Promise} - Promesse contenant les suggestions
   */
  async getSuggestions(linkId) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/technical/broken-links/${linkId}/suggestions`
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des suggestions:', error);
      throw error;
    }
  }

  /**
   * Applique une correction automatique pour un lien cassé
   * 
   * @param {number} linkId - ID du lien
   * @param {string} newUrl - Nouvelle URL de remplacement
   * @returns {Promise} - Promesse de la correction
   */
  async autoFix(linkId, newUrl) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/technical/broken-links/${linkId}/fix`,
        method: 'POST',
        data: { new_url: newUrl }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la correction automatique:', error);
      throw error;
    }
  }
}

// Exporter une instance unique du service
export default new BrokenLinksService();
