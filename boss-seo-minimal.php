<?php
/**
 * Plugin Name: Boss SEO (Minimal)
 * Plugin URI: https://bossseo.com
 * Description: Un plugin SEO avancé avec intelligence artificielle pour WordPress - Version minimale pour test
 * Version: 1.1.0
 * Author: Boss SEO Team
 * Author URI: https://bossseo.com
 * Text Domain: boss-seo
 * Domain Path: /languages
 */

// Si ce fichier est appelé directement, on sort.
if (!defined('WPINC')) {
    die;
}

// Définition des constantes
define('BOSS_SEO_VERSION', '1.1.0');
define('BOSS_SEO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BOSS_SEO_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * La fonction exécutée lors de l'activation du plugin.
 */
function activate_boss_seo_minimal() {
    require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-seo-activator.php';
    Boss_SEO_Activator::activate();
}

/**
 * La fonction exécutée lors de la désactivation du plugin.
 */
function deactivate_boss_seo_minimal() {
    // Désactivation simple
    update_option('boss_seo_deactivated', current_time('mysql'));
}

register_activation_hook(__FILE__, 'activate_boss_seo_minimal');
register_deactivation_hook(__FILE__, 'deactivate_boss_seo_minimal');

/**
 * Classe principale minimale
 */
class Boss_SEO_Minimal {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
    }
    
    public function init() {
        // Initialisation minimale
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Boss SEO',
            'Boss SEO',
            'manage_options',
            'boss-seo',
            array($this, 'admin_page'),
            'dashicons-search',
            30
        );
    }
    
    public function admin_page() {
        echo '<div class="wrap">';
        echo '<h1>Boss SEO - Version Minimale</h1>';
        echo '<p>Le plugin Boss SEO est activé avec succès !</p>';
        echo '<p>Version: ' . BOSS_SEO_VERSION . '</p>';
        echo '<p>Cette version minimale confirme que l\'activation fonctionne.</p>';
        echo '</div>';
    }
    
    public function run() {
        // Démarrer le plugin
    }
}

/**
 * Commence l'exécution du plugin.
 */
function run_boss_seo_minimal() {
    $plugin = new Boss_SEO_Minimal();
    $plugin->run();
}

run_boss_seo_minimal();
?>
