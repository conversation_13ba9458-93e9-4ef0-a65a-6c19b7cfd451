const defaultConfig = require('@wordpress/scripts/config/webpack.config');
const path = require('path');

module.exports = {
  ...defaultConfig,
  entry: {
    dashboard: path.resolve(__dirname, 'src/index.js'),
  },
  output: {
    path: path.resolve(__dirname, 'assets/js'),
    filename: '[name].js',
  },
  externals: {
    ...defaultConfig.externals,
    '@wordpress/api-fetch': 'wp.apiFetch',
    '@wordpress/url': 'wp.url'
  },
};
