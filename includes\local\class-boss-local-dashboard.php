<?php
/**
 * Classe pour le tableau de bord local.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour le tableau de bord local.
 *
 * Cette classe gère le tableau de bord local.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_Dashboard {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_local_dashboard_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_local_stats', array( $this, 'ajax_get_local_stats' ) );
        add_action( 'wp_ajax_boss_seo_get_local_visibility', array( $this, 'ajax_get_local_visibility' ) );
        add_action( 'wp_ajax_boss_seo_get_local_performance', array( $this, 'ajax_get_local_performance' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/local/dashboard',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_dashboard_data' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_local_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/visibility',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_local_visibility' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/performance',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_local_performance' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/dashboard/locations',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_dashboard_locations' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les statistiques locales.
     *
     * @since    1.2.0
     */
    public function ajax_get_local_stats() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les statistiques
        $stats = $this->get_local_stats_data();

        wp_send_json_success( array(
            'message' => __( 'Statistiques récupérées avec succès.', 'boss-seo' ),
            'stats'   => $stats,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer la visibilité locale.
     *
     * @since    1.2.0
     */
    public function ajax_get_local_visibility() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $location_id = isset( $_POST['location_id'] ) ? absint( $_POST['location_id'] ) : 0;
        $period = isset( $_POST['period'] ) ? sanitize_text_field( $_POST['period'] ) : '30d';

        // Récupérer la visibilité
        $visibility = $this->get_local_visibility_data( $location_id, $period );

        wp_send_json_success( array(
            'message'    => __( 'Visibilité récupérée avec succès.', 'boss-seo' ),
            'visibility' => $visibility,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer la performance locale.
     *
     * @since    1.2.0
     */
    public function ajax_get_local_performance() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $location_id = isset( $_POST['location_id'] ) ? absint( $_POST['location_id'] ) : 0;
        $period = isset( $_POST['period'] ) ? sanitize_text_field( $_POST['period'] ) : '30d';

        // Récupérer la performance
        $performance = $this->get_local_performance_data( $location_id, $period );

        wp_send_json_success( array(
            'message'     => __( 'Performance récupérée avec succès.', 'boss-seo' ),
            'performance' => $performance,
        ) );
    }

    /**
     * Récupère les statistiques locales via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_local_stats( $request ) {
        $stats = $this->get_local_stats_data();

        return rest_ensure_response( $stats );
    }

    /**
     * Récupère la visibilité locale via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_local_visibility( $request ) {
        $location_id = $request->get_param( 'location_id' ) ? absint( $request->get_param( 'location_id' ) ) : 0;
        $period = $request->get_param( 'period' ) ? sanitize_text_field( $request->get_param( 'period' ) ) : '30d';

        $visibility = $this->get_local_visibility_data( $location_id, $period );

        return rest_ensure_response( $visibility );
    }

    /**
     * Récupère la performance locale via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_local_performance( $request ) {
        $location_id = $request->get_param( 'location_id' ) ? absint( $request->get_param( 'location_id' ) ) : 0;
        $period = $request->get_param( 'period' ) ? sanitize_text_field( $request->get_param( 'period' ) ) : '30d';

        $performance = $this->get_local_performance_data( $location_id, $period );

        return rest_ensure_response( $performance );
    }

    /**
     * Récupère les données du tableau de bord via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_dashboard_data( $request ) {
        try {
            // Récupérer les statistiques
            $stats = $this->get_local_stats_data();

            // Récupérer les emplacements récents
            $recent_locations = $this->get_recent_locations();

            // Récupérer les tâches
            $tasks = $this->get_tasks();

            // Récupérer les performances
            $performance = $this->get_performance_summary();

            return rest_ensure_response( array(
                'stats'            => $stats,
                'recent_locations' => $recent_locations,
                'tasks'            => $tasks,
                'performance'      => $performance,
            ) );
        } catch (Exception $e) {
            // Log l'erreur
            error_log('Erreur dans get_dashboard_data: ' . $e->getMessage());

            // Retourner une réponse d'erreur
            return new WP_Error(
                'boss_seo_dashboard_error',
                __('Une erreur est survenue lors de la récupération des données du tableau de bord.', 'boss-seo'),
                array('status' => 500, 'error' => $e->getMessage())
            );
        }
    }

    /**
     * Récupère les statistiques locales.
     *
     * @since    1.2.0
     * @return   array    Les statistiques locales.
     */
    private function get_local_stats_data() {
        // Récupérer les emplacements
        $locations = get_posts( array(
            'post_type'      => 'boss_local_location',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
        ) );

        // Récupérer les pages locales
        $pages = get_posts( array(
            'post_type'      => 'boss_local_page',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
        ) );

        // Récupérer les mots-clés
        $keywords = $this->get_all_keywords();

        // Calculer les statistiques
        $total_locations = count( $locations );
        $total_pages = count( $pages );
        $total_keywords = count( $keywords );

        // Calculer le score SEO moyen
        $total_score = 0;
        $analyzed_locations = 0;

        foreach ( $locations as $location ) {
            $analysis = get_post_meta( $location->ID, '_boss_local_analysis', true );

            if ( ! empty( $analysis ) && isset( $analysis['score'] ) ) {
                $total_score += $analysis['score'];
                $analyzed_locations++;
            }
        }

        $average_score = $analyzed_locations > 0 ? round( $total_score / $analyzed_locations ) : 0;

        // Calculer la position moyenne
        $total_position = 0;
        $tracked_keywords = 0;

        foreach ( $keywords as $keyword ) {
            if ( isset( $keyword['position'] ) && $keyword['position'] > 0 ) {
                $total_position += $keyword['position'];
                $tracked_keywords++;
            }
        }

        $average_position = $tracked_keywords > 0 ? round( $total_position / $tracked_keywords, 1 ) : 0;

        // Préparer les statistiques
        $stats = array(
            'total_locations'  => $total_locations,
            'total_pages'      => $total_pages,
            'total_keywords'   => $total_keywords,
            'average_score'    => $average_score,
            'average_position' => $average_position,
            'locations'        => array(),
        );

        // Ajouter les statistiques par emplacement
        foreach ( $locations as $location ) {
            $location_keywords = array_filter( $keywords, function( $keyword ) use ( $location ) {
                return isset( $keyword['location_id'] ) && $keyword['location_id'] == $location->ID;
            } );

            $location_pages = array_filter( $pages, function( $page ) use ( $location ) {
                $page_location_id = get_post_meta( $page->ID, '_boss_local_location_id', true );
                return $page_location_id == $location->ID;
            } );

            $analysis = get_post_meta( $location->ID, '_boss_local_analysis', true );
            $score = ! empty( $analysis ) && isset( $analysis['score'] ) ? $analysis['score'] : 0;

            $total_position = 0;
            $tracked_keywords = 0;

            foreach ( $location_keywords as $keyword ) {
                if ( isset( $keyword['position'] ) && $keyword['position'] > 0 ) {
                    $total_position += $keyword['position'];
                    $tracked_keywords++;
                }
            }

            $average_position = $tracked_keywords > 0 ? round( $total_position / $tracked_keywords, 1 ) : 0;

            $stats['locations'][] = array(
                'id'               => $location->ID,
                'title'            => $location->post_title,
                'score'            => $score,
                'keywords'         => count( $location_keywords ),
                'pages'            => count( $location_pages ),
                'average_position' => $average_position,
            );
        }

        return $stats;
    }

    /**
     * Récupère la visibilité locale.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @param    string    $period         La période.
     * @return   array                     La visibilité locale.
     */
    private function get_local_visibility_data( $location_id, $period ) {
        // Déterminer la période
        $days = 30;

        switch ( $period ) {
            case '7d':
                $days = 7;
                break;
            case '30d':
                $days = 30;
                break;
            case '90d':
                $days = 90;
                break;
            case '180d':
                $days = 180;
                break;
            case '365d':
                $days = 365;
                break;
        }

        // Récupérer les mots-clés
        $keywords = $this->get_keywords_by_location( $location_id );

        // Récupérer l'historique des positions
        $history = $this->get_positions_history( $location_id, $days );

        // Préparer les données de visibilité
        $visibility = array(
            'keywords'      => $keywords,
            'history'       => $history,
            'distribution'  => array(
                'top3'      => 0,
                'top10'     => 0,
                'top20'     => 0,
                'top50'     => 0,
                'top100'    => 0,
                'not_ranked' => 0,
            ),
        );

        // Calculer la distribution des positions
        foreach ( $keywords as $keyword ) {
            if ( isset( $keyword['position'] ) ) {
                $position = $keyword['position'];

                if ( $position <= 3 ) {
                    $visibility['distribution']['top3']++;
                } elseif ( $position <= 10 ) {
                    $visibility['distribution']['top10']++;
                } elseif ( $position <= 20 ) {
                    $visibility['distribution']['top20']++;
                } elseif ( $position <= 50 ) {
                    $visibility['distribution']['top50']++;
                } elseif ( $position <= 100 ) {
                    $visibility['distribution']['top100']++;
                } else {
                    $visibility['distribution']['not_ranked']++;
                }
            } else {
                $visibility['distribution']['not_ranked']++;
            }
        }

        return $visibility;
    }

    /**
     * Récupère la performance locale.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @param    string    $period         La période.
     * @return   array                     La performance locale.
     */
    private function get_local_performance_data( $location_id, $period ) {
        // Déterminer la période
        $days = 30;

        switch ( $period ) {
            case '7d':
                $days = 7;
                break;
            case '30d':
                $days = 30;
                break;
            case '90d':
                $days = 90;
                break;
            case '180d':
                $days = 180;
                break;
            case '365d':
                $days = 365;
                break;
        }

        // Récupérer l'historique des analyses
        $analysis_history = $this->get_analysis_history( $location_id, $days );

        // Récupérer l'historique des positions
        $positions_history = $this->get_positions_history( $location_id, $days );

        // Préparer les données de performance
        $performance = array(
            'analysis_history'   => $analysis_history,
            'positions_history'  => $positions_history,
        );

        return $performance;
    }

    /**
     * Récupère tous les mots-clés.
     *
     * @since    1.2.0
     * @return   array    Les mots-clés.
     */
    private function get_all_keywords() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'boss_local_keywords';

        // Vérifier si la table existe
        if ( $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) != $table_name ) {
            return array();
        }

        // Récupérer les mots-clés
        $keywords = $wpdb->get_results( "SELECT * FROM $table_name ORDER BY location_id, keyword", ARRAY_A );

        return $keywords ? $keywords : array();
    }

    /**
     * Récupère les mots-clés par emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array                     Les mots-clés.
     */
    private function get_keywords_by_location( $location_id ) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'boss_local_keywords';

        // Vérifier si la table existe
        if ( $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) != $table_name ) {
            return array();
        }

        // Récupérer les mots-clés
        $keywords = $wpdb->get_results( $wpdb->prepare( "SELECT * FROM $table_name WHERE location_id = %d ORDER BY keyword", $location_id ), ARRAY_A );

        return $keywords ? $keywords : array();
    }

    /**
     * Récupère l'historique des positions.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @param    int       $days           Le nombre de jours.
     * @return   array                     L'historique des positions.
     */
    private function get_positions_history( $location_id, $days ) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'boss_local_positions';

        // Vérifier si la table existe
        if ( $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) != $table_name ) {
            return array();
        }

        // Récupérer l'historique
        $date = date( 'Y-m-d', strtotime( "-$days days" ) );

        // Récupérer les IDs des mots-clés pour cet emplacement
        $keywords_table = $wpdb->prefix . 'boss_local_keywords';
        $keyword_ids = $wpdb->get_col( $wpdb->prepare( "SELECT id FROM $keywords_table WHERE location_id = %d", $location_id ) );

        if ( empty( $keyword_ids ) ) {
            return array();
        }

        // Convertir les IDs en chaîne pour la requête SQL
        $keyword_ids_str = implode( ',', $keyword_ids );

        // Récupérer l'historique des positions pour ces mots-clés
        $history = $wpdb->get_results( "SELECT * FROM $table_name WHERE keyword_id IN ($keyword_ids_str) AND date >= '$date' ORDER BY date, keyword_id", ARRAY_A );

        return $history ? $history : array();
    }

    /**
     * Récupère l'historique des analyses.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @param    int       $days           Le nombre de jours.
     * @return   array                     L'historique des analyses.
     */
    private function get_analysis_history( $location_id, $days ) {
        // Récupérer l'historique
        $history = get_post_meta( $location_id, '_boss_local_analysis_history', true );

        if ( empty( $history ) || ! is_array( $history ) ) {
            return array();
        }

        // Filtrer l'historique par date
        $date = date( 'Y-m-d', strtotime( "-$days days" ) );

        $filtered_history = array_filter( $history, function( $item ) use ( $date ) {
            return isset( $item['date'] ) && $item['date'] >= $date;
        } );

        return $filtered_history;
    }

    /**
     * Récupère les emplacements récents.
     *
     * @since    1.2.0
     * @return   array    Les emplacements récents.
     */
    private function get_recent_locations() {
        $locations = array();

        $args = array(
            'post_type'      => 'boss_local_location',
            'post_status'    => 'publish',
            'posts_per_page' => 5,
            'orderby'        => 'date',
            'order'          => 'DESC',
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();

                $locations[] = array(
                    'id'        => get_the_ID(),
                    'title'     => get_the_title(),
                    'address'   => get_post_meta( get_the_ID(), '_boss_local_address', true ),
                    'city'      => get_post_meta( get_the_ID(), '_boss_local_city', true ),
                    'phone'     => get_post_meta( get_the_ID(), '_boss_local_phone', true ),
                    'email'     => get_post_meta( get_the_ID(), '_boss_local_email', true ),
                    'website'   => get_post_meta( get_the_ID(), '_boss_local_website', true ),
                    'latitude'  => get_post_meta( get_the_ID(), '_boss_local_latitude', true ),
                    'longitude' => get_post_meta( get_the_ID(), '_boss_local_longitude', true ),
                );
            }
        }

        wp_reset_postdata();

        return $locations;
    }

    /**
     * Récupère les tâches.
     *
     * @since    1.2.0
     * @return   array    Les tâches.
     */
    private function get_tasks() {
        return array(
            array(
                'id'          => 1,
                'title'       => __( 'Compléter les informations d\'entreprise', 'boss-seo' ),
                'description' => __( 'Assurez-vous que toutes les informations de votre entreprise sont complètes.', 'boss-seo' ),
                'priority'    => 'high',
                'completed'   => false,
            ),
            array(
                'id'          => 2,
                'title'       => __( 'Ajouter des emplacements', 'boss-seo' ),
                'description' => __( 'Ajoutez tous vos emplacements pour améliorer votre visibilité locale.', 'boss-seo' ),
                'priority'    => 'medium',
                'completed'   => false,
            ),
            array(
                'id'          => 3,
                'title'       => __( 'Configurer les schémas structurés', 'boss-seo' ),
                'description' => __( 'Configurez les schémas structurés pour améliorer votre référencement local.', 'boss-seo' ),
                'priority'    => 'medium',
                'completed'   => false,
            ),
        );
    }

    /**
     * Récupère le résumé des performances.
     *
     * @since    1.2.0
     * @return   array    Le résumé des performances.
     */
    private function get_performance_summary() {
        return array(
            'visibility' => array(
                'score'       => $this->get_visibility_score(),
                'improvement' => __( 'Ajoutez plus d\'emplacements pour améliorer votre visibilité.', 'boss-seo' ),
            ),
            'completeness' => array(
                'score'       => $this->get_completeness_score(),
                'improvement' => __( 'Complétez les informations de vos emplacements.', 'boss-seo' ),
            ),
            'rankings' => array(
                'score'       => $this->get_rankings_average(),
                'improvement' => __( 'Optimisez vos pages locales pour améliorer vos positions.', 'boss-seo' ),
            ),
        );
    }

    /**
     * Récupère le score de visibilité.
     *
     * @since    1.2.0
     * @return   int    Le score de visibilité.
     */
    private function get_visibility_score() {
        return get_option( $this->option_prefix . 'visibility_score', 0 );
    }

    /**
     * Récupère le score de complétude.
     *
     * @since    1.2.0
     * @return   int    Le score de complétude.
     */
    private function get_completeness_score() {
        return get_option( $this->option_prefix . 'completeness_score', 0 );
    }

    /**
     * Récupère la moyenne des positions.
     *
     * @since    1.2.0
     * @return   float    La moyenne des positions.
     */
    private function get_rankings_average() {
        $keywords = $this->get_all_keywords();

        if ( empty( $keywords ) ) {
            return 0;
        }

        $total = 0;
        $count = 0;

        foreach ( $keywords as $keyword ) {
            if ( isset( $keyword['position'] ) && $keyword['position'] > 0 ) {
                $total += $keyword['position'];
                $count++;
            }
        }

        return $count > 0 ? round( $total / $count, 1 ) : 0;
    }

    /**
     * Récupère les emplacements du tableau de bord via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_dashboard_locations( $request ) {
        try {
            // Récupérer les emplacements récents
            $locations = $this->get_recent_locations();

            return rest_ensure_response( array(
                'locations' => $locations
            ) );
        } catch (Exception $e) {
            // Log l'erreur
            error_log('Erreur dans get_dashboard_locations: ' . $e->getMessage());

            // Retourner une réponse d'erreur
            return new WP_Error(
                'boss_seo_dashboard_locations_error',
                __('Une erreur est survenue lors de la récupération des emplacements du tableau de bord.', 'boss-seo'),
                array('status' => 500, 'error' => $e->getMessage())
            );
        }
    }
}
