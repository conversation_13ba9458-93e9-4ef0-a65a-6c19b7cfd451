<?php
/**
 * Classe pour la gestion des schémas structurés locaux.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour la gestion des schémas structurés locaux.
 *
 * Cette classe gère toutes les fonctionnalités liées aux schémas structurés locaux.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_Schema {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_local_schema_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_save_schema_settings', array( $this, 'ajax_save_schema_settings' ) );
        add_action( 'wp_ajax_boss_seo_get_schema_settings', array( $this, 'ajax_get_schema_settings' ) );
        add_action( 'wp_ajax_boss_seo_generate_schema', array( $this, 'ajax_generate_schema' ) );
        add_action( 'wp_ajax_boss_seo_test_schema', array( $this, 'ajax_test_schema' ) );

        // Ajouter les actions pour insérer les schémas dans le front-end
        add_action( 'wp_head', array( $this, 'insert_schemas' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/local/schema-settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_schema_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'update_schema_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/generate-schema',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'generate_schema' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/test-schema',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'test_schema' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les paramètres des schémas via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_schema_settings( $request ) {
        $settings = $this->get_all_schema_settings();
        return rest_ensure_response( $settings );
    }

    /**
     * Met à jour les paramètres des schémas via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_schema_settings( $request ) {
        $params = $request->get_params();

        // Mettre à jour les paramètres
        $this->update_all_schema_settings( $params );

        // Récupérer les paramètres mis à jour
        $settings = $this->get_all_schema_settings();

        return rest_ensure_response( $settings );
    }

    /**
     * Génère un schéma structuré via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function generate_schema( $request ) {
        $params = $request->get_params();

        // Vérifier les paramètres requis
        if ( ! isset( $params['location_id'] ) || empty( $params['location_id'] ) ) {
            return new WP_Error( 'missing_location_id', __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $location_id = absint( $params['location_id'] );
        $schema_type = isset( $params['schema_type'] ) ? sanitize_text_field( $params['schema_type'] ) : '';
        $schema_options = isset( $params['schema_options'] ) && is_array( $params['schema_options'] ) ? $params['schema_options'] : array();

        // Générer le schéma
        $schema = $this->generate_location_schema( $location_id, $schema_type, $schema_options );

        if ( is_wp_error( $schema ) ) {
            return $schema;
        }

        return rest_ensure_response( array(
            'schema' => $schema,
            'schema_json' => wp_json_encode( $schema, JSON_PRETTY_PRINT ),
        ) );
    }

    /**
     * Teste un schéma structuré via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function test_schema( $request ) {
        $params = $request->get_params();

        // Vérifier les paramètres requis
        if ( ! isset( $params['schema'] ) || empty( $params['schema'] ) ) {
            return new WP_Error( 'missing_schema', __( 'Le schéma est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $schema = $params['schema'];

        // Tester le schéma
        $result = $this->test_schema_with_google( $schema );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( $result );
    }

    /**
     * Gère les requêtes AJAX pour enregistrer les paramètres des schémas.
     *
     * @since    1.2.0
     */
    public function ajax_save_schema_settings() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les données
        $data = isset( $_POST['data'] ) ? $_POST['data'] : array();

        // Mettre à jour les paramètres
        $this->update_all_schema_settings( $data );

        wp_send_json_success( array( 'message' => __( 'Paramètres des schémas enregistrés avec succès.', 'boss-seo' ) ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les paramètres des schémas.
     *
     * @since    1.2.0
     */
    public function ajax_get_schema_settings() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $settings = $this->get_all_schema_settings();

        wp_send_json_success( $settings );
    }

    /**
     * Gère les requêtes AJAX pour générer un schéma structuré.
     *
     * @since    1.2.0
     */
    public function ajax_generate_schema() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );
        $schema_type = isset( $_POST['schema_type'] ) ? sanitize_text_field( $_POST['schema_type'] ) : '';
        $schema_options = isset( $_POST['schema_options'] ) && is_array( $_POST['schema_options'] ) ? $_POST['schema_options'] : array();

        // Générer le schéma
        $schema = $this->generate_location_schema( $location_id, $schema_type, $schema_options );

        if ( is_wp_error( $schema ) ) {
            wp_send_json_error( array( 'message' => $schema->get_error_message() ) );
        }

        wp_send_json_success( array(
            'schema' => $schema,
            'schema_json' => wp_json_encode( $schema, JSON_PRETTY_PRINT ),
        ) );
    }

    /**
     * Insère les schémas structurés dans le head du site.
     *
     * @since    1.2.0
     */
    public function insert_schemas() {
        // Vérifier si on est sur une page d'emplacement
        if ( is_singular( 'boss_location' ) ) {
            $location_id = get_the_ID();
            $schema_type = get_post_meta( $location_id, 'boss_location_seo_schema_type', true );
            $schema_options = $this->get_schema_options_for_location( $location_id );

            $schema = $this->generate_location_schema( $location_id, $schema_type, $schema_options );

            if ( ! is_wp_error( $schema ) ) {
                echo '<script type="application/ld+json">' . wp_json_encode( $schema ) . '</script>' . "\n";
            }
        }

        // Vérifier si on est sur une page locale générée
        if ( is_page() ) {
            $location_id = get_post_meta( get_the_ID(), 'boss_local_page_location_id', true );

            if ( $location_id ) {
                $schema_type = get_post_meta( $location_id, 'boss_location_seo_schema_type', true );
                $schema_options = $this->get_schema_options_for_location( $location_id );

                $schema = $this->generate_location_schema( $location_id, $schema_type, $schema_options );

                if ( ! is_wp_error( $schema ) ) {
                    echo '<script type="application/ld+json">' . wp_json_encode( $schema ) . '</script>' . "\n";
                }
            }
        }

        // Ajouter le schéma de l'organisation
        if ( $this->get_schema_setting( 'enable_organization_schema', true ) ) {
            $organization_schema = $this->generate_organization_schema();

            if ( ! is_wp_error( $organization_schema ) ) {
                echo '<script type="application/ld+json">' . wp_json_encode( $organization_schema ) . '</script>' . "\n";
            }
        }

        // Ajouter le schéma du site web
        if ( $this->get_schema_setting( 'enable_website_schema', true ) ) {
            $website_schema = $this->generate_website_schema();

            if ( ! is_wp_error( $website_schema ) ) {
                echo '<script type="application/ld+json">' . wp_json_encode( $website_schema ) . '</script>' . "\n";
            }
        }
    }

    /**
     * Récupère tous les paramètres des schémas.
     *
     * @since    1.2.0
     * @return   array    Les paramètres des schémas.
     */
    public function get_all_schema_settings() {
        $settings = array(
            'enable_organization_schema' => $this->get_schema_setting( 'enable_organization_schema', true ),
            'enable_website_schema' => $this->get_schema_setting( 'enable_website_schema', true ),
            'enable_location_schema' => $this->get_schema_setting( 'enable_location_schema', true ),
            'organization_type' => $this->get_schema_setting( 'organization_type', 'Organization' ),
            'organization_logo' => $this->get_schema_setting( 'organization_logo', '' ),
            'organization_same_as' => $this->get_schema_setting( 'organization_same_as', array() ),
            'website_name' => $this->get_schema_setting( 'website_name', get_bloginfo( 'name' ) ),
            'website_alternate_name' => $this->get_schema_setting( 'website_alternate_name', '' ),
            'website_description' => $this->get_schema_setting( 'website_description', get_bloginfo( 'description' ) ),
            'website_language' => $this->get_schema_setting( 'website_language', get_bloginfo( 'language' ) ),
            'website_search_url' => $this->get_schema_setting( 'website_search_url', home_url( '/?s={search_term_string}' ) ),
        );

        return $settings;
    }

    /**
     * Met à jour tous les paramètres des schémas.
     *
     * @since    1.2.0
     * @param    array    $data    Les données à mettre à jour.
     */
    public function update_all_schema_settings( $data ) {
        // Paramètres généraux
        $this->update_schema_setting( 'enable_organization_schema', isset( $data['enable_organization_schema'] ) ? (bool) $data['enable_organization_schema'] : true );
        $this->update_schema_setting( 'enable_website_schema', isset( $data['enable_website_schema'] ) ? (bool) $data['enable_website_schema'] : true );
        $this->update_schema_setting( 'enable_location_schema', isset( $data['enable_location_schema'] ) ? (bool) $data['enable_location_schema'] : true );

        // Paramètres de l'organisation
        $this->update_schema_setting( 'organization_type', isset( $data['organization_type'] ) ? sanitize_text_field( $data['organization_type'] ) : 'Organization' );
        $this->update_schema_setting( 'organization_logo', isset( $data['organization_logo'] ) ? absint( $data['organization_logo'] ) : '' );
        $this->update_schema_setting( 'organization_same_as', isset( $data['organization_same_as'] ) && is_array( $data['organization_same_as'] ) ? array_map( 'esc_url_raw', $data['organization_same_as'] ) : array() );

        // Paramètres du site web
        $this->update_schema_setting( 'website_name', isset( $data['website_name'] ) ? sanitize_text_field( $data['website_name'] ) : get_bloginfo( 'name' ) );
        $this->update_schema_setting( 'website_alternate_name', isset( $data['website_alternate_name'] ) ? sanitize_text_field( $data['website_alternate_name'] ) : '' );
        $this->update_schema_setting( 'website_description', isset( $data['website_description'] ) ? sanitize_textarea_field( $data['website_description'] ) : get_bloginfo( 'description' ) );
        $this->update_schema_setting( 'website_language', isset( $data['website_language'] ) ? sanitize_text_field( $data['website_language'] ) : get_bloginfo( 'language' ) );
        $this->update_schema_setting( 'website_search_url', isset( $data['website_search_url'] ) ? esc_url_raw( $data['website_search_url'] ) : home_url( '/?s={search_term_string}' ) );
    }

    /**
     * Récupère un paramètre de schéma.
     *
     * @since    1.2.0
     * @param    string    $key      La clé du paramètre.
     * @param    mixed     $default  La valeur par défaut.
     * @return   mixed                La valeur du paramètre.
     */
    private function get_schema_setting( $key, $default = '' ) {
        return get_option( $this->option_prefix . $key, $default );
    }

    /**
     * Met à jour un paramètre de schéma.
     *
     * @since    1.2.0
     * @param    string    $key    La clé du paramètre.
     * @param    mixed     $value  La valeur du paramètre.
     * @return   bool              True si le paramètre a été mis à jour, false sinon.
     */
    private function update_schema_setting( $key, $value ) {
        return update_option( $this->option_prefix . $key, $value );
    }

    /**
     * Récupère les options de schéma pour un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array                     Les options de schéma.
     */
    private function get_schema_options_for_location( $location_id ) {
        $options = array(
            'include_logo' => true,
            'include_image' => true,
            'include_opening_hours' => true,
            'include_price_range' => true,
            'include_accepted_payment' => true,
            'include_menu' => true,
            'include_reservation' => true,
            'include_order_action' => true,
        );

        return $options;
    }

    /**
     * Génère le schéma structuré pour un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id     L'ID de l'emplacement.
     * @param    string    $schema_type     Le type de schéma.
     * @param    array     $schema_options  Les options de schéma.
     * @return   array|WP_Error             Le schéma structuré ou une erreur.
     */
    public function generate_location_schema( $location_id, $schema_type = '', $schema_options = array() ) {
        $location = get_post( $location_id );

        if ( ! $location || $location->post_type !== 'boss_location' ) {
            return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ) );
        }

        // Récupérer les métadonnées de l'emplacement
        $meta_prefix = 'boss_location_';

        $meta_fields = array(
            // Détails
            'status',
            'primary',
            'short_name',
            'description',

            // Adresse
            'street',
            'street2',
            'city',
            'state',
            'postal_code',
            'country',
            'latitude',
            'longitude',

            // Coordonnées
            'phone',
            'fax',
            'email',
            'website',

            // SEO
            'seo_title',
            'seo_description',
            'seo_keywords',
            'seo_schema_type',
            'seo_hide_in_sitemap',
        );

        $meta_data = array();

        foreach ( $meta_fields as $field ) {
            $meta_key = $meta_prefix . $field;
            $meta_data[$field] = get_post_meta( $location_id, $meta_key, true );
        }

        // Récupérer les horaires d'ouverture
        $meta_data['hours'] = get_post_meta( $location_id, $meta_prefix . 'hours', true );

        // Utiliser le type de schéma spécifié ou celui de l'emplacement
        if ( empty( $schema_type ) ) {
            $schema_type = ! empty( $meta_data['seo_schema_type'] ) ? $meta_data['seo_schema_type'] : 'LocalBusiness';
        }

        // Récupérer les informations de l'entreprise
        $business_name = get_option( 'boss_business_name', get_bloginfo( 'name' ) );
        $business_logo = get_option( 'boss_business_logo', '' );

        // Construire l'adresse formatée
        $address_parts = array();
        if ( ! empty( $meta_data['street'] ) ) $address_parts[] = $meta_data['street'];
        if ( ! empty( $meta_data['street2'] ) ) $address_parts[] = $meta_data['street2'];
        if ( ! empty( $meta_data['city'] ) ) $address_parts[] = $meta_data['city'];
        if ( ! empty( $meta_data['state'] ) ) $address_parts[] = $meta_data['state'];
        if ( ! empty( $meta_data['postal_code'] ) ) $address_parts[] = $meta_data['postal_code'];
        if ( ! empty( $meta_data['country'] ) ) $address_parts[] = $meta_data['country'];
        $address = implode( ', ', $address_parts );

        // Récupérer l'image mise en avant
        $image = '';
        if ( has_post_thumbnail( $location_id ) ) {
            $image = get_the_post_thumbnail_url( $location_id, 'full' );
        }

        // Construire le schéma
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => $schema_type,
            'name' => $location->post_title,
            'description' => ! empty( $meta_data['description'] ) ? $meta_data['description'] : '',
            'url' => get_permalink( $location_id ),
            'telephone' => ! empty( $meta_data['phone'] ) ? $meta_data['phone'] : '',
            'email' => ! empty( $meta_data['email'] ) ? $meta_data['email'] : '',
            'address' => array(
                '@type' => 'PostalAddress',
                'streetAddress' => ! empty( $meta_data['street'] ) ? $meta_data['street'] . ( ! empty( $meta_data['street2'] ) ? ', ' . $meta_data['street2'] : '' ) : '',
                'addressLocality' => ! empty( $meta_data['city'] ) ? $meta_data['city'] : '',
                'addressRegion' => ! empty( $meta_data['state'] ) ? $meta_data['state'] : '',
                'postalCode' => ! empty( $meta_data['postal_code'] ) ? $meta_data['postal_code'] : '',
                'addressCountry' => ! empty( $meta_data['country'] ) ? $meta_data['country'] : '',
            ),
        );

        // Ajouter les coordonnées géographiques
        if ( ! empty( $meta_data['latitude'] ) && ! empty( $meta_data['longitude'] ) ) {
            $schema['geo'] = array(
                '@type' => 'GeoCoordinates',
                'latitude' => $meta_data['latitude'],
                'longitude' => $meta_data['longitude'],
            );
        }

        // Ajouter le logo
        if ( ! empty( $business_logo ) && isset( $schema_options['include_logo'] ) && $schema_options['include_logo'] ) {
            $logo_url = wp_get_attachment_url( $business_logo );
            if ( $logo_url ) {
                $schema['logo'] = $logo_url;
            }
        }

        // Ajouter l'image
        if ( ! empty( $image ) && isset( $schema_options['include_image'] ) && $schema_options['include_image'] ) {
            $schema['image'] = $image;
        }

        // Ajouter les horaires d'ouverture
        if ( ! empty( $meta_data['hours'] ) && is_array( $meta_data['hours'] ) && isset( $schema_options['include_opening_hours'] ) && $schema_options['include_opening_hours'] ) {
            $opening_hours = array();
            $days_map = array(
                'monday' => 'Mo',
                'tuesday' => 'Tu',
                'wednesday' => 'We',
                'thursday' => 'Th',
                'friday' => 'Fr',
                'saturday' => 'Sa',
                'sunday' => 'Su',
            );

            foreach ( $meta_data['hours'] as $day_key => $day_hours ) {
                if ( $day_hours['status'] === 'open' && ! empty( $day_hours['hours'] ) ) {
                    foreach ( $day_hours['hours'] as $slot ) {
                        $opening_hours[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $days_map[$day_key],
                            'opens' => $slot['open'],
                            'closes' => $slot['close'],
                        );
                    }
                }
            }

            if ( ! empty( $opening_hours ) ) {
                $schema['openingHoursSpecification'] = $opening_hours;
            }
        }

        // Ajouter des propriétés spécifiques selon le type de schéma
        switch ( $schema_type ) {
            case 'Restaurant':
                // Ajouter la fourchette de prix
                if ( isset( $schema_options['include_price_range'] ) && $schema_options['include_price_range'] ) {
                    $price_range = get_option( 'boss_business_price_range', '' );
                    if ( ! empty( $price_range ) ) {
                        $schema['priceRange'] = $price_range;
                    }
                }

                // Ajouter les moyens de paiement acceptés
                if ( isset( $schema_options['include_accepted_payment'] ) && $schema_options['include_accepted_payment'] ) {
                    $payment_accepted = get_option( 'boss_business_payment_accepted', array() );
                    if ( ! empty( $payment_accepted ) ) {
                        $schema['paymentAccepted'] = implode( ', ', $payment_accepted );
                    }
                }

                // Ajouter le menu
                if ( isset( $schema_options['include_menu'] ) && $schema_options['include_menu'] ) {
                    $menu_url = get_option( 'boss_business_menu_url', '' );
                    if ( ! empty( $menu_url ) ) {
                        $schema['menu'] = $menu_url;
                    }
                }

                // Ajouter l'action de réservation
                if ( isset( $schema_options['include_reservation'] ) && $schema_options['include_reservation'] ) {
                    $reservation_url = get_option( 'boss_business_reservation_url', '' );
                    if ( ! empty( $reservation_url ) ) {
                        $schema['potentialAction'] = array(
                            '@type' => 'ReserveAction',
                            'target' => $reservation_url,
                            'result' => array(
                                '@type' => 'Reservation',
                                'name' => __( 'Réservation chez', 'boss-seo' ) . ' ' . $location->post_title,
                            ),
                        );
                    }
                }
                break;

            case 'Store':
            case 'MedicalBusiness':
            case 'FinancialService':
                // Ajouter la fourchette de prix
                if ( isset( $schema_options['include_price_range'] ) && $schema_options['include_price_range'] ) {
                    $price_range = get_option( 'boss_business_price_range', '' );
                    if ( ! empty( $price_range ) ) {
                        $schema['priceRange'] = $price_range;
                    }
                }

                // Ajouter les moyens de paiement acceptés
                if ( isset( $schema_options['include_accepted_payment'] ) && $schema_options['include_accepted_payment'] ) {
                    $payment_accepted = get_option( 'boss_business_payment_accepted', array() );
                    if ( ! empty( $payment_accepted ) ) {
                        $schema['paymentAccepted'] = implode( ', ', $payment_accepted );
                    }
                }
                break;

            case 'LodgingBusiness':
                // Ajouter la fourchette de prix
                if ( isset( $schema_options['include_price_range'] ) && $schema_options['include_price_range'] ) {
                    $price_range = get_option( 'boss_business_price_range', '' );
                    if ( ! empty( $price_range ) ) {
                        $schema['priceRange'] = $price_range;
                    }
                }

                // Ajouter les moyens de paiement acceptés
                if ( isset( $schema_options['include_accepted_payment'] ) && $schema_options['include_accepted_payment'] ) {
                    $payment_accepted = get_option( 'boss_business_payment_accepted', array() );
                    if ( ! empty( $payment_accepted ) ) {
                        $schema['paymentAccepted'] = implode( ', ', $payment_accepted );
                    }
                }

                // Ajouter l'action de réservation
                if ( isset( $schema_options['include_reservation'] ) && $schema_options['include_reservation'] ) {
                    $reservation_url = get_option( 'boss_business_reservation_url', '' );
                    if ( ! empty( $reservation_url ) ) {
                        $schema['potentialAction'] = array(
                            '@type' => 'ReserveAction',
                            'target' => $reservation_url,
                            'result' => array(
                                '@type' => 'LodgingReservation',
                                'name' => __( 'Réservation chez', 'boss-seo' ) . ' ' . $location->post_title,
                            ),
                        );
                    }
                }
                break;
        }

        return $schema;
    }

    /**
     * Teste un schéma structuré avec l'outil de test de Google.
     *
     * @since    1.2.0
     * @param    array     $schema    Le schéma à tester.
     * @return   array|WP_Error       Les résultats du test ou une erreur.
     */
    private function test_schema_with_google( $schema ) {
        // URL de l'API de test de schéma de Google
        $api_url = 'https://search.google.com/test/rich-results/perform';

        // Préparer les données
        $data = array(
            'url' => home_url(),
            'html' => '<html><head><script type="application/ld+json">' . wp_json_encode( $schema ) . '</script></head><body></body></html>',
        );

        // Effectuer la requête
        $response = wp_remote_post( $api_url, array(
            'body' => $data,
            'timeout' => 30,
        ) );

        // Vérifier la réponse
        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $body = wp_remote_retrieve_body( $response );
        $result = json_decode( $body, true );

        if ( ! $result ) {
            return new WP_Error( 'invalid_response', __( 'Réponse invalide de l\'API de test de Google.', 'boss-seo' ) );
        }

        return $result;
    }

    /**
     * Génère le schéma structuré pour l'organisation.
     *
     * @since    1.2.0
     * @return   array    Le schéma structuré.
     */
    private function generate_organization_schema() {
        $organization_type = $this->get_schema_setting( 'organization_type', 'Organization' );
        $organization_logo = $this->get_schema_setting( 'organization_logo', '' );
        $organization_same_as = $this->get_schema_setting( 'organization_same_as', array() );

        $business_name = get_option( 'boss_business_name', get_bloginfo( 'name' ) );
        $business_description = get_option( 'boss_business_description', get_bloginfo( 'description' ) );

        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => $organization_type,
            'name' => $business_name,
            'url' => home_url(),
        );

        if ( ! empty( $business_description ) ) {
            $schema['description'] = $business_description;
        }

        if ( ! empty( $organization_logo ) ) {
            $logo_url = wp_get_attachment_url( $organization_logo );
            if ( $logo_url ) {
                $schema['logo'] = $logo_url;
                $schema['image'] = $logo_url;
            }
        }

        if ( ! empty( $organization_same_as ) ) {
            $schema['sameAs'] = $organization_same_as;
        }

        // Ajouter les coordonnées
        $contact_phone = get_option( 'boss_business_contact_phone', '' );
        $contact_email = get_option( 'boss_business_contact_email', '' );

        if ( ! empty( $contact_phone ) ) {
            $schema['telephone'] = $contact_phone;
        }

        if ( ! empty( $contact_email ) ) {
            $schema['email'] = $contact_email;
        }

        // Ajouter l'adresse
        $address_street = get_option( 'boss_business_address_street', '' );
        $address_city = get_option( 'boss_business_address_city', '' );
        $address_state = get_option( 'boss_business_address_state', '' );
        $address_postal_code = get_option( 'boss_business_address_postal_code', '' );
        $address_country = get_option( 'boss_business_address_country', '' );

        if ( ! empty( $address_street ) || ! empty( $address_city ) ) {
            $schema['address'] = array(
                '@type' => 'PostalAddress',
                'streetAddress' => $address_street,
                'addressLocality' => $address_city,
                'addressRegion' => $address_state,
                'postalCode' => $address_postal_code,
                'addressCountry' => $address_country,
            );
        }

        return $schema;
    }

    /**
     * Génère le schéma structuré pour le site web.
     *
     * @since    1.2.0
     * @return   array    Le schéma structuré.
     */
    private function generate_website_schema() {
        $website_name = $this->get_schema_setting( 'website_name', get_bloginfo( 'name' ) );
        $website_alternate_name = $this->get_schema_setting( 'website_alternate_name', '' );
        $website_description = $this->get_schema_setting( 'website_description', get_bloginfo( 'description' ) );
        $website_language = $this->get_schema_setting( 'website_language', get_bloginfo( 'language' ) );
        $website_search_url = $this->get_schema_setting( 'website_search_url', home_url( '/?s={search_term_string}' ) );

        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => $website_name,
            'url' => home_url(),
        );

        if ( ! empty( $website_alternate_name ) ) {
            $schema['alternateName'] = $website_alternate_name;
        }

        if ( ! empty( $website_description ) ) {
            $schema['description'] = $website_description;
        }

        if ( ! empty( $website_language ) ) {
            $schema['inLanguage'] = $website_language;
        }

        if ( ! empty( $website_search_url ) ) {
            $schema['potentialAction'] = array(
                '@type' => 'SearchAction',
                'target' => $website_search_url,
                'query-input' => 'required name=search_term_string',
            );
        }

        return $schema;
    }

    /**
     * Gère les requêtes AJAX pour tester un schéma structuré.
     *
     * @since    1.2.0
     */
    public function ajax_test_schema() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['schema'] ) || empty( $_POST['schema'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Le schéma est requis.', 'boss-seo' ) ) );
        }

        $schema = $_POST['schema'];

        // Tester le schéma
        $result = $this->test_schema_with_google( $schema );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( $result );
    }
}
