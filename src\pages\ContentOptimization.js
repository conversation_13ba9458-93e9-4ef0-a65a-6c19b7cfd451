import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Dashicon,
  Notice,
  Panel,
  PanelBody,
  PanelRow,
  Spinner,
  TabPanel,
  TextControl,
  ToggleControl,
  Tooltip
} from '@wordpress/components';

// Composants internes
import SeoEditor from '../components/content/SeoEditor';
import KeywordResearch from '../components/content/KeywordResearch';
import AiContentStudio from '../components/content/AiContentStudio';

// Services
import optimizerService from '../services/OptimizerService';

/**
 * Composant principal du module d'optimisation de contenu
 */
const ContentOptimization = () => {
  // État pour gérer l'onglet actif
  const [activeTab, setActiveTab] = useState('editor');

  // État pour gérer le contenu en cours d'édition
  const [currentContent, setCurrentContent] = useState({
    title: '',
    content: '',
    excerpt: '',
    slug: '',
    focusKeyword: '',
    secondaryKeywords: []
  });

  // État pour gérer les mots-clés favoris/assignés
  const [savedKeywords, setSavedKeywords] = useState([]);

  // État pour gérer l'historique des générations AI
  const [aiHistory, setAiHistory] = useState([]);

  // État pour gérer le score SEO
  const [seoScore, setSeoScore] = useState({
    overall: 0,
    title: 0,
    content: 0,
    readability: 0,
    keywords: 0,
    links: 0
  });

  // État pour gérer les suggestions d'optimisation
  const [suggestions, setSuggestions] = useState([]);

  // État pour gérer le chargement
  const [isLoading, setIsLoading] = useState(false);

  // Effet pour analyser le contenu en temps réel
  useEffect(() => {
    // Simuler une analyse SEO en temps réel
    const analyzeContent = () => {
      // Dans une implémentation réelle, cette fonction ferait appel à une API d'analyse SEO
      // Pour l'exemple, nous utilisons une logique simplifiée

      const newSuggestions = [];
      const newScore = { overall: 0, title: 0, content: 0, readability: 0, keywords: 0, links: 0 };

      // Analyse du titre
      if (currentContent.title.length === 0) {
        newSuggestions.push({
          id: 'title_missing',
          type: 'error',
          text: __('Ajoutez un titre à votre contenu', 'boss-seo'),
          element: 'title'
        });
        newScore.title = 0;
      } else if (currentContent.title.length < 20) {
        newSuggestions.push({
          id: 'title_short',
          type: 'warning',
          text: __('Votre titre est trop court (moins de 20 caractères)', 'boss-seo'),
          element: 'title'
        });
        newScore.title = 50;
      } else if (currentContent.title.length > 60) {
        newSuggestions.push({
          id: 'title_long',
          type: 'warning',
          text: __('Votre titre est trop long (plus de 60 caractères)', 'boss-seo'),
          element: 'title'
        });
        newScore.title = 50;
      } else {
        newScore.title = 100;
      }

      // Analyse du contenu
      if (currentContent.content.length === 0) {
        newSuggestions.push({
          id: 'content_missing',
          type: 'error',
          text: __('Ajoutez du contenu à votre page', 'boss-seo'),
          element: 'content'
        });
        newScore.content = 0;
      } else if (currentContent.content.length < 300) {
        newSuggestions.push({
          id: 'content_short',
          type: 'warning',
          text: __('Votre contenu est trop court (moins de 300 caractères)', 'boss-seo'),
          element: 'content'
        });
        newScore.content = 50;
      } else {
        newScore.content = 100;
      }

      // Analyse des mots-clés
      if (!currentContent.focusKeyword) {
        newSuggestions.push({
          id: 'keyword_missing',
          type: 'error',
          text: __('Définissez un mot-clé principal pour votre contenu', 'boss-seo'),
          element: 'focusKeyword'
        });
        newScore.keywords = 0;
      } else if (currentContent.content.toLowerCase().indexOf(currentContent.focusKeyword.toLowerCase()) === -1) {
        newSuggestions.push({
          id: 'keyword_not_in_content',
          type: 'warning',
          text: __('Votre mot-clé principal n\'apparaît pas dans le contenu', 'boss-seo'),
          element: 'content'
        });
        newScore.keywords = 50;
      } else {
        newScore.keywords = 100;
      }

      // Calcul du score global
      newScore.overall = Math.round(
        (newScore.title + newScore.content + newScore.keywords) / 3
      );

      // Mise à jour des états
      setSuggestions(newSuggestions);
      setSeoScore(newScore);
    };

    // Analyser le contenu après un délai pour éviter trop d'appels
    const timer = setTimeout(() => {
      analyzeContent();
    }, 500);

    return () => clearTimeout(timer);
  }, [currentContent]);

  // Fonction pour ajouter un mot-clé aux favoris
  const handleSaveKeyword = (keyword) => {
    if (!savedKeywords.some(k => k.text === keyword.text)) {
      setSavedKeywords([...savedKeywords, keyword]);
    }
  };

  // Fonction pour définir un mot-clé comme mot-clé principal
  const handleSetFocusKeyword = (keyword) => {
    setCurrentContent({
      ...currentContent,
      focusKeyword: keyword.text
    });
  };

  // État pour gérer les erreurs
  const [error, setError] = useState('');

  // Fonction pour générer du contenu avec l'IA
  const handleGenerateContent = async (prompt, type) => {
    setIsLoading(true);
    setError('');

    try {
      // Préparer les options pour la génération
      const options = {
        type: type,
        focusKeyword: currentContent.focusKeyword || '',
        existingContent: type === 'enhance' || type === 'recycle' ? currentContent.content : '',
        format: 'html'
      };

      // Appeler le service d'IA pour générer du contenu
      const response = await optimizerService.generateContent(prompt, options);

      if (!response.success) {
        throw new Error(response.message || __('Erreur lors de la génération du contenu', 'boss-seo'));
      }

      // Récupérer le contenu généré
      const generatedContent = response.content;

      // Ajouter à l'historique
      const newHistoryItem = {
        id: Date.now(),
        type,
        prompt,
        content: generatedContent,
        date: new Date().toISOString(),
        model: response.model,
        provider: response.provider
      };

      setAiHistory([newHistoryItem, ...aiHistory]);

      // Mettre à jour le contenu si nécessaire
      if (type === 'draft') {
        setCurrentContent({
          ...currentContent,
          content: generatedContent
        });
      } else if (type === 'enhance' || type === 'recycle') {
        setCurrentContent({
          ...currentContent,
          content: generatedContent
        });
      } else if (type === 'template') {
        setCurrentContent({
          ...currentContent,
          content: generatedContent
        });
      }
    } catch (err) {
      console.error('Erreur lors de la génération du contenu:', err);
      setError(err.message || __('Erreur lors de la génération du contenu. Veuillez réessayer.', 'boss-seo'));

      // Contenu de secours en cas d'erreur
      let fallbackContent = '';

      if (type === 'draft') {
        fallbackContent = `<h2>Introduction à ${currentContent.focusKeyword || 'votre sujet'}</h2>
<p>Le ${currentContent.focusKeyword || 'sujet choisi'} est un sujet important dans le domaine du référencement. Comprendre comment optimiser votre contenu pour ce mot-clé peut considérablement améliorer votre visibilité en ligne.</p>

<h2>Pourquoi le ${currentContent.focusKeyword || 'sujet choisi'} est important</h2>
<p>L'importance du ${currentContent.focusKeyword || 'sujet choisi'} ne peut être sous-estimée dans une stratégie SEO efficace. Les moteurs de recherche accordent une grande valeur aux contenus bien optimisés pour des mots-clés pertinents.</p>

<h2>Comment optimiser pour le ${currentContent.focusKeyword || 'sujet choisi'}</h2>
<p>Pour optimiser efficacement votre contenu pour le ${currentContent.focusKeyword || 'sujet choisi'}, suivez ces étapes essentielles :</p>
<ul>
  <li>Recherchez des mots-clés connexes</li>
  <li>Intégrez naturellement le mot-clé dans votre contenu</li>
  <li>Optimisez vos balises méta et vos titres</li>
  <li>Créez un contenu de qualité qui répond aux questions des utilisateurs</li>
</ul>

<h2>Conclusion</h2>
<p>En suivant ces conseils d'optimisation pour le ${currentContent.focusKeyword || 'sujet choisi'}, vous améliorerez significativement vos chances d'apparaître dans les premiers résultats des moteurs de recherche.</p>`;
      } else if (type === 'enhance') {
        fallbackContent = currentContent.content + `\n\n<h2>Approfondissement sur ${currentContent.focusKeyword || 'votre sujet'}</h2>
<p>Pour aller plus loin dans l'optimisation de votre contenu pour le ${currentContent.focusKeyword || 'sujet choisi'}, considérez également ces aspects avancés :</p>
<ul>
  <li>L'intention de recherche derrière le mot-clé</li>
  <li>Les questions fréquemment posées par les utilisateurs</li>
  <li>Les tendances actuelles liées à ce sujet</li>
</ul>`;
      }

      // Ajouter à l'historique même en cas d'erreur
      const newHistoryItem = {
        id: Date.now(),
        type,
        prompt,
        content: fallbackContent,
        date: new Date().toISOString(),
        error: true
      };

      setAiHistory([newHistoryItem, ...aiHistory]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Optimisation de contenu', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Créez et optimisez votre contenu pour améliorer votre référencement', 'boss-seo')}
          </p>
        </div>

        {/* Onglets principaux */}
        <TabPanel
          className="boss-mb-6"
          activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
          tabs={[
            {
              name: 'editor',
              title: __('Éditeur SEO', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'keywords',
              title: __('Recherche de mots-clés', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'ai',
              title: __('AI Content Studio', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            }
          ]}
          onSelect={(tabName) => setActiveTab(tabName)}
        >
          {(tab) => {
            if (tab.name === 'editor') {
              return (
                <SeoEditor
                  content={currentContent}
                  setContent={setCurrentContent}
                  seoScore={seoScore}
                  suggestions={suggestions}
                  savedKeywords={savedKeywords}
                />
              );
            } else if (tab.name === 'keywords') {
              return (
                <KeywordResearch
                  focusKeyword={currentContent.focusKeyword}
                  savedKeywords={savedKeywords}
                  onSaveKeyword={handleSaveKeyword}
                  onSetFocusKeyword={handleSetFocusKeyword}
                />
              );
            } else if (tab.name === 'ai') {
              return (
                <>
                  {error && (
                    <Notice status="error" isDismissible={true} onRemove={() => setError('')} className="boss-mb-6">
                      {error}
                    </Notice>
                  )}
                  <AiContentStudio
                    content={currentContent}
                    focusKeyword={currentContent.focusKeyword}
                    isLoading={isLoading}
                    aiHistory={aiHistory}
                    onGenerateContent={handleGenerateContent}
                  />
                </>
              );
            }
          }}
        </TabPanel>
      </div>
    </div>
  );
};

export default ContentOptimization;
