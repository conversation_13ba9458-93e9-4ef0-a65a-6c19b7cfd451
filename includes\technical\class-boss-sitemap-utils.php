<?php
/**
 * Classe utilitaire pour les sitemaps.
 *
 * Cette classe fournit des méthodes utilitaires pour les sitemaps.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Sitemap_Utils {

    /**
     * Récupère les images d'un article.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID de l'article.
     * @return   array                 Un tableau d'images.
     */
    public static function get_images_from_post( $post_id ) {
        $images = array();
        $post = get_post( $post_id );
        
        if ( ! $post ) {
            return $images;
        }
        
        $content = $post->post_content;
        
        // Récupérer les images du contenu
        if ( preg_match_all( '/<img [^>]+>/', $content, $matches ) ) {
            foreach ( $matches[0] as $img ) {
                if ( preg_match( '/src=[\'"](.*?)[\'"]/', $img, $src ) ) {
                    $image_url = $src[1];
                    
                    // Récupérer le titre et le texte alternatif
                    $title = '';
                    if ( preg_match( '/title=[\'"](.*?)[\'"]/', $img, $title_match ) ) {
                        $title = $title_match[1];
                    }
                    
                    $alt = '';
                    if ( preg_match( '/alt=[\'"](.*?)[\'"]/', $img, $alt_match ) ) {
                        $alt = $alt_match[1];
                    }
                    
                    $images[] = array(
                        'loc'   => $image_url,
                        'title' => $title,
                        'alt'   => $alt,
                    );
                }
            }
        }
        
        // Récupérer les images des galeries
        if ( preg_match_all( '/\[gallery.*ids=[\'"](.*?)[\'"]/s', $content, $matches ) ) {
            foreach ( $matches[1] as $gallery_ids ) {
                $ids = explode( ',', $gallery_ids );
                foreach ( $ids as $id ) {
                    $attachment = get_post( $id );
                    if ( $attachment ) {
                        $image_url = wp_get_attachment_url( $id );
                        $images[] = array(
                            'loc'   => $image_url,
                            'title' => $attachment->post_title,
                            'alt'   => get_post_meta( $id, '_wp_attachment_image_alt', true ),
                        );
                    }
                }
            }
        }
        
        return $images;
    }

    /**
     * Récupère les vidéos d'un article.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID de l'article.
     * @return   array                 Un tableau de vidéos.
     */
    public static function get_videos_from_post( $post_id ) {
        $videos = array();
        $post = get_post( $post_id );
        
        if ( ! $post ) {
            return $videos;
        }
        
        $content = $post->post_content;
        
        // Récupérer les vidéos intégrées (iframe)
        if ( preg_match_all( '/<iframe.*?src=[\'"](.*?)[\'"].*?<\/iframe>/s', $content, $matches ) ) {
            foreach ( $matches[1] as $index => $video_url ) {
                // Vérifier si c'est une URL YouTube
                if ( strpos( $video_url, 'youtube.com' ) !== false || strpos( $video_url, 'youtu.be' ) !== false ) {
                    // Extraire l'ID de la vidéo YouTube
                    $video_id = '';
                    if ( preg_match( '/youtube\.com\/embed\/([^\/\?]+)/', $video_url, $youtube_match ) ) {
                        $video_id = $youtube_match[1];
                    } elseif ( preg_match( '/youtube\.com\/watch\?v=([^&]+)/', $video_url, $youtube_match ) ) {
                        $video_id = $youtube_match[1];
                    } elseif ( preg_match( '/youtu\.be\/([^\/\?]+)/', $video_url, $youtube_match ) ) {
                        $video_id = $youtube_match[1];
                    }
                    
                    if ( $video_id ) {
                        $videos[] = array(
                            'thumbnail_loc' => 'https://img.youtube.com/vi/' . $video_id . '/maxresdefault.jpg',
                            'title'         => get_the_title( $post_id ),
                            'description'   => wp_trim_words( strip_tags( $post->post_content ), 30 ),
                            'content_loc'   => $video_url,
                            'player_loc'    => $video_url,
                            'duration'      => 0, // Impossible de déterminer la durée sans l'API YouTube
                        );
                    }
                }
                // Vérifier si c'est une URL Vimeo
                elseif ( strpos( $video_url, 'vimeo.com' ) !== false ) {
                    // Extraire l'ID de la vidéo Vimeo
                    $video_id = '';
                    if ( preg_match( '/vimeo\.com\/video\/([^\/\?]+)/', $video_url, $vimeo_match ) ) {
                        $video_id = $vimeo_match[1];
                    } elseif ( preg_match( '/vimeo\.com\/([^\/\?]+)/', $video_url, $vimeo_match ) ) {
                        $video_id = $vimeo_match[1];
                    }
                    
                    if ( $video_id ) {
                        $videos[] = array(
                            'thumbnail_loc' => 'https://vumbnail.com/' . $video_id . '.jpg',
                            'title'         => get_the_title( $post_id ),
                            'description'   => wp_trim_words( strip_tags( $post->post_content ), 30 ),
                            'content_loc'   => $video_url,
                            'player_loc'    => $video_url,
                            'duration'      => 0, // Impossible de déterminer la durée sans l'API Vimeo
                        );
                    }
                }
            }
        }
        
        // Récupérer les vidéos intégrées avec les shortcodes WordPress
        if ( preg_match_all( '/\[video.*?src=[\'"](.*?)[\'"].*?\]/s', $content, $matches ) ) {
            foreach ( $matches[1] as $video_url ) {
                $videos[] = array(
                    'thumbnail_loc' => get_the_post_thumbnail_url( $post_id, 'full' ) ?: plugins_url( 'assets/images/video-placeholder.jpg', dirname( __FILE__, 2 ) ),
                    'title'         => get_the_title( $post_id ),
                    'description'   => wp_trim_words( strip_tags( $post->post_content ), 30 ),
                    'content_loc'   => $video_url,
                    'player_loc'    => $video_url,
                    'duration'      => 0,
                );
            }
        }
        
        // Récupérer les vidéos hébergées localement (balises video)
        if ( preg_match_all( '/<video.*?src=[\'"](.*?)[\'"].*?<\/video>/s', $content, $matches ) ) {
            foreach ( $matches[1] as $video_url ) {
                $videos[] = array(
                    'thumbnail_loc' => get_the_post_thumbnail_url( $post_id, 'full' ) ?: plugins_url( 'assets/images/video-placeholder.jpg', dirname( __FILE__, 2 ) ),
                    'title'         => get_the_title( $post_id ),
                    'description'   => wp_trim_words( strip_tags( $post->post_content ), 30 ),
                    'content_loc'   => $video_url,
                    'player_loc'    => '',
                    'duration'      => 0,
                );
            }
        }
        
        return $videos;
    }

    /**
     * Enregistre une entrée dans l'historique des générations de sitemaps.
     *
     * @since    1.2.0
     * @param    string    $sitemap_type    Le type de sitemap.
     * @param    string    $status          Le statut de la génération.
     * @param    string    $message         Un message optionnel.
     */
    public static function log_sitemap_generation( $sitemap_type, $status = 'success', $message = '' ) {
        $history_option = 'boss_seo_sitemap_generation_history';
        $history = get_option( $history_option, array() );
        
        // Limiter l'historique à 100 entrées
        if ( count( $history ) >= 100 ) {
            array_pop( $history );
        }
        
        // Ajouter la nouvelle entrée au début
        array_unshift( $history, array(
            'type'      => $sitemap_type,
            'status'    => $status,
            'message'   => $message,
            'timestamp' => current_time( 'mysql' ),
        ) );
        
        update_option( $history_option, $history );
    }

    /**
     * Récupère l'historique des générations de sitemaps.
     *
     * @since    1.2.0
     * @param    int       $limit    Le nombre d'entrées à récupérer.
     * @return   array               L'historique des générations.
     */
    public static function get_sitemap_generation_history( $limit = 100 ) {
        $history_option = 'boss_seo_sitemap_generation_history';
        $history = get_option( $history_option, array() );
        
        // Limiter le nombre d'entrées
        return array_slice( $history, 0, $limit );
    }

    /**
     * Ping les moteurs de recherche pour les informer de la mise à jour du sitemap.
     *
     * @since    1.2.0
     * @return   array    Un tableau contenant les résultats des pings.
     */
    public static function ping_search_engines() {
        $site_url = get_site_url();
        $sitemap_url = $site_url . '/sitemap.xml';
        $results = array();
        
        // Ping Google
        $google_ping_url = 'https://www.google.com/ping?sitemap=' . urlencode( $sitemap_url );
        $google_response = wp_remote_get( $google_ping_url );
        $results['google'] = array(
            'success' => ! is_wp_error( $google_response ) && wp_remote_retrieve_response_code( $google_response ) == 200,
            'message' => is_wp_error( $google_response ) ? $google_response->get_error_message() : wp_remote_retrieve_response_message( $google_response ),
        );
        
        // Ping Bing
        $bing_ping_url = 'https://www.bing.com/ping?sitemap=' . urlencode( $sitemap_url );
        $bing_response = wp_remote_get( $bing_ping_url );
        $results['bing'] = array(
            'success' => ! is_wp_error( $bing_response ) && wp_remote_retrieve_response_code( $bing_response ) == 200,
            'message' => is_wp_error( $bing_response ) ? $bing_response->get_error_message() : wp_remote_retrieve_response_message( $bing_response ),
        );
        
        return $results;
    }
}
