import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Dashicon,
  SelectControl,
  Spinner,
  Notice
} from '@wordpress/components';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Importer le service
import EcommerceService from '../../services/EcommerceService';

const EcommerceDashboard = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [productStats, setProductStats] = useState({
    total: 0,
    optimized: 0,
    needsAttention: 0,
    critical: 0,
    categories: []
  });
  const [topProducts, setTopProducts] = useState([]);
  const [topCategories, setTopCategories] = useState([]);
  const [selectedPeriod, setSelectedPeriod] = useState('30days');

  // Créer une instance du service
  const ecommerceService = new EcommerceService();

  // Fonction pour charger les données
  const loadData = async (period) => {
    try {
      setIsLoading(true);
      setError(null);

      // Récupérer les données du tableau de bord
      const dashboardData = await ecommerceService.getDashboardData();

      // Récupérer les produits les plus performants
      const topProductsData = await ecommerceService.getTopProducts(period);

      // Récupérer les catégories les plus performantes
      const topCategoriesData = await ecommerceService.getTopCategories(period);

      // Mettre à jour les états avec les données reçues ou des valeurs par défaut
      // Les services retournent maintenant des données fictives en cas d'erreur
      if (dashboardData && dashboardData.stats) {
        setProductStats(dashboardData.stats);
      } else {
        setProductStats({
          total: 0,
          optimized: 0,
          needsAttention: 0,
          critical: 0,
          categories: []
        });
      }

      if (topProductsData && topProductsData.products) {
        setTopProducts(topProductsData.products);
      } else {
        setTopProducts([]);
      }

      if (topCategoriesData && topCategoriesData.categories) {
        setTopCategories(topCategoriesData.categories);
      } else {
        setTopCategories([]);
      }
    } catch (err) {
      console.error('Erreur lors du chargement des données du tableau de bord e-commerce:', err);
      setError(__('Erreur lors du chargement des données. Veuillez réessayer.', 'boss-seo'));

      // Même en cas d'erreur, nous n'avons pas besoin d'utiliser des données fictives ici
      // car les services retournent déjà des données fictives en cas d'erreur
    } finally {
      setIsLoading(false);
    }
  };

  // Charger les données initiales
  useEffect(() => {
    loadData(selectedPeriod);
  }, []);

  // Recharger les données lorsque la période change
  useEffect(() => {
    loadData(selectedPeriod);
  }, [selectedPeriod]);

  // Fonction pour obtenir la classe de couleur en fonction du score
  const getScoreColorClass = (score) => {
    if (score >= 80) return 'boss-text-green-600';
    if (score >= 60) return 'boss-text-yellow-600';
    return 'boss-text-red-600';
  };

  // Données pour le graphique en camembert
  const pieChartData = [
    { name: __('Optimisés', 'boss-seo'), value: productStats.optimized },
    { name: __('À améliorer', 'boss-seo'), value: productStats.needsAttention },
    { name: __('Critiques', 'boss-seo'), value: productStats.critical }
  ];

  // Couleurs pour le graphique en camembert
  const COLORS = ['#10B981', '#F59E0B', '#EF4444'];

  return (
    <div>
      {error && (
        <Notice status="error" isDismissible={false} className="boss-mb-6">
          {error}
        </Notice>
      )}

      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {/* Statistiques principales */}
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-4 boss-gap-4 boss-mb-6">
            <Card>
              <CardBody className="boss-p-4">
                <div className="boss-flex boss-justify-between boss-items-start">
                  <div>
                    <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                      {__('Total des produits', 'boss-seo')}
                    </h3>
                    <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
                      {productStats.total}
                    </div>
                  </div>
                  <div className="boss-bg-blue-100 boss-p-2 boss-rounded-lg">
                    <Dashicon icon="cart" className="boss-text-blue-600 boss-text-xl" />
                  </div>
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="boss-p-4">
                <div className="boss-flex boss-justify-between boss-items-start">
                  <div>
                    <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                      {__('Produits optimisés', 'boss-seo')}
                    </h3>
                    <div className="boss-flex boss-items-baseline">
                      <span className="boss-text-2xl boss-font-bold boss-text-green-600 boss-mr-2">
                        {productStats.optimized}
                      </span>
                      <span className="boss-text-sm boss-text-boss-gray">
                        {productStats.total > 0 ? Math.round((productStats.optimized / productStats.total) * 100) : 0}%
                      </span>
                    </div>
                  </div>
                  <div className="boss-bg-green-100 boss-p-2 boss-rounded-lg">
                    <Dashicon icon="yes-alt" className="boss-text-green-600 boss-text-xl" />
                  </div>
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="boss-p-4">
                <div className="boss-flex boss-justify-between boss-items-start">
                  <div>
                    <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                      {__('À améliorer', 'boss-seo')}
                    </h3>
                    <div className="boss-flex boss-items-baseline">
                      <span className="boss-text-2xl boss-font-bold boss-text-yellow-600 boss-mr-2">
                        {productStats.needsAttention}
                      </span>
                      <span className="boss-text-sm boss-text-boss-gray">
                        {productStats.total > 0 ? Math.round((productStats.needsAttention / productStats.total) * 100) : 0}%
                      </span>
                    </div>
                  </div>
                  <div className="boss-bg-yellow-100 boss-p-2 boss-rounded-lg">
                    <Dashicon icon="warning" className="boss-text-yellow-600 boss-text-xl" />
                  </div>
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="boss-p-4">
                <div className="boss-flex boss-justify-between boss-items-start">
                  <div>
                    <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                      {__('Critiques', 'boss-seo')}
                    </h3>
                    <div className="boss-flex boss-items-baseline">
                      <span className="boss-text-2xl boss-font-bold boss-text-red-600 boss-mr-2">
                        {productStats.critical}
                      </span>
                      <span className="boss-text-sm boss-text-boss-gray">
                        {productStats.total > 0 ? Math.round((productStats.critical / productStats.total) * 100) : 0}%
                      </span>
                    </div>
                  </div>
                  <div className="boss-bg-red-100 boss-p-2 boss-rounded-lg">
                    <Dashicon icon="dismiss" className="boss-text-red-600 boss-text-xl" />
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Graphiques */}
          <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-2 boss-gap-6 boss-mb-6">
            {/* Répartition des produits */}
            <Card>
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Répartition des produits', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieChartData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {pieChartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [value, __('Produits', 'boss-seo')]} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardBody>
            </Card>

            {/* Performance par catégorie */}
            <Card>
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Performance par catégorie', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={topCategories}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                      <YAxis yAxisId="left" tick={{ fontSize: 12 }} />
                      <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 12 }} />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="clicks" name={__('Clics', 'boss-seo')} fill="#4F46E5" />
                      <Bar yAxisId="left" dataKey="conversions" name={__('Conversions', 'boss-seo')} fill="#10B981" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Produits les plus performants */}
          <Card className="boss-mb-6">
            <CardHeader className="boss-border-b boss-border-gray-200">
              <div className="boss-flex boss-justify-between boss-items-center">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Produits les plus performants', 'boss-seo')}
                </h2>
                <SelectControl
                  value={selectedPeriod}
                  options={[
                    { label: __('7 derniers jours', 'boss-seo'), value: '7days' },
                    { label: __('30 derniers jours', 'boss-seo'), value: '30days' },
                    { label: __('90 derniers jours', 'boss-seo'), value: '90days' }
                  ]}
                  onChange={setSelectedPeriod}
                />
              </div>
            </CardHeader>
            <CardBody className="boss-p-0">
              <div className="boss-overflow-x-auto">
                <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
                  <thead className="boss-bg-gray-50">
                    <tr>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Produit', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Catégorie', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Score SEO', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Clics', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Conversions', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Taux', 'boss-seo')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                    {(topProducts || []).map(product => (
                      <tr key={product.id} className="boss-hover:boss-bg-gray-50">
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className="boss-font-medium boss-text-boss-dark">{product.name}</div>
                        </td>
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className="boss-text-boss-gray">{product.category}</div>
                        </td>
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className={`boss-font-medium ${getScoreColorClass(product.score)}`}>
                            {product.score}/100
                          </div>
                        </td>
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className="boss-text-boss-gray">{product.clicks}</div>
                        </td>
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className="boss-text-boss-gray">{product.conversions}</div>
                        </td>
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className="boss-text-boss-gray">
                            {product.clicks > 0 ? ((product.conversions / product.clicks) * 100).toFixed(2) : 0}%
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardBody>
          </Card>

          {/* Statistiques par catégorie */}
          <Card>
            <CardHeader className="boss-border-b boss-border-gray-200">
              <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                {__('Statistiques par catégorie', 'boss-seo')}
              </h2>
            </CardHeader>
            <CardBody className="boss-p-0">
              <div className="boss-overflow-x-auto">
                <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
                  <thead className="boss-bg-gray-50">
                    <tr>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Catégorie', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Produits', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Optimisés', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('À améliorer', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Critiques', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-right boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Actions', 'boss-seo')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                    {(productStats.categories || []).map((category, index) => (
                      <tr key={index} className="boss-hover:boss-bg-gray-50">
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className="boss-font-medium boss-text-boss-dark">{category.name}</div>
                        </td>
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className="boss-text-boss-gray">{category.count}</div>
                        </td>
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className="boss-text-green-600">{category.optimized}</div>
                        </td>
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className="boss-text-yellow-600">{category.needsAttention}</div>
                        </td>
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                          <div className="boss-text-red-600">{category.critical}</div>
                        </td>
                        <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-right">
                          <Button
                            isSecondary
                            isSmall
                            href={`#product-optimizer?category=${category.name}`}
                          >
                            {__('Optimiser', 'boss-seo')}
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardBody>
          </Card>
        </div>
      )}
    </div>
  );
};

export default EcommerceDashboard;
