<?php
/**
 * Classe pour la gestion des emplacements.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour la gestion des emplacements.
 *
 * Cette classe gère toutes les fonctionnalités liées aux emplacements locaux.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Location {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le nom du type de contenu personnalisé pour les emplacements.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $cpt_name    Le nom du CPT.
     */
    protected $cpt_name = 'boss_location';

    /**
     * Le préfixe pour les métadonnées.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $meta_prefix    Le préfixe pour les métadonnées.
     */
    protected $meta_prefix = 'boss_location_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Enregistrer le type de contenu personnalisé
        add_action( 'init', array( $this, 'register_post_type' ) );

        // Ajouter les métaboxes
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );

        // Enregistrer les métadonnées
        add_action( 'save_post_' . $this->cpt_name, array( $this, 'save_meta_boxes' ), 10, 2 );

        // Ajouter les colonnes personnalisées
        add_filter( 'manage_' . $this->cpt_name . '_posts_columns', array( $this, 'set_custom_columns' ) );
        add_action( 'manage_' . $this->cpt_name . '_posts_custom_column', array( $this, 'custom_column_content' ), 10, 2 );

        // Ajouter les filtres personnalisés
        add_action( 'restrict_manage_posts', array( $this, 'add_custom_filters' ) );
        add_filter( 'parse_query', array( $this, 'filter_locations_by_status' ) );

        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_import_locations', array( $this, 'ajax_import_locations' ) );
        add_action( 'wp_ajax_boss_seo_export_locations', array( $this, 'ajax_export_locations' ) );
        add_action( 'wp_ajax_boss_seo_get_location_stats', array( $this, 'ajax_get_location_stats' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/locations',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_locations' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'add_location' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/locations/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_location' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'PUT',
                    'callback'            => array( $this, 'update_location' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_location' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/locations/import',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'import_locations' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/locations/export',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'export_locations' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/locations/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_location_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Enregistre le type de contenu personnalisé pour les emplacements.
     *
     * @since    1.2.0
     */
    public function register_post_type() {
        $labels = array(
            'name'                  => _x( 'Emplacements', 'Post type general name', 'boss-seo' ),
            'singular_name'         => _x( 'Emplacement', 'Post type singular name', 'boss-seo' ),
            'menu_name'             => _x( 'Emplacements', 'Admin Menu text', 'boss-seo' ),
            'name_admin_bar'        => _x( 'Emplacement', 'Add New on Toolbar', 'boss-seo' ),
            'add_new'               => __( 'Ajouter', 'boss-seo' ),
            'add_new_item'          => __( 'Ajouter un emplacement', 'boss-seo' ),
            'new_item'              => __( 'Nouvel emplacement', 'boss-seo' ),
            'edit_item'             => __( 'Modifier l\'emplacement', 'boss-seo' ),
            'view_item'             => __( 'Voir l\'emplacement', 'boss-seo' ),
            'all_items'             => __( 'Tous les emplacements', 'boss-seo' ),
            'search_items'          => __( 'Rechercher des emplacements', 'boss-seo' ),
            'parent_item_colon'     => __( 'Emplacement parent :', 'boss-seo' ),
            'not_found'             => __( 'Aucun emplacement trouvé.', 'boss-seo' ),
            'not_found_in_trash'    => __( 'Aucun emplacement trouvé dans la corbeille.', 'boss-seo' ),
            'featured_image'        => _x( 'Image de l\'emplacement', 'Overrides the "Featured Image" phrase', 'boss-seo' ),
            'set_featured_image'    => _x( 'Définir l\'image de l\'emplacement', 'Overrides the "Set featured image" phrase', 'boss-seo' ),
            'remove_featured_image' => _x( 'Supprimer l\'image de l\'emplacement', 'Overrides the "Remove featured image" phrase', 'boss-seo' ),
            'use_featured_image'    => _x( 'Utiliser comme image de l\'emplacement', 'Overrides the "Use as featured image" phrase', 'boss-seo' ),
            'archives'              => _x( 'Archives des emplacements', 'The post type archive label used in nav menus', 'boss-seo' ),
            'insert_into_item'      => _x( 'Insérer dans l\'emplacement', 'Overrides the "Insert into post" phrase', 'boss-seo' ),
            'uploaded_to_this_item' => _x( 'Téléversé sur cet emplacement', 'Overrides the "Uploaded to this post" phrase', 'boss-seo' ),
            'filter_items_list'     => _x( 'Filtrer la liste des emplacements', 'Screen reader text for the filter links heading on the post type listing screen', 'boss-seo' ),
            'items_list_navigation' => _x( 'Navigation de la liste des emplacements', 'Screen reader text for the pagination heading on the post type listing screen', 'boss-seo' ),
            'items_list'            => _x( 'Liste des emplacements', 'Screen reader text for the items list heading on the post type listing screen', 'boss-seo' ),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => false,
            'query_var'          => true,
            'rewrite'            => array( 'slug' => 'emplacement' ),
            'capability_type'    => 'post',
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => null,
            'supports'           => array( 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'       => true,
        );

        register_post_type( $this->cpt_name, $args );

        // Enregistrer la taxonomie pour les catégories d'emplacements
        $taxonomy_labels = array(
            'name'              => _x( 'Catégories d\'emplacements', 'taxonomy general name', 'boss-seo' ),
            'singular_name'     => _x( 'Catégorie d\'emplacement', 'taxonomy singular name', 'boss-seo' ),
            'search_items'      => __( 'Rechercher des catégories', 'boss-seo' ),
            'all_items'         => __( 'Toutes les catégories', 'boss-seo' ),
            'parent_item'       => __( 'Catégorie parente', 'boss-seo' ),
            'parent_item_colon' => __( 'Catégorie parente :', 'boss-seo' ),
            'edit_item'         => __( 'Modifier la catégorie', 'boss-seo' ),
            'update_item'       => __( 'Mettre à jour la catégorie', 'boss-seo' ),
            'add_new_item'      => __( 'Ajouter une catégorie', 'boss-seo' ),
            'new_item_name'     => __( 'Nom de la nouvelle catégorie', 'boss-seo' ),
            'menu_name'         => __( 'Catégories', 'boss-seo' ),
        );

        $taxonomy_args = array(
            'hierarchical'      => true,
            'labels'            => $taxonomy_labels,
            'show_ui'           => true,
            'show_admin_column' => true,
            'query_var'         => true,
            'rewrite'           => array( 'slug' => 'categorie-emplacement' ),
            'show_in_rest'      => true,
        );

        register_taxonomy( 'boss_location_category', array( $this->cpt_name ), $taxonomy_args );
    }

    /**
     * Ajoute les métaboxes pour les emplacements.
     *
     * @since    1.2.0
     */
    public function add_meta_boxes() {
        add_meta_box(
            'boss_location_details',
            __( 'Détails de l\'emplacement', 'boss-seo' ),
            array( $this, 'render_details_meta_box' ),
            $this->cpt_name,
            'normal',
            'high'
        );

        add_meta_box(
            'boss_location_address',
            __( 'Adresse', 'boss-seo' ),
            array( $this, 'render_address_meta_box' ),
            $this->cpt_name,
            'normal',
            'high'
        );

        add_meta_box(
            'boss_location_contact',
            __( 'Coordonnées', 'boss-seo' ),
            array( $this, 'render_contact_meta_box' ),
            $this->cpt_name,
            'normal',
            'high'
        );

        add_meta_box(
            'boss_location_hours',
            __( 'Horaires d\'ouverture', 'boss-seo' ),
            array( $this, 'render_hours_meta_box' ),
            $this->cpt_name,
            'normal',
            'high'
        );

        add_meta_box(
            'boss_location_map',
            __( 'Carte', 'boss-seo' ),
            array( $this, 'render_map_meta_box' ),
            $this->cpt_name,
            'side',
            'default'
        );

        add_meta_box(
            'boss_location_seo',
            __( 'Paramètres SEO', 'boss-seo' ),
            array( $this, 'render_seo_meta_box' ),
            $this->cpt_name,
            'normal',
            'low'
        );
    }

    /**
     * Affiche la métabox des détails de l'emplacement.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post.
     */
    public function render_details_meta_box( $post ) {
        // Récupérer les métadonnées existantes
        $status = get_post_meta( $post->ID, $this->meta_prefix . 'status', true );
        $primary = get_post_meta( $post->ID, $this->meta_prefix . 'primary', true );
        $short_name = get_post_meta( $post->ID, $this->meta_prefix . 'short_name', true );
        $description = get_post_meta( $post->ID, $this->meta_prefix . 'description', true );

        // Ajouter un nonce pour la sécurité
        wp_nonce_field( 'boss_location_meta_box', 'boss_location_meta_box_nonce' );

        // Afficher les champs
        ?>
        <div class="boss-seo-meta-box">
            <div class="boss-seo-field">
                <label for="boss_location_status"><?php _e( 'Statut', 'boss-seo' ); ?></label>
                <select name="boss_location_status" id="boss_location_status">
                    <option value="active" <?php selected( $status, 'active' ); ?>><?php _e( 'Actif', 'boss-seo' ); ?></option>
                    <option value="inactive" <?php selected( $status, 'inactive' ); ?>><?php _e( 'Inactif', 'boss-seo' ); ?></option>
                    <option value="coming_soon" <?php selected( $status, 'coming_soon' ); ?>><?php _e( 'À venir', 'boss-seo' ); ?></option>
                </select>
                <p class="description"><?php _e( 'Le statut de l\'emplacement.', 'boss-seo' ); ?></p>
            </div>

            <div class="boss-seo-field">
                <label for="boss_location_primary">
                    <input type="checkbox" name="boss_location_primary" id="boss_location_primary" value="1" <?php checked( $primary, '1' ); ?>>
                    <?php _e( 'Emplacement principal', 'boss-seo' ); ?>
                </label>
                <p class="description"><?php _e( 'Cochez cette case si cet emplacement est le siège social ou l\'emplacement principal.', 'boss-seo' ); ?></p>
            </div>

            <div class="boss-seo-field">
                <label for="boss_location_short_name"><?php _e( 'Nom court', 'boss-seo' ); ?></label>
                <input type="text" name="boss_location_short_name" id="boss_location_short_name" value="<?php echo esc_attr( $short_name ); ?>" class="regular-text">
                <p class="description"><?php _e( 'Un nom court pour cet emplacement (ex: Paris Centre).', 'boss-seo' ); ?></p>
            </div>

            <div class="boss-seo-field">
                <label for="boss_location_description"><?php _e( 'Description courte', 'boss-seo' ); ?></label>
                <textarea name="boss_location_description" id="boss_location_description" rows="3" class="large-text"><?php echo esc_textarea( $description ); ?></textarea>
                <p class="description"><?php _e( 'Une brève description de cet emplacement.', 'boss-seo' ); ?></p>
            </div>
        </div>
        <?php
    }

    /**
     * Affiche la métabox de l'adresse.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post.
     */
    public function render_address_meta_box( $post ) {
        // Récupérer les métadonnées existantes
        $street = get_post_meta( $post->ID, $this->meta_prefix . 'street', true );
        $street2 = get_post_meta( $post->ID, $this->meta_prefix . 'street2', true );
        $city = get_post_meta( $post->ID, $this->meta_prefix . 'city', true );
        $state = get_post_meta( $post->ID, $this->meta_prefix . 'state', true );
        $postal_code = get_post_meta( $post->ID, $this->meta_prefix . 'postal_code', true );
        $country = get_post_meta( $post->ID, $this->meta_prefix . 'country', true );
        $latitude = get_post_meta( $post->ID, $this->meta_prefix . 'latitude', true );
        $longitude = get_post_meta( $post->ID, $this->meta_prefix . 'longitude', true );

        // Afficher les champs
        ?>
        <div class="boss-seo-meta-box">
            <div class="boss-seo-field">
                <label for="boss_location_street"><?php _e( 'Adresse', 'boss-seo' ); ?></label>
                <input type="text" name="boss_location_street" id="boss_location_street" value="<?php echo esc_attr( $street ); ?>" class="regular-text">
            </div>

            <div class="boss-seo-field">
                <label for="boss_location_street2"><?php _e( 'Complément d\'adresse', 'boss-seo' ); ?></label>
                <input type="text" name="boss_location_street2" id="boss_location_street2" value="<?php echo esc_attr( $street2 ); ?>" class="regular-text">
            </div>

            <div class="boss-seo-field-row">
                <div class="boss-seo-field">
                    <label for="boss_location_city"><?php _e( 'Ville', 'boss-seo' ); ?></label>
                    <input type="text" name="boss_location_city" id="boss_location_city" value="<?php echo esc_attr( $city ); ?>" class="regular-text">
                </div>

                <div class="boss-seo-field">
                    <label for="boss_location_state"><?php _e( 'Région/État', 'boss-seo' ); ?></label>
                    <input type="text" name="boss_location_state" id="boss_location_state" value="<?php echo esc_attr( $state ); ?>" class="regular-text">
                </div>
            </div>

            <div class="boss-seo-field-row">
                <div class="boss-seo-field">
                    <label for="boss_location_postal_code"><?php _e( 'Code postal', 'boss-seo' ); ?></label>
                    <input type="text" name="boss_location_postal_code" id="boss_location_postal_code" value="<?php echo esc_attr( $postal_code ); ?>" class="regular-text">
                </div>

                <div class="boss-seo-field">
                    <label for="boss_location_country"><?php _e( 'Pays', 'boss-seo' ); ?></label>
                    <input type="text" name="boss_location_country" id="boss_location_country" value="<?php echo esc_attr( $country ); ?>" class="regular-text">
                </div>
            </div>

            <div class="boss-seo-field-row">
                <div class="boss-seo-field">
                    <label for="boss_location_latitude"><?php _e( 'Latitude', 'boss-seo' ); ?></label>
                    <input type="text" name="boss_location_latitude" id="boss_location_latitude" value="<?php echo esc_attr( $latitude ); ?>" class="regular-text">
                </div>

                <div class="boss-seo-field">
                    <label for="boss_location_longitude"><?php _e( 'Longitude', 'boss-seo' ); ?></label>
                    <input type="text" name="boss_location_longitude" id="boss_location_longitude" value="<?php echo esc_attr( $longitude ); ?>" class="regular-text">
                </div>
            </div>

            <div class="boss-seo-field">
                <button type="button" class="button" id="boss_location_geocode"><?php _e( 'Obtenir les coordonnées', 'boss-seo' ); ?></button>
                <p class="description"><?php _e( 'Cliquez pour obtenir automatiquement les coordonnées à partir de l\'adresse.', 'boss-seo' ); ?></p>
            </div>
        </div>
        <?php
    }

    /**
     * Affiche la métabox des coordonnées.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post.
     */
    public function render_contact_meta_box( $post ) {
        // Récupérer les métadonnées existantes
        $phone = get_post_meta( $post->ID, $this->meta_prefix . 'phone', true );
        $fax = get_post_meta( $post->ID, $this->meta_prefix . 'fax', true );
        $email = get_post_meta( $post->ID, $this->meta_prefix . 'email', true );
        $website = get_post_meta( $post->ID, $this->meta_prefix . 'website', true );

        // Afficher les champs
        ?>
        <div class="boss-seo-meta-box">
            <div class="boss-seo-field-row">
                <div class="boss-seo-field">
                    <label for="boss_location_phone"><?php _e( 'Téléphone', 'boss-seo' ); ?></label>
                    <input type="tel" name="boss_location_phone" id="boss_location_phone" value="<?php echo esc_attr( $phone ); ?>" class="regular-text">
                </div>

                <div class="boss-seo-field">
                    <label for="boss_location_fax"><?php _e( 'Fax', 'boss-seo' ); ?></label>
                    <input type="tel" name="boss_location_fax" id="boss_location_fax" value="<?php echo esc_attr( $fax ); ?>" class="regular-text">
                </div>
            </div>

            <div class="boss-seo-field-row">
                <div class="boss-seo-field">
                    <label for="boss_location_email"><?php _e( 'Email', 'boss-seo' ); ?></label>
                    <input type="email" name="boss_location_email" id="boss_location_email" value="<?php echo esc_attr( $email ); ?>" class="regular-text">
                </div>

                <div class="boss-seo-field">
                    <label for="boss_location_website"><?php _e( 'Site web', 'boss-seo' ); ?></label>
                    <input type="url" name="boss_location_website" id="boss_location_website" value="<?php echo esc_attr( $website ); ?>" class="regular-text">
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Affiche la métabox des horaires d'ouverture.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post.
     */
    public function render_hours_meta_box( $post ) {
        // Récupérer les métadonnées existantes
        $hours = get_post_meta( $post->ID, $this->meta_prefix . 'hours', true );
        if ( empty( $hours ) ) {
            $hours = array(
                'monday'    => array( 'status' => 'open', 'hours' => array( array( 'open' => '09:00', 'close' => '18:00' ) ) ),
                'tuesday'   => array( 'status' => 'open', 'hours' => array( array( 'open' => '09:00', 'close' => '18:00' ) ) ),
                'wednesday' => array( 'status' => 'open', 'hours' => array( array( 'open' => '09:00', 'close' => '18:00' ) ) ),
                'thursday'  => array( 'status' => 'open', 'hours' => array( array( 'open' => '09:00', 'close' => '18:00' ) ) ),
                'friday'    => array( 'status' => 'open', 'hours' => array( array( 'open' => '09:00', 'close' => '18:00' ) ) ),
                'saturday'  => array( 'status' => 'closed', 'hours' => array() ),
                'sunday'    => array( 'status' => 'closed', 'hours' => array() ),
            );
        }

        // Afficher les champs
        ?>
        <div class="boss-seo-meta-box">
            <div class="boss-seo-hours-container">
                <?php
                $days = array(
                    'monday'    => __( 'Lundi', 'boss-seo' ),
                    'tuesday'   => __( 'Mardi', 'boss-seo' ),
                    'wednesday' => __( 'Mercredi', 'boss-seo' ),
                    'thursday'  => __( 'Jeudi', 'boss-seo' ),
                    'friday'    => __( 'Vendredi', 'boss-seo' ),
                    'saturday'  => __( 'Samedi', 'boss-seo' ),
                    'sunday'    => __( 'Dimanche', 'boss-seo' ),
                );

                foreach ( $days as $day_key => $day_label ) :
                    $day_status = isset( $hours[$day_key]['status'] ) ? $hours[$day_key]['status'] : 'closed';
                    $day_hours = isset( $hours[$day_key]['hours'] ) ? $hours[$day_key]['hours'] : array();
                    ?>
                    <div class="boss-seo-hours-day" data-day="<?php echo esc_attr( $day_key ); ?>">
                        <div class="boss-seo-hours-day-header">
                            <h4><?php echo esc_html( $day_label ); ?></h4>
                            <div class="boss-seo-hours-day-status">
                                <label>
                                    <input type="radio" name="boss_location_hours[<?php echo esc_attr( $day_key ); ?>][status]" value="open" <?php checked( $day_status, 'open' ); ?>>
                                    <?php _e( 'Ouvert', 'boss-seo' ); ?>
                                </label>
                                <label>
                                    <input type="radio" name="boss_location_hours[<?php echo esc_attr( $day_key ); ?>][status]" value="closed" <?php checked( $day_status, 'closed' ); ?>>
                                    <?php _e( 'Fermé', 'boss-seo' ); ?>
                                </label>
                                <label>
                                    <input type="radio" name="boss_location_hours[<?php echo esc_attr( $day_key ); ?>][status]" value="appointment" <?php checked( $day_status, 'appointment' ); ?>>
                                    <?php _e( 'Sur rendez-vous', 'boss-seo' ); ?>
                                </label>
                            </div>
                        </div>
                        <div class="boss-seo-hours-day-slots" <?php echo $day_status !== 'open' ? 'style="display:none;"' : ''; ?>>
                            <?php if ( ! empty( $day_hours ) ) : ?>
                                <?php foreach ( $day_hours as $index => $slot ) : ?>
                                    <div class="boss-seo-hours-slot">
                                        <input type="time" name="boss_location_hours[<?php echo esc_attr( $day_key ); ?>][hours][<?php echo esc_attr( $index ); ?>][open]" value="<?php echo esc_attr( $slot['open'] ); ?>" class="boss-seo-time-input">
                                        <span class="boss-seo-time-separator">-</span>
                                        <input type="time" name="boss_location_hours[<?php echo esc_attr( $day_key ); ?>][hours][<?php echo esc_attr( $index ); ?>][close]" value="<?php echo esc_attr( $slot['close'] ); ?>" class="boss-seo-time-input">
                                        <?php if ( $index > 0 ) : ?>
                                            <button type="button" class="button boss-seo-remove-hours-slot"><?php _e( 'Supprimer', 'boss-seo' ); ?></button>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            <?php else : ?>
                                <div class="boss-seo-hours-slot">
                                    <input type="time" name="boss_location_hours[<?php echo esc_attr( $day_key ); ?>][hours][0][open]" value="09:00" class="boss-seo-time-input">
                                    <span class="boss-seo-time-separator">-</span>
                                    <input type="time" name="boss_location_hours[<?php echo esc_attr( $day_key ); ?>][hours][0][close]" value="18:00" class="boss-seo-time-input">
                                </div>
                            <?php endif; ?>
                            <button type="button" class="button boss-seo-add-hours-slot"><?php _e( 'Ajouter une plage horaire', 'boss-seo' ); ?></button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Affiche la métabox de la carte.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post.
     */
    public function render_map_meta_box( $post ) {
        // Récupérer les métadonnées existantes
        $latitude = get_post_meta( $post->ID, $this->meta_prefix . 'latitude', true );
        $longitude = get_post_meta( $post->ID, $this->meta_prefix . 'longitude', true );

        // Valeurs par défaut
        if ( empty( $latitude ) ) {
            $latitude = get_option( 'boss_seo_default_lat', '48.8566' );
        }
        if ( empty( $longitude ) ) {
            $longitude = get_option( 'boss_seo_default_lng', '2.3522' );
        }

        // Afficher la carte
        ?>
        <div id="boss_location_map" style="height: 300px;"></div>
        <p class="description"><?php _e( 'Cliquez sur la carte pour définir la position exacte.', 'boss-seo' ); ?></p>
        <script>
            jQuery(document).ready(function($) {
                // Initialiser la carte
                var map = L.map('boss_location_map').setView([<?php echo esc_js( $latitude ); ?>, <?php echo esc_js( $longitude ); ?>], 13);

                // Ajouter la couche de tuiles
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(map);

                // Ajouter un marqueur
                var marker = L.marker([<?php echo esc_js( $latitude ); ?>, <?php echo esc_js( $longitude ); ?>], {
                    draggable: true
                }).addTo(map);

                // Mettre à jour les coordonnées lorsque le marqueur est déplacé
                marker.on('dragend', function(event) {
                    var position = marker.getLatLng();
                    $('#boss_location_latitude').val(position.lat.toFixed(6));
                    $('#boss_location_longitude').val(position.lng.toFixed(6));
                });

                // Mettre à jour le marqueur lorsque les coordonnées sont modifiées
                $('#boss_location_latitude, #boss_location_longitude').on('change', function() {
                    var lat = parseFloat($('#boss_location_latitude').val());
                    var lng = parseFloat($('#boss_location_longitude').val());

                    if (!isNaN(lat) && !isNaN(lng)) {
                        marker.setLatLng([lat, lng]);
                        map.setView([lat, lng], 13);
                    }
                });

                // Géocodage
                $('#boss_location_geocode').on('click', function() {
                    var street = $('#boss_location_street').val();
                    var street2 = $('#boss_location_street2').val();
                    var city = $('#boss_location_city').val();
                    var state = $('#boss_location_state').val();
                    var postal_code = $('#boss_location_postal_code').val();
                    var country = $('#boss_location_country').val();

                    var address = [street, street2, city, state, postal_code, country].filter(Boolean).join(', ');

                    if (address) {
                        $.ajax({
                            url: 'https://nominatim.openstreetmap.org/search',
                            type: 'GET',
                            data: {
                                q: address,
                                format: 'json',
                                limit: 1
                            },
                            success: function(data) {
                                if (data.length > 0) {
                                    var lat = parseFloat(data[0].lat);
                                    var lng = parseFloat(data[0].lon);

                                    $('#boss_location_latitude').val(lat.toFixed(6));
                                    $('#boss_location_longitude').val(lng.toFixed(6));

                                    marker.setLatLng([lat, lng]);
                                    map.setView([lat, lng], 13);
                                } else {
                                    alert('<?php _e( 'Adresse non trouvée. Veuillez vérifier les informations saisies.', 'boss-seo' ); ?>');
                                }
                            },
                            error: function() {
                                alert('<?php _e( 'Erreur lors de la géolocalisation. Veuillez réessayer.', 'boss-seo' ); ?>');
                            }
                        });
                    } else {
                        alert('<?php _e( 'Veuillez saisir une adresse.', 'boss-seo' ); ?>');
                    }
                });
            });
        </script>
        <?php
    }

    /**
     * Affiche la métabox des paramètres SEO.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post.
     */
    public function render_seo_meta_box( $post ) {
        // Récupérer les métadonnées existantes
        $seo_title = get_post_meta( $post->ID, $this->meta_prefix . 'seo_title', true );
        $seo_description = get_post_meta( $post->ID, $this->meta_prefix . 'seo_description', true );
        $seo_keywords = get_post_meta( $post->ID, $this->meta_prefix . 'seo_keywords', true );
        $seo_hide_in_sitemap = get_post_meta( $post->ID, $this->meta_prefix . 'seo_hide_in_sitemap', true );
        $seo_schema_type = get_post_meta( $post->ID, $this->meta_prefix . 'seo_schema_type', true );
        if ( empty( $seo_schema_type ) ) {
            $seo_schema_type = 'LocalBusiness';
        }

        // Afficher les champs
        ?>
        <div class="boss-seo-meta-box">
            <div class="boss-seo-field">
                <label for="boss_location_seo_title"><?php _e( 'Titre SEO', 'boss-seo' ); ?></label>
                <input type="text" name="boss_location_seo_title" id="boss_location_seo_title" value="<?php echo esc_attr( $seo_title ); ?>" class="large-text">
                <p class="description"><?php _e( 'Le titre qui sera utilisé dans les résultats de recherche. Laissez vide pour utiliser le titre de l\'emplacement.', 'boss-seo' ); ?></p>
            </div>

            <div class="boss-seo-field">
                <label for="boss_location_seo_description"><?php _e( 'Description SEO', 'boss-seo' ); ?></label>
                <textarea name="boss_location_seo_description" id="boss_location_seo_description" rows="3" class="large-text"><?php echo esc_textarea( $seo_description ); ?></textarea>
                <p class="description"><?php _e( 'La description qui sera utilisée dans les résultats de recherche. Laissez vide pour utiliser la description de l\'emplacement.', 'boss-seo' ); ?></p>
            </div>

            <div class="boss-seo-field">
                <label for="boss_location_seo_keywords"><?php _e( 'Mots-clés SEO', 'boss-seo' ); ?></label>
                <input type="text" name="boss_location_seo_keywords" id="boss_location_seo_keywords" value="<?php echo esc_attr( $seo_keywords ); ?>" class="large-text">
                <p class="description"><?php _e( 'Les mots-clés séparés par des virgules.', 'boss-seo' ); ?></p>
            </div>

            <div class="boss-seo-field">
                <label for="boss_location_seo_schema_type"><?php _e( 'Type de schéma', 'boss-seo' ); ?></label>
                <select name="boss_location_seo_schema_type" id="boss_location_seo_schema_type">
                    <option value="LocalBusiness" <?php selected( $seo_schema_type, 'LocalBusiness' ); ?>><?php _e( 'Entreprise locale (générique)', 'boss-seo' ); ?></option>
                    <option value="Restaurant" <?php selected( $seo_schema_type, 'Restaurant' ); ?>><?php _e( 'Restaurant', 'boss-seo' ); ?></option>
                    <option value="Store" <?php selected( $seo_schema_type, 'Store' ); ?>><?php _e( 'Magasin', 'boss-seo' ); ?></option>
                    <option value="MedicalBusiness" <?php selected( $seo_schema_type, 'MedicalBusiness' ); ?>><?php _e( 'Établissement médical', 'boss-seo' ); ?></option>
                    <option value="FinancialService" <?php selected( $seo_schema_type, 'FinancialService' ); ?>><?php _e( 'Service financier', 'boss-seo' ); ?></option>
                    <option value="LodgingBusiness" <?php selected( $seo_schema_type, 'LodgingBusiness' ); ?>><?php _e( 'Hébergement', 'boss-seo' ); ?></option>
                </select>
                <p class="description"><?php _e( 'Le type de schéma structuré à utiliser pour cet emplacement.', 'boss-seo' ); ?></p>
            </div>

            <div class="boss-seo-field">
                <label for="boss_location_seo_hide_in_sitemap">
                    <input type="checkbox" name="boss_location_seo_hide_in_sitemap" id="boss_location_seo_hide_in_sitemap" value="1" <?php checked( $seo_hide_in_sitemap, '1' ); ?>>
                    <?php _e( 'Masquer dans le sitemap', 'boss-seo' ); ?>
                </label>
                <p class="description"><?php _e( 'Cochez cette case pour exclure cet emplacement du sitemap XML.', 'boss-seo' ); ?></p>
            </div>
        </div>
        <?php
    }

    /**
     * Enregistre les métadonnées des emplacements.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID du post.
     * @param    WP_Post   $post       L'objet post.
     */
    public function save_meta_boxes( $post_id, $post ) {
        // Vérifier le nonce
        if ( ! isset( $_POST['boss_location_meta_box_nonce'] ) || ! wp_verify_nonce( $_POST['boss_location_meta_box_nonce'], 'boss_location_meta_box' ) ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Vérifier si c'est une sauvegarde automatique
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }

        // Vérifier le type de post
        if ( $post->post_type !== $this->cpt_name ) {
            return;
        }

        // Enregistrer les métadonnées
        $meta_fields = array(
            // Détails
            'status',
            'primary',
            'short_name',
            'description',

            // Adresse
            'street',
            'street2',
            'city',
            'state',
            'postal_code',
            'country',
            'latitude',
            'longitude',

            // Coordonnées
            'phone',
            'fax',
            'email',
            'website',

            // SEO
            'seo_title',
            'seo_description',
            'seo_keywords',
            'seo_schema_type',
            'seo_hide_in_sitemap',
        );

        foreach ( $meta_fields as $field ) {
            $meta_key = $this->meta_prefix . $field;
            $old_value = get_post_meta( $post_id, $meta_key, true );
            $new_value = isset( $_POST['boss_location_' . $field] ) ? $_POST['boss_location_' . $field] : '';

            // Sanitize les valeurs
            if ( $field === 'description' || $field === 'seo_description' ) {
                $new_value = sanitize_textarea_field( $new_value );
            } elseif ( $field === 'email' ) {
                $new_value = sanitize_email( $new_value );
            } elseif ( $field === 'website' ) {
                $new_value = esc_url_raw( $new_value );
            } elseif ( $field === 'primary' || $field === 'seo_hide_in_sitemap' ) {
                $new_value = isset( $_POST['boss_location_' . $field] ) ? '1' : '';
            } else {
                $new_value = sanitize_text_field( $new_value );
            }

            // Mettre à jour ou supprimer la métadonnée
            if ( $new_value && $new_value !== $old_value ) {
                update_post_meta( $post_id, $meta_key, $new_value );
            } elseif ( '' === $new_value && $old_value ) {
                delete_post_meta( $post_id, $meta_key, $old_value );
            }
        }

        // Enregistrer les horaires d'ouverture
        if ( isset( $_POST['boss_location_hours'] ) && is_array( $_POST['boss_location_hours'] ) ) {
            $hours = array();

            foreach ( $_POST['boss_location_hours'] as $day => $day_data ) {
                $status = isset( $day_data['status'] ) ? sanitize_text_field( $day_data['status'] ) : 'closed';
                $day_hours = array();

                if ( $status === 'open' && isset( $day_data['hours'] ) && is_array( $day_data['hours'] ) ) {
                    foreach ( $day_data['hours'] as $slot ) {
                        if ( isset( $slot['open'] ) && isset( $slot['close'] ) ) {
                            $day_hours[] = array(
                                'open' => sanitize_text_field( $slot['open'] ),
                                'close' => sanitize_text_field( $slot['close'] ),
                            );
                        }
                    }
                }

                $hours[$day] = array(
                    'status' => $status,
                    'hours' => $day_hours,
                );
            }

            update_post_meta( $post_id, $this->meta_prefix . 'hours', $hours );
        }
    }

    /**
     * Définit les colonnes personnalisées pour la liste des emplacements.
     *
     * @since    1.2.0
     * @param    array    $columns    Les colonnes existantes.
     * @return   array                Les colonnes modifiées.
     */
    public function set_custom_columns( $columns ) {
        $new_columns = array();

        // Insérer les colonnes après la case à cocher
        foreach ( $columns as $key => $value ) {
            $new_columns[$key] = $value;

            if ( $key === 'cb' ) {
                $new_columns['thumbnail'] = __( 'Image', 'boss-seo' );
            }
        }

        // Ajouter les colonnes personnalisées
        $new_columns['status'] = __( 'Statut', 'boss-seo' );
        $new_columns['address'] = __( 'Adresse', 'boss-seo' );
        $new_columns['phone'] = __( 'Téléphone', 'boss-seo' );
        $new_columns['primary'] = __( 'Principal', 'boss-seo' );
        $new_columns['schema_type'] = __( 'Type de schéma', 'boss-seo' );

        return $new_columns;
    }

    /**
     * Affiche le contenu des colonnes personnalisées.
     *
     * @since    1.2.0
     * @param    string    $column     Le nom de la colonne.
     * @param    int       $post_id    L'ID du post.
     */
    public function custom_column_content( $column, $post_id ) {
        switch ( $column ) {
            case 'thumbnail':
                if ( has_post_thumbnail( $post_id ) ) {
                    echo get_the_post_thumbnail( $post_id, array( 50, 50 ) );
                } else {
                    echo '<img src="' . plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/images/location-placeholder.png" width="50" height="50" alt="' . __( 'Emplacement', 'boss-seo' ) . '">';
                }
                break;

            case 'status':
                $status = get_post_meta( $post_id, $this->meta_prefix . 'status', true );
                $status_labels = array(
                    'active' => __( 'Actif', 'boss-seo' ),
                    'inactive' => __( 'Inactif', 'boss-seo' ),
                    'coming_soon' => __( 'À venir', 'boss-seo' ),
                );

                $status_colors = array(
                    'active' => 'green',
                    'inactive' => 'red',
                    'coming_soon' => 'orange',
                );

                $status_label = isset( $status_labels[$status] ) ? $status_labels[$status] : __( 'Inconnu', 'boss-seo' );
                $status_color = isset( $status_colors[$status] ) ? $status_colors[$status] : 'gray';

                echo '<span class="boss-seo-status-badge" style="background-color: ' . esc_attr( $status_color ) . ';">' . esc_html( $status_label ) . '</span>';
                break;

            case 'address':
                $city = get_post_meta( $post_id, $this->meta_prefix . 'city', true );
                $state = get_post_meta( $post_id, $this->meta_prefix . 'state', true );
                $postal_code = get_post_meta( $post_id, $this->meta_prefix . 'postal_code', true );
                $country = get_post_meta( $post_id, $this->meta_prefix . 'country', true );

                $address_parts = array();
                if ( $city ) $address_parts[] = $city;
                if ( $state ) $address_parts[] = $state;
                if ( $postal_code ) $address_parts[] = $postal_code;
                if ( $country ) $address_parts[] = $country;

                echo esc_html( implode( ', ', $address_parts ) );
                break;

            case 'phone':
                $phone = get_post_meta( $post_id, $this->meta_prefix . 'phone', true );
                echo esc_html( $phone );
                break;

            case 'primary':
                $primary = get_post_meta( $post_id, $this->meta_prefix . 'primary', true );
                if ( $primary ) {
                    echo '<span class="dashicons dashicons-yes" style="color: green;"></span>';
                } else {
                    echo '<span class="dashicons dashicons-no" style="color: red;"></span>';
                }
                break;

            case 'schema_type':
                $schema_type = get_post_meta( $post_id, $this->meta_prefix . 'seo_schema_type', true );
                if ( empty( $schema_type ) ) {
                    $schema_type = 'LocalBusiness';
                }

                $schema_labels = array(
                    'LocalBusiness' => __( 'Entreprise locale', 'boss-seo' ),
                    'Restaurant' => __( 'Restaurant', 'boss-seo' ),
                    'Store' => __( 'Magasin', 'boss-seo' ),
                    'MedicalBusiness' => __( 'Établissement médical', 'boss-seo' ),
                    'FinancialService' => __( 'Service financier', 'boss-seo' ),
                    'LodgingBusiness' => __( 'Hébergement', 'boss-seo' ),
                );

                $schema_label = isset( $schema_labels[$schema_type] ) ? $schema_labels[$schema_type] : $schema_type;
                echo esc_html( $schema_label );
                break;
        }
    }

    /**
     * Ajoute des filtres personnalisés à la liste des emplacements.
     *
     * @since    1.2.0
     * @param    string    $post_type    Le type de post actuel.
     */
    public function add_custom_filters( $post_type ) {
        if ( $post_type !== $this->cpt_name ) {
            return;
        }

        // Filtre par statut
        $status = isset( $_GET['boss_location_status'] ) ? $_GET['boss_location_status'] : '';
        ?>
        <select name="boss_location_status">
            <option value=""><?php _e( 'Tous les statuts', 'boss-seo' ); ?></option>
            <option value="active" <?php selected( $status, 'active' ); ?>><?php _e( 'Actif', 'boss-seo' ); ?></option>
            <option value="inactive" <?php selected( $status, 'inactive' ); ?>><?php _e( 'Inactif', 'boss-seo' ); ?></option>
            <option value="coming_soon" <?php selected( $status, 'coming_soon' ); ?>><?php _e( 'À venir', 'boss-seo' ); ?></option>
        </select>
        <?php

        // Filtre par type de schéma
        $schema_type = isset( $_GET['boss_location_schema_type'] ) ? $_GET['boss_location_schema_type'] : '';
        ?>
        <select name="boss_location_schema_type">
            <option value=""><?php _e( 'Tous les types de schéma', 'boss-seo' ); ?></option>
            <option value="LocalBusiness" <?php selected( $schema_type, 'LocalBusiness' ); ?>><?php _e( 'Entreprise locale', 'boss-seo' ); ?></option>
            <option value="Restaurant" <?php selected( $schema_type, 'Restaurant' ); ?>><?php _e( 'Restaurant', 'boss-seo' ); ?></option>
            <option value="Store" <?php selected( $schema_type, 'Store' ); ?>><?php _e( 'Magasin', 'boss-seo' ); ?></option>
            <option value="MedicalBusiness" <?php selected( $schema_type, 'MedicalBusiness' ); ?>><?php _e( 'Établissement médical', 'boss-seo' ); ?></option>
            <option value="FinancialService" <?php selected( $schema_type, 'FinancialService' ); ?>><?php _e( 'Service financier', 'boss-seo' ); ?></option>
            <option value="LodgingBusiness" <?php selected( $schema_type, 'LodgingBusiness' ); ?>><?php _e( 'Hébergement', 'boss-seo' ); ?></option>
        </select>
        <?php
    }

    /**
     * Filtre les emplacements par statut.
     *
     * @since    1.2.0
     * @param    WP_Query    $query    L'objet query.
     */
    public function filter_locations_by_status( $query ) {
        global $pagenow;

        // Vérifier si nous sommes sur la page d'administration des emplacements
        if ( ! is_admin() || $pagenow !== 'edit.php' || ! isset( $query->query_vars['post_type'] ) || $query->query_vars['post_type'] !== $this->cpt_name ) {
            return;
        }

        // Filtre par statut
        if ( isset( $_GET['boss_location_status'] ) && ! empty( $_GET['boss_location_status'] ) ) {
            $meta_query = array(
                array(
                    'key'     => $this->meta_prefix . 'status',
                    'value'   => sanitize_text_field( $_GET['boss_location_status'] ),
                    'compare' => '=',
                ),
            );

            // Ajouter la requête meta à la requête existante
            if ( isset( $query->query_vars['meta_query'] ) ) {
                $query->query_vars['meta_query'][] = $meta_query;
            } else {
                $query->query_vars['meta_query'] = $meta_query;
            }
        }

        // Filtre par type de schéma
        if ( isset( $_GET['boss_location_schema_type'] ) && ! empty( $_GET['boss_location_schema_type'] ) ) {
            $meta_query = array(
                array(
                    'key'     => $this->meta_prefix . 'seo_schema_type',
                    'value'   => sanitize_text_field( $_GET['boss_location_schema_type'] ),
                    'compare' => '=',
                ),
            );

            // Ajouter la requête meta à la requête existante
            if ( isset( $query->query_vars['meta_query'] ) ) {
                $query->query_vars['meta_query'][] = $meta_query;
            } else {
                $query->query_vars['meta_query'] = $meta_query;
            }
        }
    }

    /**
     * Récupère les emplacements via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_locations( $request ) {
        // Paramètres de pagination
        $page = isset( $request['page'] ) ? absint( $request['page'] ) : 1;
        $per_page = isset( $request['per_page'] ) ? absint( $request['per_page'] ) : 10;

        // Paramètres de recherche et de filtrage
        $search = isset( $request['search'] ) ? sanitize_text_field( $request['search'] ) : '';
        $status = isset( $request['status'] ) ? sanitize_text_field( $request['status'] ) : '';
        $schema_type = isset( $request['schema_type'] ) ? sanitize_text_field( $request['schema_type'] ) : '';
        $primary = isset( $request['primary'] ) ? (bool) $request['primary'] : false;

        // Construire les arguments de la requête
        $args = array(
            'post_type'      => $this->cpt_name,
            'post_status'    => 'publish',
            'posts_per_page' => $per_page,
            'paged'          => $page,
            'orderby'        => 'title',
            'order'          => 'ASC',
        );

        // Ajouter la recherche
        if ( ! empty( $search ) ) {
            $args['s'] = $search;
        }

        // Ajouter les filtres
        $meta_query = array();

        if ( ! empty( $status ) ) {
            $meta_query[] = array(
                'key'     => $this->meta_prefix . 'status',
                'value'   => $status,
                'compare' => '=',
            );
        }

        if ( ! empty( $schema_type ) ) {
            $meta_query[] = array(
                'key'     => $this->meta_prefix . 'seo_schema_type',
                'value'   => $schema_type,
                'compare' => '=',
            );
        }

        if ( $primary ) {
            $meta_query[] = array(
                'key'     => $this->meta_prefix . 'primary',
                'value'   => '1',
                'compare' => '=',
            );
        }

        if ( ! empty( $meta_query ) ) {
            $args['meta_query'] = $meta_query;
        }

        // Exécuter la requête
        $query = new WP_Query( $args );

        // Préparer les données
        $locations = array();
        $total_locations = $query->found_posts;
        $max_pages = ceil( $total_locations / $per_page );

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();
                $location = $this->prepare_location_for_response( get_the_ID() );
                $locations[] = $location;
            }
            wp_reset_postdata();
        }

        // Préparer la réponse
        $response = array(
            'locations'       => $locations,
            'total'           => $total_locations,
            'pages'           => $max_pages,
            'current_page'    => $page,
            'locations_per_page' => $per_page,
        );

        return rest_ensure_response( $response );
    }

    /**
     * Récupère un emplacement spécifique via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_location( $request ) {
        $location_id = $request['id'];

        // Vérifier si l'emplacement existe
        $location = get_post( $location_id );
        if ( ! $location || $location->post_type !== $this->cpt_name ) {
            return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Préparer les données
        $location_data = $this->prepare_location_for_response( $location_id );

        return rest_ensure_response( $location_data );
    }

    /**
     * Ajoute un nouvel emplacement via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function add_location( $request ) {
        // Récupérer les données de la requête
        $params = $request->get_params();

        // Vérifier les champs obligatoires
        if ( ! isset( $params['title'] ) || empty( $params['title'] ) ) {
            return new WP_Error( 'missing_title', __( 'Le titre est obligatoire.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Créer l'emplacement
        $location_data = array(
            'post_title'   => sanitize_text_field( $params['title'] ),
            'post_content' => isset( $params['content'] ) ? wp_kses_post( $params['content'] ) : '',
            'post_excerpt' => isset( $params['excerpt'] ) ? sanitize_textarea_field( $params['excerpt'] ) : '',
            'post_status'  => 'publish',
            'post_type'    => $this->cpt_name,
        );

        $location_id = wp_insert_post( $location_data );

        if ( is_wp_error( $location_id ) ) {
            return $location_id;
        }

        // Enregistrer les métadonnées
        $meta_fields = array(
            // Détails
            'status'      => isset( $params['status'] ) ? sanitize_text_field( $params['status'] ) : 'active',
            'primary'     => isset( $params['primary'] ) ? '1' : '',
            'short_name'  => isset( $params['short_name'] ) ? sanitize_text_field( $params['short_name'] ) : '',
            'description' => isset( $params['description'] ) ? sanitize_textarea_field( $params['description'] ) : '',

            // Adresse
            'street'      => isset( $params['street'] ) ? sanitize_text_field( $params['street'] ) : '',
            'street2'     => isset( $params['street2'] ) ? sanitize_text_field( $params['street2'] ) : '',
            'city'        => isset( $params['city'] ) ? sanitize_text_field( $params['city'] ) : '',
            'state'       => isset( $params['state'] ) ? sanitize_text_field( $params['state'] ) : '',
            'postal_code' => isset( $params['postal_code'] ) ? sanitize_text_field( $params['postal_code'] ) : '',
            'country'     => isset( $params['country'] ) ? sanitize_text_field( $params['country'] ) : '',
            'latitude'    => isset( $params['latitude'] ) ? sanitize_text_field( $params['latitude'] ) : '',
            'longitude'   => isset( $params['longitude'] ) ? sanitize_text_field( $params['longitude'] ) : '',

            // Coordonnées
            'phone'       => isset( $params['phone'] ) ? sanitize_text_field( $params['phone'] ) : '',
            'fax'         => isset( $params['fax'] ) ? sanitize_text_field( $params['fax'] ) : '',
            'email'       => isset( $params['email'] ) ? sanitize_email( $params['email'] ) : '',
            'website'     => isset( $params['website'] ) ? esc_url_raw( $params['website'] ) : '',

            // SEO
            'seo_title'       => isset( $params['seo_title'] ) ? sanitize_text_field( $params['seo_title'] ) : '',
            'seo_description' => isset( $params['seo_description'] ) ? sanitize_textarea_field( $params['seo_description'] ) : '',
            'seo_keywords'    => isset( $params['seo_keywords'] ) ? sanitize_text_field( $params['seo_keywords'] ) : '',
            'seo_schema_type' => isset( $params['seo_schema_type'] ) ? sanitize_text_field( $params['seo_schema_type'] ) : 'LocalBusiness',
            'seo_hide_in_sitemap' => isset( $params['seo_hide_in_sitemap'] ) ? '1' : '',
        );

        foreach ( $meta_fields as $field => $value ) {
            if ( ! empty( $value ) ) {
                update_post_meta( $location_id, $this->meta_prefix . $field, $value );
            }
        }

        // Enregistrer les horaires d'ouverture
        if ( isset( $params['hours'] ) && is_array( $params['hours'] ) ) {
            update_post_meta( $location_id, $this->meta_prefix . 'hours', $params['hours'] );
        }

        // Enregistrer les catégories
        if ( isset( $params['categories'] ) && is_array( $params['categories'] ) ) {
            $categories = array_map( 'absint', $params['categories'] );
            wp_set_object_terms( $location_id, $categories, 'boss_location_category' );
        }

        // Enregistrer l'image mise en avant
        if ( isset( $params['featured_image'] ) && ! empty( $params['featured_image'] ) ) {
            $this->set_featured_image( $location_id, $params['featured_image'] );
        }

        // Préparer la réponse
        $location_data = $this->prepare_location_for_response( $location_id );

        return rest_ensure_response( $location_data );
    }

    /**
     * Met à jour un emplacement existant via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_location( $request ) {
        $location_id = $request['id'];

        // Vérifier si l'emplacement existe
        $location = get_post( $location_id );
        if ( ! $location || $location->post_type !== $this->cpt_name ) {
            return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Récupérer les données de la requête
        $params = $request->get_params();

        // Mettre à jour l'emplacement
        $location_data = array(
            'ID' => $location_id,
        );

        if ( isset( $params['title'] ) ) {
            $location_data['post_title'] = sanitize_text_field( $params['title'] );
        }

        if ( isset( $params['content'] ) ) {
            $location_data['post_content'] = wp_kses_post( $params['content'] );
        }

        if ( isset( $params['excerpt'] ) ) {
            $location_data['post_excerpt'] = sanitize_textarea_field( $params['excerpt'] );
        }

        $updated_location_id = wp_update_post( $location_data );

        if ( is_wp_error( $updated_location_id ) ) {
            return $updated_location_id;
        }

        // Mettre à jour les métadonnées
        $meta_fields = array(
            // Détails
            'status'      => isset( $params['status'] ) ? sanitize_text_field( $params['status'] ) : null,
            'primary'     => isset( $params['primary'] ) ? ( $params['primary'] ? '1' : '' ) : null,
            'short_name'  => isset( $params['short_name'] ) ? sanitize_text_field( $params['short_name'] ) : null,
            'description' => isset( $params['description'] ) ? sanitize_textarea_field( $params['description'] ) : null,

            // Adresse
            'street'      => isset( $params['street'] ) ? sanitize_text_field( $params['street'] ) : null,
            'street2'     => isset( $params['street2'] ) ? sanitize_text_field( $params['street2'] ) : null,
            'city'        => isset( $params['city'] ) ? sanitize_text_field( $params['city'] ) : null,
            'state'       => isset( $params['state'] ) ? sanitize_text_field( $params['state'] ) : null,
            'postal_code' => isset( $params['postal_code'] ) ? sanitize_text_field( $params['postal_code'] ) : null,
            'country'     => isset( $params['country'] ) ? sanitize_text_field( $params['country'] ) : null,
            'latitude'    => isset( $params['latitude'] ) ? sanitize_text_field( $params['latitude'] ) : null,
            'longitude'   => isset( $params['longitude'] ) ? sanitize_text_field( $params['longitude'] ) : null,

            // Coordonnées
            'phone'       => isset( $params['phone'] ) ? sanitize_text_field( $params['phone'] ) : null,
            'fax'         => isset( $params['fax'] ) ? sanitize_text_field( $params['fax'] ) : null,
            'email'       => isset( $params['email'] ) ? sanitize_email( $params['email'] ) : null,
            'website'     => isset( $params['website'] ) ? esc_url_raw( $params['website'] ) : null,

            // SEO
            'seo_title'       => isset( $params['seo_title'] ) ? sanitize_text_field( $params['seo_title'] ) : null,
            'seo_description' => isset( $params['seo_description'] ) ? sanitize_textarea_field( $params['seo_description'] ) : null,
            'seo_keywords'    => isset( $params['seo_keywords'] ) ? sanitize_text_field( $params['seo_keywords'] ) : null,
            'seo_schema_type' => isset( $params['seo_schema_type'] ) ? sanitize_text_field( $params['seo_schema_type'] ) : null,
            'seo_hide_in_sitemap' => isset( $params['seo_hide_in_sitemap'] ) ? ( $params['seo_hide_in_sitemap'] ? '1' : '' ) : null,
        );

        foreach ( $meta_fields as $field => $value ) {
            if ( $value !== null ) {
                $meta_key = $this->meta_prefix . $field;
                $old_value = get_post_meta( $location_id, $meta_key, true );

                if ( $value && $value !== $old_value ) {
                    update_post_meta( $location_id, $meta_key, $value );
                } elseif ( '' === $value && $old_value ) {
                    delete_post_meta( $location_id, $meta_key );
                }
            }
        }

        // Mettre à jour les horaires d'ouverture
        if ( isset( $params['hours'] ) && is_array( $params['hours'] ) ) {
            update_post_meta( $location_id, $this->meta_prefix . 'hours', $params['hours'] );
        }

        // Mettre à jour les catégories
        if ( isset( $params['categories'] ) && is_array( $params['categories'] ) ) {
            $categories = array_map( 'absint', $params['categories'] );
            wp_set_object_terms( $location_id, $categories, 'boss_location_category' );
        }

        // Mettre à jour l'image mise en avant
        if ( isset( $params['featured_image'] ) && ! empty( $params['featured_image'] ) ) {
            $this->set_featured_image( $location_id, $params['featured_image'] );
        }

        // Préparer la réponse
        $location_data = $this->prepare_location_for_response( $location_id );

        return rest_ensure_response( $location_data );
    }

    /**
     * Supprime un emplacement via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_location( $request ) {
        $location_id = $request['id'];

        // Vérifier si l'emplacement existe
        $location = get_post( $location_id );
        if ( ! $location || $location->post_type !== $this->cpt_name ) {
            return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Supprimer l'emplacement
        $result = wp_delete_post( $location_id, true );

        if ( ! $result ) {
            return new WP_Error( 'location_delete_failed', __( 'La suppression de l\'emplacement a échoué.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array(
            'deleted'  => true,
            'previous' => $this->prepare_location_for_response( $location ),
        ) );
    }

    /**
     * Importe des emplacements via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function import_locations( $request ) {
        // Récupérer les données de la requête
        $params = $request->get_params();

        if ( ! isset( $params['locations'] ) || ! is_array( $params['locations'] ) ) {
            return new WP_Error( 'missing_locations', __( 'Aucun emplacement à importer.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $locations = $params['locations'];
        $imported = 0;
        $failed = 0;
        $imported_locations = array();

        foreach ( $locations as $location ) {
            // Vérifier les champs obligatoires
            if ( ! isset( $location['title'] ) || empty( $location['title'] ) ) {
                $failed++;
                continue;
            }

            // Créer l'emplacement
            $location_data = array(
                'post_title'   => sanitize_text_field( $location['title'] ),
                'post_content' => isset( $location['content'] ) ? wp_kses_post( $location['content'] ) : '',
                'post_excerpt' => isset( $location['excerpt'] ) ? sanitize_textarea_field( $location['excerpt'] ) : '',
                'post_status'  => 'publish',
                'post_type'    => $this->cpt_name,
            );

            $location_id = wp_insert_post( $location_data );

            if ( is_wp_error( $location_id ) ) {
                $failed++;
                continue;
            }

            // Enregistrer les métadonnées
            $meta_fields = array(
                // Détails
                'status'      => isset( $location['status'] ) ? sanitize_text_field( $location['status'] ) : 'active',
                'primary'     => isset( $location['primary'] ) ? '1' : '',
                'short_name'  => isset( $location['short_name'] ) ? sanitize_text_field( $location['short_name'] ) : '',
                'description' => isset( $location['description'] ) ? sanitize_textarea_field( $location['description'] ) : '',

                // Adresse
                'street'      => isset( $location['street'] ) ? sanitize_text_field( $location['street'] ) : '',
                'street2'     => isset( $location['street2'] ) ? sanitize_text_field( $location['street2'] ) : '',
                'city'        => isset( $location['city'] ) ? sanitize_text_field( $location['city'] ) : '',
                'state'       => isset( $location['state'] ) ? sanitize_text_field( $location['state'] ) : '',
                'postal_code' => isset( $location['postal_code'] ) ? sanitize_text_field( $location['postal_code'] ) : '',
                'country'     => isset( $location['country'] ) ? sanitize_text_field( $location['country'] ) : '',
                'latitude'    => isset( $location['latitude'] ) ? sanitize_text_field( $location['latitude'] ) : '',
                'longitude'   => isset( $location['longitude'] ) ? sanitize_text_field( $location['longitude'] ) : '',

                // Coordonnées
                'phone'       => isset( $location['phone'] ) ? sanitize_text_field( $location['phone'] ) : '',
                'fax'         => isset( $location['fax'] ) ? sanitize_text_field( $location['fax'] ) : '',
                'email'       => isset( $location['email'] ) ? sanitize_email( $location['email'] ) : '',
                'website'     => isset( $location['website'] ) ? esc_url_raw( $location['website'] ) : '',

                // SEO
                'seo_title'       => isset( $location['seo_title'] ) ? sanitize_text_field( $location['seo_title'] ) : '',
                'seo_description' => isset( $location['seo_description'] ) ? sanitize_textarea_field( $location['seo_description'] ) : '',
                'seo_keywords'    => isset( $location['seo_keywords'] ) ? sanitize_text_field( $location['seo_keywords'] ) : '',
                'seo_schema_type' => isset( $location['seo_schema_type'] ) ? sanitize_text_field( $location['seo_schema_type'] ) : 'LocalBusiness',
                'seo_hide_in_sitemap' => isset( $location['seo_hide_in_sitemap'] ) ? '1' : '',
            );

            foreach ( $meta_fields as $field => $value ) {
                if ( ! empty( $value ) ) {
                    update_post_meta( $location_id, $this->meta_prefix . $field, $value );
                }
            }

            // Enregistrer les horaires d'ouverture
            if ( isset( $location['hours'] ) && is_array( $location['hours'] ) ) {
                update_post_meta( $location_id, $this->meta_prefix . 'hours', $location['hours'] );
            }

            // Enregistrer les catégories
            if ( isset( $location['categories'] ) && is_array( $location['categories'] ) ) {
                $categories = array_map( 'absint', $location['categories'] );
                wp_set_object_terms( $location_id, $categories, 'boss_location_category' );
            }

            $imported++;
            $imported_locations[] = $this->prepare_location_for_response( $location_id );
        }

        return rest_ensure_response( array(
            'imported' => $imported,
            'failed'   => $failed,
            'locations' => $imported_locations,
        ) );
    }

    /**
     * Exporte les emplacements via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function export_locations( $request ) {
        // Paramètres de filtrage
        $status = isset( $request['status'] ) ? sanitize_text_field( $request['status'] ) : '';
        $schema_type = isset( $request['schema_type'] ) ? sanitize_text_field( $request['schema_type'] ) : '';
        $primary = isset( $request['primary'] ) ? (bool) $request['primary'] : false;

        // Construire les arguments de la requête
        $args = array(
            'post_type'      => $this->cpt_name,
            'post_status'    => 'publish',
            'posts_per_page' => -1,
            'orderby'        => 'title',
            'order'          => 'ASC',
        );

        // Ajouter les filtres
        $meta_query = array();

        if ( ! empty( $status ) ) {
            $meta_query[] = array(
                'key'     => $this->meta_prefix . 'status',
                'value'   => $status,
                'compare' => '=',
            );
        }

        if ( ! empty( $schema_type ) ) {
            $meta_query[] = array(
                'key'     => $this->meta_prefix . 'seo_schema_type',
                'value'   => $schema_type,
                'compare' => '=',
            );
        }

        if ( $primary ) {
            $meta_query[] = array(
                'key'     => $this->meta_prefix . 'primary',
                'value'   => '1',
                'compare' => '=',
            );
        }

        if ( ! empty( $meta_query ) ) {
            $args['meta_query'] = $meta_query;
        }

        // Exécuter la requête
        $query = new WP_Query( $args );

        // Préparer les données
        $locations = array();

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();
                $location = $this->prepare_location_for_response( get_the_ID() );
                $locations[] = $location;
            }
            wp_reset_postdata();
        }

        return rest_ensure_response( array(
            'locations' => $locations,
            'total'     => count( $locations ),
        ) );
    }

    /**
     * Récupère les statistiques des emplacements via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_location_stats( $request ) {
        // Compter le nombre total d'emplacements
        $total_locations = wp_count_posts( $this->cpt_name )->publish;

        // Compter le nombre d'emplacements par statut
        $active_locations = $this->count_locations_by_meta( 'status', 'active' );
        $inactive_locations = $this->count_locations_by_meta( 'status', 'inactive' );
        $coming_soon_locations = $this->count_locations_by_meta( 'status', 'coming_soon' );

        // Compter le nombre d'emplacements principaux
        $primary_locations = $this->count_locations_by_meta( 'primary', '1' );

        // Compter le nombre d'emplacements par type de schéma
        $schema_types = array(
            'LocalBusiness',
            'Restaurant',
            'Store',
            'MedicalBusiness',
            'FinancialService',
            'LodgingBusiness',
        );

        $schema_counts = array();

        foreach ( $schema_types as $schema_type ) {
            $schema_counts[$schema_type] = $this->count_locations_by_meta( 'seo_schema_type', $schema_type );
        }

        // Préparer la réponse
        $stats = array(
            'total'       => $total_locations,
            'status'      => array(
                'active'      => $active_locations,
                'inactive'    => $inactive_locations,
                'coming_soon' => $coming_soon_locations,
            ),
            'primary'     => $primary_locations,
            'schema_type' => $schema_counts,
        );

        return rest_ensure_response( $stats );
    }

    /**
     * Compte le nombre d'emplacements par valeur de métadonnée.
     *
     * @since    1.2.0
     * @param    string    $meta_key     La clé de la métadonnée.
     * @param    string    $meta_value   La valeur de la métadonnée.
     * @return   int                     Le nombre d'emplacements.
     */
    private function count_locations_by_meta( $meta_key, $meta_value ) {
        $args = array(
            'post_type'      => $this->cpt_name,
            'post_status'    => 'publish',
            'posts_per_page' => -1,
            'meta_query'     => array(
                array(
                    'key'     => $this->meta_prefix . $meta_key,
                    'value'   => $meta_value,
                    'compare' => '=',
                ),
            ),
        );

        $query = new WP_Query( $args );

        return $query->found_posts;
    }

    /**
     * Prépare les données d'un emplacement pour la réponse API.
     *
     * @since    1.2.0
     * @param    int|WP_Post    $location    L'ID de l'emplacement ou l'objet post.
     * @return   array                       Les données de l'emplacement.
     */
    private function prepare_location_for_response( $location ) {
        if ( is_numeric( $location ) ) {
            $location = get_post( $location );
        }

        if ( ! $location || $location->post_type !== $this->cpt_name ) {
            return array();
        }

        // Récupérer les métadonnées
        $meta_fields = array(
            // Détails
            'status',
            'primary',
            'short_name',
            'description',

            // Adresse
            'street',
            'street2',
            'city',
            'state',
            'postal_code',
            'country',
            'latitude',
            'longitude',

            // Coordonnées
            'phone',
            'fax',
            'email',
            'website',

            // SEO
            'seo_title',
            'seo_description',
            'seo_keywords',
            'seo_schema_type',
            'seo_hide_in_sitemap',
        );

        $meta_data = array();

        foreach ( $meta_fields as $field ) {
            $meta_key = $this->meta_prefix . $field;
            $meta_data[$field] = get_post_meta( $location->ID, $meta_key, true );
        }

        // Récupérer les horaires d'ouverture
        $hours = get_post_meta( $location->ID, $this->meta_prefix . 'hours', true );

        // Récupérer les catégories
        $categories = array();
        $terms = wp_get_object_terms( $location->ID, 'boss_location_category' );

        if ( ! is_wp_error( $terms ) && ! empty( $terms ) ) {
            foreach ( $terms as $term ) {
                $categories[] = array(
                    'id'   => $term->term_id,
                    'name' => $term->name,
                    'slug' => $term->slug,
                );
            }
        }

        // Récupérer l'image mise en avant
        $featured_image = null;

        if ( has_post_thumbnail( $location->ID ) ) {
            $thumbnail_id = get_post_thumbnail_id( $location->ID );
            $thumbnail = wp_get_attachment_image_src( $thumbnail_id, 'full' );

            if ( $thumbnail ) {
                $featured_image = array(
                    'id'    => $thumbnail_id,
                    'url'   => $thumbnail[0],
                    'width' => $thumbnail[1],
                    'height' => $thumbnail[2],
                );
            }
        }

        // Construire la réponse
        $location_data = array(
            'id'           => $location->ID,
            'title'        => $location->post_title,
            'content'      => $location->post_content,
            'excerpt'      => $location->post_excerpt,
            'date'         => $location->post_date,
            'modified'     => $location->post_modified,
            'status'       => $meta_data['status'],
            'primary'      => ! empty( $meta_data['primary'] ),
            'short_name'   => $meta_data['short_name'],
            'description'  => $meta_data['description'],
            'address'      => array(
                'street'      => $meta_data['street'],
                'street2'     => $meta_data['street2'],
                'city'        => $meta_data['city'],
                'state'       => $meta_data['state'],
                'postal_code' => $meta_data['postal_code'],
                'country'     => $meta_data['country'],
                'latitude'    => $meta_data['latitude'],
                'longitude'   => $meta_data['longitude'],
                'formatted'   => $this->get_formatted_address( $meta_data ),
            ),
            'contact'      => array(
                'phone'    => $meta_data['phone'],
                'fax'      => $meta_data['fax'],
                'email'    => $meta_data['email'],
                'website'  => $meta_data['website'],
            ),
            'hours'        => $hours,
            'categories'   => $categories,
            'featured_image' => $featured_image,
            'seo'          => array(
                'title'       => $meta_data['seo_title'],
                'description' => $meta_data['seo_description'],
                'keywords'    => $meta_data['seo_keywords'],
                'schema_type' => $meta_data['seo_schema_type'],
                'hide_in_sitemap' => ! empty( $meta_data['seo_hide_in_sitemap'] ),
            ),
            'permalink'    => get_permalink( $location->ID ),
        );

        return $location_data;
    }

    /**
     * Définit l'image mise en avant d'un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @param    int|array $image          L'ID de l'image ou un tableau avec les données de l'image.
     * @return   bool                      True si l'image a été définie, false sinon.
     */
    private function set_featured_image( $location_id, $image ) {
        if ( is_array( $image ) && isset( $image['id'] ) ) {
            $image_id = $image['id'];
        } elseif ( is_numeric( $image ) ) {
            $image_id = $image;
        } else {
            return false;
        }

        return set_post_thumbnail( $location_id, $image_id );
    }

    /**
     * Formate l'adresse d'un emplacement.
     *
     * @since    1.2.0
     * @param    array     $meta_data    Les métadonnées de l'emplacement.
     * @return   string                  L'adresse formatée.
     */
    private function get_formatted_address( $meta_data ) {
        $address_parts = array();

        if ( ! empty( $meta_data['street'] ) ) {
            $address_parts[] = $meta_data['street'];
        }

        if ( ! empty( $meta_data['street2'] ) ) {
            $address_parts[] = $meta_data['street2'];
        }

        $city_parts = array();

        if ( ! empty( $meta_data['postal_code'] ) ) {
            $city_parts[] = $meta_data['postal_code'];
        }

        if ( ! empty( $meta_data['city'] ) ) {
            $city_parts[] = $meta_data['city'];
        }

        if ( ! empty( $city_parts ) ) {
            $address_parts[] = implode( ' ', $city_parts );
        }

        if ( ! empty( $meta_data['state'] ) ) {
            $address_parts[] = $meta_data['state'];
        }

        if ( ! empty( $meta_data['country'] ) ) {
            $address_parts[] = $meta_data['country'];
        }

        return implode( ', ', $address_parts );
    }

    /**
     * Gère les requêtes AJAX pour importer des emplacements.
     *
     * @since    1.2.0
     */
    public function ajax_import_locations() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier le fichier
        if ( ! isset( $_FILES['file'] ) || empty( $_FILES['file']['tmp_name'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Aucun fichier n\'a été téléversé.', 'boss-seo' ) ) );
        }

        // Récupérer le format
        $format = isset( $_POST['format'] ) ? sanitize_text_field( $_POST['format'] ) : 'csv';

        // Lire le fichier
        $file_content = file_get_contents( $_FILES['file']['tmp_name'] );

        if ( ! $file_content ) {
            wp_send_json_error( array( 'message' => __( 'Impossible de lire le fichier.', 'boss-seo' ) ) );
        }

        // Traiter le fichier selon le format
        $locations = array();

        if ( $format === 'csv' ) {
            $locations = $this->parse_csv_file( $file_content );
        } elseif ( $format === 'json' ) {
            $locations = $this->parse_json_file( $file_content );
        } else {
            wp_send_json_error( array( 'message' => __( 'Format de fichier non pris en charge.', 'boss-seo' ) ) );
        }

        if ( empty( $locations ) ) {
            wp_send_json_error( array( 'message' => __( 'Aucun emplacement trouvé dans le fichier.', 'boss-seo' ) ) );
        }

        // Importer les emplacements
        $imported = 0;
        $failed = 0;

        foreach ( $locations as $location ) {
            // Vérifier les champs obligatoires
            if ( ! isset( $location['title'] ) || empty( $location['title'] ) ) {
                $failed++;
                continue;
            }

            // Créer l'emplacement
            $location_data = array(
                'post_title'   => sanitize_text_field( $location['title'] ),
                'post_content' => isset( $location['content'] ) ? wp_kses_post( $location['content'] ) : '',
                'post_excerpt' => isset( $location['excerpt'] ) ? sanitize_textarea_field( $location['excerpt'] ) : '',
                'post_status'  => 'publish',
                'post_type'    => $this->cpt_name,
            );

            $location_id = wp_insert_post( $location_data );

            if ( is_wp_error( $location_id ) ) {
                $failed++;
                continue;
            }

            // Enregistrer les métadonnées
            $meta_fields = array(
                // Détails
                'status'      => isset( $location['status'] ) ? sanitize_text_field( $location['status'] ) : 'active',
                'primary'     => isset( $location['primary'] ) && $location['primary'] ? '1' : '',
                'short_name'  => isset( $location['short_name'] ) ? sanitize_text_field( $location['short_name'] ) : '',
                'description' => isset( $location['description'] ) ? sanitize_textarea_field( $location['description'] ) : '',

                // Adresse
                'street'      => isset( $location['street'] ) ? sanitize_text_field( $location['street'] ) : '',
                'street2'     => isset( $location['street2'] ) ? sanitize_text_field( $location['street2'] ) : '',
                'city'        => isset( $location['city'] ) ? sanitize_text_field( $location['city'] ) : '',
                'state'       => isset( $location['state'] ) ? sanitize_text_field( $location['state'] ) : '',
                'postal_code' => isset( $location['postal_code'] ) ? sanitize_text_field( $location['postal_code'] ) : '',
                'country'     => isset( $location['country'] ) ? sanitize_text_field( $location['country'] ) : '',
                'latitude'    => isset( $location['latitude'] ) ? sanitize_text_field( $location['latitude'] ) : '',
                'longitude'   => isset( $location['longitude'] ) ? sanitize_text_field( $location['longitude'] ) : '',

                // Coordonnées
                'phone'       => isset( $location['phone'] ) ? sanitize_text_field( $location['phone'] ) : '',
                'fax'         => isset( $location['fax'] ) ? sanitize_text_field( $location['fax'] ) : '',
                'email'       => isset( $location['email'] ) ? sanitize_email( $location['email'] ) : '',
                'website'     => isset( $location['website'] ) ? esc_url_raw( $location['website'] ) : '',

                // SEO
                'seo_title'       => isset( $location['seo_title'] ) ? sanitize_text_field( $location['seo_title'] ) : '',
                'seo_description' => isset( $location['seo_description'] ) ? sanitize_textarea_field( $location['seo_description'] ) : '',
                'seo_keywords'    => isset( $location['seo_keywords'] ) ? sanitize_text_field( $location['seo_keywords'] ) : '',
                'seo_schema_type' => isset( $location['seo_schema_type'] ) ? sanitize_text_field( $location['seo_schema_type'] ) : 'LocalBusiness',
                'seo_hide_in_sitemap' => isset( $location['seo_hide_in_sitemap'] ) && $location['seo_hide_in_sitemap'] ? '1' : '',
            );

            foreach ( $meta_fields as $field => $value ) {
                if ( ! empty( $value ) ) {
                    update_post_meta( $location_id, $this->meta_prefix . $field, $value );
                }
            }

            // Enregistrer les horaires d'ouverture
            if ( isset( $location['hours'] ) && is_array( $location['hours'] ) ) {
                update_post_meta( $location_id, $this->meta_prefix . 'hours', $location['hours'] );
            }

            // Enregistrer les catégories
            if ( isset( $location['categories'] ) && is_array( $location['categories'] ) ) {
                $categories = array();

                foreach ( $location['categories'] as $category ) {
                    if ( is_numeric( $category ) ) {
                        $categories[] = intval( $category );
                    } elseif ( is_string( $category ) ) {
                        $term = get_term_by( 'name', $category, 'boss_location_category' );

                        if ( $term ) {
                            $categories[] = $term->term_id;
                        } else {
                            $new_term = wp_insert_term( $category, 'boss_location_category' );

                            if ( ! is_wp_error( $new_term ) ) {
                                $categories[] = $new_term['term_id'];
                            }
                        }
                    }
                }

                if ( ! empty( $categories ) ) {
                    wp_set_object_terms( $location_id, $categories, 'boss_location_category' );
                }
            }

            $imported++;
        }

        wp_send_json_success( array(
            'message'   => sprintf( __( '%d emplacements importés avec succès. %d emplacements n\'ont pas pu être importés.', 'boss-seo' ), $imported, $failed ),
            'imported'  => $imported,
            'failed'    => $failed,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour exporter des emplacements.
     *
     * @since    1.2.0
     */
    public function ajax_export_locations() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer le format
        $format = isset( $_POST['format'] ) ? sanitize_text_field( $_POST['format'] ) : 'csv';

        // Récupérer les filtres
        $status = isset( $_POST['status'] ) ? sanitize_text_field( $_POST['status'] ) : '';
        $schema_type = isset( $_POST['schema_type'] ) ? sanitize_text_field( $_POST['schema_type'] ) : '';
        $primary = isset( $_POST['primary'] ) ? (bool) $_POST['primary'] : false;

        // Construire les arguments de la requête
        $args = array(
            'post_type'      => $this->cpt_name,
            'post_status'    => 'publish',
            'posts_per_page' => -1,
            'orderby'        => 'title',
            'order'          => 'ASC',
        );

        // Ajouter les filtres
        $meta_query = array();

        if ( ! empty( $status ) ) {
            $meta_query[] = array(
                'key'     => $this->meta_prefix . 'status',
                'value'   => $status,
                'compare' => '=',
            );
        }

        if ( ! empty( $schema_type ) ) {
            $meta_query[] = array(
                'key'     => $this->meta_prefix . 'seo_schema_type',
                'value'   => $schema_type,
                'compare' => '=',
            );
        }

        if ( $primary ) {
            $meta_query[] = array(
                'key'     => $this->meta_prefix . 'primary',
                'value'   => '1',
                'compare' => '=',
            );
        }

        if ( ! empty( $meta_query ) ) {
            $args['meta_query'] = $meta_query;
        }

        // Exécuter la requête
        $query = new WP_Query( $args );

        // Préparer les données
        $locations = array();

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();
                $location = $this->prepare_location_for_response( get_the_ID() );
                $locations[] = $location;
            }
            wp_reset_postdata();
        }

        // Générer le fichier d'exportation
        $file_content = '';
        $file_name = 'boss-seo-locations-' . date( 'Y-m-d' ) . '.' . $format;
        $file_type = '';

        if ( $format === 'csv' ) {
            $file_content = $this->generate_csv_file( $locations );
            $file_type = 'text/csv';
        } elseif ( $format === 'json' ) {
            $file_content = $this->generate_json_file( $locations );
            $file_type = 'application/json';
        } else {
            wp_send_json_error( array( 'message' => __( 'Format de fichier non pris en charge.', 'boss-seo' ) ) );
        }

        // Envoyer le fichier
        wp_send_json_success( array(
            'file_content' => $file_content,
            'file_name'    => $file_name,
            'file_type'    => $file_type,
            'count'        => count( $locations ),
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les statistiques des emplacements.
     *
     * @since    1.2.0
     */
    public function ajax_get_location_stats() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Compter le nombre total d'emplacements
        $total_locations = wp_count_posts( $this->cpt_name )->publish;

        // Compter le nombre d'emplacements par statut
        $active_locations = $this->count_locations_by_meta( 'status', 'active' );
        $inactive_locations = $this->count_locations_by_meta( 'status', 'inactive' );
        $coming_soon_locations = $this->count_locations_by_meta( 'status', 'coming_soon' );

        // Compter le nombre d'emplacements principaux
        $primary_locations = $this->count_locations_by_meta( 'primary', '1' );

        // Compter le nombre d'emplacements par type de schéma
        $schema_types = array(
            'LocalBusiness',
            'Restaurant',
            'Store',
            'MedicalBusiness',
            'FinancialService',
            'LodgingBusiness',
        );

        $schema_counts = array();

        foreach ( $schema_types as $schema_type ) {
            $schema_counts[$schema_type] = $this->count_locations_by_meta( 'seo_schema_type', $schema_type );
        }

        // Récupérer les emplacements récemment ajoutés
        $recent_locations = array();

        $recent_query = new WP_Query( array(
            'post_type'      => $this->cpt_name,
            'post_status'    => 'publish',
            'posts_per_page' => 5,
            'orderby'        => 'date',
            'order'          => 'DESC',
        ) );

        if ( $recent_query->have_posts() ) {
            while ( $recent_query->have_posts() ) {
                $recent_query->the_post();
                $recent_locations[] = array(
                    'id'    => get_the_ID(),
                    'title' => get_the_title(),
                    'date'  => get_the_date( 'Y-m-d' ),
                    'link'  => get_edit_post_link( get_the_ID(), 'raw' ),
                );
            }
            wp_reset_postdata();
        }

        // Préparer la réponse
        $stats = array(
            'total'       => $total_locations,
            'status'      => array(
                'active'      => $active_locations,
                'inactive'    => $inactive_locations,
                'coming_soon' => $coming_soon_locations,
            ),
            'primary'     => $primary_locations,
            'schema_type' => $schema_counts,
            'recent'      => $recent_locations,
        );

        wp_send_json_success( $stats );
    }

    /**
     * Analyse un fichier CSV.
     *
     * @since    1.2.0
     * @param    string    $file_content    Le contenu du fichier CSV.
     * @return   array                      Les données analysées.
     */
    private function parse_csv_file( $file_content ) {
        $locations = array();
        $rows = str_getcsv( $file_content, "\n" );

        if ( empty( $rows ) ) {
            return $locations;
        }

        $headers = str_getcsv( $rows[0] );

        for ( $i = 1; $i < count( $rows ); $i++ ) {
            $row = str_getcsv( $rows[$i] );

            if ( count( $row ) !== count( $headers ) ) {
                continue;
            }

            $location = array();

            foreach ( $headers as $j => $header ) {
                $location[trim( $header )] = isset( $row[$j] ) ? trim( $row[$j] ) : '';
            }

            $locations[] = $location;
        }

        return $locations;
    }

    /**
     * Analyse un fichier JSON.
     *
     * @since    1.2.0
     * @param    string    $file_content    Le contenu du fichier JSON.
     * @return   array                      Les données analysées.
     */
    private function parse_json_file( $file_content ) {
        $locations = json_decode( $file_content, true );

        if ( ! is_array( $locations ) ) {
            return array();
        }

        return $locations;
    }

    /**
     * Génère un fichier CSV.
     *
     * @since    1.2.0
     * @param    array     $locations    Les données des emplacements.
     * @return   string                  Le contenu du fichier CSV.
     */
    private function generate_csv_file( $locations ) {
        if ( empty( $locations ) ) {
            return '';
        }

        $headers = array(
            'title',
            'content',
            'excerpt',
            'status',
            'primary',
            'short_name',
            'description',
            'street',
            'street2',
            'city',
            'state',
            'postal_code',
            'country',
            'latitude',
            'longitude',
            'phone',
            'fax',
            'email',
            'website',
            'seo_title',
            'seo_description',
            'seo_keywords',
            'seo_schema_type',
            'seo_hide_in_sitemap',
        );

        $csv = implode( ',', $headers ) . "\n";

        foreach ( $locations as $location ) {
            $row = array();

            foreach ( $headers as $header ) {
                $value = '';

                switch ( $header ) {
                    case 'title':
                    case 'content':
                    case 'excerpt':
                        $value = isset( $location[$header] ) ? $location[$header] : '';
                        break;
                    case 'status':
                        $value = isset( $location[$header] ) ? $location[$header] : 'active';
                        break;
                    case 'primary':
                    case 'seo_hide_in_sitemap':
                        $value = isset( $location[$header] ) && $location[$header] ? '1' : '0';
                        break;
                    case 'street':
                    case 'street2':
                    case 'city':
                    case 'state':
                    case 'postal_code':
                    case 'country':
                    case 'latitude':
                    case 'longitude':
                        $value = isset( $location['address'][$header] ) ? $location['address'][$header] : '';
                        break;
                    case 'phone':
                    case 'fax':
                    case 'email':
                    case 'website':
                        $value = isset( $location['contact'][$header] ) ? $location['contact'][$header] : '';
                        break;
                    case 'seo_title':
                    case 'seo_description':
                    case 'seo_keywords':
                    case 'seo_schema_type':
                        $value = isset( $location['seo'][str_replace( 'seo_', '', $header )] ) ? $location['seo'][str_replace( 'seo_', '', $header )] : '';
                        break;
                    default:
                        $value = isset( $location[$header] ) ? $location[$header] : '';
                        break;
                }

                // Échapper les guillemets et les virgules
                $value = str_replace( '"', '""', $value );
                $row[] = '"' . $value . '"';
            }

            $csv .= implode( ',', $row ) . "\n";
        }

        return $csv;
    }

    /**
     * Génère un fichier JSON.
     *
     * @since    1.2.0
     * @param    array     $locations    Les données des emplacements.
     * @return   string                  Le contenu du fichier JSON.
     */
    private function generate_json_file( $locations ) {
        return json_encode( $locations, JSON_PRETTY_PRINT );
    }
}