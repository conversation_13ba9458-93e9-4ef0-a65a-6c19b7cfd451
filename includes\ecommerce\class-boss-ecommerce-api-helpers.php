<?php
/**
 * Méthodes utilitaires pour l'API E-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Trait contenant les méthodes utilitaires pour l'API E-commerce.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
trait Boss_Ecommerce_API_Helpers {

    /**
     * Récupère les problèmes d'un produit basés sur son score.
     *
     * @since    1.2.0
     * @param    WC_Product    $product    Le produit WooCommerce.
     * @param    int           $score      Score SEO du produit.
     * @return   array                     Liste des problèmes.
     */
    private function get_product_issues( $product, $score ) {
        $issues = array();

        // Vérifier le titre
        $title = $product->get_name();
        if ( empty( $title ) || strlen( $title ) < 20 ) {
            $issues[] = 'title_too_short';
        }

        // Vérifier la description courte
        $short_description = $product->get_short_description();
        if ( empty( $short_description ) || strlen( $short_description ) < 80 ) {
            $issues[] = 'missing_meta_description';
        }

        // Vérifier les images
        $image_id = $product->get_image_id();
        if ( $image_id ) {
            $alt_text = get_post_meta( $image_id, '_wp_attachment_image_alt', true );
            if ( empty( $alt_text ) ) {
                $issues[] = 'no_alt_text';
            }
        } else {
            $issues[] = 'no_alt_text';
        }

        // Vérifier le contenu dupliqué (basique)
        $description = $product->get_description();
        if ( ! empty( $description ) && ! empty( $short_description ) ) {
            if ( $description === $short_description ) {
                $issues[] = 'duplicate_content';
            }
        }

        // Vérifier le schéma
        if ( ! $this->has_product_schema( $product->get_id() ) ) {
            $issues[] = 'missing_schema';
        }

        return $issues;
    }

    /**
     * Vérifie si un produit a un schéma.
     *
     * @since    1.2.0
     * @param    int    $product_id    ID du produit.
     * @return   bool                  True si le produit a un schéma, false sinon.
     */
    private function has_product_schema( $product_id ) {
        // Vérifier si un schéma existe pour ce produit
        $schema = get_post_meta( $product_id, '_boss_seo_product_schema', true );
        return ! empty( $schema );
    }

    /**
     * Récupère les images d'un produit.
     *
     * @since    1.2.0
     * @param    WC_Product    $product    Le produit WooCommerce.
     * @return   array                     Liste des images.
     */
    private function get_product_images( $product ) {
        $images = array();

        // Image principale
        $image_id = $product->get_image_id();
        if ( $image_id ) {
            $image_url = wp_get_attachment_image_url( $image_id, 'full' );
            $alt_text = get_post_meta( $image_id, '_wp_attachment_image_alt', true );
            
            $images[] = array(
                'id' => $image_id,
                'url' => $image_url,
                'alt' => $alt_text ?: $product->get_name()
            );
        }

        // Galerie d'images
        $gallery_ids = $product->get_gallery_image_ids();
        foreach ( $gallery_ids as $gallery_id ) {
            $image_url = wp_get_attachment_image_url( $gallery_id, 'full' );
            $alt_text = get_post_meta( $gallery_id, '_wp_attachment_image_alt', true );
            
            $images[] = array(
                'id' => $gallery_id,
                'url' => $image_url,
                'alt' => $alt_text ?: $product->get_name()
            );
        }

        return $images;
    }

    /**
     * Effectue l'optimisation d'un produit.
     *
     * @since    1.2.0
     * @param    WC_Product    $product     Le produit WooCommerce.
     * @param    array         $settings    Paramètres d'optimisation.
     * @return   array                      Résultat de l'optimisation.
     */
    private function perform_product_optimization( $product, $settings ) {
        $product_id = $product->get_id();
        $optimizations_applied = array();

        // Optimiser le titre si demandé
        if ( ! empty( $settings['updateTitles'] ) ) {
            $current_title = $product->get_name();
            if ( strlen( $current_title ) < 30 ) {
                // Améliorer le titre (exemple basique)
                $categories = wp_get_post_terms( $product_id, 'product_cat' );
                $category_name = ! empty( $categories ) ? $categories[0]->name : '';
                
                if ( ! empty( $category_name ) && strpos( $current_title, $category_name ) === false ) {
                    $new_title = $current_title . ' - ' . $category_name;
                    wp_update_post( array(
                        'ID' => $product_id,
                        'post_title' => $new_title
                    ) );
                    $optimizations_applied[] = 'title_optimized';
                }
            }
        }

        // Optimiser la description courte si demandée
        if ( ! empty( $settings['updateDescriptions'] ) ) {
            $short_description = $product->get_short_description();
            if ( strlen( $short_description ) < 80 ) {
                // Générer une description basique
                $description = $product->get_description();
                if ( ! empty( $description ) ) {
                    $new_short_description = wp_trim_words( $description, 20, '...' );
                    update_post_meta( $product_id, '_product_short_description', $new_short_description );
                    $optimizations_applied[] = 'description_optimized';
                }
            }
        }

        // Optimiser les images si demandé
        if ( ! empty( $settings['updateImages'] ) ) {
            $image_id = $product->get_image_id();
            if ( $image_id ) {
                $alt_text = get_post_meta( $image_id, '_wp_attachment_image_alt', true );
                if ( empty( $alt_text ) ) {
                    update_post_meta( $image_id, '_wp_attachment_image_alt', $product->get_name() );
                    $optimizations_applied[] = 'images_optimized';
                }
            }
        }

        // Générer un schéma si demandé
        if ( ! empty( $settings['generateSchemas'] ) ) {
            if ( ! $this->has_product_schema( $product_id ) ) {
                $schema = $this->generate_basic_product_schema( $product );
                update_post_meta( $product_id, '_boss_seo_product_schema', $schema );
                $optimizations_applied[] = 'schema_generated';
            }
        }

        // Recalculer le score après optimisation
        $new_score = $this->woo_manager->calculate_seo_score( $product );

        return array(
            'id' => $product_id,
            'name' => $product->get_name(),
            'score' => $new_score,
            'optimizations_applied' => $optimizations_applied,
            'timestamp' => current_time( 'timestamp' )
        );
    }

    /**
     * Génère un schéma de base pour un produit.
     *
     * @since    1.2.0
     * @param    WC_Product    $product    Le produit WooCommerce.
     * @return   array                     Schéma JSON-LD.
     */
    private function generate_basic_product_schema( $product ) {
        $categories = wp_get_post_terms( $product->get_id(), 'product_cat' );
        $category_name = ! empty( $categories ) ? $categories[0]->name : 'Product';

        $schema = array(
            '@context' => 'https://schema.org/',
            '@type' => 'Product',
            'name' => $product->get_name(),
            'description' => $product->get_short_description() ?: $product->get_description(),
            'category' => $category_name,
            'sku' => $product->get_sku(),
            'offers' => array(
                '@type' => 'Offer',
                'price' => $product->get_price(),
                'priceCurrency' => get_woocommerce_currency(),
                'availability' => $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'
            )
        );

        // Ajouter l'image si disponible
        $image_id = $product->get_image_id();
        if ( $image_id ) {
            $image_url = wp_get_attachment_image_url( $image_id, 'full' );
            if ( $image_url ) {
                $schema['image'] = $image_url;
            }
        }

        return $schema;
    }

    /**
     * Construit le prompt pour la génération de description IA.
     *
     * @since    1.2.0
     * @param    WC_Product    $product     Le produit WooCommerce.
     * @param    array         $settings    Paramètres de génération.
     * @return   string                     Prompt pour l'IA.
     */
    private function build_description_prompt( $product, $settings ) {
        $categories = wp_get_post_terms( $product->get_id(), 'product_cat' );
        $category_name = ! empty( $categories ) ? $categories[0]->name : 'produit';

        $tone = $settings['tone'] ?? 'professionnel';
        $length = $settings['length'] ?? 'moyen';
        $include_features = $settings['include_features'] ?? true;

        $prompt = "Génère une description SEO optimisée pour ce produit e-commerce :\n\n";
        $prompt .= "Nom du produit : " . $product->get_name() . "\n";
        $prompt .= "Catégorie : " . $category_name . "\n";
        $prompt .= "Prix : " . $product->get_price() . " " . get_woocommerce_currency() . "\n";

        if ( $product->get_short_description() ) {
            $prompt .= "Description courte actuelle : " . $product->get_short_description() . "\n";
        }

        $prompt .= "\nConsignes :\n";
        $prompt .= "- Ton : " . $tone . "\n";
        $prompt .= "- Longueur : " . $length . "\n";
        $prompt .= "- Optimisé pour le SEO avec des mots-clés pertinents\n";
        $prompt .= "- Inclure les avantages et caractéristiques du produit\n";
        $prompt .= "- Appel à l'action convaincant\n";
        $prompt .= "- Format : titre SEO (50-60 caractères), meta description (150-160 caractères), description longue (300+ mots)\n\n";
        $prompt .= "Réponds au format JSON avec les clés : title, meta_description, description";

        return $prompt;
    }

    /**
     * Parse le contenu généré par l'IA.
     *
     * @since    1.2.0
     * @param    string    $content    Contenu généré par l'IA.
     * @return   array                 Contenu parsé.
     */
    private function parse_generated_content( $content ) {
        // Essayer de parser le JSON
        $decoded = json_decode( $content, true );
        
        if ( json_last_error() === JSON_ERROR_NONE && is_array( $decoded ) ) {
            return array(
                'title' => $decoded['title'] ?? '',
                'meta_description' => $decoded['meta_description'] ?? '',
                'description' => $decoded['description'] ?? ''
            );
        }

        // Si ce n'est pas du JSON valide, essayer de parser manuellement
        $lines = explode( "\n", $content );
        $parsed = array(
            'title' => '',
            'meta_description' => '',
            'description' => ''
        );

        $current_section = '';
        foreach ( $lines as $line ) {
            $line = trim( $line );
            
            if ( stripos( $line, 'titre' ) !== false || stripos( $line, 'title' ) !== false ) {
                $current_section = 'title';
                continue;
            } elseif ( stripos( $line, 'meta' ) !== false || stripos( $line, 'description' ) !== false ) {
                $current_section = 'meta_description';
                continue;
            } elseif ( stripos( $line, 'description' ) !== false ) {
                $current_section = 'description';
                continue;
            }

            if ( ! empty( $line ) && ! empty( $current_section ) ) {
                $parsed[ $current_section ] .= $line . ' ';
            }
        }

        // Nettoyer les résultats
        foreach ( $parsed as $key => $value ) {
            $parsed[ $key ] = trim( $value );
        }

        return $parsed;
    }
}
