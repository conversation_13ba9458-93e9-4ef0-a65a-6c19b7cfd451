import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  TabPanel,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Notice
} from '@wordpress/components';
import { motion } from 'framer-motion';

// Composants
import AnalyticsSetup from '../components/analytics/AnalyticsSetup';
import AnalyticsDashboard from '../components/analytics/AnalyticsDashboard';
import KeywordPerformance from '../components/analytics/KeywordPerformance';
import PopularPages from '../components/analytics/PopularPages';
import OpportunityAnalysis from '../components/analytics/OpportunityAnalysis';
import TrendsAnalysis from '../components/analytics/TrendsAnalysis';
import ReportManager from '../components/analytics/ReportManager';

const AnalyticsManager = () => {
  // États
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isLoading, setIsLoading] = useState(true);
  const [isConfigured, setIsConfigured] = useState(false);
  const [integrations, setIntegrations] = useState({
    ga4: { connected: false, properties: [] },
    gsc: { connected: false, properties: [] }
  });
  const [selectedPeriod, setSelectedPeriod] = useState('last30days');
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [error, setError] = useState(null);

  // Effet pour charger l'état des intégrations
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour les intégrations
      const mockIntegrations = {
        ga4: {
          connected: true,
          properties: [
            { id: 'ga4-prop1', name: 'monsite.com (GA4)', selected: true },
            { id: 'ga4-prop2', name: 'boutique.monsite.com (GA4)', selected: false }
          ]
        },
        gsc: {
          connected: true,
          properties: [
            { id: 'gsc-prop1', name: 'https://monsite.com/', selected: true },
            { id: 'gsc-prop2', name: 'https://boutique.monsite.com/', selected: false }
          ]
        }
      };
      
      setIntegrations(mockIntegrations);
      setIsConfigured(mockIntegrations.ga4.connected && mockIntegrations.gsc.connected);
      
      // Sélectionner la première propriété par défaut
      if (mockIntegrations.ga4.properties.length > 0) {
        const defaultProperty = mockIntegrations.ga4.properties.find(prop => prop.selected) || mockIntegrations.ga4.properties[0];
        setSelectedProperty(defaultProperty.id);
      }
      
      setIsLoading(false);
    }, 1000);
  }, []);

  // Effet pour charger les données analytiques lorsque la période ou la propriété change
  useEffect(() => {
    if (isConfigured && selectedProperty && selectedPeriod) {
      setIsLoading(true);
      
      // Simuler le chargement des données analytiques
      setTimeout(() => {
        // Données fictives pour les analytics
        const mockAnalyticsData = generateMockAnalyticsData(selectedPeriod);
        setAnalyticsData(mockAnalyticsData);
        setIsLoading(false);
      }, 1500);
    }
  }, [isConfigured, selectedProperty, selectedPeriod]);

  // Fonction pour générer des données fictives
  const generateMockAnalyticsData = (period) => {
    // Déterminer le nombre de points de données en fonction de la période
    let dataPoints = 30;
    switch (period) {
      case 'last7days':
        dataPoints = 7;
        break;
      case 'last30days':
        dataPoints = 30;
        break;
      case 'last90days':
        dataPoints = 90;
        break;
      case 'lastYear':
        dataPoints = 12; // Mensuel pour l'année
        break;
      default:
        dataPoints = 30;
    }
    
    // Générer des données de trafic
    const trafficData = Array.from({ length: dataPoints }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (dataPoints - i - 1));
      
      return {
        date: date.toISOString().split('T')[0],
        users: Math.floor(Math.random() * 500) + 100,
        sessions: Math.floor(Math.random() * 700) + 150,
        pageviews: Math.floor(Math.random() * 1500) + 300,
        bounceRate: Math.random() * 30 + 40
      };
    });
    
    // Générer des données de mots-clés
    const keywordData = [
      { keyword: 'seo wordpress plugin', position: 3.2, clicks: 450, impressions: 5600, ctr: 8.04 },
      { keyword: 'meilleur plugin seo', position: 5.7, clicks: 320, impressions: 4800, ctr: 6.67 },
      { keyword: 'optimisation seo wordpress', position: 8.1, clicks: 210, impressions: 3900, ctr: 5.38 },
      { keyword: 'plugin seo gratuit', position: 4.3, clicks: 380, impressions: 5100, ctr: 7.45 },
      { keyword: 'comment améliorer seo', position: 6.8, clicks: 290, impressions: 4200, ctr: 6.90 },
      { keyword: 'audit seo wordpress', position: 2.5, clicks: 520, impressions: 6300, ctr: 8.25 },
      { keyword: 'schema markup wordpress', position: 7.2, clicks: 180, impressions: 3200, ctr: 5.63 },
      { keyword: 'structured data seo', position: 9.4, clicks: 120, impressions: 2800, ctr: 4.29 },
      { keyword: 'google analytics wordpress', position: 5.1, clicks: 340, impressions: 4500, ctr: 7.56 },
      { keyword: 'search console integration', position: 4.7, clicks: 360, impressions: 4900, ctr: 7.35 }
    ];
    
    // Générer des données de pages populaires
    const pagesData = [
      { url: '/accueil/', title: 'Accueil - Mon Site', pageviews: 2800, avgTimeOnPage: 125, bounceRate: 42.5 },
      { url: '/blog/seo-guide-2023/', title: 'Guide SEO Complet 2023', pageviews: 1950, avgTimeOnPage: 310, bounceRate: 35.2 },
      { url: '/services/', title: 'Nos Services', pageviews: 1650, avgTimeOnPage: 180, bounceRate: 48.7 },
      { url: '/blog/wordpress-optimisation/', title: 'Comment Optimiser WordPress', pageviews: 1420, avgTimeOnPage: 275, bounceRate: 39.1 },
      { url: '/contact/', title: 'Contactez-nous', pageviews: 980, avgTimeOnPage: 95, bounceRate: 52.3 },
      { url: '/a-propos/', title: 'À Propos de Nous', pageviews: 850, avgTimeOnPage: 145, bounceRate: 47.8 },
      { url: '/blog/schema-markup-guide/', title: 'Guide des Données Structurées', pageviews: 780, avgTimeOnPage: 290, bounceRate: 36.4 },
      { url: '/produits/', title: 'Nos Produits', pageviews: 720, avgTimeOnPage: 165, bounceRate: 45.2 },
      { url: '/blog/google-analytics-4/', title: 'Migrer vers GA4', pageviews: 690, avgTimeOnPage: 255, bounceRate: 38.9 },
      { url: '/faq/', title: 'Questions Fréquentes', pageviews: 580, avgTimeOnPage: 200, bounceRate: 41.7 }
    ];
    
    // Générer des données d'opportunités
    const opportunitiesData = [
      { type: 'keyword', title: 'Optimiser pour "plugin seo avancé"', potential: 85, difficulty: 'medium', currentPosition: 12 },
      { type: 'content', title: 'Améliorer le contenu de la page Services', potential: 72, difficulty: 'low', pageUrl: '/services/' },
      { type: 'technical', title: 'Corriger les balises title dupliquées', potential: 68, difficulty: 'medium', affectedPages: 5 },
      { type: 'backlink', title: 'Opportunité de backlink sur site-partenaire.com', potential: 79, difficulty: 'high', domainAuthority: 65 },
      { type: 'keyword', title: 'Cibler "tutoriel schema markup"', potential: 81, difficulty: 'medium', currentPosition: 15 },
      { type: 'content', title: 'Enrichir le contenu de la page FAQ', potential: 70, difficulty: 'low', pageUrl: '/faq/' }
    ];
    
    // Générer des données de tendances
    const trendsData = {
      traffic: {
        current: 15800,
        previous: 14200,
        change: 11.27
      },
      keywords: {
        current: 245,
        previous: 220,
        change: 11.36
      },
      avgPosition: {
        current: 18.5,
        previous: 22.3,
        change: 17.04
      },
      conversions: {
        current: 320,
        previous: 280,
        change: 14.29
      }
    };
    
    return {
      traffic: trafficData,
      keywords: keywordData,
      pages: pagesData,
      opportunities: opportunitiesData,
      trends: trendsData
    };
  };

  // Fonction pour gérer la connexion à GA4
  const handleConnectGA4 = () => {
    setIsLoading(true);
    
    // Simuler la connexion à GA4
    setTimeout(() => {
      setIntegrations({
        ...integrations,
        ga4: {
          connected: true,
          properties: [
            { id: 'ga4-prop1', name: 'monsite.com (GA4)', selected: true },
            { id: 'ga4-prop2', name: 'boutique.monsite.com (GA4)', selected: false }
          ]
        }
      });
      
      setIsLoading(false);
      
      // Vérifier si toutes les intégrations sont configurées
      if (integrations.gsc.connected) {
        setIsConfigured(true);
      }
    }, 1500);
  };

  // Fonction pour gérer la connexion à GSC
  const handleConnectGSC = () => {
    setIsLoading(true);
    
    // Simuler la connexion à GSC
    setTimeout(() => {
      setIntegrations({
        ...integrations,
        gsc: {
          connected: true,
          properties: [
            { id: 'gsc-prop1', name: 'https://monsite.com/', selected: true },
            { id: 'gsc-prop2', name: 'https://boutique.monsite.com/', selected: false }
          ]
        }
      });
      
      setIsLoading(false);
      
      // Vérifier si toutes les intégrations sont configurées
      if (integrations.ga4.connected) {
        setIsConfigured(true);
      }
    }, 1500);
  };

  // Fonction pour gérer le changement de période
  const handlePeriodChange = (period) => {
    setSelectedPeriod(period);
  };

  // Fonction pour gérer le changement de propriété
  const handlePropertyChange = (propertyId) => {
    setSelectedProperty(propertyId);
  };

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Intégrations Analytics', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Connectez vos outils d\'analyse et visualisez vos performances SEO', 'boss-seo')}
          </p>
        </div>

        {!isConfigured && !isLoading && (
          <Notice status="warning" isDismissible={false} className="boss-mb-6">
            {__('Veuillez configurer vos intégrations Google Analytics 4 et Google Search Console pour accéder aux données.', 'boss-seo')}
          </Notice>
        )}

        {isLoading ? (
          <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
            <Spinner />
          </div>
        ) : (
          <TabPanel
            className="boss-mb-6"
            activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
            tabs={[
              {
                name: 'dashboard',
                title: __('Tableau de bord', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'keywords',
                title: __('Mots-clés', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'pages',
                title: __('Pages populaires', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'opportunities',
                title: __('Opportunités', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'trends',
                title: __('Tendances', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'reports',
                title: __('Rapports', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'setup',
                title: __('Configuration', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              }
            ]}
            onSelect={(tabName) => setActiveTab(tabName)}
          >
            {(tab) => {
              if (tab.name === 'setup') {
                return (
                  <AnalyticsSetup 
                    integrations={integrations}
                    onConnectGA4={handleConnectGA4}
                    onConnectGSC={handleConnectGSC}
                    onPropertyChange={handlePropertyChange}
                  />
                );
              } else if (!isConfigured) {
                return (
                  <Card>
                    <CardBody>
                      <div className="boss-text-center boss-py-12">
                        <p className="boss-text-boss-gray boss-mb-6">
                          {__('Veuillez configurer vos intégrations pour accéder à cette section.', 'boss-seo')}
                        </p>
                        <Button
                          isPrimary
                          onClick={() => setActiveTab('setup')}
                        >
                          {__('Configurer les intégrations', 'boss-seo')}
                        </Button>
                      </div>
                    </CardBody>
                  </Card>
                );
              } else if (tab.name === 'dashboard') {
                return (
                  <AnalyticsDashboard 
                    data={analyticsData}
                    selectedPeriod={selectedPeriod}
                    onPeriodChange={handlePeriodChange}
                  />
                );
              } else if (tab.name === 'keywords') {
                return (
                  <KeywordPerformance 
                    data={analyticsData?.keywords}
                    selectedPeriod={selectedPeriod}
                    onPeriodChange={handlePeriodChange}
                  />
                );
              } else if (tab.name === 'pages') {
                return (
                  <PopularPages 
                    data={analyticsData?.pages}
                    selectedPeriod={selectedPeriod}
                    onPeriodChange={handlePeriodChange}
                  />
                );
              } else if (tab.name === 'opportunities') {
                return (
                  <OpportunityAnalysis 
                    data={analyticsData?.opportunities}
                  />
                );
              } else if (tab.name === 'trends') {
                return (
                  <TrendsAnalysis 
                    data={analyticsData?.trends}
                    trafficData={analyticsData?.traffic}
                    selectedPeriod={selectedPeriod}
                    onPeriodChange={handlePeriodChange}
                  />
                );
              } else if (tab.name === 'reports') {
                return (
                  <ReportManager 
                    analyticsData={analyticsData}
                  />
                );
              }
            }}
          </TabPanel>
        )}
      </div>
    </div>
  );
};

export default AnalyticsManager;
