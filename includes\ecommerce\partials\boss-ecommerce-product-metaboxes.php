<?php
/**
 * Template pour l'affichage des métaboxes de produit.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce/partials
 */

// Si ce fichier est appelé directement, abandonner.
if ( ! defined( 'WPINC' ) ) {
    die;
}

/**
 * Affiche la métabox pour les extraits enrichis.
 *
 * @since    1.2.0
 * @param    WP_Post    $post    L'objet post.
 */
function boss_seo_render_rich_snippets_meta_box( $post ) {
    // Récupérer les données du produit
    $product_id = $post->ID;
    $snippet_type = get_post_meta( $product_id, '_boss_rich_snippet_type', true );
    $snippet_options = get_post_meta( $product_id, '_boss_rich_snippet_options', true );
    
    if ( empty( $snippet_type ) ) {
        $snippet_type = 'product';
    }
    
    if ( empty( $snippet_options ) || ! is_array( $snippet_options ) ) {
        $snippet_options = array(
            'show_price' => true,
            'show_availability' => true,
            'show_rating' => true,
            'show_reviews' => true,
            'show_brand' => true,
            'show_sku' => true,
            'show_description' => true,
            'show_image' => true,
        );
    }
    
    // Afficher le formulaire
    wp_nonce_field( 'boss_seo_rich_snippet_meta_box', 'boss_seo_rich_snippet_meta_box_nonce' );
    ?>
    <div class="boss-seo-rich-snippet-meta-box">
        <p>
            <label for="boss_rich_snippet_type"><?php esc_html_e( 'Type d\'extrait enrichi :', 'boss-seo' ); ?></label>
            <select id="boss_rich_snippet_type" name="boss_rich_snippet_type">
                <option value="product" <?php selected( $snippet_type, 'product' ); ?>><?php esc_html_e( 'Produit', 'boss-seo' ); ?></option>
                <option value="offer" <?php selected( $snippet_type, 'offer' ); ?>><?php esc_html_e( 'Offre', 'boss-seo' ); ?></option>
                <option value="review" <?php selected( $snippet_type, 'review' ); ?>><?php esc_html_e( 'Avis', 'boss-seo' ); ?></option>
                <option value="aggregate_rating" <?php selected( $snippet_type, 'aggregate_rating' ); ?>><?php esc_html_e( 'Note moyenne', 'boss-seo' ); ?></option>
                <option value="product_with_offer" <?php selected( $snippet_type, 'product_with_offer' ); ?>><?php esc_html_e( 'Produit avec offre', 'boss-seo' ); ?></option>
                <option value="product_with_review" <?php selected( $snippet_type, 'product_with_review' ); ?>><?php esc_html_e( 'Produit avec avis', 'boss-seo' ); ?></option>
                <option value="product_with_aggregate_rating" <?php selected( $snippet_type, 'product_with_aggregate_rating' ); ?>><?php esc_html_e( 'Produit avec note moyenne', 'boss-seo' ); ?></option>
                <option value="product_complete" <?php selected( $snippet_type, 'product_complete' ); ?>><?php esc_html_e( 'Produit complet', 'boss-seo' ); ?></option>
            </select>
        </p>
        
        <p><?php esc_html_e( 'Options de l\'extrait enrichi :', 'boss-seo' ); ?></p>
        <ul>
            <li>
                <label>
                    <input type="checkbox" name="boss_rich_snippet_options[show_price]" value="1" <?php checked( isset( $snippet_options['show_price'] ) ? $snippet_options['show_price'] : true ); ?>>
                    <?php esc_html_e( 'Afficher le prix', 'boss-seo' ); ?>
                </label>
            </li>
            <li>
                <label>
                    <input type="checkbox" name="boss_rich_snippet_options[show_availability]" value="1" <?php checked( isset( $snippet_options['show_availability'] ) ? $snippet_options['show_availability'] : true ); ?>>
                    <?php esc_html_e( 'Afficher la disponibilité', 'boss-seo' ); ?>
                </label>
            </li>
            <li>
                <label>
                    <input type="checkbox" name="boss_rich_snippet_options[show_rating]" value="1" <?php checked( isset( $snippet_options['show_rating'] ) ? $snippet_options['show_rating'] : true ); ?>>
                    <?php esc_html_e( 'Afficher la note', 'boss-seo' ); ?>
                </label>
            </li>
            <li>
                <label>
                    <input type="checkbox" name="boss_rich_snippet_options[show_reviews]" value="1" <?php checked( isset( $snippet_options['show_reviews'] ) ? $snippet_options['show_reviews'] : true ); ?>>
                    <?php esc_html_e( 'Afficher les avis', 'boss-seo' ); ?>
                </label>
            </li>
            <li>
                <label>
                    <input type="checkbox" name="boss_rich_snippet_options[show_brand]" value="1" <?php checked( isset( $snippet_options['show_brand'] ) ? $snippet_options['show_brand'] : true ); ?>>
                    <?php esc_html_e( 'Afficher la marque', 'boss-seo' ); ?>
                </label>
            </li>
            <li>
                <label>
                    <input type="checkbox" name="boss_rich_snippet_options[show_sku]" value="1" <?php checked( isset( $snippet_options['show_sku'] ) ? $snippet_options['show_sku'] : true ); ?>>
                    <?php esc_html_e( 'Afficher le SKU', 'boss-seo' ); ?>
                </label>
            </li>
            <li>
                <label>
                    <input type="checkbox" name="boss_rich_snippet_options[show_description]" value="1" <?php checked( isset( $snippet_options['show_description'] ) ? $snippet_options['show_description'] : true ); ?>>
                    <?php esc_html_e( 'Afficher la description', 'boss-seo' ); ?>
                </label>
            </li>
            <li>
                <label>
                    <input type="checkbox" name="boss_rich_snippet_options[show_image]" value="1" <?php checked( isset( $snippet_options['show_image'] ) ? $snippet_options['show_image'] : true ); ?>>
                    <?php esc_html_e( 'Afficher l\'image', 'boss-seo' ); ?>
                </label>
            </li>
        </ul>
        
        <div class="boss-seo-rich-snippet-actions">
            <button type="button" class="button button-secondary" id="boss-seo-generate-rich-snippet" data-product-id="<?php echo esc_attr( $product_id ); ?>"><?php esc_html_e( 'Générer l\'extrait enrichi', 'boss-seo' ); ?></button>
            <button type="button" class="button button-secondary" id="boss-seo-test-rich-snippet" data-product-id="<?php echo esc_attr( $product_id ); ?>"><?php esc_html_e( 'Tester l\'extrait enrichi', 'boss-seo' ); ?></button>
        </div>
        
        <div class="boss-seo-rich-snippet-result" style="display: none;">
            <h4><?php esc_html_e( 'Extrait enrichi généré :', 'boss-seo' ); ?></h4>
            <div id="boss-seo-rich-snippet-preview"></div>
        </div>
        
        <div class="boss-seo-rich-snippet-test-result" style="display: none;">
            <h4><?php esc_html_e( 'Résultat du test :', 'boss-seo' ); ?></h4>
            <div id="boss-seo-rich-snippet-test-result"></div>
        </div>
    </div>
    <?php
}

/**
 * Affiche la métabox pour l'analyse SEO.
 *
 * @since    1.2.0
 * @param    WP_Post    $post    L'objet post.
 */
function boss_seo_render_analyzer_meta_box( $post ) {
    // Récupérer les données du produit
    $product_id = $post->ID;
    $analysis = get_post_meta( $product_id, '_boss_seo_analysis', true );
    
    // Afficher le formulaire
    wp_nonce_field( 'boss_seo_analyzer_meta_box', 'boss_seo_analyzer_meta_box_nonce' );
    ?>
    <div class="boss-seo-analyzer-meta-box">
        <div class="boss-seo-analyzer-actions">
            <button type="button" class="button button-primary" id="boss-seo-analyze-product" data-product-id="<?php echo esc_attr( $product_id ); ?>"><?php esc_html_e( 'Analyser le produit', 'boss-seo' ); ?></button>
            <button type="button" class="button button-secondary" id="boss-seo-view-analysis-history" data-product-id="<?php echo esc_attr( $product_id ); ?>"><?php esc_html_e( 'Voir l\'historique d\'analyse', 'boss-seo' ); ?></button>
        </div>
        
        <div class="boss-seo-analyzer-result" <?php echo empty( $analysis ) ? 'style="display: none;"' : ''; ?>>
            <h4><?php esc_html_e( 'Résultat de l\'analyse :', 'boss-seo' ); ?></h4>
            <div id="boss-seo-analyzer-result">
                <?php if ( ! empty( $analysis ) ) : ?>
                    <?php echo boss_seo_render_analysis_result( $analysis ); ?>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="boss-seo-analyzer-history" style="display: none;">
            <h4><?php esc_html_e( 'Historique d\'analyse :', 'boss-seo' ); ?></h4>
            <div id="boss-seo-analyzer-history"></div>
        </div>
    </div>
    <?php
}

/**
 * Affiche le résultat de l'analyse.
 *
 * @since    1.2.0
 * @param    array     $analysis    Les résultats de l'analyse.
 * @return   string                 Le HTML du résultat de l'analyse.
 */
function boss_seo_render_analysis_result( $analysis ) {
    $output = '<div class="boss-seo-analysis-result">';
    
    // Afficher le score global
    $score = $analysis['score'];
    $score_class = '';
    
    if ( $score < 50 ) {
        $score_class = 'error';
    } elseif ( $score < 70 ) {
        $score_class = 'warning';
    } elseif ( $score < 90 ) {
        $score_class = 'good';
    } else {
        $score_class = 'excellent';
    }
    
    $output .= '<div class="boss-seo-analysis-score boss-seo-analysis-score-' . esc_attr( $score_class ) . '">';
    $output .= '<span class="boss-seo-analysis-score-value">' . esc_html( $score ) . '</span>';
    $output .= '<span class="boss-seo-analysis-score-label">' . esc_html__( 'Score SEO', 'boss-seo' ) . '</span>';
    $output .= '</div>';
    
    // Afficher la date de l'analyse
    $output .= '<div class="boss-seo-analysis-date">';
    $output .= '<span class="boss-seo-analysis-date-label">' . esc_html__( 'Date de l\'analyse :', 'boss-seo' ) . '</span>';
    $output .= '<span class="boss-seo-analysis-date-value">' . esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $analysis['date'] ) ) ) . '</span>';
    $output .= '</div>';
    
    // Afficher les résultats détaillés
    $output .= '<div class="boss-seo-analysis-details">';
    
    // Afficher les analyses individuelles
    $analyses = array( 'title', 'description', 'images', 'categories_tags', 'attributes', 'price', 'stock', 'reviews', 'sku', 'permalink' );
    
    foreach ( $analyses as $key ) {
        if ( isset( $analysis[$key] ) ) {
            $output .= '<div class="boss-seo-analysis-detail boss-seo-analysis-detail-' . esc_attr( $analysis[$key]['status'] ) . '">';
            $output .= '<h4 class="boss-seo-analysis-detail-title">' . esc_html( $analysis[$key]['title'] ) . '</h4>';
            $output .= '<div class="boss-seo-analysis-detail-score">';
            $output .= '<span class="boss-seo-analysis-detail-score-value">' . esc_html( $analysis[$key]['score'] ) . '</span>';
            $output .= '<span class="boss-seo-analysis-detail-score-max">/' . esc_html( $analysis[$key]['max_score'] ) . '</span>';
            $output .= '</div>';
            $output .= '<div class="boss-seo-analysis-detail-description">' . esc_html( $analysis[$key]['description'] ) . '</div>';
            
            if ( ! empty( $analysis[$key]['recommendations'] ) ) {
                $output .= '<div class="boss-seo-analysis-detail-recommendations">';
                $output .= '<h5>' . esc_html__( 'Recommandations :', 'boss-seo' ) . '</h5>';
                $output .= '<ul>';
                
                foreach ( $analysis[$key]['recommendations'] as $recommendation ) {
                    $output .= '<li>' . esc_html( $recommendation ) . '</li>';
                }
                
                $output .= '</ul>';
                $output .= '</div>';
            }
            
            $output .= '</div>';
        }
    }
    
    $output .= '</div>';
    $output .= '</div>';
    
    return $output;
}
