<?php
/**
 * Classe pour la gestion des redirections.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 */

/**
 * Classe pour la gestion des redirections.
 *
 * Cette classe gère les redirections d'URL pour le plugin Boss SEO.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Redirections {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le nom de la table des redirections.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $table_name    Le nom de la table des redirections.
     */
    protected $table_name;

    /**
     * Le nom de la table des logs de redirections.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $logs_table_name    Le nom de la table des logs de redirections.
     */
    protected $logs_table_name;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        global $wpdb;

        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->table_name = $wpdb->prefix . 'boss_seo_redirections';
        $this->logs_table_name = $wpdb->prefix . 'boss_seo_redirection_logs';
    }

    /**
     * Enregistre les hooks WordPress nécessaires.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Hook pour intercepter les requêtes 404 et appliquer les redirections
        add_action( 'template_redirect', array( $this, 'handle_redirections' ) );
    }

    /**
     * Crée les tables nécessaires lors de l'activation du plugin.
     *
     * @since    1.1.0
     */
    public function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Table des redirections
        $sql = "CREATE TABLE $this->table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            source varchar(255) NOT NULL,
            target varchar(255) NOT NULL,
            type varchar(10) NOT NULL,
            status varchar(20) NOT NULL,
            match_case tinyint(1) NOT NULL DEFAULT 0,
            regex tinyint(1) NOT NULL DEFAULT 0,
            notes text,
            hits mediumint(9) NOT NULL DEFAULT 0,
            last_used datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY source (source(191))
        ) $charset_collate;";

        // Table des logs de redirections
        $sql .= "CREATE TABLE $this->logs_table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            redirection_id mediumint(9) NOT NULL,
            url varchar(255) NOT NULL,
            user_agent varchar(255),
            ip varchar(45),
            referer varchar(255),
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY redirection_id (redirection_id)
        ) $charset_collate;";

        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.1.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/redirections',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_redirections' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'add_redirection' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/redirections/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_redirection' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'PUT',
                    'callback'            => array( $this, 'update_redirection' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_redirection' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/redirections/logs',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_logs' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/redirections/logs/clear',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'clear_logs' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/redirections/import',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'import_redirections' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/redirections/export',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'export_redirections' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/redirections/test',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'test_redirection' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.1.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère la liste des redirections.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_redirections( $request ) {
        global $wpdb;

        // Paramètres de pagination
        $page = isset( $request['page'] ) ? intval( $request['page'] ) : 1;
        $per_page = isset( $request['per_page'] ) ? intval( $request['per_page'] ) : 20;
        $offset = ( $page - 1 ) * $per_page;

        // Paramètres de filtrage
        $search = isset( $request['search'] ) ? sanitize_text_field( $request['search'] ) : '';
        $type = isset( $request['type'] ) ? sanitize_text_field( $request['type'] ) : '';
        $status = isset( $request['status'] ) ? sanitize_text_field( $request['status'] ) : '';

        // Construction de la requête
        $where = array();
        $where_values = array();

        if ( ! empty( $search ) ) {
            $where[] = '(source LIKE %s OR target LIKE %s OR notes LIKE %s)';
            $search_like = '%' . $wpdb->esc_like( $search ) . '%';
            $where_values[] = $search_like;
            $where_values[] = $search_like;
            $where_values[] = $search_like;
        }

        if ( ! empty( $type ) ) {
            $where[] = 'type = %s';
            $where_values[] = $type;
        }

        if ( ! empty( $status ) ) {
            $where[] = 'status = %s';
            $where_values[] = $status;
        }

        $where_clause = '';
        if ( ! empty( $where ) ) {
            $where_clause = 'WHERE ' . implode( ' AND ', $where );
        }

        // Requête pour le nombre total
        $count_query = "SELECT COUNT(*) FROM $this->table_name $where_clause";
        if ( ! empty( $where_values ) ) {
            $count_query = $wpdb->prepare( $count_query, $where_values );
        }
        $total_items = $wpdb->get_var( $count_query );

        // Requête pour les données
        $query = "SELECT * FROM $this->table_name $where_clause ORDER BY id DESC LIMIT %d OFFSET %d";
        $query_args = array_merge( $where_values, array( $per_page, $offset ) );
        $query = $wpdb->prepare( $query, $query_args );
        $redirections = $wpdb->get_results( $query );

        // Préparer la réponse
        $data = array(
            'redirections' => $redirections,
            'total' => intval( $total_items ),
            'pages' => ceil( $total_items / $per_page ),
            'page' => $page,
            'per_page' => $per_page,
        );

        return rest_ensure_response( $data );
    }

    /**
     * Récupère une redirection spécifique.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_redirection( $request ) {
        global $wpdb;

        $id = intval( $request['id'] );

        $redirection = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $this->table_name WHERE id = %d",
                $id
            )
        );

        if ( ! $redirection ) {
            return new WP_Error(
                'boss_seo_redirection_not_found',
                __( 'Redirection non trouvée.', 'boss-seo' ),
                array( 'status' => 404 )
            );
        }

        return rest_ensure_response( $redirection );
    }

    /**
     * Ajoute une nouvelle redirection.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function add_redirection( $request ) {
        global $wpdb;

        $source = sanitize_text_field( $request['source'] );
        $target = sanitize_text_field( $request['target'] );
        $type = sanitize_text_field( $request['type'] );
        $status = sanitize_text_field( $request['status'] );
        $match_case = isset( $request['matchCase'] ) ? (bool) $request['matchCase'] : false;
        $regex = isset( $request['regex'] ) ? (bool) $request['regex'] : false;
        $notes = isset( $request['notes'] ) ? sanitize_textarea_field( $request['notes'] ) : '';

        // Validation
        if ( empty( $source ) || empty( $target ) ) {
            return new WP_Error(
                'boss_seo_invalid_redirection',
                __( 'Les champs source et cible sont obligatoires.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        // Vérifier si la redirection existe déjà
        $exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $this->table_name WHERE source = %s",
                $source
            )
        );

        if ( $exists ) {
            return new WP_Error(
                'boss_seo_redirection_exists',
                __( 'Une redirection avec cette source existe déjà.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        // Insérer la redirection
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'source' => $source,
                'target' => $target,
                'type' => $type,
                'status' => $status,
                'match_case' => $match_case ? 1 : 0,
                'regex' => $regex ? 1 : 0,
                'notes' => $notes,
                'hits' => 0,
                'created_at' => current_time( 'mysql' ),
                'updated_at' => current_time( 'mysql' ),
            ),
            array( '%s', '%s', '%s', '%s', '%d', '%d', '%s', '%d', '%s', '%s' )
        );

        if ( ! $result ) {
            return new WP_Error(
                'boss_seo_redirection_insert_failed',
                __( 'Échec de l\'ajout de la redirection.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }

        $redirection_id = $wpdb->insert_id;
        $redirection = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $this->table_name WHERE id = %d",
                $redirection_id
            )
        );

        return rest_ensure_response( $redirection );
    }

    /**
     * Met à jour une redirection existante.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function update_redirection( $request ) {
        global $wpdb;

        $id = intval( $request['id'] );
        $source = sanitize_text_field( $request['source'] );
        $target = sanitize_text_field( $request['target'] );
        $type = sanitize_text_field( $request['type'] );
        $status = sanitize_text_field( $request['status'] );
        $match_case = isset( $request['matchCase'] ) ? (bool) $request['matchCase'] : false;
        $regex = isset( $request['regex'] ) ? (bool) $request['regex'] : false;
        $notes = isset( $request['notes'] ) ? sanitize_textarea_field( $request['notes'] ) : '';

        // Validation
        if ( empty( $source ) || empty( $target ) ) {
            return new WP_Error(
                'boss_seo_invalid_redirection',
                __( 'Les champs source et cible sont obligatoires.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        // Vérifier si la redirection existe
        $exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $this->table_name WHERE id = %d",
                $id
            )
        );

        if ( ! $exists ) {
            return new WP_Error(
                'boss_seo_redirection_not_found',
                __( 'Redirection non trouvée.', 'boss-seo' ),
                array( 'status' => 404 )
            );
        }

        // Mettre à jour la redirection
        $result = $wpdb->update(
            $this->table_name,
            array(
                'source' => $source,
                'target' => $target,
                'type' => $type,
                'status' => $status,
                'match_case' => $match_case ? 1 : 0,
                'regex' => $regex ? 1 : 0,
                'notes' => $notes,
                'updated_at' => current_time( 'mysql' ),
            ),
            array( 'id' => $id ),
            array( '%s', '%s', '%s', '%s', '%d', '%d', '%s', '%s' ),
            array( '%d' )
        );

        if ( $result === false ) {
            return new WP_Error(
                'boss_seo_redirection_update_failed',
                __( 'Échec de la mise à jour de la redirection.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }

        $redirection = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $this->table_name WHERE id = %d",
                $id
            )
        );

        return rest_ensure_response( $redirection );
    }

    /**
     * Supprime une redirection.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function delete_redirection( $request ) {
        global $wpdb;

        $id = intval( $request['id'] );

        // Vérifier si la redirection existe
        $exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $this->table_name WHERE id = %d",
                $id
            )
        );

        if ( ! $exists ) {
            return new WP_Error(
                'boss_seo_redirection_not_found',
                __( 'Redirection non trouvée.', 'boss-seo' ),
                array( 'status' => 404 )
            );
        }

        // Supprimer la redirection
        $result = $wpdb->delete(
            $this->table_name,
            array( 'id' => $id ),
            array( '%d' )
        );

        if ( ! $result ) {
            return new WP_Error(
                'boss_seo_redirection_delete_failed',
                __( 'Échec de la suppression de la redirection.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }

        // Supprimer les logs associés
        $wpdb->delete(
            $this->logs_table_name,
            array( 'redirection_id' => $id ),
            array( '%d' )
        );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Redirection supprimée avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les logs de redirections.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_logs( $request ) {
        global $wpdb;

        // Paramètres de pagination
        $page = isset( $request['page'] ) ? intval( $request['page'] ) : 1;
        $per_page = isset( $request['per_page'] ) ? intval( $request['per_page'] ) : 20;
        $offset = ( $page - 1 ) * $per_page;

        // Requête pour le nombre total
        $total_items = $wpdb->get_var( "SELECT COUNT(*) FROM $this->logs_table_name" );

        // Requête pour les données
        $logs = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT l.*, r.source, r.target, r.type
                FROM $this->logs_table_name l
                LEFT JOIN $this->table_name r ON l.redirection_id = r.id
                ORDER BY l.timestamp DESC
                LIMIT %d OFFSET %d",
                $per_page,
                $offset
            )
        );

        // Préparer la réponse
        $data = array(
            'logs' => $logs,
            'total' => intval( $total_items ),
            'pages' => ceil( $total_items / $per_page ),
            'page' => $page,
            'per_page' => $per_page,
        );

        return rest_ensure_response( $data );
    }

    /**
     * Efface les logs de redirections.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function clear_logs( $request ) {
        global $wpdb;

        $result = $wpdb->query( "TRUNCATE TABLE $this->logs_table_name" );

        if ( $result === false ) {
            return new WP_Error(
                'boss_seo_logs_clear_failed',
                __( 'Échec de la suppression des logs.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Logs supprimés avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Importe des redirections.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function import_redirections( $request ) {
        global $wpdb;

        $redirections = isset( $request['redirections'] ) ? $request['redirections'] : array();

        if ( empty( $redirections ) || ! is_array( $redirections ) ) {
            return new WP_Error(
                'boss_seo_invalid_import',
                __( 'Données d\'import invalides.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        $imported = 0;
        $errors = array();

        foreach ( $redirections as $redirection ) {
            // Validation des données
            if ( empty( $redirection['source'] ) || empty( $redirection['target'] ) ) {
                $errors[] = __( 'Les champs source et cible sont obligatoires.', 'boss-seo' );
                continue;
            }

            $source = sanitize_text_field( $redirection['source'] );
            $target = sanitize_text_field( $redirection['target'] );
            $type = isset( $redirection['type'] ) ? sanitize_text_field( $redirection['type'] ) : '301';
            $status = isset( $redirection['status'] ) ? sanitize_text_field( $redirection['status'] ) : 'active';
            $match_case = isset( $redirection['matchCase'] ) ? (bool) $redirection['matchCase'] : false;
            $regex = isset( $redirection['regex'] ) ? (bool) $redirection['regex'] : false;
            $notes = isset( $redirection['notes'] ) ? sanitize_textarea_field( $redirection['notes'] ) : '';

            // Vérifier si la redirection existe déjà
            $exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM $this->table_name WHERE source = %s",
                    $source
                )
            );

            if ( $exists ) {
                $errors[] = sprintf( __( 'La redirection pour %s existe déjà.', 'boss-seo' ), $source );
                continue;
            }

            // Insérer la redirection
            $result = $wpdb->insert(
                $this->table_name,
                array(
                    'source' => $source,
                    'target' => $target,
                    'type' => $type,
                    'status' => $status,
                    'match_case' => $match_case ? 1 : 0,
                    'regex' => $regex ? 1 : 0,
                    'notes' => $notes,
                    'hits' => 0,
                    'created_at' => current_time( 'mysql' ),
                    'updated_at' => current_time( 'mysql' ),
                ),
                array( '%s', '%s', '%s', '%s', '%d', '%d', '%s', '%d', '%s', '%s' )
            );

            if ( $result ) {
                $imported++;
            } else {
                $errors[] = sprintf( __( 'Échec de l\'import pour %s.', 'boss-seo' ), $source );
            }
        }

        return rest_ensure_response( array(
            'success' => $imported > 0,
            'imported' => $imported,
            'errors' => $errors,
            'message' => sprintf( __( '%d redirections importées avec succès.', 'boss-seo' ), $imported ),
        ) );
    }

    /**
     * Exporte les redirections.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function export_redirections( $request ) {
        global $wpdb;

        $format = isset( $request['format'] ) ? sanitize_text_field( $request['format'] ) : 'json';

        // Récupérer toutes les redirections
        $redirections = $wpdb->get_results( "SELECT * FROM $this->table_name ORDER BY id ASC" );

        if ( $format === 'csv' ) {
            $csv_data = array();
            $csv_data[] = array( 'source', 'target', 'type', 'status', 'match_case', 'regex', 'notes' );

            foreach ( $redirections as $redirection ) {
                $csv_data[] = array(
                    $redirection->source,
                    $redirection->target,
                    $redirection->type,
                    $redirection->status,
                    $redirection->match_case ? 'true' : 'false',
                    $redirection->regex ? 'true' : 'false',
                    $redirection->notes,
                );
            }

            return rest_ensure_response( array(
                'format' => 'csv',
                'data' => $csv_data,
            ) );
        }

        // Format JSON par défaut
        return rest_ensure_response( array(
            'format' => 'json',
            'data' => $redirections,
        ) );
    }

    /**
     * Teste une redirection.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function test_redirection( $request ) {
        $url = isset( $request['url'] ) ? esc_url_raw( $request['url'] ) : '';

        if ( empty( $url ) ) {
            return new WP_Error(
                'boss_seo_invalid_url',
                __( 'URL invalide.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        // Tester la redirection
        $response = wp_remote_head( $url, array( 'timeout' => 5, 'redirection' => 0 ) );

        if ( is_wp_error( $response ) ) {
            return new WP_Error(
                'boss_seo_test_failed',
                $response->get_error_message(),
                array( 'status' => 500 )
            );
        }

        $status_code = wp_remote_retrieve_response_code( $response );
        $location = wp_remote_retrieve_header( $response, 'location' );

        return rest_ensure_response( array(
            'url' => $url,
            'status_code' => $status_code,
            'location' => $location,
            'is_redirect' => in_array( $status_code, array( 301, 302, 303, 307, 308 ) ),
        ) );
    }

    /**
     * Gère les redirections.
     *
     * @since    1.1.0
     */
    public function handle_redirections() {
        // Ne pas traiter les requêtes admin ou AJAX
        if ( is_admin() || wp_doing_ajax() ) {
            return;
        }

        // Ne traiter que les requêtes 404
        if ( ! is_404() ) {
            return;
        }

        global $wpdb;
        $current_url = $_SERVER['REQUEST_URI'];

        // Récupérer toutes les redirections actives
        $redirections = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $this->table_name WHERE status = %s",
                'active'
            )
        );

        foreach ( $redirections as $redirection ) {
            $match = false;

            if ( $redirection->regex ) {
                // Traiter comme une expression régulière
                $pattern = $redirection->match_case ? $redirection->source : $redirection->source . 'i';
                $match = preg_match( '#' . $pattern . '#', $current_url );
            } else {
                // Traiter comme une correspondance directe
                if ( $redirection->match_case ) {
                    $match = $current_url === $redirection->source;
                } else {
                    $match = strtolower( $current_url ) === strtolower( $redirection->source );
                }
            }

            if ( $match ) {
                // Mettre à jour les statistiques
                $wpdb->update(
                    $this->table_name,
                    array(
                        'hits' => $redirection->hits + 1,
                        'last_used' => current_time( 'mysql' ),
                    ),
                    array( 'id' => $redirection->id ),
                    array( '%d', '%s' ),
                    array( '%d' )
                );

                // Enregistrer le log
                $this->log_redirection( $redirection->id, $current_url );

                // Effectuer la redirection
                wp_redirect( $redirection->target, $redirection->type );
                exit;
            }
        }
    }

    /**
     * Enregistre un log de redirection.
     *
     * @since    1.1.0
     * @param    int       $redirection_id    L'ID de la redirection.
     * @param    string    $url               L'URL qui a déclenché la redirection.
     */
    private function log_redirection( $redirection_id, $url ) {
        global $wpdb;

        $wpdb->insert(
            $this->logs_table_name,
            array(
                'redirection_id' => $redirection_id,
                'url' => $url,
                'user_agent' => isset( $_SERVER['HTTP_USER_AGENT'] ) ? $_SERVER['HTTP_USER_AGENT'] : '',
                'ip' => $this->get_client_ip(),
                'referer' => isset( $_SERVER['HTTP_REFERER'] ) ? $_SERVER['HTTP_REFERER'] : '',
            ),
            array( '%d', '%s', '%s', '%s', '%s' )
        );
    }

    /**
     * Récupère l'adresse IP du client.
     *
     * @since    1.1.0
     * @return   string    L'adresse IP du client.
     */
    private function get_client_ip() {
        $ip = '';

        if ( ! empty( $_SERVER['HTTP_CLIENT_IP'] ) ) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif ( ! empty( $_SERVER['HTTP_X_FORWARDED_FOR'] ) ) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }

        return $ip;
    }
}
