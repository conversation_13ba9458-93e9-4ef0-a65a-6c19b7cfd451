import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  ToggleControl,
  RangeControl,
  SelectControl,
  TextControl,
  Notice
} from '@wordpress/components';

const MediaSettings = ({ 
  settings, 
  setSettings, 
  onSave, 
  isSaving 
}) => {
  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Paramètres d\'optimisation', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
          <div>
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
              {__('Compression d\'images', 'boss-seo')}
            </h3>
            
            <div className="boss-space-y-4">
              <ToggleControl
                label={__('Activer la compression automatique', 'boss-seo')}
                help={__('Compresser automatiquement les images lors de leur téléchargement', 'boss-seo')}
                checked={settings.compression.enabled}
                onChange={(value) => setSettings({
                  ...settings,
                  compression: {
                    ...settings.compression,
                    enabled: value
                  }
                })}
              />
              
              <RangeControl
                label={__('Niveau de compression JPEG', 'boss-seo')}
                value={settings.compression.jpegQuality}
                onChange={(value) => setSettings({
                  ...settings,
                  compression: {
                    ...settings.compression,
                    jpegQuality: value
                  }
                })}
                min={1}
                max={100}
                help={__('Une valeur plus basse = fichier plus petit mais qualité réduite', 'boss-seo')}
              />
              
              <RangeControl
                label={__('Niveau de compression PNG', 'boss-seo')}
                value={settings.compression.pngQuality}
                onChange={(value) => setSettings({
                  ...settings,
                  compression: {
                    ...settings.compression,
                    pngQuality: value
                  }
                })}
                min={1}
                max={9}
                help={__('Une valeur plus haute = compression plus forte (1-9)', 'boss-seo')}
              />
              
              <ToggleControl
                label={__('Convertir en WebP', 'boss-seo')}
                help={__('Créer automatiquement des versions WebP des images', 'boss-seo')}
                checked={settings.compression.convertToWebP}
                onChange={(value) => setSettings({
                  ...settings,
                  compression: {
                    ...settings.compression,
                    convertToWebP: value
                  }
                })}
              />
              
              <ToggleControl
                label={__('Conserver les métadonnées EXIF', 'boss-seo')}
                help={__('Conserver les informations EXIF dans les images', 'boss-seo')}
                checked={settings.compression.keepExif}
                onChange={(value) => setSettings({
                  ...settings,
                  compression: {
                    ...settings.compression,
                    keepExif: value
                  }
                })}
              />
            </div>
          </div>
          
          <div>
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
              {__('Redimensionnement', 'boss-seo')}
            </h3>
            
            <div className="boss-space-y-4">
              <ToggleControl
                label={__('Redimensionner les grandes images', 'boss-seo')}
                help={__('Redimensionner automatiquement les images trop grandes', 'boss-seo')}
                checked={settings.resize.enabled}
                onChange={(value) => setSettings({
                  ...settings,
                  resize: {
                    ...settings.resize,
                    enabled: value
                  }
                })}
              />
              
              <div className="boss-grid boss-grid-cols-2 boss-gap-4">
                <TextControl
                  type="number"
                  label={__('Largeur maximale', 'boss-seo')}
                  value={settings.resize.maxWidth}
                  onChange={(value) => setSettings({
                    ...settings,
                    resize: {
                      ...settings.resize,
                      maxWidth: parseInt(value)
                    }
                  })}
                  help={__('En pixels', 'boss-seo')}
                />
                
                <TextControl
                  type="number"
                  label={__('Hauteur maximale', 'boss-seo')}
                  value={settings.resize.maxHeight}
                  onChange={(value) => setSettings({
                    ...settings,
                    resize: {
                      ...settings.resize,
                      maxHeight: parseInt(value)
                    }
                  })}
                  help={__('En pixels', 'boss-seo')}
                />
              </div>
              
              <SelectControl
                label={__('Méthode de redimensionnement', 'boss-seo')}
                value={settings.resize.method}
                options={[
                  { label: __('Proportionnel', 'boss-seo'), value: 'proportional' },
                  { label: __('Recadrage', 'boss-seo'), value: 'crop' },
                  { label: __('Exact', 'boss-seo'), value: 'exact' }
                ]}
                onChange={(value) => setSettings({
                  ...settings,
                  resize: {
                    ...settings.resize,
                    method: value
                  }
                })}
              />
            </div>
            
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mt-6 boss-mb-3">
              {__('Texte alternatif', 'boss-seo')}
            </h3>
            
            <div className="boss-space-y-4">
              <ToggleControl
                label={__('Générer automatiquement le texte alternatif', 'boss-seo')}
                help={__('Utiliser l\'IA pour générer automatiquement le texte alternatif lors du téléchargement', 'boss-seo')}
                checked={settings.altText.autoGenerate}
                onChange={(value) => setSettings({
                  ...settings,
                  altText: {
                    ...settings.altText,
                    autoGenerate: value
                  }
                })}
              />
              
              <SelectControl
                label={__('Modèle d\'IA', 'boss-seo')}
                value={settings.altText.aiModel}
                options={[
                  { label: __('Standard', 'boss-seo'), value: 'standard' },
                  { label: __('Avancé', 'boss-seo'), value: 'advanced' },
                  { label: __('Expert', 'boss-seo'), value: 'expert' }
                ]}
                onChange={(value) => setSettings({
                  ...settings,
                  altText: {
                    ...settings.altText,
                    aiModel: value
                  }
                })}
                disabled={!settings.altText.autoGenerate}
              />
            </div>
          </div>
        </div>
        
        <div className="boss-mt-6">
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
            {__('Lazy Loading', 'boss-seo')}
          </h3>
          
          <div className="boss-space-y-4">
            <ToggleControl
              label={__('Activer le lazy loading', 'boss-seo')}
              help={__('Charger les images uniquement lorsqu\'elles deviennent visibles', 'boss-seo')}
              checked={settings.lazyLoading.enabled}
              onChange={(value) => setSettings({
                ...settings,
                lazyLoading: {
                  ...settings.lazyLoading,
                  enabled: value
                }
              })}
            />
            
            <ToggleControl
              label={__('Utiliser le lazy loading natif', 'boss-seo')}
              help={__('Utiliser l\'attribut loading="lazy" natif du navigateur', 'boss-seo')}
              checked={settings.lazyLoading.useNative}
              onChange={(value) => setSettings({
                ...settings,
                lazyLoading: {
                  ...settings.lazyLoading,
                  useNative: value
                }
              })}
              disabled={!settings.lazyLoading.enabled}
            />
            
            <TextControl
              type="number"
              label={__('Marge de chargement', 'boss-seo')}
              value={settings.lazyLoading.threshold}
              onChange={(value) => setSettings({
                ...settings,
                lazyLoading: {
                  ...settings.lazyLoading,
                  threshold: parseInt(value)
                }
              })}
              help={__('Distance en pixels avant que l\'image ne soit visible', 'boss-seo')}
              disabled={!settings.lazyLoading.enabled || settings.lazyLoading.useNative}
            />
          </div>
        </div>
        
        <Notice status="info" isDismissible={false} className="boss-mt-6">
          <p>
            {__('Ces paramètres s\'appliquent aux nouvelles images téléchargées. Pour optimiser les images existantes, utilisez les options d\'optimisation par lot.', 'boss-seo')}
          </p>
        </Notice>
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200">
        <div className="boss-flex boss-justify-end">
          <Button
            isPrimary
            onClick={() => onSave(settings)}
            isBusy={isSaving}
            disabled={isSaving}
          >
            {isSaving ? __('Enregistrement...', 'boss-seo') : __('Enregistrer les paramètres', 'boss-seo')}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default MediaSettings;
