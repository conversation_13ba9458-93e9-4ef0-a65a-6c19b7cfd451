/**
 * Styles pour l'administration du module SEO Local
 */

/* <PERSON> généraux */
.boss-local-container {
    margin: 20px 0;
}

.boss-local-header {
    margin-bottom: 20px;
}

.boss-local-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
}

.boss-local-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
}

/* Tableaux */
.boss-local-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.boss-local-table th,
.boss-local-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.boss-local-table th {
    background-color: #f5f5f5;
    font-weight: 600;
}

/* Formulaires */
.boss-local-form {
    margin-bottom: 20px;
}

.boss-local-form-row {
    margin-bottom: 15px;
}

.boss-local-form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.boss-local-form-input,
.boss-local-form-textarea,
.boss-local-form-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.boss-local-form-textarea {
    min-height: 100px;
}

/* Boutons */
.boss-local-button {
    display: inline-block;
    padding: 8px 16px;
    background-color: #0073aa;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
}

.boss-local-button:hover {
    background-color: #005f8b;
}

.boss-local-button-secondary {
    background-color: #f7f7f7;
    color: #555;
    border: 1px solid #ccc;
}

.boss-local-button-secondary:hover {
    background-color: #eee;
}

/* Cartes */
.boss-local-card {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.boss-local-card-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.boss-local-card-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.boss-local-card-body {
    margin-bottom: 15px;
}

.boss-local-card-footer {
    border-top: 1px solid #eee;
    padding-top: 10px;
}

/* Notifications */
.boss-local-notice {
    padding: 10px 15px;
    margin-bottom: 20px;
    border-left: 4px solid #0073aa;
    background-color: #f7f7f7;
}

.boss-local-notice-success {
    border-left-color: #46b450;
}

.boss-local-notice-error {
    border-left-color: #dc3232;
}

.boss-local-notice-warning {
    border-left-color: #ffb900;
}

.boss-local-notice-info {
    border-left-color: #00a0d2;
}

/* Onglets */
.boss-local-tabs {
    margin-bottom: 20px;
}

.boss-local-tabs-nav {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.boss-local-tab {
    padding: 10px 15px;
    cursor: pointer;
    border: 1px solid transparent;
    border-bottom: none;
    margin-bottom: -1px;
}

.boss-local-tab-active {
    background-color: #fff;
    border-color: #ddd;
    border-bottom-color: #fff;
}

.boss-local-tab-content {
    display: none;
}

.boss-local-tab-content-active {
    display: block;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .boss-local-form-row {
        margin-bottom: 20px;
    }
    
    .boss-local-button {
        width: 100%;
        text-align: center;
    }
}
