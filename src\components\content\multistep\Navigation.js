/**
 * Composant de navigation pour le workflow multistep
 */
import { __ } from '@wordpress/i18n';
import { Button } from '@wordpress/components';

/**
 * Composant de navigation entre les étapes
 * 
 * @param {Object} props Propriétés du composant
 * @param {number} props.currentStep Étape actuelle
 * @param {number} props.totalSteps Nombre total d'étapes
 * @param {Function} props.onNext Fonction appelée pour passer à l'étape suivante
 * @param {Function} props.onPrevious Fonction appelée pour revenir à l'étape précédente
 * @param {Function} props.onReset Fonction appelée pour réinitialiser le workflow
 * @param {boolean} props.isNextDisabled Indique si le bouton "Suivant" est désactivé
 * @param {boolean} props.isPreviousDisabled Indique si le bouton "Précédent" est désactivé
 */
const Navigation = ({ 
  currentStep, 
  totalSteps, 
  onNext, 
  onPrevious, 
  onReset,
  isNextDisabled = false,
  isPreviousDisabled = false
}) => {
  // Déterminer le libellé du bouton "Suivant" en fonction de l'étape actuelle
  const getNextButtonLabel = () => {
    if (currentStep === totalSteps) {
      return __('Terminer', 'boss-seo');
    }
    return __('Suivant', 'boss-seo');
  };
  
  return (
    <div className="boss-flex boss-justify-between boss-items-center">
      <div>
        <Button
          isSecondary
          onClick={onReset}
          className="boss-mr-2"
        >
          {__('Réinitialiser', 'boss-seo')}
        </Button>
        
        <Button
          isSecondary
          onClick={onPrevious}
          disabled={isPreviousDisabled}
          className="boss-mr-2"
        >
          {__('Précédent', 'boss-seo')}
        </Button>
      </div>
      
      <div>
        {currentStep < totalSteps && (
          <Button
            isPrimary
            onClick={onNext}
            disabled={isNextDisabled}
          >
            {getNextButtonLabel()}
          </Button>
        )}
      </div>
    </div>
  );
};

export default Navigation;
