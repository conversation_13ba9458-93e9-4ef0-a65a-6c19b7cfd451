/**
 * Composant de barre de progression pour le workflow multistep
 */
import { __ } from '@wordpress/i18n';

/**
 * Composant de barre de progression
 * 
 * @param {Object} props Propriétés du composant
 * @param {number} props.currentStep Étape actuelle
 * @param {number} props.totalSteps Nombre total d'étapes
 */
const ProgressBar = ({ currentStep, totalSteps }) => {
  // Calculer le pourcentage de progression
  const progressPercentage = (currentStep / totalSteps) * 100;
  
  // Définir les étapes du workflow
  const steps = [
    { number: 1, label: __('Objectif SEO', 'boss-seo') },
    { number: 2, label: __('Recherche de mots-clés', 'boss-seo') },
    { number: 3, label: __('Génération de contenu', 'boss-seo') },
    { number: 4, label: __('Sélection d\'images', 'boss-seo') },
    { number: 5, label: __('Publication', 'boss-seo') },
  ];
  
  return (
    <div className="boss-mb-8">
      {/* Barre de progression */}
      <div className="boss-relative boss-mb-6">
        <div className="boss-h-2 boss-bg-gray-200 boss-rounded-full">
          <div 
            className="boss-h-2 boss-bg-boss-primary boss-rounded-full boss-transition-all boss-duration-300 boss-ease-in-out"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
        
        {/* Étapes */}
        <div className="boss-flex boss-justify-between boss-mt-2">
          {steps.map((step) => (
            <div 
              key={step.number}
              className={`boss-flex boss-flex-col boss-items-center boss-relative boss-transition-all boss-duration-300 boss-ease-in-out ${
                step.number <= currentStep ? 'boss-text-boss-primary' : 'boss-text-boss-gray'
              }`}
              style={{ 
                left: `${step.number === 1 ? '0%' : step.number === totalSteps ? '0%' : ''}`,
                transform: `translateX(${step.number === 1 ? '0%' : step.number === totalSteps ? '0%' : '-50%'})`,
              }}
            >
              {/* Indicateur d'étape */}
              <div 
                className={`boss-w-8 boss-h-8 boss-rounded-full boss-flex boss-items-center boss-justify-center boss-font-medium boss-text-sm boss-mb-2 boss-transition-all boss-duration-300 boss-ease-in-out ${
                  step.number < currentStep 
                    ? 'boss-bg-boss-primary boss-text-white' 
                    : step.number === currentStep
                      ? 'boss-bg-white boss-text-boss-primary boss-border-2 boss-border-boss-primary' 
                      : 'boss-bg-white boss-text-boss-gray boss-border-2 boss-border-gray-200'
                }`}
              >
                {step.number < currentStep ? (
                  <span className="dashicons dashicons-yes-alt"></span>
                ) : (
                  step.number
                )}
              </div>
              
              {/* Libellé de l'étape */}
              <span className={`boss-text-xs boss-font-medium boss-text-center boss-transition-all boss-duration-300 boss-ease-in-out ${
                step.number <= currentStep ? 'boss-text-boss-primary' : 'boss-text-boss-gray'
              }`}>
                {step.label}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProgressBar;
