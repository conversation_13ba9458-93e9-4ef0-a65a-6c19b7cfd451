/**
 * Service pour la gestion des en-têtes HTTP
 * 
 * Gère les communications avec l'API pour les fonctionnalités d'en-têtes HTTP
 */

import apiFetch from '@wordpress/api-fetch';

class HttpHeadersService {
  /**
   * Récupère la liste des en-têtes HTTP
   * 
   * @returns {Promise} Promesse contenant la liste des en-têtes HTTP
   */
  async getHeaders() {
    try {
      const path = '/boss-seo/v1/http-headers';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des en-têtes HTTP:', error);
      throw error;
    }
  }

  /**
   * Ajoute un nouvel en-tête HTTP
   * 
   * @param {Object} header - Données de l'en-tête
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async addHeader(header) {
    try {
      const path = '/boss-seo/v1/http-headers';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: header
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'en-tête HTTP:', error);
      throw error;
    }
  }

  /**
   * Met à jour un en-tête HTTP existant
   * 
   * @param {number} id - ID de l'en-tête
   * @param {Object} header - Nouvelles données de l'en-tête
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async updateHeader(id, header) {
    try {
      const path = `/boss-seo/v1/http-headers/${id}`;
      const response = await apiFetch({
        path,
        method: 'PUT',
        data: header
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'en-tête HTTP:', error);
      throw error;
    }
  }

  /**
   * Supprime un en-tête HTTP
   * 
   * @param {number} id - ID de l'en-tête
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async deleteHeader(id) {
    try {
      const path = `/boss-seo/v1/http-headers/${id}`;
      const response = await apiFetch({
        path,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'en-tête HTTP:', error);
      throw error;
    }
  }

  /**
   * Active ou désactive un en-tête HTTP
   * 
   * @param {number} id - ID de l'en-tête
   * @param {boolean} active - État d'activation
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async toggleHeader(id, active) {
    try {
      const path = `/boss-seo/v1/http-headers/${id}/toggle`;
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { active }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'activation/désactivation de l\'en-tête HTTP:', error);
      throw error;
    }
  }

  /**
   * Récupère les modèles d'en-têtes prédéfinis
   * 
   * @returns {Promise} Promesse contenant les modèles d'en-têtes
   */
  async getHeaderTemplates() {
    try {
      const path = '/boss-seo/v1/http-headers/templates';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des modèles d\'en-têtes:', error);
      throw error;
    }
  }

  /**
   * Teste les en-têtes HTTP actuels
   * 
   * @param {string} url - URL à tester (optionnel)
   * @returns {Promise} Promesse contenant les résultats du test
   */
  async testHeaders(url = null) {
    try {
      let path = '/boss-seo/v1/http-headers/test';
      if (url) {
        path += `?url=${encodeURIComponent(url)}`;
      }
      
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du test des en-têtes HTTP:', error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres de préconnexion
   * 
   * @returns {Promise} Promesse contenant les paramètres de préconnexion
   */
  async getPreconnectSettings() {
    try {
      const path = '/boss-seo/v1/http-headers/preconnect';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres de préconnexion:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres de préconnexion
   * 
   * @param {Array} domains - Liste des domaines pour la préconnexion
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async savePreconnectSettings(domains) {
    try {
      const path = '/boss-seo/v1/http-headers/preconnect';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { domains }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres de préconnexion:', error);
      throw error;
    }
  }
}

export default new HttpHeadersService();
