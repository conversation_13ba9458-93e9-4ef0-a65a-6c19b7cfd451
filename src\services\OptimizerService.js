/**
 * Service pour gérer les appels API du module Boss Optimizer
 */
import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

/**
 * Classe de service pour le module Boss Optimizer
 */
class OptimizerService {
  /**
   * Récupère la liste des contenus avec filtrage et pagination
   *
   * @param {Object} filters - Les filtres à appliquer
   * @param {string} filters.contentType - Type de contenu (all, post, page, etc.)
   * @param {string} filters.seoScore - Score SEO (all, excellent, good, average, poor)
   * @param {string} filters.status - Statut (all, publish, draft, etc.)
   * @param {string} filters.author - Auteur (all ou ID)
   * @param {string} filters.dateRange - Plage de dates (all, today, week, month, year)
   * @param {string} searchQuery - Terme de recherche
   * @param {number} page - Numéro de page
   * @param {number} perPage - Nombre d'éléments par page
   * @param {string} orderby - Champ de tri (date, title, seoScore, etc.)
   * @param {string} order - Ordre de tri (ASC, DESC)
   * @returns {Promise} - Promesse contenant les résultats
   */
  async getContents(filters = {}, searchQuery = '', page = 1, perPage = 10, orderby = 'date', order = 'DESC') {
    try {
      // Convertir les filtres pour l'API
      const apiFilters = {
        content_type: filters.contentType || 'all',
        seo_score: filters.seoScore || 'all',
        status: filters.status || 'all',
        author: filters.author || 'all',
        date_range: filters.dateRange || 'all',
        search: searchQuery,
        page,
        per_page: perPage,
        orderby,
        order
      };

      // Construire l'URL avec les paramètres
      const path = addQueryArgs('/boss-seo/v1/contents', apiFilters);

      // Effectuer la requête
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des contenus:', error);
      throw error;
    }
  }

  /**
   * Récupère les détails d'un contenu spécifique
   *
   * @param {number} id - ID du contenu
   * @returns {Promise} - Promesse contenant les détails du contenu
   */
  async getContent(id) {
    try {
      const path = `/boss-seo/v1/contents/${id}`;
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération du contenu ${id}:`, error);
      throw error;
    }
  }

  /**
   * Analyse un contenu
   *
   * @param {number} id - ID du contenu à analyser
   * @returns {Promise} - Promesse contenant les résultats de l'analyse
   */
  async analyzeContent(id) {
    try {
      const path = '/boss-seo/v1/analyze';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { id }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'analyse du contenu ${id}:`, error);
      throw error;
    }
  }

  /**
   * Analyse plusieurs contenus en masse
   *
   * @param {Array} ids - Liste des IDs des contenus à analyser
   * @returns {Promise} - Promesse contenant les résultats de l'analyse
   */
  async analyzeContentsBulk(ids) {
    try {
      const path = '/boss-seo/v1/analyze/bulk';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { ids }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'analyse en masse des contenus:', error);
      throw error;
    }
  }

  /**
   * Optimise un contenu
   *
   * @param {number} id - ID du contenu à optimiser
   * @param {Object} settings - Paramètres d'optimisation
   * @returns {Promise} - Promesse contenant les résultats de l'optimisation
   */
  async optimizeContent(id, settings = {}) {
    try {
      // Par défaut, on ne modifie que les métadonnées SEO
      const defaultSettings = {
        title_seo: true,
        meta_description: true,
        keywords: true,
        modify_content: false,
        modify_title: false,
        modify_headings: false,
        modify_images: false,
        modify_links: false,
        useAI: true
      };

      // Fusionner les paramètres par défaut avec ceux fournis
      const mergedSettings = { ...defaultSettings, ...settings };

      const path = `/boss-seo/v1/optimize/${id}`;
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { settings: mergedSettings }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'optimisation du contenu ${id}:`, error);
      throw error;
    }
  }

  /**
   * Optimise plusieurs contenus en masse
   *
   * @param {Array} ids - Liste des IDs des contenus à optimiser
   * @param {Object} settings - Paramètres d'optimisation
   * @returns {Promise} - Promesse contenant les résultats de l'optimisation
   */
  async optimizeContentsBulk(ids, settings = {}) {
    try {
      // Par défaut, on ne modifie que les métadonnées SEO
      const defaultSettings = {
        title_seo: true,
        meta_description: true,
        keywords: true,
        modify_content: false,
        modify_title: false,
        modify_headings: false,
        modify_images: false,
        modify_links: false,
        useAI: true
      };

      // Fusionner les paramètres par défaut avec ceux fournis
      const mergedSettings = { ...defaultSettings, ...settings };

      const path = '/boss-seo/v1/optimize/bulk';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { ids, settings: mergedSettings }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'optimisation en masse des contenus:', error);
      throw error;
    }
  }

  /**
   * Récupère les recommandations pour un contenu
   *
   * @param {number} id - ID du contenu
   * @returns {Promise} - Promesse contenant les recommandations
   */
  async getRecommendations(id) {
    try {
      const path = `/boss-seo/v1/recommendations/${id}`;
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération des recommandations pour le contenu ${id}:`, error);
      throw error;
    }
  }

  /**
   * Applique une recommandation spécifique
   *
   * @param {number} postId - ID du contenu
   * @param {number} recommendationId - ID de la recommandation à appliquer
   * @returns {Promise} - Promesse contenant le résultat de l'application
   */
  async applyRecommendation(postId, recommendationId) {
    try {
      const path = '/boss-seo/v1/recommendations/apply';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { post_id: postId, recommendation_id: recommendationId }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'application de la recommandation ${recommendationId} pour le contenu ${postId}:`, error);
      throw error;
    }
  }

  /**
   * Vérifie la validité d'une clé API
   *
   * @param {string} provider - Le fournisseur de l'API (openai, anthropic, etc.)
   * @param {string} apiKey - La clé API à vérifier
   * @returns {Promise} - Promesse contenant le résultat de la vérification
   */
  async verifyApiKey(provider, apiKey) {
    try {
      const path = '/boss-seo/v1/verify-api-key';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { provider, api_key: apiKey }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la vérification de la clé API ${provider}:`, error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres API
   *
   * @param {Object} settings - Les paramètres à enregistrer
   * @returns {Promise} - Promesse contenant le résultat de l'enregistrement
   */
  async saveSettings(settings) {
    try {
      const path = '/boss-seo/v1/save-settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { settings }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres:', error);
      throw error;
    }
  }

  /**
   * Optimise tous les contenus
   *
   * @param {Object} settings - Paramètres d'optimisation
   * @returns {Promise} - Promesse contenant les résultats de l'optimisation
   */
  async optimizeAllContents(settings = {}) {
    try {
      // Par défaut, on ne modifie que les métadonnées SEO
      const defaultSettings = {
        title_seo: true,
        meta_description: true,
        keywords: true,
        modify_content: false,
        modify_title: false,
        modify_headings: false,
        modify_images: false,
        modify_links: false,
        useAI: true
      };

      // Fusionner les paramètres par défaut avec ceux fournis
      const mergedSettings = { ...defaultSettings, ...settings };

      const path = '/boss-seo/v1/optimize/all';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { settings: mergedSettings }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'optimisation de tous les contenus:', error);
      throw error;
    }
  }

  /**
   * Analyse tous les contenus
   *
   * @returns {Promise} - Promesse contenant les résultats de l'analyse
   */
  async analyzeAllContents() {
    try {
      const path = '/boss-seo/v1/analyze/all';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'analyse de tous les contenus:', error);
      throw error;
    }
  }

  /**
   * Ajoute des balises à plusieurs contenus
   *
   * @param {Array} ids - Liste des IDs des contenus
   * @param {Array} tags - Liste des balises à ajouter
   * @returns {Promise} - Promesse contenant les résultats de l'opération
   */
  async addTagsToContents(ids, tags) {
    try {
      const path = '/boss-seo/v1/tags/add';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { ids, tags }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'ajout des balises:', error);
      throw error;
    }
  }

  /**
   * Change la catégorie de plusieurs contenus
   *
   * @param {Array} ids - Liste des IDs des contenus
   * @param {number} categoryId - ID de la catégorie
   * @returns {Promise} - Promesse contenant les résultats de l'opération
   */
  async changeCategoryForContents(ids, categoryId) {
    try {
      const path = '/boss-seo/v1/category/change';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { ids, category_id: categoryId }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la modification de la catégorie:', error);
      throw error;
    }
  }

  /**
   * Change l'auteur de plusieurs contenus
   *
   * @param {Array} ids - Liste des IDs des contenus
   * @param {number} authorId - ID de l'auteur
   * @returns {Promise} - Promesse contenant les résultats de l'opération
   */
  async changeAuthorForContents(ids, authorId) {
    try {
      const path = '/boss-seo/v1/author/change';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { ids, author_id: authorId }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du changement d\'auteur:', error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres d'IA
   *
   * @returns {Promise} - Promesse contenant les paramètres d'IA
   */
  async getAiSettings() {
    try {
      const path = '/boss-seo/v1/ai/settings';
      const response = await apiFetch({ path });

      // Convertir les noms de paramètres pour correspondre à l'interface utilisateur
      if (response && typeof response === 'object') {
        const convertedResponse = {
          provider: response.provider || 'openai',
          openaiApiKey: response.openai_api_key || '',
          openaiModel: response.openai_model || 'gpt-4o',
          openaiTemperature: response.openai_temperature || 0.7,
          anthropicApiKey: response.anthropic_api_key || '',
          anthropicModel: response.anthropic_model || 'claude-3-opus',
          anthropicTemperature: response.anthropic_temperature || 0.7,
          geminiApiKey: response.gemini_api_key || '',
          geminiModel: response.gemini_model || 'gemini-1.5-pro',
          geminiTemperature: response.gemini_temperature || 0.7,
          useAiForTitles: response.use_ai_for_titles !== undefined ? response.use_ai_for_titles : true,
          useAiForDescriptions: response.use_ai_for_descriptions !== undefined ? response.use_ai_for_descriptions : true,
          useAiForContent: response.use_ai_for_content !== undefined ? response.use_ai_for_content : true,
          useAiForAltText: response.use_ai_for_alt_text !== undefined ? response.use_ai_for_alt_text : true
        };
        return convertedResponse;
      }

      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres d\'IA:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres d'IA
   *
   * @param {Object} settings - Les paramètres d'IA à enregistrer
   * @returns {Promise} - Promesse contenant le résultat de l'enregistrement
   */
  async saveAiSettings(settings) {
    try {
      // Convertir les noms de paramètres pour correspondre au backend
      const convertedSettings = {
        provider: settings.provider,
        openai_api_key: settings.openaiApiKey,
        openai_model: settings.openaiModel,
        openai_temperature: settings.openaiTemperature,
        anthropic_api_key: settings.anthropicApiKey,
        anthropic_model: settings.anthropicModel,
        anthropic_temperature: settings.anthropicTemperature,
        gemini_api_key: settings.geminiApiKey,
        gemini_model: settings.geminiModel,
        gemini_temperature: settings.geminiTemperature,
        use_ai_for_titles: settings.useAiForTitles,
        use_ai_for_descriptions: settings.useAiForDescriptions,
        use_ai_for_content: settings.useAiForContent,
        use_ai_for_alt_text: settings.useAiForAltText
      };

      const path = '/boss-seo/v1/ai/settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { settings: convertedSettings }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres d\'IA:', error);
      throw error;
    }
  }

  /**
   * Teste une clé API d'IA
   *
   * @param {string} provider - Le fournisseur de l'API (openai, gemini, anthropic)
   * @param {string} apiKey - La clé API à tester
   * @returns {Promise} - Promesse contenant le résultat du test
   */
  async testAiApiKey(provider, apiKey) {
    try {
      const path = '/boss-seo/v1/ai/test-api-key';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { provider, api_key: apiKey }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors du test de la clé API ${provider}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres des services externes
   *
   * @returns {Promise} - Promesse contenant les paramètres des services externes
   */
  async getExternalServicesSettings() {
    try {
      const path = '/boss-seo/v1/external-services/settings';
      const response = await apiFetch({ path });

      // Convertir les noms de paramètres si nécessaire pour correspondre à l'interface utilisateur
      if (response && typeof response === 'object') {
        // Ici, nous pouvons ajouter une conversion si nécessaire
        // Pour l'instant, nous retournons la réponse telle quelle
      }

      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres des services externes:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres des services externes
   *
   * @param {Object} settings - Les paramètres des services externes à enregistrer
   * @returns {Promise} - Promesse contenant le résultat de l'enregistrement
   */
  async saveExternalServicesSettings(settings) {
    try {
      const path = '/boss-seo/v1/external-services/settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { settings }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres des services externes:', error);
      throw error;
    }
  }

  /**
   * Génère du contenu avec l'IA
   *
   * @param {string} prompt - Le prompt à envoyer à l'IA
   * @param {Object} options - Options supplémentaires pour la génération
   * @returns {Promise} - Promesse contenant le contenu généré
   */
  async generateContent(prompt, options = {}) {
    try {
      const path = '/boss-seo/v1/ai/generate-content';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          prompt,
          options
        }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la génération de contenu avec l\'IA:', error);
      throw error;
    }
  }

  /**
   * Vérifie une clé API pour un service externe
   *
   * @param {string} service - Le service (pagespeed, tinypng, etc.)
   * @param {string} apiKey - La clé API à vérifier
   * @returns {Promise} - Promesse contenant le résultat de la vérification
   */
  async verifyExternalServiceApiKey(service, apiKey) {
    try {
      const path = '/boss-seo/v1/external-services/verify-api-key';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { service, api_key: apiKey }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la vérification de la clé API ${service}:`, error);
      throw error;
    }
  }

  /**
   * Récupère la liste des services externes disponibles
   *
   * @returns {Promise} - Promesse contenant la liste des services disponibles
   */
  async getAvailableExternalServices() {
    try {
      const path = '/boss-seo/v1/external-services/available';
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des services externes disponibles:', error);
      throw error;
    }
  }

  /**
   * Récupère le statut des services externes
   *
   * @returns {Promise} - Promesse contenant le statut des services
   */
  async getExternalServicesStatus() {
    try {
      const path = '/boss-seo/v1/external-services/status';
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération du statut des services externes:', error);
      throw error;
    }
  }

  /**
   * Récupère l'URL d'authentification Google
   *
   * @returns {Promise} - Promesse contenant l'URL d'authentification
   */
  async getGoogleAuthUrl() {
    try {
      const path = '/boss-seo/v1/external-services/google-auth-url';
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'URL d\'authentification Google:', error);
      throw error;
    }
  }

  /**
   * Connecte Google Search Console
   *
   * @param {string} authCode - Code d'autorisation
   * @returns {Promise} - Promesse contenant le résultat de la connexion
   */
  async connectGoogleSearchConsole(authCode) {
    try {
      const path = '/boss-seo/v1/external-services/connect-google-search-console';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { auth_code: authCode }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la connexion à Google Search Console:', error);
      throw error;
    }
  }

  /**
   * Déconnecte Google Search Console
   *
   * @returns {Promise} - Promesse contenant le résultat de la déconnexion
   */
  async disconnectGoogleSearchConsole() {
    try {
      const path = '/boss-seo/v1/external-services/disconnect-google-search-console';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la déconnexion de Google Search Console:', error);
      throw error;
    }
  }
}

// Exporter une instance unique du service
const optimizerService = new OptimizerService();
export default optimizerService;
