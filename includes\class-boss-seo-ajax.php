<?php
/**
 * Classe pour gérer les requêtes AJAX du module de paramètres du plugin Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.0.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe pour gérer les requêtes AJAX du module de paramètres du plugin Boss SEO.
 *
 * Cette classe gère les requêtes AJAX spécifiques au module de paramètres,
 * telles que la mise à jour des paramètres, la création de sauvegardes, etc.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Settings_AJAX {

    /**
     * Le nom du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Boss_SEO_Settings    $settings    Instance de la classe de paramètres.
     */
    protected $settings;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.0.0
     * @param    string             $plugin_name    Le nom du plugin.
     * @param    string             $version        La version du plugin.
     * @param    Boss_SEO_Settings  $settings       Instance de la classe de paramètres.
     */
    public function __construct( $plugin_name, $version, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.0.0
     */
    public function register_hooks() {
        // Hooks pour les requêtes AJAX
        add_action( 'wp_ajax_boss_seo_save_general_settings', array( $this, 'save_general_settings' ) );
        add_action( 'wp_ajax_boss_seo_save_advanced_settings', array( $this, 'save_advanced_settings' ) );
        add_action( 'wp_ajax_boss_seo_save_backup_settings', array( $this, 'save_backup_settings' ) );
        add_action( 'wp_ajax_boss_seo_create_backup', array( $this, 'create_backup' ) );
        add_action( 'wp_ajax_boss_seo_restore_backup', array( $this, 'restore_backup' ) );
        add_action( 'wp_ajax_boss_seo_delete_backup', array( $this, 'delete_backup' ) );
        add_action( 'wp_ajax_boss_seo_save_user_preferences', array( $this, 'save_user_preferences' ) );
        add_action( 'wp_ajax_boss_seo_reset_user_preferences', array( $this, 'reset_user_preferences' ) );
        add_action( 'wp_ajax_boss_seo_mark_notification_read', array( $this, 'mark_notification_read' ) );
    }

    /**
     * Enregistre les paramètres généraux via AJAX.
     *
     * @since    1.0.0
     */
    public function save_general_settings() {
        // Vérifie les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array(
                'message' => __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'boss-seo' ),
            ) );
        }

        // Vérifie le nonce
        if ( ! check_ajax_referer( 'boss_seo_save_general_settings', 'nonce', false ) ) {
            wp_send_json_error( array(
                'message' => __( 'Erreur de sécurité. Veuillez actualiser la page et réessayer.', 'boss-seo' ),
            ) );
        }

        // Récupère les paramètres
        $settings = isset( $_POST['settings'] ) ? json_decode( stripslashes( $_POST['settings'] ), true ) : array();

        // Enregistre les paramètres
        $result = $this->settings->get_general_settings()->save_settings( $settings );

        if ( $result ) {
            wp_send_json_success( array(
                'message' => __( 'Paramètres généraux enregistrés avec succès.', 'boss-seo' ),
                'settings' => $this->settings->get_general_settings()->get_settings(),
            ) );
        } else {
            wp_send_json_error( array(
                'message' => __( 'Impossible d\'enregistrer les paramètres généraux.', 'boss-seo' ),
            ) );
        }
    }

    /**
     * Enregistre les paramètres avancés via AJAX.
     *
     * @since    1.0.0
     */
    public function save_advanced_settings() {
        // Vérifie les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array(
                'message' => __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'boss-seo' ),
            ) );
        }

        // Vérifie le nonce
        if ( ! check_ajax_referer( 'boss_seo_save_advanced_settings', 'nonce', false ) ) {
            wp_send_json_error( array(
                'message' => __( 'Erreur de sécurité. Veuillez actualiser la page et réessayer.', 'boss-seo' ),
            ) );
        }

        // Récupère les paramètres
        $settings = isset( $_POST['settings'] ) ? json_decode( stripslashes( $_POST['settings'] ), true ) : array();

        // Enregistre les paramètres
        $result = $this->settings->get_advanced_settings()->save_settings( $settings );

        if ( $result ) {
            wp_send_json_success( array(
                'message' => __( 'Paramètres avancés enregistrés avec succès.', 'boss-seo' ),
                'settings' => $this->settings->get_advanced_settings()->get_settings(),
            ) );
        } else {
            wp_send_json_error( array(
                'message' => __( 'Impossible d\'enregistrer les paramètres avancés.', 'boss-seo' ),
            ) );
        }
    }

    /**
     * Enregistre les paramètres de sauvegarde via AJAX.
     *
     * @since    1.0.0
     */
    public function save_backup_settings() {
        // Vérifie les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array(
                'message' => __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'boss-seo' ),
            ) );
        }

        // Vérifie le nonce
        if ( ! check_ajax_referer( 'boss_seo_save_backup_settings', 'nonce', false ) ) {
            wp_send_json_error( array(
                'message' => __( 'Erreur de sécurité. Veuillez actualiser la page et réessayer.', 'boss-seo' ),
            ) );
        }

        // Récupère les paramètres
        $settings = isset( $_POST['settings'] ) ? json_decode( stripslashes( $_POST['settings'] ), true ) : array();

        // Enregistre les paramètres
        $result = $this->settings->get_backup_manager()->save_settings( $settings );

        if ( $result ) {
            wp_send_json_success( array(
                'message' => __( 'Paramètres de sauvegarde enregistrés avec succès.', 'boss-seo' ),
                'settings' => $this->settings->get_backup_manager()->get_settings(),
            ) );
        } else {
            wp_send_json_error( array(
                'message' => __( 'Impossible d\'enregistrer les paramètres de sauvegarde.', 'boss-seo' ),
            ) );
        }
    }

    /**
     * Crée une sauvegarde via AJAX.
     *
     * @since    1.0.0
     */
    public function create_backup() {
        // Vérifie les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array(
                'message' => __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'boss-seo' ),
            ) );
        }

        // Vérifie le nonce
        if ( ! check_ajax_referer( 'boss_seo_create_backup', 'nonce', false ) ) {
            wp_send_json_error( array(
                'message' => __( 'Erreur de sécurité. Veuillez actualiser la page et réessayer.', 'boss-seo' ),
            ) );
        }

        // Récupère les paramètres
        $name = isset( $_POST['name'] ) ? sanitize_text_field( $_POST['name'] ) : '';
        $include_settings = isset( $_POST['include_settings'] ) ? (bool) $_POST['include_settings'] : true;
        $include_data = isset( $_POST['include_data'] ) ? (bool) $_POST['include_data'] : true;

        // Crée la sauvegarde
        $result = $this->settings->get_backup_manager()->create_backup( $name, $include_settings, $include_data );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array(
                'message' => $result->get_error_message(),
            ) );
        } else {
            wp_send_json_success( array(
                'message' => __( 'Sauvegarde créée avec succès.', 'boss-seo' ),
                'backup' => $result,
            ) );
        }
    }

    /**
     * Restaure une sauvegarde via AJAX.
     *
     * @since    1.0.0
     */
    public function restore_backup() {
        // Vérifie les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array(
                'message' => __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'boss-seo' ),
            ) );
        }

        // Vérifie le nonce
        if ( ! check_ajax_referer( 'boss_seo_restore_backup', 'nonce', false ) ) {
            wp_send_json_error( array(
                'message' => __( 'Erreur de sécurité. Veuillez actualiser la page et réessayer.', 'boss-seo' ),
            ) );
        }

        // Récupère les paramètres
        $name = isset( $_POST['name'] ) ? sanitize_text_field( $_POST['name'] ) : '';

        // Restaure la sauvegarde
        $result = $this->settings->get_backup_manager()->restore_backup( $name );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array(
                'message' => $result->get_error_message(),
            ) );
        } else {
            wp_send_json_success( array(
                'message' => __( 'Sauvegarde restaurée avec succès.', 'boss-seo' ),
            ) );
        }
    }

    /**
     * Supprime une sauvegarde via AJAX.
     *
     * @since    1.0.0
     */
    public function delete_backup() {
        // Vérifie les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array(
                'message' => __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'boss-seo' ),
            ) );
        }

        // Vérifie le nonce
        if ( ! check_ajax_referer( 'boss_seo_delete_backup', 'nonce', false ) ) {
            wp_send_json_error( array(
                'message' => __( 'Erreur de sécurité. Veuillez actualiser la page et réessayer.', 'boss-seo' ),
            ) );
        }

        // Récupère les paramètres
        $name = isset( $_POST['name'] ) ? sanitize_text_field( $_POST['name'] ) : '';

        // Supprime la sauvegarde
        $result = $this->settings->get_backup_manager()->delete_backup( $name );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array(
                'message' => $result->get_error_message(),
            ) );
        } else {
            wp_send_json_success( array(
                'message' => __( 'Sauvegarde supprimée avec succès.', 'boss-seo' ),
            ) );
        }
    }

    /**
     * Enregistre les préférences utilisateur via AJAX.
     *
     * @since    1.0.0
     */
    public function save_user_preferences() {
        // Vérifie les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array(
                'message' => __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'boss-seo' ),
            ) );
        }

        // Vérifie le nonce
        if ( ! check_ajax_referer( 'boss_seo_save_user_preferences', 'nonce', false ) ) {
            wp_send_json_error( array(
                'message' => __( 'Erreur de sécurité. Veuillez actualiser la page et réessayer.', 'boss-seo' ),
            ) );
        }

        // Récupère les paramètres
        $preferences = isset( $_POST['preferences'] ) ? json_decode( stripslashes( $_POST['preferences'] ), true ) : array();

        // Enregistre les préférences
        $user_id = get_current_user_id();
        $result = $this->settings->get_user_preferences()->save_user_preferences( $user_id, $preferences );

        if ( $result ) {
            wp_send_json_success( array(
                'message' => __( 'Préférences utilisateur enregistrées avec succès.', 'boss-seo' ),
                'preferences' => $this->settings->get_user_preferences()->get_user_preferences( $user_id ),
            ) );
        } else {
            wp_send_json_error( array(
                'message' => __( 'Impossible d\'enregistrer les préférences utilisateur.', 'boss-seo' ),
            ) );
        }
    }

    /**
     * Réinitialise les préférences utilisateur via AJAX.
     *
     * @since    1.0.0
     */
    public function reset_user_preferences() {
        // Vérifie les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array(
                'message' => __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'boss-seo' ),
            ) );
        }

        // Vérifie le nonce
        if ( ! check_ajax_referer( 'boss_seo_reset_user_preferences', 'nonce', false ) ) {
            wp_send_json_error( array(
                'message' => __( 'Erreur de sécurité. Veuillez actualiser la page et réessayer.', 'boss-seo' ),
            ) );
        }

        // Réinitialise les préférences
        $user_id = get_current_user_id();
        $result = $this->settings->get_user_preferences()->reset_user_preferences( $user_id );

        if ( $result ) {
            wp_send_json_success( array(
                'message' => __( 'Préférences utilisateur réinitialisées avec succès.', 'boss-seo' ),
                'preferences' => $this->settings->get_user_preferences()->get_user_preferences( $user_id ),
            ) );
        } else {
            wp_send_json_error( array(
                'message' => __( 'Impossible de réinitialiser les préférences utilisateur.', 'boss-seo' ),
            ) );
        }
    }

    /**
     * Marque une notification comme lue via AJAX.
     *
     * @since    1.0.0
     */
    public function mark_notification_read() {
        // Vérifie les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array(
                'message' => __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'boss-seo' ),
            ) );
        }

        // Vérifie le nonce
        if ( ! check_ajax_referer( 'boss_seo_mark_notification_read', 'nonce', false ) ) {
            wp_send_json_error( array(
                'message' => __( 'Erreur de sécurité. Veuillez actualiser la page et réessayer.', 'boss-seo' ),
            ) );
        }

        // Récupère les paramètres
        $notification_id = isset( $_POST['notification_id'] ) ? sanitize_text_field( $_POST['notification_id'] ) : '';

        // Marque la notification comme lue
        $user_id = get_current_user_id();
        $result = $this->settings->get_user_preferences()->mark_notification_read( $user_id, $notification_id );

        if ( $result ) {
            wp_send_json_success( array(
                'message' => __( 'Notification marquée comme lue.', 'boss-seo' ),
            ) );
        } else {
            wp_send_json_error( array(
                'message' => __( 'Impossible de marquer la notification comme lue.', 'boss-seo' ),
            ) );
        }
    }
}
