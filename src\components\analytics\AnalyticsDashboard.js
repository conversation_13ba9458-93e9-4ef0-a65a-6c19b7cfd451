import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  ButtonGroup,
  Dashicon,
  SelectControl,
  ToggleControl
} from '@wordpress/components';
import { TrafficChart, KeywordsChart, PagesDistributionChart } from './ChartComponents';

const AnalyticsDashboard = ({ data, selectedPeriod, onPeriodChange }) => {
  // États
  const [visibleWidgets, setVisibleWidgets] = useState({
    traffic: true,
    keywords: true,
    pages: true,
    topMetrics: true
  });
  
  // Fonction pour basculer la visibilité d'un widget
  const toggleWidget = (widget) => {
    setVisibleWidgets({
      ...visibleWidgets,
      [widget]: !visibleWidgets[widget]
    });
  };
  
  // Fonction pour formater les métriques
  const formatMetric = (value, type = 'number') => {
    if (type === 'percentage') {
      return `${value.toFixed(2)}%`;
    } else if (type === 'time') {
      const minutes = Math.floor(value / 60);
      const seconds = Math.floor(value % 60);
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return new Intl.NumberFormat('fr-FR').format(value);
    }
  };
  
  // Fonction pour calculer la variation
  const calculateChange = (current, previous) => {
    if (!previous) return 0;
    return ((current - previous) / previous) * 100;
  };
  
  // Fonction pour obtenir la classe de couleur en fonction de la variation
  const getChangeColorClass = (change, isInverse = false) => {
    if (change === 0) return 'boss-text-boss-gray';
    if (isInverse) {
      return change > 0 ? 'boss-text-red-500' : 'boss-text-green-500';
    }
    return change > 0 ? 'boss-text-green-500' : 'boss-text-red-500';
  };
  
  // Fonction pour obtenir l'icône en fonction de la variation
  const getChangeIcon = (change, isInverse = false) => {
    if (change === 0) return 'minus';
    if (isInverse) {
      return change > 0 ? 'arrow-up' : 'arrow-down';
    }
    return change > 0 ? 'arrow-up' : 'arrow-down';
  };
  
  // Calculer les métriques globales
  const calculateOverallMetrics = () => {
    if (!data || !data.traffic || data.traffic.length === 0) {
      return {
        users: { value: 0, change: 0 },
        sessions: { value: 0, change: 0 },
        pageviews: { value: 0, change: 0 },
        bounceRate: { value: 0, change: 0 }
      };
    }
    
    // Calculer les totaux pour la période actuelle
    const currentPeriod = data.traffic;
    const totalUsers = currentPeriod.reduce((sum, day) => sum + day.users, 0);
    const totalSessions = currentPeriod.reduce((sum, day) => sum + day.sessions, 0);
    const totalPageviews = currentPeriod.reduce((sum, day) => sum + day.pageviews, 0);
    const avgBounceRate = currentPeriod.reduce((sum, day) => sum + day.bounceRate, 0) / currentPeriod.length;
    
    // Simuler les totaux pour la période précédente (pour l'exemple)
    const previousUsers = totalUsers * 0.9; // -10%
    const previousSessions = totalSessions * 0.85; // -15%
    const previousPageviews = totalPageviews * 0.95; // -5%
    const previousBounceRate = avgBounceRate * 1.05; // +5%
    
    return {
      users: { 
        value: totalUsers, 
        change: calculateChange(totalUsers, previousUsers)
      },
      sessions: { 
        value: totalSessions, 
        change: calculateChange(totalSessions, previousSessions)
      },
      pageviews: { 
        value: totalPageviews, 
        change: calculateChange(totalPageviews, previousPageviews)
      },
      bounceRate: { 
        value: avgBounceRate, 
        change: calculateChange(avgBounceRate, previousBounceRate)
      }
    };
  };
  
  // Calculer les métriques de mots-clés
  const calculateKeywordMetrics = () => {
    if (!data || !data.keywords || data.keywords.length === 0) {
      return {
        totalKeywords: { value: 0, change: 0 },
        avgPosition: { value: 0, change: 0 },
        totalClicks: { value: 0, change: 0 },
        avgCTR: { value: 0, change: 0 }
      };
    }
    
    const keywords = data.keywords;
    const totalKeywords = keywords.length;
    const avgPosition = keywords.reduce((sum, kw) => sum + kw.position, 0) / totalKeywords;
    const totalClicks = keywords.reduce((sum, kw) => sum + kw.clicks, 0);
    const totalImpressions = keywords.reduce((sum, kw) => sum + kw.impressions, 0);
    const avgCTR = (totalClicks / totalImpressions) * 100;
    
    // Simuler les métriques pour la période précédente
    const previousTotalKeywords = totalKeywords * 0.9; // -10%
    const previousAvgPosition = avgPosition * 1.15; // +15% (pire)
    const previousTotalClicks = totalClicks * 0.8; // -20%
    const previousAvgCTR = avgCTR * 0.9; // -10%
    
    return {
      totalKeywords: { 
        value: totalKeywords, 
        change: calculateChange(totalKeywords, previousTotalKeywords)
      },
      avgPosition: { 
        value: avgPosition, 
        change: calculateChange(avgPosition, previousAvgPosition)
      },
      totalClicks: { 
        value: totalClicks, 
        change: calculateChange(totalClicks, previousTotalClicks)
      },
      avgCTR: { 
        value: avgCTR, 
        change: calculateChange(avgCTR, previousAvgCTR)
      }
    };
  };
  
  const overallMetrics = calculateOverallMetrics();
  const keywordMetrics = calculateKeywordMetrics();

  return (
    <div>
      {/* Sélecteurs de période */}
      <div className="boss-flex boss-justify-between boss-items-center boss-mb-6">
        <ButtonGroup>
          <Button
            isPrimary={selectedPeriod === 'last7days'}
            isSecondary={selectedPeriod !== 'last7days'}
            onClick={() => onPeriodChange('last7days')}
          >
            {__('7 derniers jours', 'boss-seo')}
          </Button>
          <Button
            isPrimary={selectedPeriod === 'last30days'}
            isSecondary={selectedPeriod !== 'last30days'}
            onClick={() => onPeriodChange('last30days')}
          >
            {__('30 derniers jours', 'boss-seo')}
          </Button>
          <Button
            isPrimary={selectedPeriod === 'last90days'}
            isSecondary={selectedPeriod !== 'last90days'}
            onClick={() => onPeriodChange('last90days')}
          >
            {__('90 derniers jours', 'boss-seo')}
          </Button>
          <Button
            isPrimary={selectedPeriod === 'lastYear'}
            isSecondary={selectedPeriod !== 'lastYear'}
            onClick={() => onPeriodChange('lastYear')}
          >
            {__('12 derniers mois', 'boss-seo')}
          </Button>
        </ButtonGroup>
        
        <div className="boss-flex boss-items-center boss-space-x-2">
          <Button
            isSecondary
            icon="update"
          >
            {__('Actualiser', 'boss-seo')}
          </Button>
          <Button
            isSecondary
            icon="download"
          >
            {__('Exporter', 'boss-seo')}
          </Button>
        </div>
      </div>
      
      {/* Métriques principales */}
      {visibleWidgets.topMetrics && (
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-4 boss-gap-6 boss-mb-6">
          {/* Utilisateurs */}
          <Card className="boss-card">
            <CardBody>
              <div className="boss-flex boss-justify-between boss-items-start">
                <div>
                  <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                    {__('Utilisateurs', 'boss-seo')}
                  </h3>
                  <div className="boss-flex boss-items-baseline">
                    <span className="boss-text-2xl boss-font-bold boss-mr-2">
                      {formatMetric(overallMetrics.users.value)}
                    </span>
                    <span className={`boss-text-sm boss-font-medium ${getChangeColorClass(overallMetrics.users.change)}`}>
                      <Dashicon icon={getChangeIcon(overallMetrics.users.change)} />
                      {formatMetric(Math.abs(overallMetrics.users.change), 'percentage')}
                    </span>
                  </div>
                </div>
                <div className="boss-bg-blue-100 boss-p-2 boss-rounded-lg">
                  <Dashicon icon="admin-users" className="boss-text-blue-600 boss-text-xl" />
                </div>
              </div>
            </CardBody>
          </Card>
          
          {/* Sessions */}
          <Card className="boss-card">
            <CardBody>
              <div className="boss-flex boss-justify-between boss-items-start">
                <div>
                  <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                    {__('Sessions', 'boss-seo')}
                  </h3>
                  <div className="boss-flex boss-items-baseline">
                    <span className="boss-text-2xl boss-font-bold boss-mr-2">
                      {formatMetric(overallMetrics.sessions.value)}
                    </span>
                    <span className={`boss-text-sm boss-font-medium ${getChangeColorClass(overallMetrics.sessions.change)}`}>
                      <Dashicon icon={getChangeIcon(overallMetrics.sessions.change)} />
                      {formatMetric(Math.abs(overallMetrics.sessions.change), 'percentage')}
                    </span>
                  </div>
                </div>
                <div className="boss-bg-green-100 boss-p-2 boss-rounded-lg">
                  <Dashicon icon="chart-bar" className="boss-text-green-600 boss-text-xl" />
                </div>
              </div>
            </CardBody>
          </Card>
          
          {/* Position moyenne */}
          <Card className="boss-card">
            <CardBody>
              <div className="boss-flex boss-justify-between boss-items-start">
                <div>
                  <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                    {__('Position moyenne', 'boss-seo')}
                  </h3>
                  <div className="boss-flex boss-items-baseline">
                    <span className="boss-text-2xl boss-font-bold boss-mr-2">
                      {formatMetric(keywordMetrics.avgPosition.value.toFixed(1))}
                    </span>
                    <span className={`boss-text-sm boss-font-medium ${getChangeColorClass(keywordMetrics.avgPosition.change, true)}`}>
                      <Dashicon icon={getChangeIcon(keywordMetrics.avgPosition.change, true)} />
                      {formatMetric(Math.abs(keywordMetrics.avgPosition.change), 'percentage')}
                    </span>
                  </div>
                </div>
                <div className="boss-bg-yellow-100 boss-p-2 boss-rounded-lg">
                  <Dashicon icon="arrow-up-alt" className="boss-text-yellow-600 boss-text-xl" />
                </div>
              </div>
            </CardBody>
          </Card>
          
          {/* CTR */}
          <Card className="boss-card">
            <CardBody>
              <div className="boss-flex boss-justify-between boss-items-start">
                <div>
                  <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                    {__('CTR moyen', 'boss-seo')}
                  </h3>
                  <div className="boss-flex boss-items-baseline">
                    <span className="boss-text-2xl boss-font-bold boss-mr-2">
                      {formatMetric(keywordMetrics.avgCTR.value, 'percentage')}
                    </span>
                    <span className={`boss-text-sm boss-font-medium ${getChangeColorClass(keywordMetrics.avgCTR.change)}`}>
                      <Dashicon icon={getChangeIcon(keywordMetrics.avgCTR.change)} />
                      {formatMetric(Math.abs(keywordMetrics.avgCTR.change), 'percentage')}
                    </span>
                  </div>
                </div>
                <div className="boss-bg-purple-100 boss-p-2 boss-rounded-lg">
                  <Dashicon icon="external" className="boss-text-purple-600 boss-text-xl" />
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      )}
      
      {/* Graphique de trafic */}
      {visibleWidgets.traffic && data && data.traffic && (
        <Card className="boss-mb-6">
          <CardHeader className="boss-border-b boss-border-gray-200">
            <div className="boss-flex boss-justify-between boss-items-center">
              <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                {__('Trafic et engagement', 'boss-seo')}
              </h2>
              <Button
                isSmall
                isSecondary
                icon="visibility"
                onClick={() => toggleWidget('traffic')}
              >
                {__('Masquer', 'boss-seo')}
              </Button>
            </div>
          </CardHeader>
          <CardBody>
            <TrafficChart data={data.traffic} />
          </CardBody>
        </Card>
      )}
      
      {/* Graphiques de mots-clés et pages */}
      <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-2 boss-gap-6 boss-mb-6">
        {/* Mots-clés */}
        {visibleWidgets.keywords && data && data.keywords && (
          <Card>
            <CardHeader className="boss-border-b boss-border-gray-200">
              <div className="boss-flex boss-justify-between boss-items-center">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Performance des mots-clés', 'boss-seo')}
                </h2>
                <Button
                  isSmall
                  isSecondary
                  icon="visibility"
                  onClick={() => toggleWidget('keywords')}
                >
                  {__('Masquer', 'boss-seo')}
                </Button>
              </div>
            </CardHeader>
            <CardBody>
              <KeywordsChart data={data.keywords.slice(0, 5)} />
            </CardBody>
          </Card>
        )}
        
        {/* Pages */}
        {visibleWidgets.pages && data && data.pages && (
          <Card>
            <CardHeader className="boss-border-b boss-border-gray-200">
              <div className="boss-flex boss-justify-between boss-items-center">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Distribution des pages vues', 'boss-seo')}
                </h2>
                <Button
                  isSmall
                  isSecondary
                  icon="visibility"
                  onClick={() => toggleWidget('pages')}
                >
                  {__('Masquer', 'boss-seo')}
                </Button>
              </div>
            </CardHeader>
            <CardBody>
              <PagesDistributionChart data={data.pages.slice(0, 5)} />
            </CardBody>
          </Card>
        )}
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
