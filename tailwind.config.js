/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        'boss-primary': '#4F46E5',
        'boss-secondary': '#10B981',
        'boss-dark': '#1E293B',
        'boss-light': '#F8FAFC',
        'boss-gray': '#64748B',
        'boss-error': '#EF4444',
        'boss-warning': '#F59E0B',
        'boss-success': '#10B981',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      boxShadow: {
        'card': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'card-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
      },
    },
  },
  plugins: [],
  // Préfixe pour éviter les conflits avec les styles WordPress
  prefix: 'boss-',
  important: true,
}
