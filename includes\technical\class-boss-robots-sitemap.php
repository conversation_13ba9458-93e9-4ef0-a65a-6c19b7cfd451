<?php
/**
 * Classe pour la gestion des robots.txt et sitemaps.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 */

/**
 * Classe pour la gestion des robots.txt et sitemaps.
 *
 * Cette classe gère les fonctionnalités de robots.txt et sitemaps pour le plugin Boss SEO.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Robots_Sitemap {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le nom de l'option pour les paramètres du sitemap.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $sitemap_option    Le nom de l'option pour les paramètres du sitemap.
     */
    protected $sitemap_option;

    /**
     * Le nom de l'option pour le contenu du robots.txt.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $robots_option    Le nom de l'option pour le contenu du robots.txt.
     */
    protected $robots_option;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->sitemap_option = $plugin_name . '_sitemap_settings';
        $this->robots_option = $plugin_name . '_robots_content';
    }

    /**
     * Enregistre les hooks WordPress nécessaires.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Hook pour le robots.txt
        add_filter( 'robots_txt', array( $this, 'filter_robots_txt' ), 10, 2 );

        // Hooks pour le sitemap
        add_action( 'init', array( $this, 'register_sitemap' ) );
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.1.0
     */
    public function register_rest_routes() {
        // Ajouter un log pour le débogage
        error_log('Boss_Robots_Sitemap::register_rest_routes() called');

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/robots',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_robots_content' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_robots_content' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/sitemap/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_sitemap_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_sitemap_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/sitemap/regenerate',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'regenerate_sitemap' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/content-types',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_content_types' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/taxonomies',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_taxonomies' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_indexation_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Point de terminaison supplémentaire pour la compatibilité avec le frontend
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/indexation-stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_indexation_stats' ),
                'permission_callback' => '__return_true', // Permettre l'accès à tous pour le débogage
            )
        );

        // Ajouter un log pour confirmer l'enregistrement du point de terminaison
        error_log('Boss_Robots_Sitemap: /robots-sitemap/indexation-stats endpoint registered');
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.1.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Filtre le contenu du robots.txt.
     *
     * @since    1.1.0
     * @param    string    $output    Le contenu du robots.txt.
     * @param    bool      $public    Si le site est public ou non.
     * @return   string               Le contenu filtré du robots.txt.
     */
    public function filter_robots_txt( $output, $public ) {
        // Si le site n'est pas public, ne pas modifier le robots.txt
        if ( '0' == $public ) {
            return $output;
        }

        // Récupérer le contenu personnalisé du robots.txt
        $custom_content = get_option( $this->robots_option, '' );

        // Si le contenu personnalisé est vide, utiliser le contenu par défaut
        if ( empty( $custom_content ) ) {
            return $output;
        }

        return $custom_content;
    }

    /**
     * Enregistre le sitemap.
     *
     * @since    1.1.0
     */
    public function register_sitemap() {
        // Récupérer les paramètres du sitemap
        $settings = get_option( $this->sitemap_option, array() );

        // Si le sitemap n'est pas activé, ne rien faire
        if ( isset( $settings['enabled'] ) && ! $settings['enabled'] ) {
            return;
        }

        // Enregistrer le sitemap
        if ( function_exists( 'wp_sitemaps_get_server' ) ) {
            // Utiliser l'API de sitemap de WordPress 5.5+
            add_filter( 'wp_sitemaps_enabled', '__return_true' );

            // Filtrer les types de contenu inclus
            add_filter( 'wp_sitemaps_post_types', array( $this, 'filter_sitemap_post_types' ) );

            // Filtrer les taxonomies incluses
            add_filter( 'wp_sitemaps_taxonomies', array( $this, 'filter_sitemap_taxonomies' ) );

            // Filtrer les URLs
            add_filter( 'wp_sitemaps_posts_query_args', array( $this, 'filter_sitemap_posts_query_args' ), 10, 2 );

            // Ajouter les images si nécessaire
            if ( isset( $settings['includeImages'] ) && $settings['includeImages'] ) {
                add_filter( 'wp_sitemaps_posts_entry', array( $this, 'add_images_to_sitemap' ), 10, 3 );
            }

            // Modifier la fréquence de changement et la priorité
            add_filter( 'wp_sitemaps_posts_entry', array( $this, 'modify_sitemap_entry' ), 10, 3 );
        } else {
            // Fallback pour les versions antérieures de WordPress
            // Implémenter un sitemap personnalisé
            add_action( 'template_redirect', array( $this, 'serve_sitemap' ) );
        }
    }

    /**
     * Filtre les types de contenu inclus dans le sitemap.
     *
     * @since    1.1.0
     * @param    array    $post_types    Les types de contenu.
     * @return   array                   Les types de contenu filtrés.
     */
    public function filter_sitemap_post_types( $post_types ) {
        $settings = get_option( $this->sitemap_option, array() );

        // Si aucun type de contenu n'est spécifié, retourner tous les types
        if ( ! isset( $settings['includedPostTypes'] ) || empty( $settings['includedPostTypes'] ) ) {
            return $post_types;
        }

        // Filtrer les types de contenu
        $included_post_types = array();
        foreach ( $post_types as $post_type => $post_type_object ) {
            if ( in_array( $post_type, $settings['includedPostTypes'] ) ) {
                $included_post_types[ $post_type ] = $post_type_object;
            }
        }

        return $included_post_types;
    }

    /**
     * Filtre les taxonomies incluses dans le sitemap.
     *
     * @since    1.1.0
     * @param    array    $taxonomies    Les taxonomies.
     * @return   array                   Les taxonomies filtrées.
     */
    public function filter_sitemap_taxonomies( $taxonomies ) {
        $settings = get_option( $this->sitemap_option, array() );

        // Si aucune taxonomie n'est spécifiée, retourner toutes les taxonomies
        if ( ! isset( $settings['includedTaxonomies'] ) || empty( $settings['includedTaxonomies'] ) ) {
            return $taxonomies;
        }

        // Filtrer les taxonomies
        $included_taxonomies = array();
        foreach ( $taxonomies as $taxonomy => $taxonomy_object ) {
            if ( in_array( $taxonomy, $settings['includedTaxonomies'] ) ) {
                $included_taxonomies[ $taxonomy ] = $taxonomy_object;
            }
        }

        return $included_taxonomies;
    }

    /**
     * Filtre les arguments de requête pour les posts dans le sitemap.
     *
     * @since    1.1.0
     * @param    array     $args         Les arguments de requête.
     * @param    WP_Post_Type    $post_type    Le type de post.
     * @return   array                    Les arguments de requête filtrés.
     */
    public function filter_sitemap_posts_query_args( $args, $post_type ) {
        $settings = get_option( $this->sitemap_option, array() );

        // Si aucune URL exclue n'est spécifiée, retourner les arguments par défaut
        if ( ! isset( $settings['excludedUrls'] ) || empty( $settings['excludedUrls'] ) ) {
            return $args;
        }

        // Convertir les URLs exclues en IDs de posts
        $excluded_urls = explode( "\n", $settings['excludedUrls'] );
        $excluded_urls = array_map( 'trim', $excluded_urls );
        $excluded_ids = array();

        foreach ( $excluded_urls as $url ) {
            $post_id = url_to_postid( $url );
            if ( $post_id ) {
                $excluded_ids[] = $post_id;
            }
        }

        // Ajouter les IDs exclus aux arguments de requête
        if ( ! empty( $excluded_ids ) ) {
            $args['post__not_in'] = isset( $args['post__not_in'] ) ? array_merge( $args['post__not_in'], $excluded_ids ) : $excluded_ids;
        }

        return $args;
    }

    /**
     * Ajoute les images aux entrées du sitemap.
     *
     * @since    1.1.0
     * @param    array     $entry      L'entrée du sitemap.
     * @param    WP_Post   $post       Le post.
     * @param    string    $post_type  Le type de post.
     * @return   array                 L'entrée du sitemap modifiée.
     */
    public function add_images_to_sitemap( $entry, $post, $post_type ) {
        // Récupérer les images du post
        $images = $this->get_post_images( $post->ID );

        // Ajouter les images à l'entrée
        if ( ! empty( $images ) ) {
            $entry['images'] = $images;
        }

        return $entry;
    }

    /**
     * Modifie les entrées du sitemap pour ajouter la fréquence de changement et la priorité.
     *
     * @since    1.1.0
     * @param    array     $entry      L'entrée du sitemap.
     * @param    WP_Post   $post       Le post.
     * @param    string    $post_type  Le type de post.
     * @return   array                 L'entrée du sitemap modifiée.
     */
    public function modify_sitemap_entry( $entry, $post, $post_type ) {
        $settings = get_option( $this->sitemap_option, array() );

        // Ajouter la fréquence de changement
        if ( isset( $settings['defaultChangeFreq'] ) && ! empty( $settings['defaultChangeFreq'] ) ) {
            $entry['changefreq'] = $settings['defaultChangeFreq'];
        }

        // Ajouter la priorité
        if ( isset( $settings['defaultPriority'] ) && ! empty( $settings['defaultPriority'] ) ) {
            $entry['priority'] = $settings['defaultPriority'];
        }

        // Ajouter la date de dernière modification
        if ( isset( $settings['includeLastMod'] ) && $settings['includeLastMod'] ) {
            $entry['lastmod'] = get_the_modified_date( 'c', $post );
        }

        return $entry;
    }

    /**
     * Récupère les images d'un post.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du post.
     * @return   array                 Les images du post.
     */
    private function get_post_images( $post_id ) {
        $images = array();

        // Récupérer l'image mise en avant
        if ( has_post_thumbnail( $post_id ) ) {
            $thumbnail_id = get_post_thumbnail_id( $post_id );
            $thumbnail = wp_get_attachment_image_src( $thumbnail_id, 'full' );
            if ( $thumbnail ) {
                $images[] = array(
                    'loc' => $thumbnail[0],
                    'title' => get_the_title( $thumbnail_id ),
                    'caption' => wp_get_attachment_caption( $thumbnail_id ),
                );
            }
        }

        // Récupérer les images du contenu
        $post = get_post( $post_id );
        $content = $post->post_content;

        if ( preg_match_all( '/<img [^>]+>/', $content, $matches ) ) {
            foreach ( $matches[0] as $img ) {
                if ( preg_match( '/src=[\'"](.*?)[\'"]/', $img, $src ) ) {
                    $image_url = $src[1];
                    $images[] = array(
                        'loc' => $image_url,
                    );
                }
            }
        }

        return $images;
    }

    /**
     * Sert le sitemap pour les versions antérieures de WordPress.
     *
     * @since    1.1.0
     */
    public function serve_sitemap() {
        $request_uri = isset( $_SERVER['REQUEST_URI'] ) ? $_SERVER['REQUEST_URI'] : '';

        // Vérifier si c'est une requête pour le sitemap
        if ( '/sitemap.xml' === $request_uri ) {
            $this->generate_sitemap_xml();
            exit;
        }
    }

    /**
     * Génère le sitemap XML pour les versions antérieures de WordPress.
     *
     * @since    1.1.0
     */
    private function generate_sitemap_xml() {
        $settings = get_option( $this->sitemap_option, array() );

        // Définir l'en-tête
        header( 'Content-Type: application/xml; charset=UTF-8' );

        // Début du sitemap
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"';
        echo ' xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"';
        echo ' xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd"';

        // Ajouter l'espace de noms pour les images si nécessaire
        if ( isset( $settings['includeImages'] ) && $settings['includeImages'] ) {
            echo ' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"';
        }

        echo '>' . "\n";

        // Récupérer les types de contenu inclus
        $post_types = isset( $settings['includedPostTypes'] ) ? $settings['includedPostTypes'] : array( 'post', 'page' );

        // Récupérer les URLs exclues
        $excluded_urls = isset( $settings['excludedUrls'] ) ? explode( "\n", $settings['excludedUrls'] ) : array();
        $excluded_urls = array_map( 'trim', $excluded_urls );
        $excluded_ids = array();

        foreach ( $excluded_urls as $url ) {
            $post_id = url_to_postid( $url );
            if ( $post_id ) {
                $excluded_ids[] = $post_id;
            }
        }

        // Ajouter la page d'accueil
        echo '<url>' . "\n";
        echo '<loc>' . esc_url( home_url( '/' ) ) . '</loc>' . "\n";
        echo '<changefreq>' . esc_html( isset( $settings['defaultChangeFreq'] ) ? $settings['defaultChangeFreq'] : 'weekly' ) . '</changefreq>' . "\n";
        echo '<priority>' . esc_html( isset( $settings['defaultPriority'] ) ? $settings['defaultPriority'] : '1.0' ) . '</priority>' . "\n";
        echo '</url>' . "\n";

        // Ajouter les posts
        foreach ( $post_types as $post_type ) {
            $args = array(
                'post_type' => $post_type,
                'post_status' => 'publish',
                'posts_per_page' => -1,
                'post__not_in' => $excluded_ids,
            );

            $query = new WP_Query( $args );

            while ( $query->have_posts() ) {
                $query->the_post();
                $post_id = get_the_ID();
                $permalink = get_permalink( $post_id );

                echo '<url>' . "\n";
                echo '<loc>' . esc_url( $permalink ) . '</loc>' . "\n";

                // Ajouter la date de dernière modification
                if ( isset( $settings['includeLastMod'] ) && $settings['includeLastMod'] ) {
                    echo '<lastmod>' . esc_html( get_the_modified_date( 'c' ) ) . '</lastmod>' . "\n";
                }

                echo '<changefreq>' . esc_html( isset( $settings['defaultChangeFreq'] ) ? $settings['defaultChangeFreq'] : 'weekly' ) . '</changefreq>' . "\n";
                echo '<priority>' . esc_html( isset( $settings['defaultPriority'] ) ? $settings['defaultPriority'] : '0.7' ) . '</priority>' . "\n";

                // Ajouter les images
                if ( isset( $settings['includeImages'] ) && $settings['includeImages'] ) {
                    $images = $this->get_post_images( $post_id );
                    foreach ( $images as $image ) {
                        echo '<image:image>' . "\n";
                        echo '<image:loc>' . esc_url( $image['loc'] ) . '</image:loc>' . "\n";
                        if ( isset( $image['title'] ) ) {
                            echo '<image:title>' . esc_html( $image['title'] ) . '</image:title>' . "\n";
                        }
                        if ( isset( $image['caption'] ) ) {
                            echo '<image:caption>' . esc_html( $image['caption'] ) . '</image:caption>' . "\n";
                        }
                        echo '</image:image>' . "\n";
                    }
                }

                echo '</url>' . "\n";
            }

            wp_reset_postdata();
        }

        // Ajouter les taxonomies
        $taxonomies = isset( $settings['includedTaxonomies'] ) ? $settings['includedTaxonomies'] : array( 'category', 'post_tag' );

        foreach ( $taxonomies as $taxonomy ) {
            $terms = get_terms( array(
                'taxonomy' => $taxonomy,
                'hide_empty' => true,
            ) );

            foreach ( $terms as $term ) {
                $term_link = get_term_link( $term );

                if ( ! is_wp_error( $term_link ) ) {
                    echo '<url>' . "\n";
                    echo '<loc>' . esc_url( $term_link ) . '</loc>' . "\n";
                    echo '<changefreq>' . esc_html( isset( $settings['defaultChangeFreq'] ) ? $settings['defaultChangeFreq'] : 'weekly' ) . '</changefreq>' . "\n";
                    echo '<priority>' . esc_html( isset( $settings['defaultPriority'] ) ? $settings['defaultPriority'] : '0.5' ) . '</priority>' . "\n";
                    echo '</url>' . "\n";
                }
            }
        }

        // Fin du sitemap
        echo '</urlset>';
    }

    /**
     * Récupère le contenu du robots.txt.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_robots_content( $request ) {
        $content = get_option( $this->robots_option, '' );

        // Si le contenu est vide, générer un contenu par défaut
        if ( empty( $content ) ) {
            $content = $this->get_default_robots_content();
        }

        return rest_ensure_response( array(
            'content' => $content,
        ) );
    }

    /**
     * Enregistre le contenu du robots.txt.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function save_robots_content( $request ) {
        $content = isset( $request['content'] ) ? sanitize_textarea_field( $request['content'] ) : '';

        // Enregistrer le contenu
        update_option( $this->robots_option, $content );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Le contenu du robots.txt a été enregistré avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les paramètres du sitemap.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_sitemap_settings( $request ) {
        $settings = get_option( $this->sitemap_option, array() );

        // Si les paramètres sont vides, utiliser les paramètres par défaut
        if ( empty( $settings ) ) {
            $settings = $this->get_default_sitemap_settings();
        }

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres du sitemap.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function save_sitemap_settings( $request ) {
        $settings = isset( $request['settings'] ) ? $request['settings'] : array();

        // Valider et sanitiser les paramètres
        $sanitized_settings = array(
            'enabled' => isset( $settings['enabled'] ) ? (bool) $settings['enabled'] : true,
            'includedPostTypes' => isset( $settings['includedPostTypes'] ) ? array_map( 'sanitize_text_field', $settings['includedPostTypes'] ) : array(),
            'includedTaxonomies' => isset( $settings['includedTaxonomies'] ) ? array_map( 'sanitize_text_field', $settings['includedTaxonomies'] ) : array(),
            'defaultChangeFreq' => isset( $settings['defaultChangeFreq'] ) ? sanitize_text_field( $settings['defaultChangeFreq'] ) : 'weekly',
            'defaultPriority' => isset( $settings['defaultPriority'] ) ? (float) $settings['defaultPriority'] : 0.7,
            'includeImages' => isset( $settings['includeImages'] ) ? (bool) $settings['includeImages'] : true,
            'includeLastMod' => isset( $settings['includeLastMod'] ) ? (bool) $settings['includeLastMod'] : true,
            'excludedUrls' => isset( $settings['excludedUrls'] ) ? sanitize_textarea_field( $settings['excludedUrls'] ) : '',
        );

        // Enregistrer les paramètres
        update_option( $this->sitemap_option, $sanitized_settings );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Les paramètres du sitemap ont été enregistrés avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Régénère le sitemap.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function regenerate_sitemap( $request ) {
        // Si WordPress 5.5+ est utilisé, vider le cache du sitemap
        if ( function_exists( 'wp_sitemaps_get_server' ) ) {
            // Vider le cache du sitemap
            delete_transient( 'wp_sitemaps_index' );

            // Récupérer les types de contenu et taxonomies
            $settings = get_option( $this->sitemap_option, array() );
            $post_types = isset( $settings['includedPostTypes'] ) ? $settings['includedPostTypes'] : array();
            $taxonomies = isset( $settings['includedTaxonomies'] ) ? $settings['includedTaxonomies'] : array();

            // Vider le cache pour chaque type de contenu
            foreach ( $post_types as $post_type ) {
                delete_transient( 'wp_sitemaps_posts_' . $post_type );
            }

            // Vider le cache pour chaque taxonomie
            foreach ( $taxonomies as $taxonomy ) {
                delete_transient( 'wp_sitemaps_taxonomies_' . $taxonomy );
            }
        }

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Le sitemap a été régénéré avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les types de contenu disponibles.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_content_types( $request ) {
        $post_types = get_post_types( array( 'public' => true ), 'objects' );
        $content_types = array();

        foreach ( $post_types as $post_type ) {
            $content_types[] = array(
                'name' => $post_type->name,
                'label' => $post_type->label,
                'description' => $post_type->description,
            );
        }

        return rest_ensure_response( $content_types );
    }

    /**
     * Récupère les taxonomies disponibles.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_taxonomies( $request ) {
        $taxonomies_objects = get_taxonomies( array( 'public' => true ), 'objects' );
        $taxonomies = array();

        foreach ( $taxonomies_objects as $taxonomy ) {
            $taxonomies[] = array(
                'name' => $taxonomy->name,
                'label' => $taxonomy->label,
                'description' => $taxonomy->description,
            );
        }

        return rest_ensure_response( $taxonomies );
    }

    /**
     * Récupère les statistiques d'indexation.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_indexation_stats( $request ) {
        // Récupérer les vraies statistiques d'indexation
        global $wpdb;

        // Compter les pages publiées
        $published_posts = wp_count_posts();
        $total_published = 0;
        foreach ($published_posts as $status => $count) {
            if ($status === 'publish') {
                $total_published += $count;
            }
        }

        // Compter les pages publiées
        $published_pages = wp_count_posts('page');
        $total_published += $published_pages->publish;

        // Analyser les problèmes réels d'indexation
        $indexation_issues = array();

        // Vérifier les pages avec noindex
        $noindex_count = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->postmeta} pm
             INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
             WHERE pm.meta_key = '_boss_seo_meta_robots'
             AND pm.meta_value LIKE '%noindex%'
             AND p.post_status = 'publish'"
        );

        if ($noindex_count > 0) {
            $indexation_issues[] = array(
                'type' => 'error',
                'message' => __( 'Pages avec des balises meta robots noindex', 'boss-seo' ),
                'count' => intval($noindex_count),
            );
        }

        // Vérifier les titres dupliqués
        $duplicate_titles = $wpdb->get_var(
            "SELECT COUNT(*) FROM (
                SELECT post_title, COUNT(*) as count
                FROM {$wpdb->posts}
                WHERE post_status = 'publish'
                AND post_type IN ('post', 'page')
                AND post_title != ''
                GROUP BY post_title
                HAVING count > 1
            ) as duplicates"
        );

        if ($duplicate_titles > 0) {
            $indexation_issues[] = array(
                'type' => 'warning',
                'message' => __( 'Pages avec des titres dupliqués', 'boss-seo' ),
                'count' => intval($duplicate_titles),
            );
        }

        // Vérifier les descriptions méta manquantes
        $missing_descriptions = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->posts} p
             LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
             AND pm.meta_key = '_boss_seo_meta_description'
             WHERE p.post_status = 'publish'
             AND p.post_type IN ('post', 'page')
             AND (pm.meta_value IS NULL OR pm.meta_value = '')"
        );

        if ($missing_descriptions > 0) {
            $indexation_issues[] = array(
                'type' => 'info',
                'message' => __( 'Pages avec des descriptions méta manquantes', 'boss-seo' ),
                'count' => intval($missing_descriptions),
            );
        }

        // Récupérer la dernière modification de sitemap
        $sitemap_option = get_option($this->plugin_name . '_sitemap_settings', array());
        $last_sitemap_update = isset($sitemap_option['last_update']) ? $sitemap_option['last_update'] : null;

        $stats = array(
            'indexedPages' => $total_published,
            'crawlRate' => 100, // Valeur par défaut, peut être améliorée avec l'API Search Console
            'lastCrawl' => $last_sitemap_update ? $last_sitemap_update : date('Y-m-d H:i:s', strtotime('-1 day')),
            'indexationIssues' => $indexation_issues,
        );

        return rest_ensure_response( $stats );
    }

    /**
     * Récupère le contenu par défaut du robots.txt.
     *
     * @since    1.1.0
     * @return   string    Le contenu par défaut du robots.txt.
     */
    private function get_default_robots_content() {
        $site_url = get_site_url();
        $sitemap_url = trailingslashit( $site_url ) . 'sitemap.xml';

        $content = "User-agent: *\n";
        $content .= "Disallow: /wp-admin/\n";
        $content .= "Allow: /wp-admin/admin-ajax.php\n\n";
        $content .= "Disallow: /wp-includes/\n";
        $content .= "Disallow: /wp-content/plugins/\n";
        $content .= "Disallow: /wp-content/themes/\n\n";
        $content .= "Sitemap: " . $sitemap_url;

        return $content;
    }

    /**
     * Récupère les paramètres par défaut du sitemap.
     *
     * @since    1.1.0
     * @return   array    Les paramètres par défaut du sitemap.
     */
    private function get_default_sitemap_settings() {
        return array(
            'enabled' => true,
            'includedPostTypes' => array( 'post', 'page' ),
            'includedTaxonomies' => array( 'category', 'post_tag' ),
            'defaultChangeFreq' => 'weekly',
            'defaultPriority' => 0.7,
            'includeImages' => true,
            'includeLastMod' => true,
            'excludedUrls' => '',
        );
    }
}
