/**
 * Composant pour l'étape 2 : Recherche de mots-clés
 */
import { useState, useEffect, useRef, useCallback } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Dashicon,
  Spinner,
  SearchControl,
  SelectControl,
  Badge,
  Notice,
  ToggleControl
} from '@wordpress/components';

// Services
import keywordService from '../../../services/KeywordService';

/**
 * Composant pour afficher un mot-clé avec ses métriques
 */
const KeywordCard = ({ keyword, onSelect, isSelected, isMain }) => {
  const handleClick = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      onSelect();
    } catch (error) {
      console.error('Erreur lors de la sélection du mot-clé:', error);
    }
  }, [onSelect]);

  return (
    <Card
      className={`boss-cursor-pointer boss-transition-all boss-duration-200 boss-ease-in-out ${
        isMain
          ? 'boss-border-2 boss-border-boss-primary boss-shadow-md'
          : isSelected
            ? 'boss-border-2 boss-border-boss-secondary'
            : 'boss-hover:boss-shadow-sm'
      }`}
      onClick={handleClick}
    >
      <CardBody>
        <div className="boss-flex boss-justify-between boss-items-center boss-mb-2">
          <h4 className="boss-font-medium boss-text-boss-dark">{keyword.text}</h4>
          {isMain && (
            <Badge className="boss-bg-boss-primary boss-text-white">
              {__('Principal', 'boss-seo')}
            </Badge>
          )}
        </div>

        <div className="boss-grid boss-grid-cols-3 boss-gap-2 boss-text-sm boss-text-boss-gray">
          <div>
            <span className="boss-font-medium">{__('Volume:', 'boss-seo')}</span> {keyword.volume}
          </div>
          <div>
            <span className="boss-font-medium">{__('Difficulté:', 'boss-seo')}</span> {keyword.difficulty}/100
          </div>
          <div>
            <span className="boss-font-medium">{__('CPC:', 'boss-seo')}</span> {keyword.cpc}€
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

/**
 * Composant pour l'étape 2 : Recherche de mots-clés
 *
 * @param {Object} props Propriétés du composant
 * @param {Object} props.data Données de l'étape
 * @param {Function} props.updateData Fonction pour mettre à jour les données
 * @param {Object} props.objectiveData Données de l'étape 1 (Objectif SEO)
 */
const StepKeywords = ({ data = {}, updateData, objectiveData = {} }) => {
  // Ref pour vérifier si le composant est monté
  const isMountedRef = useRef(true);

  // Initialiser les données par défaut si elles n'existent pas
  const safeData = {
    main: '',
    secondary: [],
    ...data
  };

  // États pour la recherche
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [error, setError] = useState('');

  // États pour les filtres
  const [language, setLanguage] = useState('fr');
  const [country, setCountry] = useState('fr');
  const [useAi, setUseAi] = useState(true);

  // Fonction pour rechercher des mots-clés
  const handleSearch = useCallback(async () => {
    if (!searchQuery || !isMountedRef.current) return;

    setIsSearching(true);
    setError('');

    try {
      // Rechercher des mots-clés via l'API
      const results = await keywordService.searchKeywords(searchQuery, language, country);

      // Vérifier si le composant est toujours monté avant de continuer
      if (!isMountedRef.current) return;

      // Si l'option IA est activée, récupérer également des suggestions d'IA
      if (useAi) {
        try {
          const aiSuggestions = await keywordService.getAiSuggestedKeywords(
            searchQuery,
            objectiveData.contentType
          );

          // Vérifier à nouveau si le composant est toujours monté
          if (!isMountedRef.current) return;

          // Fusionner les résultats
          const mergedResults = [...results];

          // Ajouter les suggestions d'IA qui ne sont pas déjà présentes
          aiSuggestions.forEach(suggestion => {
            if (!mergedResults.some(r => r.text.toLowerCase() === suggestion.text.toLowerCase())) {
              mergedResults.push(suggestion);
            }
          });

          setSearchResults(mergedResults);
        } catch (aiError) {
          console.error('Erreur lors de la récupération des suggestions d\'IA:', aiError);
          // Continuer avec les résultats de l'API standard si le composant est toujours monté
          if (isMountedRef.current) {
            setSearchResults(results);
          }
        }
      } else {
        setSearchResults(results);
      }
    } catch (error) {
      console.error('Erreur lors de la recherche de mots-clés:', error);

      // Mettre à jour l'état seulement si le composant est toujours monté
      if (isMountedRef.current) {
        setError(__('Erreur lors de la recherche de mots-clés. Veuillez réessayer.', 'boss-seo'));

        // Utiliser des données fictives en cas d'erreur
        setSearchResults([
          { text: searchQuery, volume: '1000-10000', difficulty: 45, cpc: '0.50' },
          { text: searchQuery + ' guide', volume: '100-1000', difficulty: 30, cpc: '0.35' },
          { text: 'meilleur ' + searchQuery, volume: '500-5000', difficulty: 60, cpc: '0.75' },
          { text: searchQuery + ' comparatif', volume: '100-1000', difficulty: 40, cpc: '0.45' },
          { text: searchQuery + ' avis', volume: '500-5000', difficulty: 35, cpc: '0.40' },
          { text: 'comment choisir ' + searchQuery, volume: '100-1000', difficulty: 25, cpc: '0.30' },
        ]);
      }
    } finally {
      // Mettre à jour l'état seulement si le composant est toujours monté
      if (isMountedRef.current) {
        setIsSearching(false);
      }
    }
  }, [searchQuery, language, country, useAi, objectiveData.contentType]);

  // Fonction pour sélectionner un mot-clé principal (avec vérification de montage)
  const selectMainKeyword = useCallback((keyword) => {
    if (!isMountedRef.current) return;

    try {
      updateData({
        ...safeData,
        main: keyword.text
      });
    } catch (error) {
      console.error('Erreur lors de la sélection du mot-clé principal:', error);
    }
  }, [safeData, updateData]);

  // Fonction pour sélectionner/désélectionner un mot-clé secondaire (avec vérification de montage)
  const toggleSecondaryKeyword = useCallback((keyword) => {
    if (!isMountedRef.current) return;

    try {
      const isAlreadySelected = safeData.secondary.includes(keyword.text);

      if (isAlreadySelected) {
        // Retirer le mot-clé des mots-clés secondaires
        updateData({
          ...safeData,
          secondary: safeData.secondary.filter(k => k !== keyword.text)
        });
      } else {
        // Ajouter le mot-clé aux mots-clés secondaires
        updateData({
          ...safeData,
          secondary: [...safeData.secondary, keyword.text]
        });
      }
    } catch (error) {
      console.error('Erreur lors de la sélection du mot-clé secondaire:', error);
    }
  }, [safeData, updateData]);

  // Fonction pour récupérer des mots-clés connexes
  const getRelatedKeywords = useCallback(async () => {
    if (!safeData.main || !isMountedRef.current) {
      if (isMountedRef.current) {
        setError(__('Veuillez d\'abord sélectionner un mot-clé principal.', 'boss-seo'));
      }
      return;
    }

    setIsSearching(true);
    setError('');

    try {
      const relatedKeywords = await keywordService.getRelatedKeywords(safeData.main, language, country);

      // Vérifier si le composant est toujours monté avant de mettre à jour l'état
      if (!isMountedRef.current) return;

      // Ajouter les mots-clés connexes aux résultats de recherche
      setSearchResults(prevResults => {
        const newResults = [...prevResults];

        // Ajouter uniquement les mots-clés qui ne sont pas déjà présents
        relatedKeywords.forEach(keyword => {
          if (!newResults.some(r => r.text.toLowerCase() === keyword.text.toLowerCase())) {
            newResults.push(keyword);
          }
        });

        return newResults;
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des mots-clés connexes:', error);

      // Mettre à jour l'état seulement si le composant est toujours monté
      if (isMountedRef.current) {
        setError(__('Erreur lors de la récupération des mots-clés connexes. Veuillez réessayer.', 'boss-seo'));
      }
    } finally {
      // Mettre à jour l'état seulement si le composant est toujours monté
      if (isMountedRef.current) {
        setIsSearching(false);
      }
    }
  }, [safeData.main, language, country]);

  // Effet pour lancer la recherche lorsque la requête change
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery) {
        handleSearch();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Effet de nettoyage pour marquer le composant comme démonté
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return (
    <div className="boss-space-y-6">
      <div className="boss-mb-6">
        <h2 className="boss-text-xl boss-font-semibold boss-text-boss-dark boss-mb-4">
          {__('Étape 2 : Recherche de mots-clés', 'boss-seo')}
        </h2>
        <p className="boss-text-boss-gray">
          {__('Recherchez et sélectionnez des mots-clés pertinents pour votre contenu. Choisissez un mot-clé principal et des mots-clés secondaires.', 'boss-seo')}
        </p>
      </div>

      {error && (
        <Notice status="error" isDismissible={true} onRemove={() => setError('')} className="boss-mb-4">
          {error}
        </Notice>
      )}

      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="search" className="boss-mr-2" />
            {__('Recherche de mots-clés', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-mb-4">
            <SearchControl
              label={__('Rechercher des mots-clés', 'boss-seo')}
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder={__('Entrez un mot-clé...', 'boss-seo')}
              className="boss-mb-4"
            />

            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4 boss-mb-4">
              <SelectControl
                label={__('Langue', 'boss-seo')}
                value={language}
                options={[
                  { label: __('Français', 'boss-seo'), value: 'fr' },
                  { label: __('Anglais', 'boss-seo'), value: 'en' },
                  { label: __('Espagnol', 'boss-seo'), value: 'es' },
                  { label: __('Allemand', 'boss-seo'), value: 'de' },
                  { label: __('Italien', 'boss-seo'), value: 'it' },
                ]}
                onChange={setLanguage}
              />

              <SelectControl
                label={__('Pays', 'boss-seo')}
                value={country}
                options={[
                  { label: __('France', 'boss-seo'), value: 'fr' },
                  { label: __('États-Unis', 'boss-seo'), value: 'us' },
                  { label: __('Royaume-Uni', 'boss-seo'), value: 'gb' },
                  { label: __('Canada', 'boss-seo'), value: 'ca' },
                  { label: __('Belgique', 'boss-seo'), value: 'be' },
                  { label: __('Suisse', 'boss-seo'), value: 'ch' },
                ]}
                onChange={setCountry}
              />

              <div className="boss-flex boss-items-end">
                <ToggleControl
                  label={__('Utiliser l\'IA pour les suggestions', 'boss-seo')}
                  checked={useAi}
                  onChange={setUseAi}
                />
              </div>
            </div>

            <div className="boss-flex boss-justify-between boss-items-center">
              <Button
                isPrimary
                onClick={handleSearch}
                disabled={!searchQuery || isSearching}
                isBusy={isSearching}
              >
                {__('Rechercher', 'boss-seo')}
              </Button>

              <Button
                isSecondary
                onClick={getRelatedKeywords}
                disabled={!safeData.main || isSearching}
                isBusy={isSearching}
              >
                {__('Mots-clés connexes', 'boss-seo')}
              </Button>
            </div>
          </div>

          {isSearching ? (
            <div className="boss-text-center boss-py-8">
              <Spinner />
              <p className="boss-mt-2 boss-text-boss-gray">
                {__('Recherche en cours...', 'boss-seo')}
              </p>
            </div>
          ) : searchResults.length > 0 ? (
            <div>
              <h4 className="boss-font-medium boss-mb-4">
                {__('Résultats de recherche', 'boss-seo')}
              </h4>

              <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mb-6">
                {searchResults.map((keyword, index) => (
                  <KeywordCard
                    key={index}
                    keyword={keyword}
                    onSelect={() => safeData.main === keyword.text ? toggleSecondaryKeyword(keyword) : selectMainKeyword(keyword)}
                    isSelected={safeData.secondary.includes(keyword.text)}
                    isMain={safeData.main === keyword.text}
                  />
                ))}
              </div>
            </div>
          ) : searchQuery ? (
            <div className="boss-text-center boss-py-8 boss-text-boss-gray">
              <Dashicon icon="info" className="boss-mb-2 boss-text-3xl" />
              <p>{__('Aucun résultat trouvé pour cette recherche.', 'boss-seo')}</p>
            </div>
          ) : (
            <div className="boss-text-center boss-py-8 boss-text-boss-gray">
              <Dashicon icon="search" className="boss-mb-2 boss-text-3xl" />
              <p>{__('Entrez un mot-clé dans la barre de recherche pour commencer.', 'boss-seo')}</p>
            </div>
          )}
        </CardBody>
      </Card>

      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="tag" className="boss-mr-2" />
            {__('Mots-clés sélectionnés', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-mb-4">
            <h4 className="boss-font-medium boss-mb-2">
              {__('Mot-clé principal', 'boss-seo')}
            </h4>

            {safeData.main ? (
              <div className="boss-p-3 boss-bg-boss-primary boss-bg-opacity-10 boss-rounded boss-border boss-border-boss-primary">
                <div className="boss-flex boss-justify-between boss-items-center">
                  <span className="boss-font-medium">{safeData.main}</span>
                  <Button
                    isSmall
                    isDestructive
                    onClick={() => updateData({ ...safeData, main: '' })}
                    icon="no-alt"
                  />
                </div>
              </div>
            ) : (
              <div className="boss-p-3 boss-bg-gray-50 boss-rounded boss-border boss-border-gray-200 boss-text-boss-gray boss-text-center">
                {__('Aucun mot-clé principal sélectionné', 'boss-seo')}
              </div>
            )}
          </div>

          <div>
            <h4 className="boss-font-medium boss-mb-2">
              {__('Mots-clés secondaires', 'boss-seo')}
            </h4>

            {safeData.secondary.length > 0 ? (
              <div className="boss-flex boss-flex-wrap boss-gap-2">
                {safeData.secondary.map((keyword, index) => (
                  <Badge
                    key={index}
                    className="boss-bg-boss-secondary boss-text-white boss-px-3 boss-py-1 boss-rounded-full boss-flex boss-items-center"
                  >
                    {keyword}
                    <Button
                      isSmall
                      icon="no-alt"
                      onClick={() => updateData({
                        ...safeData,
                        secondary: safeData.secondary.filter(k => k !== keyword)
                      })}
                      className="boss-ml-2 boss-text-white boss-hover:boss-text-red-500"
                    />
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="boss-p-3 boss-bg-gray-50 boss-rounded boss-border boss-border-gray-200 boss-text-boss-gray boss-text-center">
                {__('Aucun mot-clé secondaire sélectionné', 'boss-seo')}
              </div>
            )}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default StepKeywords;
