<?php
/**
 * Système de cache pour le module E-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Système de cache pour le module E-commerce.
 *
 * Cette classe gère le cache des données e-commerce pour améliorer les performances.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Ecommerce_Cache {

    /**
     * Instance singleton.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Ecommerce_Cache    $instance    Instance singleton.
     */
    private static $instance = null;

    /**
     * Préfixe pour les clés de cache.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $cache_prefix    Préfixe pour les clés de cache.
     */
    private $cache_prefix = 'boss_ecommerce_';

    /**
     * Durée de vie par défaut du cache (en secondes).
     *
     * @since    1.2.0
     * @access   private
     * @var      int    $default_expiration    Durée de vie par défaut.
     */
    private $default_expiration = 3600; // 1 heure

    /**
     * Récupère l'instance singleton.
     *
     * @since    1.2.0
     * @return   Boss_Ecommerce_Cache    Instance singleton.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructeur privé pour le pattern singleton.
     *
     * @since    1.2.0
     */
    private function __construct() {
        // Ajouter les hooks pour l'invalidation automatique
        $this->register_hooks();
    }

    /**
     * Enregistre les hooks pour l'invalidation automatique du cache.
     *
     * @since    1.2.0
     */
    private function register_hooks() {
        // Invalider le cache lors de la modification d'un produit
        add_action( 'woocommerce_update_product', array( $this, 'invalidate_product_cache' ) );
        add_action( 'woocommerce_new_product', array( $this, 'invalidate_all_cache' ) );
        add_action( 'woocommerce_delete_product', array( $this, 'invalidate_all_cache' ) );
        
        // Invalider le cache lors de la modification des catégories
        add_action( 'edited_product_cat', array( $this, 'invalidate_all_cache' ) );
        add_action( 'created_product_cat', array( $this, 'invalidate_all_cache' ) );
        add_action( 'deleted_product_cat', array( $this, 'invalidate_all_cache' ) );
    }

    /**
     * Stocke une valeur dans le cache.
     *
     * @since    1.2.0
     * @param    string    $key         Clé du cache.
     * @param    mixed     $value       Valeur à stocker.
     * @param    int       $expiration  Durée de vie en secondes (optionnel).
     * @return   bool                   True en cas de succès, false sinon.
     */
    public function set( $key, $value, $expiration = null ) {
        if ( null === $expiration ) {
            $expiration = $this->default_expiration;
        }

        $cache_key = $this->get_cache_key( $key );
        
        return wp_cache_set( $cache_key, $value, 'boss_ecommerce', $expiration );
    }

    /**
     * Récupère une valeur du cache.
     *
     * @since    1.2.0
     * @param    string    $key    Clé du cache.
     * @return   mixed             Valeur du cache ou false si non trouvée.
     */
    public function get( $key ) {
        $cache_key = $this->get_cache_key( $key );
        
        return wp_cache_get( $cache_key, 'boss_ecommerce' );
    }

    /**
     * Supprime une valeur du cache.
     *
     * @since    1.2.0
     * @param    string    $key    Clé du cache.
     * @return   bool              True en cas de succès, false sinon.
     */
    public function delete( $key ) {
        $cache_key = $this->get_cache_key( $key );
        
        return wp_cache_delete( $cache_key, 'boss_ecommerce' );
    }

    /**
     * Vide tout le cache e-commerce.
     *
     * @since    1.2.0
     * @return   bool    True en cas de succès, false sinon.
     */
    public function flush() {
        return wp_cache_flush_group( 'boss_ecommerce' );
    }

    /**
     * Stocke le score SEO d'un produit.
     *
     * @since    1.2.0
     * @param    int    $product_id    ID du produit.
     * @param    int    $score         Score SEO.
     * @return   bool                  True en cas de succès, false sinon.
     */
    public function set_seo_score( $product_id, $score ) {
        $key = "seo_score_{$product_id}";
        return $this->set( $key, $score, 7200 ); // 2 heures
    }

    /**
     * Récupère le score SEO d'un produit.
     *
     * @since    1.2.0
     * @param    int    $product_id    ID du produit.
     * @return   int|false             Score SEO ou false si non trouvé.
     */
    public function get_seo_score( $product_id ) {
        $key = "seo_score_{$product_id}";
        return $this->get( $key );
    }

    /**
     * Stocke les recommandations d'un produit.
     *
     * @since    1.2.0
     * @param    int      $product_id        ID du produit.
     * @param    array    $recommendations   Recommandations.
     * @return   bool                        True en cas de succès, false sinon.
     */
    public function set_recommendations( $product_id, $recommendations ) {
        $key = "recommendations_{$product_id}";
        return $this->set( $key, $recommendations, 7200 ); // 2 heures
    }

    /**
     * Récupère les recommandations d'un produit.
     *
     * @since    1.2.0
     * @param    int    $product_id    ID du produit.
     * @return   array|false           Recommandations ou false si non trouvées.
     */
    public function get_recommendations( $product_id ) {
        $key = "recommendations_{$product_id}";
        return $this->get( $key );
    }

    /**
     * Stocke l'analyse d'un produit.
     *
     * @since    1.2.0
     * @param    int      $product_id    ID du produit.
     * @param    array    $analysis      Analyse.
     * @return   bool                    True en cas de succès, false sinon.
     */
    public function set_product_analysis( $product_id, $analysis ) {
        $key = "analysis_{$product_id}";
        return $this->set( $key, $analysis, 3600 ); // 1 heure
    }

    /**
     * Récupère l'analyse d'un produit.
     *
     * @since    1.2.0
     * @param    int    $product_id    ID du produit.
     * @return   array|false           Analyse ou false si non trouvée.
     */
    public function get_product_analysis( $product_id ) {
        $key = "analysis_{$product_id}";
        return $this->get( $key );
    }

    /**
     * Invalide le cache d'un produit spécifique.
     *
     * @since    1.2.0
     * @param    int    $product_id    ID du produit.
     */
    public function invalidate_product_cache( $product_id ) {
        $keys = array(
            "seo_score_{$product_id}",
            "recommendations_{$product_id}",
            "analysis_{$product_id}",
            "product_{$product_id}"
        );

        foreach ( $keys as $key ) {
            $this->delete( $key );
        }

        // Invalider aussi les caches globaux
        $this->delete( 'dashboard_data' );
        $this->delete( 'product_stats' );
        $this->delete( 'top_products' );
        $this->delete( 'top_categories' );
    }

    /**
     * Invalide tout le cache.
     *
     * @since    1.2.0
     */
    public function invalidate_all_cache() {
        $this->flush();
    }

    /**
     * Génère une clé de cache complète.
     *
     * @since    1.2.0
     * @param    string    $key    Clé de base.
     * @return   string            Clé de cache complète.
     */
    private function get_cache_key( $key ) {
        return $this->cache_prefix . $key;
    }

    /**
     * Récupère les statistiques du cache.
     *
     * @since    1.2.0
     * @return   array    Statistiques du cache.
     */
    public function get_cache_stats() {
        // Cette méthode pourrait être étendue pour fournir des statistiques détaillées
        return array(
            'cache_group' => 'boss_ecommerce',
            'prefix' => $this->cache_prefix,
            'default_expiration' => $this->default_expiration
        );
    }
}
