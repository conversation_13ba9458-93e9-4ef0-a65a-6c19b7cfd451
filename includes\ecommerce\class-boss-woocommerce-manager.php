<?php
/**
 * Gestionnaire centralisé pour l'intégration WooCommerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Gestionnaire centralisé pour l'intégration WooCommerce.
 *
 * Cette classe centralise toutes les interactions avec WooCommerce
 * et fournit une interface cohérente pour le module e-commerce.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_WooCommerce_Manager {

    /**
     * Instance singleton.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_WooCommerce_Manager    $instance    Instance singleton.
     */
    private static $instance = null;

    /**
     * Cache des données.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $cache    Cache des données.
     */
    private $cache = array();

    /**
     * Récupère l'instance singleton.
     *
     * @since    1.2.0
     * @return   Boss_WooCommerce_Manager    Instance singleton.
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructeur privé pour le pattern singleton.
     *
     * @since    1.2.0
     */
    private function __construct() {
        // Constructeur privé
    }

    /**
     * Vérifie si WooCommerce est installé et activé.
     *
     * @since    1.2.0
     * @return   bool    True si WooCommerce est actif, false sinon.
     */
    public function is_woocommerce_active() {
        return class_exists( 'WooCommerce' ) && function_exists( 'wc_get_product' );
    }

    /**
     * Vérifie si des produits existent.
     *
     * @since    1.2.0
     * @return   bool    True si des produits existent, false sinon.
     */
    public function has_products() {
        if ( ! $this->is_woocommerce_active() ) {
            return false;
        }

        $cache_key = 'has_products';
        if ( isset( $this->cache[ $cache_key ] ) ) {
            return $this->cache[ $cache_key ];
        }

        $products = wc_get_products( array(
            'limit' => 1,
            'status' => 'publish'
        ) );

        $has_products = ! empty( $products );
        $this->cache[ $cache_key ] = $has_products;

        return $has_products;
    }

    /**
     * Récupère les statistiques des produits.
     *
     * @since    1.2.0
     * @return   array    Statistiques des produits.
     */
    public function get_product_stats() {
        if ( ! $this->is_woocommerce_active() ) {
            throw new Exception( __( 'WooCommerce n\'est pas disponible.', 'boss-seo' ) );
        }

        $cache_key = 'product_stats';
        if ( isset( $this->cache[ $cache_key ] ) ) {
            return $this->cache[ $cache_key ];
        }

        // Récupérer tous les produits
        $products = wc_get_products( array(
            'limit' => -1,
            'status' => 'publish'
        ) );

        $total = count( $products );
        $optimized = 0;
        $needs_attention = 0;
        $critical = 0;
        $categories_stats = array();

        foreach ( $products as $product ) {
            $score = $this->calculate_seo_score( $product );

            if ( $score >= 80 ) {
                $optimized++;
            } elseif ( $score >= 60 ) {
                $needs_attention++;
            } else {
                $critical++;
            }

            // Statistiques par catégorie
            $categories = wp_get_post_terms( $product->get_id(), 'product_cat' );
            foreach ( $categories as $category ) {
                if ( ! isset( $categories_stats[ $category->name ] ) ) {
                    $categories_stats[ $category->name ] = array(
                        'name' => $category->name,
                        'count' => 0,
                        'optimized' => 0,
                        'needsAttention' => 0,
                        'critical' => 0
                    );
                }

                $categories_stats[ $category->name ]['count']++;

                if ( $score >= 80 ) {
                    $categories_stats[ $category->name ]['optimized']++;
                } elseif ( $score >= 60 ) {
                    $categories_stats[ $category->name ]['needsAttention']++;
                } else {
                    $categories_stats[ $category->name ]['critical']++;
                }
            }
        }

        $stats = array(
            'total' => $total,
            'optimized' => $optimized,
            'needsAttention' => $needs_attention,
            'critical' => $critical,
            'categories' => array_values( $categories_stats )
        );

        $this->cache[ $cache_key ] = $stats;
        return $stats;
    }

    /**
     * Calcule le score SEO d'un produit.
     *
     * @since    1.2.0
     * @param    WC_Product    $product    Le produit WooCommerce.
     * @return   int                      Score SEO (0-100).
     */
    public function calculate_seo_score( $product ) {
        $score = 0;
        $max_score = 100;

        // Titre (20 points)
        $title = $product->get_name();
        if ( ! empty( $title ) ) {
            if ( strlen( $title ) >= 30 && strlen( $title ) <= 60 ) {
                $score += 20;
            } elseif ( strlen( $title ) >= 20 ) {
                $score += 15;
            } else {
                $score += 5;
            }
        }

        // Description courte (15 points)
        $short_description = $product->get_short_description();
        if ( ! empty( $short_description ) ) {
            if ( strlen( $short_description ) >= 120 && strlen( $short_description ) <= 160 ) {
                $score += 15;
            } elseif ( strlen( $short_description ) >= 80 ) {
                $score += 10;
            } else {
                $score += 5;
            }
        }

        // Description longue (15 points)
        $description = $product->get_description();
        if ( ! empty( $description ) ) {
            if ( strlen( $description ) >= 300 ) {
                $score += 15;
            } elseif ( strlen( $description ) >= 150 ) {
                $score += 10;
            } else {
                $score += 5;
            }
        }

        // Images (20 points)
        $image_id = $product->get_image_id();
        if ( $image_id ) {
            $score += 10;

            // Vérifier le texte alternatif
            $alt_text = get_post_meta( $image_id, '_wp_attachment_image_alt', true );
            if ( ! empty( $alt_text ) ) {
                $score += 10;
            }
        }

        // Galerie d'images (10 points)
        $gallery_ids = $product->get_gallery_image_ids();
        if ( ! empty( $gallery_ids ) ) {
            $score += 10;
        }

        // Catégories (10 points)
        $categories = wp_get_post_terms( $product->get_id(), 'product_cat' );
        if ( ! empty( $categories ) ) {
            $score += 10;
        }

        // Prix (10 points)
        if ( $product->get_price() ) {
            $score += 10;
        }

        return min( $score, $max_score );
    }

    /**
     * Récupère l'activité récente.
     *
     * @since    1.2.0
     * @return   array    Activité récente.
     */
    public function get_recent_activity() {
        if ( ! $this->is_woocommerce_active() ) {
            return array();
        }

        $cache_key = 'recent_activity';
        if ( isset( $this->cache[ $cache_key ] ) ) {
            return $this->cache[ $cache_key ];
        }

        // Récupérer les produits récemment modifiés
        $recent_products = wc_get_products( array(
            'limit' => 5,
            'orderby' => 'modified',
            'order' => 'DESC',
            'status' => 'publish'
        ) );

        $activity = array();
        foreach ( $recent_products as $product ) {
            $activity[] = array(
                'type' => 'product_updated',
                'product_name' => $product->get_name(),
                'product_id' => $product->get_id(),
                'date' => $product->get_date_modified()->format( 'c' )
            );
        }

        $this->cache[ $cache_key ] = $activity;
        return $activity;
    }

    /**
     * Récupère les données de performance.
     *
     * @since    1.2.0
     * @return   array    Données de performance.
     */
    public function get_performance_data() {
        // Pour l'instant, retourner des données simulées
        // Dans une version future, intégrer avec Google Analytics ou Search Console
        return array(
            'current_month' => array(
                'clicks' => 0,
                'conversions' => 0,
                'revenue' => 0
            ),
            'previous_month' => array(
                'clicks' => 0,
                'conversions' => 0,
                'revenue' => 0
            ),
            'growth' => array(
                'clicks' => 0,
                'conversions' => 0,
                'revenue' => 0
            )
        );
    }

    /**
     * Récupère les produits avec pagination et filtres.
     *
     * @since    1.2.0
     * @param    array    $args    Arguments de la requête.
     * @return   array             Produits et métadonnées.
     */
    public function get_products_with_filters( $args = array() ) {
        if ( ! $this->is_woocommerce_active() ) {
            throw new Exception( __( 'WooCommerce n\'est pas disponible.', 'boss-seo' ) );
        }

        $defaults = array(
            'limit' => 20,
            'offset' => 0,
            'status' => 'publish',
            'orderby' => 'date',
            'order' => 'DESC',
            'category' => '',
            'search' => '',
            'seo_status' => ''
        );

        $args = wp_parse_args( $args, $defaults );

        // Arguments pour WooCommerce
        $wc_args = array(
            'limit' => $args['limit'],
            'offset' => $args['offset'],
            'status' => $args['status'],
            'orderby' => $args['orderby'],
            'order' => $args['order']
        );

        // Filtrer par catégorie
        if ( ! empty( $args['category'] ) && $args['category'] !== 'all' ) {
            $wc_args['category'] = array( $args['category'] );
        }

        // Filtrer par recherche
        if ( ! empty( $args['search'] ) ) {
            $wc_args['s'] = $args['search'];
        }

        // Récupérer les produits
        $products = wc_get_products( $wc_args );
        $total_products = wc_get_products( array_merge( $wc_args, array( 'limit' => -1, 'offset' => 0, 'return' => 'ids' ) ) );

        $formatted_products = array();
        foreach ( $products as $product ) {
            $score = $this->calculate_seo_score( $product );
            $categories = wp_get_post_terms( $product->get_id(), 'product_cat' );
            $category_name = ! empty( $categories ) ? $categories[0]->name : __( 'Non catégorisé', 'boss-seo' );

            // Déterminer le statut SEO
            if ( $score >= 80 ) {
                $seo_status = 'optimized';
            } elseif ( $score >= 60 ) {
                $seo_status = 'needs_attention';
            } else {
                $seo_status = 'critical';
            }

            $formatted_products[] = array(
                'id' => $product->get_id(),
                'name' => $product->get_name(),
                'category' => $category_name,
                'price' => $product->get_price(),
                'stock' => $product->get_stock_quantity(),
                'score' => $score,
                'seo_status' => $seo_status,
                'last_updated' => $product->get_date_modified()->format( 'Y-m-d' )
            );
        }

        // Filtrer par statut SEO si demandé
        if ( ! empty( $args['seo_status'] ) && $args['seo_status'] !== 'all' ) {
            $formatted_products = array_filter( $formatted_products, function( $product ) use ( $args ) {
                return $product['seo_status'] === $args['seo_status'];
            } );
            $formatted_products = array_values( $formatted_products );
        }

        return array(
            'products' => $formatted_products,
            'total' => count( $total_products ),
            'found' => count( $formatted_products )
        );
    }

    /**
     * Optimise un produit spécifique.
     *
     * @since    1.2.0
     * @param    int      $product_id    ID du produit.
     * @param    array    $settings      Paramètres d'optimisation.
     * @return   array                   Résultat de l'optimisation.
     */
    public function optimize_product( $product_id, $settings = array() ) {
        if ( ! $this->is_woocommerce_active() ) {
            throw new Exception( __( 'WooCommerce n\'est pas disponible.', 'boss-seo' ) );
        }

        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            throw new Exception( __( 'Produit non trouvé.', 'boss-seo' ) );
        }

        $optimizations = array();

        // Optimiser le titre si nécessaire
        if ( ! empty( $settings['optimize_title'] ) ) {
            $title = $product->get_name();
            if ( strlen( $title ) < 30 ) {
                $categories = wp_get_post_terms( $product_id, 'product_cat' );
                if ( ! empty( $categories ) ) {
                    $category_name = $categories[0]->name;
                    if ( strpos( $title, $category_name ) === false ) {
                        $new_title = $title . ' - ' . $category_name;
                        wp_update_post( array(
                            'ID' => $product_id,
                            'post_title' => $new_title
                        ) );
                        $optimizations[] = 'title_optimized';
                    }
                }
            }
        }

        // Optimiser la description courte
        if ( ! empty( $settings['optimize_description'] ) ) {
            $short_description = $product->get_short_description();
            if ( strlen( $short_description ) < 80 ) {
                $description = $product->get_description();
                if ( ! empty( $description ) ) {
                    $new_short_description = wp_trim_words( $description, 20, '...' );
                    update_post_meta( $product_id, '_product_short_description', $new_short_description );
                    $optimizations[] = 'description_optimized';
                }
            }
        }

        // Optimiser les images
        if ( ! empty( $settings['optimize_images'] ) ) {
            $image_id = $product->get_image_id();
            if ( $image_id ) {
                $alt_text = get_post_meta( $image_id, '_wp_attachment_image_alt', true );
                if ( empty( $alt_text ) ) {
                    update_post_meta( $image_id, '_wp_attachment_image_alt', $product->get_name() );
                    $optimizations[] = 'images_optimized';
                }
            }
        }

        // Recalculer le score
        $new_score = $this->calculate_seo_score( $product );

        return array(
            'product_id' => $product_id,
            'old_score' => $this->calculate_seo_score( $product ), // Score avant optimisation
            'new_score' => $new_score,
            'optimizations' => $optimizations,
            'timestamp' => current_time( 'timestamp' )
        );
    }

    /**
     * Vide le cache.
     *
     * @since    1.2.0
     */
    public function clear_cache() {
        $this->cache = array();
    }
}
