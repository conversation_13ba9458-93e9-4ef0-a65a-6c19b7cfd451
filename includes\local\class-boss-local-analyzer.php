<?php
/**
 * Classe pour l'analyse SEO locale.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour l'analyse SEO locale.
 *
 * Cette classe gère l'analyse SEO locale.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_Analyzer {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_local_analyzer_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_analyze_location', array( $this, 'ajax_analyze_location' ) );
        add_action( 'wp_ajax_boss_seo_get_location_analysis', array( $this, 'ajax_get_location_analysis' ) );
        add_action( 'wp_ajax_boss_seo_get_analysis_history', array( $this, 'ajax_get_analysis_history' ) );

        // Ajouter les actions pour les tâches planifiées
        add_action( 'boss_seo_daily_analysis', array( $this, 'analyze_all_locations' ) );

        // Ajouter les actions pour les métaboxes
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/local/analyze-location',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'analyze_location' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/location-analysis/(?P<id>\d+)',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_location_analysis' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/analysis-history/(?P<id>\d+)',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_analysis_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Ajoute les métaboxes pour les emplacements.
     *
     * @since    1.2.0
     */
    public function add_meta_boxes() {
        add_meta_box(
            'boss_local_location_analysis',
            __( 'Analyse SEO locale', 'boss-seo' ),
            array( $this, 'render_location_analysis_meta_box' ),
            'boss_local_location',
            'normal',
            'default'
        );
    }

    /**
     * Affiche la métabox d'analyse SEO locale.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     */
    public function render_location_analysis_meta_box( $post ) {
        // Récupérer l'analyse
        $analysis = $this->get_location_analysis_data( $post->ID );

        // Afficher l'analyse
        include plugin_dir_path( dirname( dirname( __FILE__ ) ) ) . 'admin/partials/boss-local-location-analysis-meta-box.php';
    }

    /**
     * Gère les requêtes AJAX pour analyser un emplacement.
     *
     * @since    1.2.0
     */
    public function ajax_analyze_location() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );

        // Analyser l'emplacement
        $analysis = $this->analyze_location_data( $location_id );

        if ( is_wp_error( $analysis ) ) {
            wp_send_json_error( array( 'message' => $analysis->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Emplacement analysé avec succès.', 'boss-seo' ),
            'analysis' => $analysis,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer l'analyse d'un emplacement.
     *
     * @since    1.2.0
     */
    public function ajax_get_location_analysis() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );

        // Récupérer l'analyse
        $analysis = $this->get_location_analysis_data( $location_id );

        wp_send_json_success( array(
            'message'  => __( 'Analyse récupérée avec succès.', 'boss-seo' ),
            'analysis' => $analysis,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer l'historique des analyses.
     *
     * @since    1.2.0
     */
    public function ajax_get_analysis_history() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );

        // Récupérer l'historique
        $history = $this->get_analysis_history_data( $location_id );

        wp_send_json_success( array(
            'message' => __( 'Historique récupéré avec succès.', 'boss-seo' ),
            'history' => $history,
        ) );
    }

    /**
     * Analyse un emplacement via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function analyze_location( $request ) {
        $location_id = $request->get_param( 'location_id' );

        if ( ! $location_id ) {
            return new WP_Error( 'missing_location_id', __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $analysis = $this->analyze_location_data( $location_id );

        if ( is_wp_error( $analysis ) ) {
            return $analysis;
        }

        return rest_ensure_response( $analysis );
    }

    /**
     * Récupère l'analyse d'un emplacement via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_location_analysis( $request ) {
        $location_id = $request['id'];

        $analysis = $this->get_location_analysis_data( $location_id );

        return rest_ensure_response( $analysis );
    }

    /**
     * Récupère l'historique des analyses via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_analysis_history( $request ) {
        $location_id = $request['id'];

        $history = $this->get_analysis_history_data( $location_id );

        return rest_ensure_response( $history );
    }

    /**
     * Analyse tous les emplacements.
     *
     * @since    1.2.0
     */
    public function analyze_all_locations() {
        // Récupérer tous les emplacements
        $locations = get_posts( array(
            'post_type'      => 'boss_local_location',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
        ) );

        foreach ( $locations as $location ) {
            $this->analyze_location_data( $location->ID );
        }
    }

    /**
     * Analyse un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array|WP_Error            L'analyse ou une erreur.
     */
    private function analyze_location_data( $location_id ) {
        // Vérifier si l'emplacement existe
        $location = get_post( $location_id );

        if ( ! $location || $location->post_type !== 'boss_local_location' ) {
            return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ) );
        }

        // Récupérer les données de l'emplacement
        $title = $location->post_title;
        $content = $location->post_content;
        $excerpt = $location->post_excerpt;

        // Récupérer les métadonnées
        $address = get_post_meta( $location_id, '_boss_local_location_address', true );
        $city = get_post_meta( $location_id, '_boss_local_location_city', true );
        $state = get_post_meta( $location_id, '_boss_local_location_state', true );
        $postal_code = get_post_meta( $location_id, '_boss_local_location_postal_code', true );
        $country = get_post_meta( $location_id, '_boss_local_location_country', true );
        $phone = get_post_meta( $location_id, '_boss_local_location_phone', true );
        $email = get_post_meta( $location_id, '_boss_local_location_email', true );
        $website = get_post_meta( $location_id, '_boss_local_location_website', true );
        $latitude = get_post_meta( $location_id, '_boss_local_location_latitude', true );
        $longitude = get_post_meta( $location_id, '_boss_local_location_longitude', true );
        $hours = get_post_meta( $location_id, '_boss_local_location_hours', true );

        // Récupérer les mots-clés
        $keywords = $this->get_location_keywords( $location_id );

        // Récupérer les pages locales
        $pages = $this->get_location_pages( $location_id );

        // Effectuer l'analyse
        $analysis = array(
            'location_id'   => $location_id,
            'date'          => current_time( 'mysql' ),
            'score'         => 0,
            'issues'        => array(),
            'improvements'  => array(),
            'good'          => array(),
            'keywords'      => $keywords,
            'pages'         => $pages,
        );

        // Analyser le titre
        if ( empty( $title ) ) {
            $analysis['issues'][] = array(
                'type'    => 'title_missing',
                'message' => __( 'Le titre de l\'emplacement est manquant.', 'boss-seo' ),
            );
        } elseif ( strlen( $title ) < 20 ) {
            $analysis['improvements'][] = array(
                'type'    => 'title_short',
                'message' => __( 'Le titre de l\'emplacement est trop court.', 'boss-seo' ),
            );
        } elseif ( strlen( $title ) > 60 ) {
            $analysis['improvements'][] = array(
                'type'    => 'title_long',
                'message' => __( 'Le titre de l\'emplacement est trop long.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'title_good',
                'message' => __( 'Le titre de l\'emplacement a une longueur optimale.', 'boss-seo' ),
            );
        }

        // Analyser le contenu
        if ( empty( $content ) ) {
            $analysis['issues'][] = array(
                'type'    => 'content_missing',
                'message' => __( 'Le contenu de l\'emplacement est manquant.', 'boss-seo' ),
            );
        } elseif ( strlen( $content ) < 300 ) {
            $analysis['improvements'][] = array(
                'type'    => 'content_short',
                'message' => __( 'Le contenu de l\'emplacement est trop court.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'content_good',
                'message' => __( 'Le contenu de l\'emplacement a une longueur optimale.', 'boss-seo' ),
            );
        }

        // Analyser l'extrait
        if ( empty( $excerpt ) ) {
            $analysis['improvements'][] = array(
                'type'    => 'excerpt_missing',
                'message' => __( 'L\'extrait de l\'emplacement est manquant.', 'boss-seo' ),
            );
        } elseif ( strlen( $excerpt ) < 50 ) {
            $analysis['improvements'][] = array(
                'type'    => 'excerpt_short',
                'message' => __( 'L\'extrait de l\'emplacement est trop court.', 'boss-seo' ),
            );
        } elseif ( strlen( $excerpt ) > 160 ) {
            $analysis['improvements'][] = array(
                'type'    => 'excerpt_long',
                'message' => __( 'L\'extrait de l\'emplacement est trop long.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'excerpt_good',
                'message' => __( 'L\'extrait de l\'emplacement a une longueur optimale.', 'boss-seo' ),
            );
        }

        // Analyser l'adresse
        if ( empty( $address ) ) {
            $analysis['issues'][] = array(
                'type'    => 'address_missing',
                'message' => __( 'L\'adresse de l\'emplacement est manquante.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'address_good',
                'message' => __( 'L\'adresse de l\'emplacement est renseignée.', 'boss-seo' ),
            );
        }

        // Analyser la ville
        if ( empty( $city ) ) {
            $analysis['issues'][] = array(
                'type'    => 'city_missing',
                'message' => __( 'La ville de l\'emplacement est manquante.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'city_good',
                'message' => __( 'La ville de l\'emplacement est renseignée.', 'boss-seo' ),
            );
        }

        // Analyser le code postal
        if ( empty( $postal_code ) ) {
            $analysis['issues'][] = array(
                'type'    => 'postal_code_missing',
                'message' => __( 'Le code postal de l\'emplacement est manquant.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'postal_code_good',
                'message' => __( 'Le code postal de l\'emplacement est renseigné.', 'boss-seo' ),
            );
        }

        // Analyser le pays
        if ( empty( $country ) ) {
            $analysis['issues'][] = array(
                'type'    => 'country_missing',
                'message' => __( 'Le pays de l\'emplacement est manquant.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'country_good',
                'message' => __( 'Le pays de l\'emplacement est renseigné.', 'boss-seo' ),
            );
        }

        // Analyser le téléphone
        if ( empty( $phone ) ) {
            $analysis['improvements'][] = array(
                'type'    => 'phone_missing',
                'message' => __( 'Le numéro de téléphone de l\'emplacement est manquant.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'phone_good',
                'message' => __( 'Le numéro de téléphone de l\'emplacement est renseigné.', 'boss-seo' ),
            );
        }

        // Analyser l'email
        if ( empty( $email ) ) {
            $analysis['improvements'][] = array(
                'type'    => 'email_missing',
                'message' => __( 'L\'adresse e-mail de l\'emplacement est manquante.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'email_good',
                'message' => __( 'L\'adresse e-mail de l\'emplacement est renseignée.', 'boss-seo' ),
            );
        }

        // Analyser le site web
        if ( empty( $website ) ) {
            $analysis['improvements'][] = array(
                'type'    => 'website_missing',
                'message' => __( 'Le site web de l\'emplacement est manquant.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'website_good',
                'message' => __( 'Le site web de l\'emplacement est renseigné.', 'boss-seo' ),
            );
        }

        // Analyser les coordonnées géographiques
        if ( empty( $latitude ) || empty( $longitude ) ) {
            $analysis['issues'][] = array(
                'type'    => 'coordinates_missing',
                'message' => __( 'Les coordonnées géographiques de l\'emplacement sont manquantes.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'coordinates_good',
                'message' => __( 'Les coordonnées géographiques de l\'emplacement sont renseignées.', 'boss-seo' ),
            );
        }

        // Analyser les horaires d'ouverture
        if ( empty( $hours ) ) {
            $analysis['improvements'][] = array(
                'type'    => 'hours_missing',
                'message' => __( 'Les horaires d\'ouverture de l\'emplacement sont manquants.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'hours_good',
                'message' => __( 'Les horaires d\'ouverture de l\'emplacement sont renseignés.', 'boss-seo' ),
            );
        }

        // Analyser les mots-clés
        if ( empty( $keywords ) ) {
            $analysis['improvements'][] = array(
                'type'    => 'keywords_missing',
                'message' => __( 'Aucun mot-clé n\'est associé à cet emplacement.', 'boss-seo' ),
            );
        } elseif ( count( $keywords ) < 5 ) {
            $analysis['improvements'][] = array(
                'type'    => 'keywords_few',
                'message' => __( 'Peu de mots-clés sont associés à cet emplacement.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'keywords_good',
                'message' => __( 'Un bon nombre de mots-clés sont associés à cet emplacement.', 'boss-seo' ),
            );
        }

        // Analyser les pages locales
        if ( empty( $pages ) ) {
            $analysis['improvements'][] = array(
                'type'    => 'pages_missing',
                'message' => __( 'Aucune page locale n\'est associée à cet emplacement.', 'boss-seo' ),
            );
        } else {
            $analysis['good'][] = array(
                'type'    => 'pages_good',
                'message' => __( 'Des pages locales sont associées à cet emplacement.', 'boss-seo' ),
            );
        }

        // Calculer le score
        $total_issues = count( $analysis['issues'] );
        $total_improvements = count( $analysis['improvements'] );
        $total_good = count( $analysis['good'] );
        $total_checks = $total_issues + $total_improvements + $total_good;

        if ( $total_checks > 0 ) {
            $analysis['score'] = round( ( $total_good / $total_checks ) * 100 );
        }

        // Enregistrer l'analyse
        update_post_meta( $location_id, '_boss_local_analysis', $analysis );

        // Enregistrer l'historique
        $this->save_analysis_history( $location_id, $analysis );

        return $analysis;
    }

    /**
     * Récupère l'analyse d'un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array                     L'analyse.
     */
    private function get_location_analysis_data( $location_id ) {
        $analysis = get_post_meta( $location_id, '_boss_local_analysis', true );

        if ( empty( $analysis ) ) {
            // Effectuer une analyse si aucune n'existe
            $analysis = $this->analyze_location_data( $location_id );

            if ( is_wp_error( $analysis ) ) {
                return array(
                    'location_id'   => $location_id,
                    'date'          => current_time( 'mysql' ),
                    'score'         => 0,
                    'issues'        => array(),
                    'improvements'  => array(),
                    'good'          => array(),
                    'keywords'      => array(),
                    'pages'         => array(),
                );
            }
        }

        return $analysis;
    }

    /**
     * Récupère l'historique des analyses d'un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array                     L'historique des analyses.
     */
    private function get_analysis_history_data( $location_id ) {
        $history = get_post_meta( $location_id, '_boss_local_analysis_history', true );

        if ( empty( $history ) ) {
            return array();
        }

        return $history;
    }

    /**
     * Enregistre une analyse dans l'historique.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @param    array     $analysis       L'analyse.
     */
    private function save_analysis_history( $location_id, $analysis ) {
        $history = get_post_meta( $location_id, '_boss_local_analysis_history', true );

        if ( empty( $history ) ) {
            $history = array();
        }

        // Limiter l'historique à 30 entrées
        if ( count( $history ) >= 30 ) {
            array_shift( $history );
        }

        // Ajouter l'analyse à l'historique
        $history[] = array(
            'date'  => $analysis['date'],
            'score' => $analysis['score'],
        );

        update_post_meta( $location_id, '_boss_local_analysis_history', $history );
    }

    /**
     * Récupère les mots-clés d'un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array                     Les mots-clés.
     */
    private function get_location_keywords( $location_id ) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'boss_local_rankings';

        // Vérifier si la table existe
        if ( $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) != $table_name ) {
            return array();
        }

        // Récupérer les mots-clés
        $keywords = $wpdb->get_results( $wpdb->prepare( "SELECT * FROM $table_name WHERE location_id = %d", $location_id ), ARRAY_A );

        return $keywords ? $keywords : array();
    }

    /**
     * Récupère les pages locales d'un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array                     Les pages locales.
     */
    private function get_location_pages( $location_id ) {
        $args = array(
            'post_type'      => 'boss_local_page',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
            'meta_query'     => array(
                array(
                    'key'     => '_boss_local_page_location_id',
                    'value'   => $location_id,
                    'compare' => '=',
                ),
            ),
        );

        $query = new WP_Query( $args );

        $pages = array();

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();

                $pages[] = array(
                    'id'        => get_the_ID(),
                    'title'     => get_the_title(),
                    'permalink' => get_permalink(),
                );
            }

            wp_reset_postdata();
        }

        return $pages;
    }
}
