import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Card<PERSON>ooter,
  Dashicon
} from '@wordpress/components';

/**
 * Composant pour la section d'aperçu global
 */
const OverviewSection = ({ data, onStartAnalysis, isAnalyzing }) => {
  // Fonction pour formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Fonction pour obtenir la classe de couleur en fonction du score
  const getScoreColorClass = (score) => {
    if (score >= 90) return 'boss-text-green-600';
    if (score >= 70) return 'boss-text-yellow-600';
    if (score >= 50) return 'boss-text-orange-600';
    return 'boss-text-red-600';
  };

  // Fonction pour obtenir la couleur de fond en fonction du score
  const getScoreBackgroundClass = (score) => {
    if (score >= 90) return 'boss-bg-green-100';
    if (score >= 70) return 'boss-bg-yellow-100';
    if (score >= 50) return 'boss-bg-orange-100';
    return 'boss-bg-red-100';
  };

  // Fonction pour obtenir le texte d'évaluation en fonction du score
  const getScoreLabel = (score) => {
    if (score >= 90) return __('Excellent', 'boss-seo');
    if (score >= 70) return __('Bon', 'boss-seo');
    if (score >= 50) return __('Moyen', 'boss-seo');
    return __('À améliorer', 'boss-seo');
  };

  // Calculer le nombre total de problèmes
  const getTotalIssues = () => {
    if (!data || !data.issues) return 0;

    return (
      data.issues.critical.length +
      data.issues.errors.length +
      data.issues.warnings.length +
      data.issues.improvements.length
    );
  };

  return (
    <div>
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-6 boss-mb-6">
        {/* Carte du score global */}
        <Card className="boss-h-full">
          <CardHeader>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Score technique global', 'boss-seo')}
            </h3>
          </CardHeader>
          <CardBody>
            <div className="boss-flex boss-flex-col boss-items-center boss-justify-center boss-p-4">
              <div className="boss-relative boss-w-48 boss-h-48 boss-mb-4">
                {/* Cercle de fond */}
                <svg className="boss-w-full boss-h-full boss-transform boss-rotate-[-90deg]" viewBox="0 0 100 100">
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="transparent"
                    stroke="#e5e7eb"
                    strokeWidth="10"
                  />
                  {/* Cercle de progression */}
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="transparent"
                    stroke={
                      data.score >= 90 ? '#10B981' :
                      data.score >= 70 ? '#FBBF24' :
                      data.score >= 50 ? '#F97316' :
                      '#EF4444'
                    }
                    strokeWidth="10"
                    strokeDasharray={`${2 * Math.PI * 45 * data.score / 100} ${2 * Math.PI * 45 * (1 - data.score / 100)}`}
                    strokeLinecap="round"
                    className="boss-transition-all boss-duration-1000 boss-ease-out"
                  />
                </svg>
                {/* Score au centre */}
                <div className="boss-absolute boss-inset-0 boss-flex boss-flex-col boss-items-center boss-justify-center">
                  <span className={`boss-text-5xl boss-font-bold ${getScoreColorClass(data.score)}`}>
                    {data.score}
                  </span>
                  <span className="boss-text-sm boss-text-boss-gray">{__('sur', 'boss-seo')} 100</span>
                </div>
              </div>

              <div className={`boss-text-center boss-px-4 boss-py-2 boss-rounded-full ${getScoreBackgroundClass(data.score)} ${getScoreColorClass(data.score)} boss-font-medium boss-mb-4`}>
                {getScoreLabel(data.score)}
              </div>

              <p className="boss-text-center boss-text-boss-gray boss-mb-4">
                {__('Dernière analyse :', 'boss-seo')} {formatDate(data.lastAnalysis)}
              </p>

              <Button
                isPrimary
                isBusy={isAnalyzing}
                disabled={isAnalyzing}
                className="boss-w-full boss-justify-center"
                onClick={onStartAnalysis}
              >
                {isAnalyzing
                  ? __('Analyse en cours...', 'boss-seo')
                  : __('Lancer une analyse complète', 'boss-seo')
                }
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* Carte des statistiques */}
        <Card className="boss-h-full">
          <CardHeader>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Problèmes détectés', 'boss-seo')}
            </h3>
          </CardHeader>
          <CardBody>
            <div className="boss-space-y-4">
              {/* Problèmes critiques */}
              <div className="boss-flex boss-justify-between boss-items-center boss-p-3 boss-bg-red-50 boss-rounded-lg">
                <div className="boss-flex boss-items-center">
                  <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-red-100 boss-flex boss-items-center boss-justify-center boss-mr-3">
                    <Dashicon icon="warning" className="boss-text-red-600" />
                  </div>
                  <span className="boss-font-medium boss-text-boss-dark">
                    {__('Critiques', 'boss-seo')}
                  </span>
                </div>
                <span className="boss-text-xl boss-font-bold boss-text-red-600">
                  {data.issues.critical.length}
                </span>
              </div>

              {/* Erreurs */}
              <div className="boss-flex boss-justify-between boss-items-center boss-p-3 boss-bg-orange-50 boss-rounded-lg">
                <div className="boss-flex boss-items-center">
                  <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-orange-100 boss-flex boss-items-center boss-justify-center boss-mr-3">
                    <Dashicon icon="dismiss" className="boss-text-orange-600" />
                  </div>
                  <span className="boss-font-medium boss-text-boss-dark">
                    {__('Erreurs', 'boss-seo')}
                  </span>
                </div>
                <span className="boss-text-xl boss-font-bold boss-text-orange-600">
                  {data.issues.errors.length}
                </span>
              </div>

              {/* Avertissements */}
              <div className="boss-flex boss-justify-between boss-items-center boss-p-3 boss-bg-yellow-50 boss-rounded-lg">
                <div className="boss-flex boss-items-center">
                  <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-yellow-100 boss-flex boss-items-center boss-justify-center boss-mr-3">
                    <Dashicon icon="flag" className="boss-text-yellow-600" />
                  </div>
                  <span className="boss-font-medium boss-text-boss-dark">
                    {__('Avertissements', 'boss-seo')}
                  </span>
                </div>
                <span className="boss-text-xl boss-font-bold boss-text-yellow-600">
                  {data.issues.warnings.length}
                </span>
              </div>

              {/* Améliorations */}
              <div className="boss-flex boss-justify-between boss-items-center boss-p-3 boss-bg-blue-50 boss-rounded-lg">
                <div className="boss-flex boss-items-center">
                  <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-blue-100 boss-flex boss-items-center boss-justify-center boss-mr-3">
                    <Dashicon icon="lightbulb" className="boss-text-blue-600" />
                  </div>
                  <span className="boss-font-medium boss-text-boss-dark">
                    {__('Améliorations', 'boss-seo')}
                  </span>
                </div>
                <span className="boss-text-xl boss-font-bold boss-text-blue-600">
                  {data.issues.improvements.length}
                </span>
              </div>

              {/* Réussites */}
              <div className="boss-flex boss-justify-between boss-items-center boss-p-3 boss-bg-green-50 boss-rounded-lg">
                <div className="boss-flex boss-items-center">
                  <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-green-100 boss-flex boss-items-center boss-justify-center boss-mr-3">
                    <Dashicon icon="yes-alt" className="boss-text-green-600" />
                  </div>
                  <span className="boss-font-medium boss-text-boss-dark">
                    {__('Réussites', 'boss-seo')}
                  </span>
                </div>
                <span className="boss-text-xl boss-font-bold boss-text-green-600">
                  {data.issues.successes.length}
                </span>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Carte des catégories */}
        <Card className="boss-h-full">
          <CardHeader>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Catégories', 'boss-seo')}
            </h3>
          </CardHeader>
          <CardBody>
            <div className="boss-space-y-4">
              {data.categories && Object.entries(data.categories).map(([key, category]) => (
                <div key={key} className="boss-relative boss-pt-1">
                  <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                    <span className="boss-font-medium boss-text-boss-dark boss-capitalize">
                      {key === 'seo' ? 'SEO' :
                       key === 'bestPractices' ? __('Bonnes pratiques', 'boss-seo') :
                       __(key, 'boss-seo')}
                    </span>
                    <div className="boss-flex boss-items-center">
                      <span className={`boss-font-bold boss-mr-2 ${getScoreColorClass(category.score)}`}>
                        {category.score}
                      </span>
                      <span className="boss-text-sm boss-text-boss-gray">
                        ({category.issues} {__('problèmes', 'boss-seo')})
                      </span>
                    </div>
                  </div>
                  <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2.5">
                    <div
                      className={`boss-h-2.5 boss-rounded-full ${
                        category.color === 'green' ? 'boss-bg-green-500' :
                        category.color === 'yellow' ? 'boss-bg-yellow-500' :
                        category.color === 'orange' ? 'boss-bg-orange-500' :
                        'boss-bg-red-500'
                      }`}
                      style={{ width: `${category.score}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
          <CardFooter>
            <Button
              isSecondary
              className="boss-w-full boss-justify-center"
              onClick={() => window.location.hash = '#issues'}
            >
              {__('Voir tous les problèmes', 'boss-seo')} ({getTotalIssues()})
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Résumé des problèmes */}
      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Problèmes prioritaires', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody className="boss-p-0">
          <div className="boss-divide-y boss-divide-gray-200">
            {data.issues && data.issues.critical && data.issues.critical.slice(0, 2).map((issue) => (
              <div key={issue.id} className="boss-p-4 boss-flex boss-items-start">
                <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-red-100 boss-flex boss-items-center boss-justify-center boss-mr-3 boss-flex-shrink-0">
                  <Dashicon icon="warning" className="boss-text-red-600" />
                </div>
                <div className="boss-flex-1">
                  <h4 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                    {issue.title}
                  </h4>
                  <p className="boss-text-sm boss-text-boss-gray boss-mb-2">
                    {issue.description}
                  </p>
                  <Button
                    isSmall
                    isSecondary
                    className="boss-text-xs"
                    onClick={() => window.location.hash = `#issue-${issue.id}`}
                  >
                    {__('Voir les détails', 'boss-seo')}
                  </Button>
                </div>
              </div>
            ))}

            {data.issues && data.issues.errors && data.issues.errors.slice(0, 1).map((issue) => (
              <div key={issue.id} className="boss-p-4 boss-flex boss-items-start">
                <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-orange-100 boss-flex boss-items-center boss-justify-center boss-mr-3 boss-flex-shrink-0">
                  <Dashicon icon="dismiss" className="boss-text-orange-600" />
                </div>
                <div className="boss-flex-1">
                  <h4 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                    {issue.title}
                  </h4>
                  <p className="boss-text-sm boss-text-boss-gray boss-mb-2">
                    {issue.description}
                  </p>
                  <Button
                    isSmall
                    isSecondary
                    className="boss-text-xs"
                    onClick={() => window.location.hash = `#issue-${issue.id}`}
                  >
                    {__('Voir les détails', 'boss-seo')}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardBody>
        <CardFooter>
          <Button
            isSecondary
            className="boss-w-full boss-justify-center"
            onClick={() => window.location.hash = '#issues'}
          >
            {__('Voir tous les problèmes', 'boss-seo')}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default OverviewSection;
