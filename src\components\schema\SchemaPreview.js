import { __ } from '@wordpress/i18n';
import {
  Button,
  Dashicon,
  Notice
} from '@wordpress/components';

const SchemaPreview = ({ schema, schemaStructure, mode, jsonLd }) => {
  // Fonction pour copier le code JSON-LD
  const copyJsonLd = () => {
    navigator.clipboard.writeText(jsonLd);
    // Ici, vous pourriez ajouter une notification de succès
  };

  // Rendu du mode code
  if (mode === 'code') {
    return (
      <div>
        <div className="boss-flex boss-justify-between boss-items-center boss-mb-3">
          <h3 className="boss-text-sm boss-font-medium boss-text-boss-gray">
            {__('Code JSON-LD', 'boss-seo')}
          </h3>
          <Button
            isSmall
            isSecondary
            onClick={copyJsonLd}
            icon="clipboard"
          >
            {__('Copier', 'boss-seo')}
          </Button>
        </div>
        <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-overflow-auto boss-max-h-96">
          <pre className="boss-text-xs boss-font-mono boss-whitespace-pre boss-text-boss-dark">
            {jsonLd}
          </pre>
        </div>
      </div>
    );
  }

  // Rendu du mode visuel
  return (
    <div>
      <div className="boss-mb-4">
        <h3 className="boss-text-sm boss-font-medium boss-text-boss-gray boss-mb-2">
          {__('Aperçu dans les résultats de recherche', 'boss-seo')}
        </h3>
        
        {/* Prévisualisation en fonction du type de schéma */}
        {schemaStructure['@type'] === 'Article' && (
          <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-bg-white">
            <div className="boss-text-xs boss-text-green-600 boss-mb-1">
              {schema.properties?.publisher?.name || __('Nom du site', 'boss-seo')}
            </div>
            <div className="boss-text-blue-600 boss-text-lg boss-font-medium boss-mb-1">
              {schema.properties?.headline || __('Titre de l\'article', 'boss-seo')}
            </div>
            <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
              {schema.properties?.description || __('Description de l\'article...', 'boss-seo')}
            </div>
            <div className="boss-text-xs boss-text-boss-gray">
              {schema.properties?.datePublished || '2023-06-15'} - {__('Par', 'boss-seo')} {schema.properties?.author?.name || __('Auteur', 'boss-seo')}
            </div>
          </div>
        )}
        
        {schemaStructure['@type'] === 'Product' && (
          <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-bg-white">
            <div className="boss-text-xs boss-text-green-600 boss-mb-1">
              {schema.properties?.brand?.name || __('Marque', 'boss-seo')}
            </div>
            <div className="boss-text-blue-600 boss-text-lg boss-font-medium boss-mb-1">
              {schema.properties?.name || __('Nom du produit', 'boss-seo')}
            </div>
            <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
              {schema.properties?.description || __('Description du produit...', 'boss-seo')}
            </div>
            <div className="boss-flex boss-items-center boss-text-xs boss-text-boss-gray">
              <div className="boss-flex boss-items-center boss-text-yellow-500 boss-mr-2">
                {Array(5).fill(0).map((_, i) => (
                  <Dashicon key={i} icon="star-filled" />
                ))}
              </div>
              <span>
                {schema.properties?.aggregateRating?.ratingValue || '4.5'} ({schema.properties?.aggregateRating?.reviewCount || '42'} {__('avis', 'boss-seo')})
              </span>
              <span className="boss-mx-2">•</span>
              <span className="boss-font-bold boss-text-green-700">
                {schema.properties?.offers?.price || '99.99'} {schema.properties?.offers?.priceCurrency || 'EUR'}
              </span>
            </div>
          </div>
        )}
        
        {schemaStructure['@type'] === 'LocalBusiness' && (
          <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-bg-white">
            <div className="boss-text-blue-600 boss-text-lg boss-font-medium boss-mb-1">
              {schema.properties?.name || __('Nom de l\'entreprise', 'boss-seo')}
            </div>
            <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
              {schema.properties?.description || __('Description de l\'entreprise...', 'boss-seo')}
            </div>
            <div className="boss-text-xs boss-text-boss-gray boss-mb-1">
              <Dashicon icon="location" className="boss-mr-1" />
              {schema.properties?.address?.streetAddress || __('123 Rue Principale')}, 
              {schema.properties?.address?.addressLocality || __('Ville')}, 
              {schema.properties?.address?.postalCode || __('75000')}
            </div>
            <div className="boss-text-xs boss-text-boss-gray">
              <Dashicon icon="phone" className="boss-mr-1" />
              {schema.properties?.telephone || __('01 23 45 67 89')}
            </div>
          </div>
        )}
        
        {schemaStructure['@type'] === 'FAQPage' && (
          <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-bg-white">
            <div className="boss-text-blue-600 boss-text-lg boss-font-medium boss-mb-3">
              {__('FAQ', 'boss-seo')}
            </div>
            <div className="boss-mb-3">
              <div className="boss-font-medium boss-mb-1">
                {schema.properties?.mainEntity?.name || __('Question fréquente ?', 'boss-seo')}
              </div>
              <div className="boss-text-sm boss-text-boss-gray">
                {schema.properties?.mainEntity?.acceptedAnswer?.text || __('Réponse à la question...', 'boss-seo')}
              </div>
            </div>
          </div>
        )}
        
        {schemaStructure['@type'] === 'Event' && (
          <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-bg-white">
            <div className="boss-text-xs boss-text-green-600 boss-mb-1">
              {schema.properties?.organizer?.name || __('Organisateur', 'boss-seo')}
            </div>
            <div className="boss-text-blue-600 boss-text-lg boss-font-medium boss-mb-1">
              {schema.properties?.name || __('Nom de l\'événement', 'boss-seo')}
            </div>
            <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
              {schema.properties?.description || __('Description de l\'événement...', 'boss-seo')}
            </div>
            <div className="boss-text-xs boss-text-boss-gray boss-mb-1">
              <Dashicon icon="calendar-alt" className="boss-mr-1" />
              {schema.properties?.startDate || '2023-06-30T18:00'} 
              {schema.properties?.endDate && ` - ${schema.properties.endDate}`}
            </div>
            <div className="boss-text-xs boss-text-boss-gray">
              <Dashicon icon="location" className="boss-mr-1" />
              {schema.properties?.location?.name || __('Lieu de l\'événement')}
            </div>
          </div>
        )}
        
        {/* Prévisualisation par défaut si le type n'est pas géré spécifiquement */}
        {!['Article', 'Product', 'LocalBusiness', 'FAQPage', 'Event'].includes(schemaStructure['@type']) && (
          <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-bg-white">
            <div className="boss-text-blue-600 boss-text-lg boss-font-medium boss-mb-1">
              {schema.name || __('Nom du schéma', 'boss-seo')}
            </div>
            <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
              {__('Prévisualisation non disponible pour ce type de schéma.', 'boss-seo')}
            </div>
            <div className="boss-text-xs boss-text-boss-gray">
              {__('Type:', 'boss-seo')} {schemaStructure['@type']}
            </div>
          </div>
        )}
      </div>
      
      <Notice status="info" isDismissible={false}>
        <p>
          {__('Cette prévisualisation est une approximation de l\'apparence dans les résultats de recherche. L\'affichage réel peut varier.', 'boss-seo')}
        </p>
      </Notice>
    </div>
  );
};

export default SchemaPreview;
