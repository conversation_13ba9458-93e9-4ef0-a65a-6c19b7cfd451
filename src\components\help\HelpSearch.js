import { useState, useEffect, useRef } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { TextControl, Spinner, Button } from '@wordpress/components';

/**
 * Composant de recherche pour la documentation
 * 
 * @param {Object} props - Propriétés du composant
 * @param {Function} props.onSearch - Callback avec les résultats de recherche
 * @param {Array} props.articles - Articles de la base de connaissances
 * @param {boolean} props.showInstantResults - Afficher les résultats instantanés
 * @param {number} props.maxResults - Nombre maximum de résultats à afficher
 * @param {Function} props.onResultClick - Callback lors du clic sur un résultat
 * @returns {React.ReactElement} Composant HelpSearch
 */
const HelpSearch = ({ 
  onSearch = () => {}, 
  articles = [],
  showInstantResults = true,
  maxResults = 5,
  onResultClick = () => {}
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [showResults, setShowResults] = useState(false);
  const searchRef = useRef(null);
  
  // Effectuer la recherche
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }
    
    const performSearch = () => {
      setIsSearching(true);
      
      // Simuler un délai de recherche
      setTimeout(() => {
        // Recherche simple dans les articles
        const results = articles.filter(article => {
          const query = searchQuery.toLowerCase();
          return (
            article.title.toLowerCase().includes(query) ||
            article.content.toLowerCase().includes(query) ||
            article.tags?.some(tag => tag.toLowerCase().includes(query)) ||
            article.category?.toLowerCase().includes(query)
          );
        });
        
        // Trier les résultats par pertinence
        results.sort((a, b) => {
          // Priorité aux titres qui contiennent la requête
          const aInTitle = a.title.toLowerCase().includes(searchQuery.toLowerCase());
          const bInTitle = b.title.toLowerCase().includes(searchQuery.toLowerCase());
          
          if (aInTitle && !bInTitle) return -1;
          if (!aInTitle && bInTitle) return 1;
          
          return 0;
        });
        
        setSearchResults(results.slice(0, maxResults));
        setShowResults(showInstantResults && results.length > 0);
        setIsSearching(false);
        
        // Appeler le callback avec tous les résultats
        onSearch(results);
      }, 300);
    };
    
    // Délai pour éviter trop de recherches pendant la frappe
    const timer = setTimeout(performSearch, 300);
    
    return () => clearTimeout(timer);
  }, [searchQuery, articles, maxResults, onSearch, showInstantResults]);
  
  // Gérer le clic en dehors des résultats pour les fermer
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Gérer le clic sur un résultat
  const handleResultClick = (article) => {
    setShowResults(false);
    setSearchQuery('');
    onResultClick(article);
  };
  
  // Gérer la soumission du formulaire
  const handleSubmit = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      onSearch(searchResults);
      setShowResults(false);
    }
  };
  
  // Extraire un extrait du contenu avec le terme recherché
  const getContentExcerpt = (content, query, maxLength = 100) => {
    if (!query.trim() || !content) return '';
    
    const lowerContent = content.toLowerCase();
    const lowerQuery = query.toLowerCase();
    const index = lowerContent.indexOf(lowerQuery);
    
    if (index === -1) return content.substring(0, maxLength) + '...';
    
    const start = Math.max(0, index - 40);
    const end = Math.min(content.length, index + query.length + 40);
    
    let excerpt = content.substring(start, end);
    if (start > 0) excerpt = '...' + excerpt;
    if (end < content.length) excerpt = excerpt + '...';
    
    return excerpt;
  };
  
  // Mettre en surbrillance le terme recherché
  const highlightText = (text, query) => {
    if (!query.trim() || !text) return text;
    
    const parts = text.split(new RegExp(`(${query})`, 'gi'));
    
    return parts.map((part, i) => 
      part.toLowerCase() === query.toLowerCase() 
        ? <span key={i} className="boss-bg-yellow-200 boss-font-medium">{part}</span> 
        : part
    );
  };
  
  return (
    <div className="boss-relative" ref={searchRef}>
      <form onSubmit={handleSubmit}>
        <div className="boss-flex boss-items-center">
          <TextControl
            className="boss-flex-1"
            placeholder={__('Rechercher dans la documentation...', 'boss-seo')}
            value={searchQuery}
            onChange={setSearchQuery}
            autoComplete="off"
          />
          <Button
            isPrimary
            type="submit"
            className="boss-ml-2"
            disabled={!searchQuery.trim() || isSearching}
          >
            {isSearching ? <Spinner /> : __('Rechercher', 'boss-seo')}
          </Button>
        </div>
      </form>
      
      {/* Résultats instantanés */}
      {showResults && searchResults.length > 0 && (
        <div className="boss-absolute boss-z-50 boss-mt-1 boss-w-full boss-bg-white boss-rounded-md boss-shadow-lg boss-border boss-border-gray-200 boss-max-h-96 boss-overflow-y-auto">
          <ul className="boss-py-1">
            {searchResults.map((article) => (
              <li 
                key={article.id} 
                className="boss-px-4 boss-py-2 boss-hover:boss-bg-gray-100 boss-cursor-pointer"
                onClick={() => handleResultClick(article)}
              >
                <div className="boss-font-medium boss-text-boss-dark">
                  {highlightText(article.title, searchQuery)}
                </div>
                <div className="boss-text-sm boss-text-boss-gray boss-mt-1">
                  {highlightText(getContentExcerpt(article.content, searchQuery), searchQuery)}
                </div>
                {article.category && (
                  <div className="boss-mt-1">
                    <span className="boss-inline-block boss-px-2 boss-py-1 boss-text-xs boss-font-medium boss-rounded-full boss-bg-blue-100 boss-text-blue-800">
                      {article.category}
                    </span>
                  </div>
                )}
              </li>
            ))}
          </ul>
          <div className="boss-px-4 boss-py-2 boss-border-t boss-border-gray-200 boss-text-xs boss-text-boss-gray boss-text-right">
            {searchResults.length === maxResults 
              ? __('Afficher tous les résultats', 'boss-seo')
              : __('Résultats de recherche', 'boss-seo')}
          </div>
        </div>
      )}
    </div>
  );
};

export default HelpSearch;
