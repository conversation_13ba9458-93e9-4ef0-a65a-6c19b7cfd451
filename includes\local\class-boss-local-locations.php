<?php
/**
 * Classe pour la gestion des emplacements.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour la gestion des emplacements.
 *
 * Cette classe gère les emplacements locaux.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_Locations {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_local_locations_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_locations', array( $this, 'ajax_get_locations' ) );
        add_action( 'wp_ajax_boss_seo_get_location', array( $this, 'ajax_get_location' ) );
        add_action( 'wp_ajax_boss_seo_save_location', array( $this, 'ajax_save_location' ) );
        add_action( 'wp_ajax_boss_seo_delete_location', array( $this, 'ajax_delete_location' ) );
        add_action( 'wp_ajax_boss_seo_import_locations', array( $this, 'ajax_import_locations' ) );
        add_action( 'wp_ajax_boss_seo_export_locations', array( $this, 'ajax_export_locations' ) );

        // Ajouter les actions pour les métaboxes
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );
        add_action( 'save_post_boss_local_location', array( $this, 'save_meta_boxes' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/local/locations',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_locations' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_location' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/locations/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_location' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'update_location' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_location' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/locations/import',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'import_locations' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local/locations/export',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'export_locations' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Ajoute les métaboxes pour les emplacements.
     *
     * @since    1.2.0
     */
    public function add_meta_boxes() {
        add_meta_box(
            'boss_local_location_details',
            __( 'Détails de l\'emplacement', 'boss-seo' ),
            array( $this, 'render_location_details_meta_box' ),
            'boss_local_location',
            'normal',
            'high'
        );

        add_meta_box(
            'boss_local_location_hours',
            __( 'Horaires d\'ouverture', 'boss-seo' ),
            array( $this, 'render_location_hours_meta_box' ),
            'boss_local_location',
            'normal',
            'default'
        );

        add_meta_box(
            'boss_local_location_map',
            __( 'Carte', 'boss-seo' ),
            array( $this, 'render_location_map_meta_box' ),
            'boss_local_location',
            'side',
            'default'
        );
    }

    /**
     * Affiche la métabox des détails de l'emplacement.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     */
    public function render_location_details_meta_box( $post ) {
        // Récupérer les données de l'emplacement
        $location = $this->get_location_data( $post->ID );

        // Ajouter un nonce pour la sécurité
        wp_nonce_field( 'boss_local_location_details', 'boss_local_location_details_nonce' );

        // Afficher le formulaire
        include plugin_dir_path( dirname( dirname( __FILE__ ) ) ) . 'admin/partials/boss-local-location-details-meta-box.php';
    }

    /**
     * Affiche la métabox des horaires d'ouverture.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     */
    public function render_location_hours_meta_box( $post ) {
        // Récupérer les données de l'emplacement
        $location = $this->get_location_data( $post->ID );

        // Ajouter un nonce pour la sécurité
        wp_nonce_field( 'boss_local_location_hours', 'boss_local_location_hours_nonce' );

        // Afficher le formulaire
        include plugin_dir_path( dirname( dirname( __FILE__ ) ) ) . 'admin/partials/boss-local-location-hours-meta-box.php';
    }

    /**
     * Affiche la métabox de la carte.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     */
    public function render_location_map_meta_box( $post ) {
        // Récupérer les données de l'emplacement
        $location = $this->get_location_data( $post->ID );

        // Ajouter un nonce pour la sécurité
        wp_nonce_field( 'boss_local_location_map', 'boss_local_location_map_nonce' );

        // Afficher la carte
        include plugin_dir_path( dirname( dirname( __FILE__ ) ) ) . 'admin/partials/boss-local-location-map-meta-box.php';
    }

    /**
     * Enregistre les métaboxes pour les emplacements.
     *
     * @since    1.2.0
     * @param    int    $post_id    L'ID du post.
     */
    public function save_meta_boxes( $post_id ) {
        // Vérifier si c'est une sauvegarde automatique
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Vérifier les nonces
        if ( ! isset( $_POST['boss_local_location_details_nonce'] ) || ! wp_verify_nonce( $_POST['boss_local_location_details_nonce'], 'boss_local_location_details' ) ) {
            return;
        }

        if ( ! isset( $_POST['boss_local_location_hours_nonce'] ) || ! wp_verify_nonce( $_POST['boss_local_location_hours_nonce'], 'boss_local_location_hours' ) ) {
            return;
        }

        if ( ! isset( $_POST['boss_local_location_map_nonce'] ) || ! wp_verify_nonce( $_POST['boss_local_location_map_nonce'], 'boss_local_location_map' ) ) {
            return;
        }

        // Récupérer les données du formulaire
        $location = array(
            'address'       => isset( $_POST['boss_local_location_address'] ) ? sanitize_text_field( $_POST['boss_local_location_address'] ) : '',
            'city'          => isset( $_POST['boss_local_location_city'] ) ? sanitize_text_field( $_POST['boss_local_location_city'] ) : '',
            'state'         => isset( $_POST['boss_local_location_state'] ) ? sanitize_text_field( $_POST['boss_local_location_state'] ) : '',
            'postal_code'   => isset( $_POST['boss_local_location_postal_code'] ) ? sanitize_text_field( $_POST['boss_local_location_postal_code'] ) : '',
            'country'       => isset( $_POST['boss_local_location_country'] ) ? sanitize_text_field( $_POST['boss_local_location_country'] ) : '',
            'phone'         => isset( $_POST['boss_local_location_phone'] ) ? sanitize_text_field( $_POST['boss_local_location_phone'] ) : '',
            'email'         => isset( $_POST['boss_local_location_email'] ) ? sanitize_email( $_POST['boss_local_location_email'] ) : '',
            'website'       => isset( $_POST['boss_local_location_website'] ) ? esc_url_raw( $_POST['boss_local_location_website'] ) : '',
            'latitude'      => isset( $_POST['boss_local_location_latitude'] ) ? (float) $_POST['boss_local_location_latitude'] : 0,
            'longitude'     => isset( $_POST['boss_local_location_longitude'] ) ? (float) $_POST['boss_local_location_longitude'] : 0,
            'hours'         => isset( $_POST['boss_local_location_hours'] ) ? $this->sanitize_hours( $_POST['boss_local_location_hours'] ) : array(),
            'special_hours' => isset( $_POST['boss_local_location_special_hours'] ) ? $this->sanitize_special_hours( $_POST['boss_local_location_special_hours'] ) : array(),
        );

        // Enregistrer les données
        $this->save_location_data( $post_id, $location );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les emplacements.
     *
     * @since    1.2.0
     */
    public function ajax_get_locations() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $page = isset( $_POST['page'] ) ? absint( $_POST['page'] ) : 1;
        $per_page = isset( $_POST['per_page'] ) ? absint( $_POST['per_page'] ) : 10;
        $search = isset( $_POST['search'] ) ? sanitize_text_field( $_POST['search'] ) : '';

        // Récupérer les emplacements
        $locations = $this->get_locations_data( $page, $per_page, $search );

        wp_send_json_success( array(
            'message'   => __( 'Emplacements récupérés avec succès.', 'boss-seo' ),
            'locations' => $locations['locations'],
            'total'     => $locations['total'],
            'pages'     => $locations['pages'],
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer un emplacement.
     *
     * @since    1.2.0
     */
    public function ajax_get_location() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );

        // Récupérer l'emplacement
        $location = $this->get_location_data( $location_id );

        if ( ! $location ) {
            wp_send_json_error( array( 'message' => __( 'Emplacement non trouvé.', 'boss-seo' ) ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Emplacement récupéré avec succès.', 'boss-seo' ),
            'location' => $location,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour enregistrer un emplacement.
     *
     * @since    1.2.0
     */
    public function ajax_save_location() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location'] ) || ! is_array( $_POST['location'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Les données de l\'emplacement sont requises.', 'boss-seo' ) ) );
        }

        $location_data = $_POST['location'];

        // Vérifier si c'est une mise à jour ou une création
        $location_id = isset( $location_data['id'] ) ? absint( $location_data['id'] ) : 0;

        // Enregistrer l'emplacement
        $result = $this->save_location_data_from_form( $location_data, $location_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Emplacement enregistré avec succès.', 'boss-seo' ),
            'location' => $this->get_location_data( $result ),
        ) );
    }

    /**
     * Gère les requêtes AJAX pour supprimer un emplacement.
     *
     * @since    1.2.0
     */
    public function ajax_delete_location() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_id'] ) || empty( $_POST['location_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ) ) );
        }

        $location_id = absint( $_POST['location_id'] );

        // Supprimer l'emplacement
        $result = $this->delete_location_data( $location_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Emplacement supprimé avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Gère les requêtes AJAX pour importer des emplacements.
     *
     * @since    1.2.0
     */
    public function ajax_import_locations() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_FILES['file'] ) || empty( $_FILES['file'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Aucun fichier n\'a été téléchargé.', 'boss-seo' ) ) );
        }

        // Importer les emplacements
        $result = $this->import_locations_data( $_FILES['file'] );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'   => __( 'Emplacements importés avec succès.', 'boss-seo' ),
            'imported'  => $result['imported'],
            'skipped'   => $result['skipped'],
            'locations' => $result['locations'],
        ) );
    }

    /**
     * Gère les requêtes AJAX pour exporter des emplacements.
     *
     * @since    1.2.0
     */
    public function ajax_export_locations() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Exporter les emplacements
        $result = $this->export_locations_data();

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Emplacements exportés avec succès.', 'boss-seo' ),
            'file'    => $result,
        ) );
    }

    /**
     * Récupère les emplacements via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_locations( $request ) {
        try {
            $page = $request->get_param( 'page' ) ? absint( $request->get_param( 'page' ) ) : 1;
            $per_page = $request->get_param( 'per_page' ) ? absint( $request->get_param( 'per_page' ) ) : 10;
            $search = $request->get_param( 'search' ) ? sanitize_text_field( $request->get_param( 'search' ) ) : '';

            // Utiliser la classe de correction pour récupérer les emplacements
            require_once plugin_dir_path( __FILE__ ) . 'class-boss-local-locations-fix.php';
            $locations_fix = new Boss_Local_Locations_Fix();
            $locations = $locations_fix->get_locations_data( $page, $per_page, $search );

            return rest_ensure_response( $locations );
        } catch (Exception $e) {
            // Log l'erreur
            error_log('Erreur dans get_locations: ' . $e->getMessage());

            // Retourner une réponse d'erreur avec des données vides plutôt qu'une erreur
            return rest_ensure_response( array(
                'locations' => array(),
                'total'     => 0,
                'pages'     => 0,
            ) );
        }
    }

    /**
     * Récupère un emplacement via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_location( $request ) {
        $location_id = $request['id'];

        $location = $this->get_location_data( $location_id );

        if ( ! $location ) {
            return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        return rest_ensure_response( $location );
    }

    /**
     * Enregistre un emplacement via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_location( $request ) {
        try {
            $params = $request->get_params();

            if ( ! isset( $params['location'] ) || ! is_array( $params['location'] ) ) {
                return new WP_Error( 'missing_location', __( 'Les données de l\'emplacement sont requises.', 'boss-seo' ), array( 'status' => 400 ) );
            }

            $location_data = $params['location'];

            // Vérifier si le type de post existe
            if (!post_type_exists('boss_local_location')) {
                return new WP_Error(
                    'post_type_not_exists',
                    __('Le type de post boss_local_location n\'existe pas.', 'boss-seo'),
                    array('status' => 500)
                );
            }

            $result = $this->save_location_data_from_form( $location_data );

            if ( is_wp_error( $result ) ) {
                return $result;
            }

            return rest_ensure_response( array(
                'message'  => __( 'Emplacement enregistré avec succès.', 'boss-seo' ),
                'location' => $this->get_location_data( $result ),
            ) );
        } catch (Exception $e) {
            // Log l'erreur
            error_log('Erreur dans save_location: ' . $e->getMessage());

            // Retourner une réponse d'erreur
            return new WP_Error(
                'boss_seo_save_location_error',
                __('Une erreur est survenue lors de l\'enregistrement de l\'emplacement.', 'boss-seo'),
                array('status' => 500, 'error' => $e->getMessage())
            );
        }
    }

    /**
     * Met à jour un emplacement via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_location( $request ) {
        $location_id = $request['id'];
        $params = $request->get_params();

        if ( ! isset( $params['location'] ) || ! is_array( $params['location'] ) ) {
            return new WP_Error( 'missing_location', __( 'Les données de l\'emplacement sont requises.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $location_data = $params['location'];

        $result = $this->save_location_data_from_form( $location_data, $location_id );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message'  => __( 'Emplacement mis à jour avec succès.', 'boss-seo' ),
            'location' => $this->get_location_data( $result ),
        ) );
    }

    /**
     * Supprime un emplacement via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_location( $request ) {
        $location_id = $request['id'];

        $result = $this->delete_location_data( $location_id );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Emplacement supprimé avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Importe des emplacements via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function import_locations( $request ) {
        $file = $request->get_file_params();

        if ( ! isset( $file['file'] ) || empty( $file['file'] ) ) {
            return new WP_Error( 'missing_file', __( 'Aucun fichier n\'a été téléchargé.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $result = $this->import_locations_data( $file['file'] );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message'   => __( 'Emplacements importés avec succès.', 'boss-seo' ),
            'imported'  => $result['imported'],
            'skipped'   => $result['skipped'],
            'locations' => $result['locations'],
        ) );
    }

    /**
     * Exporte des emplacements via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function export_locations( $request ) {
        $result = $this->export_locations_data();

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Emplacements exportés avec succès.', 'boss-seo' ),
            'file'    => $result,
        ) );
    }

    /**
     * Récupère les emplacements.
     *
     * @since    1.2.0
     * @param    int       $page       Le numéro de page.
     * @param    int       $per_page   Le nombre d'éléments par page.
     * @param    string    $search     La recherche.
     * @return   array                 Les emplacements.
     */
    private function get_locations_data( $page, $per_page, $search ) {
        // Paramètres de la requête
        $args = array(
            'post_type'      => 'boss_local_location',
            'posts_per_page' => $per_page,
            'paged'          => $page,
            'post_status'    => 'publish',
        );

        // Ajouter la recherche
        if ( ! empty( $search ) ) {
            $args['s'] = $search;
        }

        // Exécuter la requête
        $query = new WP_Query( $args );

        // Préparer les résultats
        $locations = array();

        foreach ( $query->posts as $post ) {
            $locations[] = $this->get_location_data( $post->ID );
        }

        return array(
            'locations' => $locations,
            'total'     => $query->found_posts,
            'pages'     => ceil( $query->found_posts / $per_page ),
        );
    }

    /**
     * Récupère un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array|false               L'emplacement ou false si non trouvé.
     */
    private function get_location_data( $location_id ) {
        $post = get_post( $location_id );

        if ( ! $post || $post->post_type !== 'boss_local_location' ) {
            return false;
        }

        // Récupérer les métadonnées
        $address = get_post_meta( $location_id, '_boss_local_location_address', true );
        $city = get_post_meta( $location_id, '_boss_local_location_city', true );
        $state = get_post_meta( $location_id, '_boss_local_location_state', true );
        $postal_code = get_post_meta( $location_id, '_boss_local_location_postal_code', true );
        $country = get_post_meta( $location_id, '_boss_local_location_country', true );
        $phone = get_post_meta( $location_id, '_boss_local_location_phone', true );
        $email = get_post_meta( $location_id, '_boss_local_location_email', true );
        $website = get_post_meta( $location_id, '_boss_local_location_website', true );
        $latitude = get_post_meta( $location_id, '_boss_local_location_latitude', true );
        $longitude = get_post_meta( $location_id, '_boss_local_location_longitude', true );
        $hours = get_post_meta( $location_id, '_boss_local_location_hours', true );
        $special_hours = get_post_meta( $location_id, '_boss_local_location_special_hours', true );

        // Récupérer les types d'emplacement
        $types = wp_get_post_terms( $location_id, 'boss_local_location_type', array( 'fields' => 'names' ) );

        // Préparer les données
        $location = array(
            'id'            => $post->ID,
            'title'         => $post->post_title,
            'content'       => $post->post_content,
            'excerpt'       => $post->post_excerpt,
            'address'       => $address,
            'city'          => $city,
            'state'         => $state,
            'postal_code'   => $postal_code,
            'country'       => $country,
            'phone'         => $phone,
            'email'         => $email,
            'website'       => $website,
            'latitude'      => (float) $latitude,
            'longitude'     => (float) $longitude,
            'hours'         => $hours ? $hours : array(),
            'special_hours' => $special_hours ? $special_hours : array(),
            'types'         => $types,
        );

        return $location;
    }

    /**
     * Enregistre un emplacement à partir des données du formulaire.
     *
     * @since    1.2.0
     * @param    array     $location_data    Les données de l'emplacement.
     * @param    int       $location_id      L'ID de l'emplacement (0 pour une création).
     * @return   int|WP_Error                L'ID de l'emplacement ou une erreur.
     */
    private function save_location_data_from_form( $location_data, $location_id = 0 ) {
        // Vérifier les données requises
        if ( ! isset( $location_data['title'] ) || empty( $location_data['title'] ) ) {
            return new WP_Error( 'missing_title', __( 'Le titre de l\'emplacement est requis.', 'boss-seo' ) );
        }

        // Préparer les données du post
        $post_data = array(
            'post_title'   => sanitize_text_field( $location_data['title'] ),
            'post_content' => isset( $location_data['content'] ) ? wp_kses_post( $location_data['content'] ) : '',
            'post_excerpt' => isset( $location_data['excerpt'] ) ? wp_kses_post( $location_data['excerpt'] ) : '',
            'post_status'  => 'publish',
            'post_type'    => 'boss_local_location',
        );

        // Mettre à jour ou créer le post
        if ( $location_id > 0 ) {
            $post_data['ID'] = $location_id;
            $result = wp_update_post( $post_data, true );
        } else {
            $result = wp_insert_post( $post_data, true );
        }

        // Vérifier les erreurs
        if ( is_wp_error( $result ) ) {
            return $result;
        }

        $location_id = $result;

        // Enregistrer les métadonnées
        $meta_fields = array(
            'address'       => isset( $location_data['address'] ) ? sanitize_text_field( $location_data['address'] ) : '',
            'city'          => isset( $location_data['city'] ) ? sanitize_text_field( $location_data['city'] ) : '',
            'state'         => isset( $location_data['state'] ) ? sanitize_text_field( $location_data['state'] ) : '',
            'postal_code'   => isset( $location_data['postal_code'] ) ? sanitize_text_field( $location_data['postal_code'] ) : '',
            'country'       => isset( $location_data['country'] ) ? sanitize_text_field( $location_data['country'] ) : '',
            'phone'         => isset( $location_data['phone'] ) ? sanitize_text_field( $location_data['phone'] ) : '',
            'email'         => isset( $location_data['email'] ) ? sanitize_email( $location_data['email'] ) : '',
            'website'       => isset( $location_data['website'] ) ? esc_url_raw( $location_data['website'] ) : '',
            'latitude'      => isset( $location_data['latitude'] ) ? (float) $location_data['latitude'] : 0,
            'longitude'     => isset( $location_data['longitude'] ) ? (float) $location_data['longitude'] : 0,
        );

        foreach ( $meta_fields as $key => $value ) {
            update_post_meta( $location_id, '_boss_local_location_' . $key, $value );
        }

        // Enregistrer les horaires
        if ( isset( $location_data['hours'] ) && is_array( $location_data['hours'] ) ) {
            update_post_meta( $location_id, '_boss_local_location_hours', $this->sanitize_hours( $location_data['hours'] ) );
        }

        // Enregistrer les horaires spéciaux
        if ( isset( $location_data['special_hours'] ) && is_array( $location_data['special_hours'] ) ) {
            update_post_meta( $location_id, '_boss_local_location_special_hours', $this->sanitize_special_hours( $location_data['special_hours'] ) );
        }

        // Enregistrer les types d'emplacement
        if ( isset( $location_data['types'] ) && is_array( $location_data['types'] ) ) {
            wp_set_object_terms( $location_id, $location_data['types'], 'boss_local_location_type' );
        }

        return $location_id;
    }

    /**
     * Supprime un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   bool|WP_Error             True en cas de succès, WP_Error en cas d'erreur.
     */
    private function delete_location_data( $location_id ) {
        $post = get_post( $location_id );

        if ( ! $post || $post->post_type !== 'boss_local_location' ) {
            return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ) );
        }

        $result = wp_delete_post( $location_id, true );

        if ( ! $result ) {
            return new WP_Error( 'delete_failed', __( 'La suppression de l\'emplacement a échoué.', 'boss-seo' ) );
        }

        return true;
    }

    /**
     * Importe des emplacements à partir d'un fichier CSV.
     *
     * @since    1.2.0
     * @param    array     $file    Le fichier téléchargé.
     * @return   array|WP_Error     Les résultats de l'importation ou une erreur.
     */
    private function import_locations_data( $file ) {
        // Vérifier le fichier
        if ( $file['error'] !== UPLOAD_ERR_OK ) {
            return new WP_Error( 'upload_error', __( 'Erreur lors du téléchargement du fichier.', 'boss-seo' ) );
        }

        // Vérifier le type de fichier
        $file_type = wp_check_filetype( $file['name'] );

        if ( $file_type['ext'] !== 'csv' ) {
            return new WP_Error( 'invalid_file_type', __( 'Le fichier doit être au format CSV.', 'boss-seo' ) );
        }

        // Ouvrir le fichier
        $handle = fopen( $file['tmp_name'], 'r' );

        if ( ! $handle ) {
            return new WP_Error( 'file_open_error', __( 'Impossible d\'ouvrir le fichier.', 'boss-seo' ) );
        }

        // Lire l'en-tête
        $header = fgetcsv( $handle );

        if ( ! $header ) {
            fclose( $handle );
            return new WP_Error( 'invalid_csv', __( 'Le fichier CSV est invalide.', 'boss-seo' ) );
        }

        // Vérifier les colonnes requises
        $required_columns = array( 'title', 'address', 'city', 'postal_code', 'country' );
        $missing_columns = array();

        foreach ( $required_columns as $column ) {
            if ( ! in_array( $column, $header ) ) {
                $missing_columns[] = $column;
            }
        }

        if ( ! empty( $missing_columns ) ) {
            fclose( $handle );
            return new WP_Error( 'missing_columns', sprintf( __( 'Colonnes manquantes : %s', 'boss-seo' ), implode( ', ', $missing_columns ) ) );
        }

        // Importer les emplacements
        $imported = 0;
        $skipped = 0;
        $locations = array();

        while ( ( $row = fgetcsv( $handle ) ) !== false ) {
            // Créer un tableau associatif
            $data = array();

            foreach ( $header as $index => $column ) {
                if ( isset( $row[ $index ] ) ) {
                    $data[ $column ] = $row[ $index ];
                }
            }

            // Vérifier les données requises
            if ( empty( $data['title'] ) ) {
                $skipped++;
                continue;
            }

            // Préparer les données de l'emplacement
            $location_data = array(
                'title'       => $data['title'],
                'content'     => isset( $data['content'] ) ? $data['content'] : '',
                'excerpt'     => isset( $data['excerpt'] ) ? $data['excerpt'] : '',
                'address'     => isset( $data['address'] ) ? $data['address'] : '',
                'city'        => isset( $data['city'] ) ? $data['city'] : '',
                'state'       => isset( $data['state'] ) ? $data['state'] : '',
                'postal_code' => isset( $data['postal_code'] ) ? $data['postal_code'] : '',
                'country'     => isset( $data['country'] ) ? $data['country'] : '',
                'phone'       => isset( $data['phone'] ) ? $data['phone'] : '',
                'email'       => isset( $data['email'] ) ? $data['email'] : '',
                'website'     => isset( $data['website'] ) ? $data['website'] : '',
                'latitude'    => isset( $data['latitude'] ) ? (float) $data['latitude'] : 0,
                'longitude'   => isset( $data['longitude'] ) ? (float) $data['longitude'] : 0,
                'types'       => isset( $data['types'] ) ? explode( ',', $data['types'] ) : array(),
            );

            // Enregistrer l'emplacement
            $result = $this->save_location_data_from_form( $location_data );

            if ( is_wp_error( $result ) ) {
                $skipped++;
            } else {
                $imported++;
                $locations[] = $this->get_location_data( $result );
            }
        }

        fclose( $handle );

        return array(
            'imported'  => $imported,
            'skipped'   => $skipped,
            'locations' => $locations,
        );
    }

    /**
     * Exporte les emplacements au format CSV.
     *
     * @since    1.2.0
     * @return   array|WP_Error    Les informations sur le fichier exporté ou une erreur.
     */
    private function export_locations_data() {
        // Récupérer tous les emplacements
        $locations = get_posts( array(
            'post_type'      => 'boss_local_location',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
        ) );

        if ( empty( $locations ) ) {
            return new WP_Error( 'no_locations', __( 'Aucun emplacement à exporter.', 'boss-seo' ) );
        }

        // Créer le dossier d'export
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/boss-seo/exports';

        if ( ! file_exists( $export_dir ) ) {
            wp_mkdir_p( $export_dir );
        }

        // Créer le fichier CSV
        $filename = 'boss-seo-locations-' . date( 'Y-m-d' ) . '.csv';
        $filepath = $export_dir . '/' . $filename;
        $fileurl = $upload_dir['baseurl'] . '/boss-seo/exports/' . $filename;

        $handle = fopen( $filepath, 'w' );

        if ( ! $handle ) {
            return new WP_Error( 'file_create_error', __( 'Impossible de créer le fichier d\'export.', 'boss-seo' ) );
        }

        // Écrire l'en-tête
        $header = array(
            'title',
            'content',
            'excerpt',
            'address',
            'city',
            'state',
            'postal_code',
            'country',
            'phone',
            'email',
            'website',
            'latitude',
            'longitude',
            'types',
        );

        fputcsv( $handle, $header );

        // Écrire les données
        foreach ( $locations as $post ) {
            $location = $this->get_location_data( $post->ID );

            $row = array(
                $location['title'],
                $location['content'],
                $location['excerpt'],
                $location['address'],
                $location['city'],
                $location['state'],
                $location['postal_code'],
                $location['country'],
                $location['phone'],
                $location['email'],
                $location['website'],
                $location['latitude'],
                $location['longitude'],
                implode( ',', $location['types'] ),
            );

            fputcsv( $handle, $row );
        }

        fclose( $handle );

        return array(
            'filename' => $filename,
            'filepath' => $filepath,
            'fileurl'  => $fileurl,
        );
    }

    /**
     * Sanitize les horaires d'ouverture.
     *
     * @since    1.2.0
     * @param    array    $hours    Les horaires d'ouverture.
     * @return   array              Les horaires d'ouverture sanitizés.
     */
    private function sanitize_hours( $hours ) {
        $sanitized_hours = array();
        $days = array( 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday' );

        foreach ( $days as $day ) {
            $sanitized_hours[ $day ] = array(
                'open'  => isset( $hours[ $day ]['open'] ) && $hours[ $day ]['open'] ? true : false,
                'hours' => array(),
            );

            if ( isset( $hours[ $day ]['hours'] ) && is_array( $hours[ $day ]['hours'] ) ) {
                foreach ( $hours[ $day ]['hours'] as $period ) {
                    if ( isset( $period['open'] ) && isset( $period['close'] ) ) {
                        $sanitized_hours[ $day ]['hours'][] = array(
                            'open'  => sanitize_text_field( $period['open'] ),
                            'close' => sanitize_text_field( $period['close'] ),
                        );
                    }
                }
            }
        }

        return $sanitized_hours;
    }

    /**
     * Sanitize les horaires spéciaux.
     *
     * @since    1.2.0
     * @param    array    $special_hours    Les horaires spéciaux.
     * @return   array                      Les horaires spéciaux sanitizés.
     */
    private function sanitize_special_hours( $special_hours ) {
        $sanitized_special_hours = array();

        if ( is_array( $special_hours ) ) {
            foreach ( $special_hours as $special_hour ) {
                if ( isset( $special_hour['date'] ) && isset( $special_hour['open'] ) ) {
                    $sanitized_special_hour = array(
                        'date'  => sanitize_text_field( $special_hour['date'] ),
                        'open'  => $special_hour['open'] ? true : false,
                        'hours' => array(),
                    );

                    if ( isset( $special_hour['hours'] ) && is_array( $special_hour['hours'] ) ) {
                        foreach ( $special_hour['hours'] as $period ) {
                            if ( isset( $period['open'] ) && isset( $period['close'] ) ) {
                                $sanitized_special_hour['hours'][] = array(
                                    'open'  => sanitize_text_field( $period['open'] ),
                                    'close' => sanitize_text_field( $period['close'] ),
                                );
                            }
                        }
                    }

                    $sanitized_special_hours[] = $sanitized_special_hour;
                }
            }
        }

        return $sanitized_special_hours;
    }
}
