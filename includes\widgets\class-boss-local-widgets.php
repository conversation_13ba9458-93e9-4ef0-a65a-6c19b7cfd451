<?php
/**
 * Widgets pour le module SEO Local.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/widgets
 */

/**
 * Widget pour afficher les informations d'un emplacement.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/widgets
 * <AUTHOR> SEO Team
 */
class Boss_Local_Info_Widget extends WP_Widget {

    /**
     * Initialise le widget.
     *
     * @since    1.2.0
     */
    public function __construct() {
        parent::__construct(
            'boss_local_info_widget',
            __( 'Boss SEO - Informations locales', 'boss-seo' ),
            array(
                'description' => __( 'Affiche les informations d\'un emplacement.', 'boss-seo' ),
            )
        );
    }

    /**
     * Affiche le contenu du widget.
     *
     * @since    1.2.0
     * @param    array    $args        Les arguments du widget.
     * @param    array    $instance    Les paramètres du widget.
     */
    public function widget( $args, $instance ) {
        echo $args['before_widget'];

        if ( ! empty( $instance['title'] ) ) {
            echo $args['before_title'] . apply_filters( 'widget_title', $instance['title'] ) . $args['after_title'];
        }

        $location_id = ! empty( $instance['location_id'] ) ? absint( $instance['location_id'] ) : 0;
        $show_name = isset( $instance['show_name'] ) ? $instance['show_name'] : 'yes';
        $show_address = isset( $instance['show_address'] ) ? $instance['show_address'] : 'yes';
        $show_contact = isset( $instance['show_contact'] ) ? $instance['show_contact'] : 'yes';
        $show_description = isset( $instance['show_description'] ) ? $instance['show_description'] : 'yes';
        $layout = isset( $instance['layout'] ) ? $instance['layout'] : 'default';

        // Utiliser le shortcode pour afficher les informations
        echo do_shortcode( '[boss_local_info location_id="' . $location_id . '" show_name="' . $show_name . '" show_address="' . $show_address . '" show_contact="' . $show_contact . '" show_description="' . $show_description . '" layout="' . $layout . '"]' );

        echo $args['after_widget'];
    }

    /**
     * Affiche le formulaire de paramètres du widget.
     *
     * @since    1.2.0
     * @param    array    $instance    Les paramètres actuels du widget.
     */
    public function form( $instance ) {
        $title = ! empty( $instance['title'] ) ? $instance['title'] : __( 'Informations locales', 'boss-seo' );
        $location_id = ! empty( $instance['location_id'] ) ? absint( $instance['location_id'] ) : 0;
        $show_name = isset( $instance['show_name'] ) ? $instance['show_name'] : 'yes';
        $show_address = isset( $instance['show_address'] ) ? $instance['show_address'] : 'yes';
        $show_contact = isset( $instance['show_contact'] ) ? $instance['show_contact'] : 'yes';
        $show_description = isset( $instance['show_description'] ) ? $instance['show_description'] : 'yes';
        $layout = isset( $instance['layout'] ) ? $instance['layout'] : 'default';

        // Récupérer les emplacements
        $locations = get_posts( array(
            'post_type'      => 'boss_location',
            'post_status'    => 'publish',
            'posts_per_page' => -1,
            'orderby'        => 'title',
            'order'          => 'ASC',
        ) );
        ?>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>"><?php esc_html_e( 'Titre :', 'boss-seo' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'title' ) ); ?>" type="text" value="<?php echo esc_attr( $title ); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'location_id' ) ); ?>"><?php esc_html_e( 'Emplacement :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'location_id' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'location_id' ) ); ?>">
                <option value="0"><?php esc_html_e( '-- Sélectionner un emplacement --', 'boss-seo' ); ?></option>
                <?php foreach ( $locations as $location ) : ?>
                    <option value="<?php echo esc_attr( $location->ID ); ?>" <?php selected( $location_id, $location->ID ); ?>><?php echo esc_html( $location->post_title ); ?></option>
                <?php endforeach; ?>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'show_name' ) ); ?>"><?php esc_html_e( 'Afficher le nom :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'show_name' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'show_name' ) ); ?>">
                <option value="yes" <?php selected( $show_name, 'yes' ); ?>><?php esc_html_e( 'Oui', 'boss-seo' ); ?></option>
                <option value="no" <?php selected( $show_name, 'no' ); ?>><?php esc_html_e( 'Non', 'boss-seo' ); ?></option>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'show_address' ) ); ?>"><?php esc_html_e( 'Afficher l\'adresse :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'show_address' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'show_address' ) ); ?>">
                <option value="yes" <?php selected( $show_address, 'yes' ); ?>><?php esc_html_e( 'Oui', 'boss-seo' ); ?></option>
                <option value="no" <?php selected( $show_address, 'no' ); ?>><?php esc_html_e( 'Non', 'boss-seo' ); ?></option>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'show_contact' ) ); ?>"><?php esc_html_e( 'Afficher les coordonnées :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'show_contact' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'show_contact' ) ); ?>">
                <option value="yes" <?php selected( $show_contact, 'yes' ); ?>><?php esc_html_e( 'Oui', 'boss-seo' ); ?></option>
                <option value="no" <?php selected( $show_contact, 'no' ); ?>><?php esc_html_e( 'Non', 'boss-seo' ); ?></option>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'show_description' ) ); ?>"><?php esc_html_e( 'Afficher la description :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'show_description' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'show_description' ) ); ?>">
                <option value="yes" <?php selected( $show_description, 'yes' ); ?>><?php esc_html_e( 'Oui', 'boss-seo' ); ?></option>
                <option value="no" <?php selected( $show_description, 'no' ); ?>><?php esc_html_e( 'Non', 'boss-seo' ); ?></option>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'layout' ) ); ?>"><?php esc_html_e( 'Mise en page :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'layout' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'layout' ) ); ?>">
                <option value="default" <?php selected( $layout, 'default' ); ?>><?php esc_html_e( 'Par défaut', 'boss-seo' ); ?></option>
                <option value="compact" <?php selected( $layout, 'compact' ); ?>><?php esc_html_e( 'Compact', 'boss-seo' ); ?></option>
                <option value="detailed" <?php selected( $layout, 'detailed' ); ?>><?php esc_html_e( 'Détaillé', 'boss-seo' ); ?></option>
            </select>
        </p>
        <?php
    }

    /**
     * Traite les options du widget lors de l'enregistrement.
     *
     * @since    1.2.0
     * @param    array    $new_instance    Les nouvelles options.
     * @param    array    $old_instance    Les anciennes options.
     * @return   array                     Les options mises à jour.
     */
    public function update( $new_instance, $old_instance ) {
        $instance = array();
        $instance['title'] = ! empty( $new_instance['title'] ) ? sanitize_text_field( $new_instance['title'] ) : '';
        $instance['location_id'] = ! empty( $new_instance['location_id'] ) ? absint( $new_instance['location_id'] ) : 0;
        $instance['show_name'] = ! empty( $new_instance['show_name'] ) ? sanitize_text_field( $new_instance['show_name'] ) : 'yes';
        $instance['show_address'] = ! empty( $new_instance['show_address'] ) ? sanitize_text_field( $new_instance['show_address'] ) : 'yes';
        $instance['show_contact'] = ! empty( $new_instance['show_contact'] ) ? sanitize_text_field( $new_instance['show_contact'] ) : 'yes';
        $instance['show_description'] = ! empty( $new_instance['show_description'] ) ? sanitize_text_field( $new_instance['show_description'] ) : 'yes';
        $instance['layout'] = ! empty( $new_instance['layout'] ) ? sanitize_text_field( $new_instance['layout'] ) : 'default';

        return $instance;
    }
}

/**
 * Widget pour afficher une carte d'un emplacement.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/widgets
 * <AUTHOR> SEO Team
 */
class Boss_Local_Map_Widget extends WP_Widget {

    /**
     * Initialise le widget.
     *
     * @since    1.2.0
     */
    public function __construct() {
        parent::__construct(
            'boss_local_map_widget',
            __( 'Boss SEO - Carte locale', 'boss-seo' ),
            array(
                'description' => __( 'Affiche une carte d\'un emplacement.', 'boss-seo' ),
            )
        );
    }

    /**
     * Affiche le contenu du widget.
     *
     * @since    1.2.0
     * @param    array    $args        Les arguments du widget.
     * @param    array    $instance    Les paramètres du widget.
     */
    public function widget( $args, $instance ) {
        echo $args['before_widget'];

        if ( ! empty( $instance['title'] ) ) {
            echo $args['before_title'] . apply_filters( 'widget_title', $instance['title'] ) . $args['after_title'];
        }

        $location_id = ! empty( $instance['location_id'] ) ? absint( $instance['location_id'] ) : 0;
        $width = ! empty( $instance['width'] ) ? $instance['width'] : '100%';
        $height = ! empty( $instance['height'] ) ? $instance['height'] : '400px';
        $zoom = ! empty( $instance['zoom'] ) ? absint( $instance['zoom'] ) : 15;
        $show_marker = isset( $instance['show_marker'] ) ? $instance['show_marker'] : 'yes';
        $marker_title = ! empty( $instance['marker_title'] ) ? $instance['marker_title'] : '';

        // Utiliser le shortcode pour afficher la carte
        echo do_shortcode( '[boss_local_map location_id="' . $location_id . '" width="' . $width . '" height="' . $height . '" zoom="' . $zoom . '" show_marker="' . $show_marker . '" marker_title="' . $marker_title . '"]' );

        echo $args['after_widget'];
    }

    /**
     * Affiche le formulaire de paramètres du widget.
     *
     * @since    1.2.0
     * @param    array    $instance    Les paramètres actuels du widget.
     */
    public function form( $instance ) {
        $title = ! empty( $instance['title'] ) ? $instance['title'] : __( 'Carte locale', 'boss-seo' );
        $location_id = ! empty( $instance['location_id'] ) ? absint( $instance['location_id'] ) : 0;
        $width = ! empty( $instance['width'] ) ? $instance['width'] : '100%';
        $height = ! empty( $instance['height'] ) ? $instance['height'] : '400px';
        $zoom = ! empty( $instance['zoom'] ) ? absint( $instance['zoom'] ) : 15;
        $show_marker = isset( $instance['show_marker'] ) ? $instance['show_marker'] : 'yes';
        $marker_title = ! empty( $instance['marker_title'] ) ? $instance['marker_title'] : '';

        // Récupérer les emplacements
        $locations = get_posts( array(
            'post_type'      => 'boss_location',
            'post_status'    => 'publish',
            'posts_per_page' => -1,
            'orderby'        => 'title',
            'order'          => 'ASC',
        ) );
        ?>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>"><?php esc_html_e( 'Titre :', 'boss-seo' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'title' ) ); ?>" type="text" value="<?php echo esc_attr( $title ); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'location_id' ) ); ?>"><?php esc_html_e( 'Emplacement :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'location_id' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'location_id' ) ); ?>">
                <option value="0"><?php esc_html_e( '-- Sélectionner un emplacement --', 'boss-seo' ); ?></option>
                <?php foreach ( $locations as $location ) : ?>
                    <option value="<?php echo esc_attr( $location->ID ); ?>" <?php selected( $location_id, $location->ID ); ?>><?php echo esc_html( $location->post_title ); ?></option>
                <?php endforeach; ?>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'width' ) ); ?>"><?php esc_html_e( 'Largeur :', 'boss-seo' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'width' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'width' ) ); ?>" type="text" value="<?php echo esc_attr( $width ); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'height' ) ); ?>"><?php esc_html_e( 'Hauteur :', 'boss-seo' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'height' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'height' ) ); ?>" type="text" value="<?php echo esc_attr( $height ); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'zoom' ) ); ?>"><?php esc_html_e( 'Niveau de zoom :', 'boss-seo' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'zoom' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'zoom' ) ); ?>" type="number" min="1" max="20" value="<?php echo esc_attr( $zoom ); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'show_marker' ) ); ?>"><?php esc_html_e( 'Afficher le marqueur :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'show_marker' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'show_marker' ) ); ?>">
                <option value="yes" <?php selected( $show_marker, 'yes' ); ?>><?php esc_html_e( 'Oui', 'boss-seo' ); ?></option>
                <option value="no" <?php selected( $show_marker, 'no' ); ?>><?php esc_html_e( 'Non', 'boss-seo' ); ?></option>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'marker_title' ) ); ?>"><?php esc_html_e( 'Titre du marqueur :', 'boss-seo' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'marker_title' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'marker_title' ) ); ?>" type="text" value="<?php echo esc_attr( $marker_title ); ?>">
        </p>
        <?php
    }

    /**
     * Traite les options du widget lors de l'enregistrement.
     *
     * @since    1.2.0
     * @param    array    $new_instance    Les nouvelles options.
     * @param    array    $old_instance    Les anciennes options.
     * @return   array                     Les options mises à jour.
     */
    public function update( $new_instance, $old_instance ) {
        $instance = array();
        $instance['title'] = ! empty( $new_instance['title'] ) ? sanitize_text_field( $new_instance['title'] ) : '';
        $instance['location_id'] = ! empty( $new_instance['location_id'] ) ? absint( $new_instance['location_id'] ) : 0;
        $instance['width'] = ! empty( $new_instance['width'] ) ? sanitize_text_field( $new_instance['width'] ) : '100%';
        $instance['height'] = ! empty( $new_instance['height'] ) ? sanitize_text_field( $new_instance['height'] ) : '400px';
        $instance['zoom'] = ! empty( $new_instance['zoom'] ) ? absint( $new_instance['zoom'] ) : 15;
        $instance['show_marker'] = ! empty( $new_instance['show_marker'] ) ? sanitize_text_field( $new_instance['show_marker'] ) : 'yes';
        $instance['marker_title'] = ! empty( $new_instance['marker_title'] ) ? sanitize_text_field( $new_instance['marker_title'] ) : '';

        return $instance;
    }
}

/**
 * Widget pour afficher les horaires d'ouverture d'un emplacement.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/widgets
 * <AUTHOR> SEO Team
 */
class Boss_Local_Hours_Widget extends WP_Widget {

    /**
     * Initialise le widget.
     *
     * @since    1.2.0
     */
    public function __construct() {
        parent::__construct(
            'boss_local_hours_widget',
            __( 'Boss SEO - Horaires d\'ouverture', 'boss-seo' ),
            array(
                'description' => __( 'Affiche les horaires d\'ouverture d\'un emplacement.', 'boss-seo' ),
            )
        );
    }

    /**
     * Affiche le contenu du widget.
     *
     * @since    1.2.0
     * @param    array    $args        Les arguments du widget.
     * @param    array    $instance    Les paramètres du widget.
     */
    public function widget( $args, $instance ) {
        echo $args['before_widget'];

        if ( ! empty( $instance['title'] ) ) {
            echo $args['before_title'] . apply_filters( 'widget_title', $instance['title'] ) . $args['after_title'];
        }

        $location_id = ! empty( $instance['location_id'] ) ? absint( $instance['location_id'] ) : 0;
        $show_title = isset( $instance['show_title'] ) ? $instance['show_title'] : 'yes';
        $title = ! empty( $instance['hours_title'] ) ? $instance['hours_title'] : __( 'Horaires d\'ouverture', 'boss-seo' );
        $layout = isset( $instance['layout'] ) ? $instance['layout'] : 'table';

        // Utiliser le shortcode pour afficher les horaires
        echo do_shortcode( '[boss_local_hours location_id="' . $location_id . '" show_title="' . $show_title . '" title="' . $title . '" layout="' . $layout . '"]' );

        echo $args['after_widget'];
    }

    /**
     * Affiche le formulaire de paramètres du widget.
     *
     * @since    1.2.0
     * @param    array    $instance    Les paramètres actuels du widget.
     */
    public function form( $instance ) {
        $title = ! empty( $instance['title'] ) ? $instance['title'] : __( 'Horaires d\'ouverture', 'boss-seo' );
        $location_id = ! empty( $instance['location_id'] ) ? absint( $instance['location_id'] ) : 0;
        $show_title = isset( $instance['show_title'] ) ? $instance['show_title'] : 'yes';
        $hours_title = ! empty( $instance['hours_title'] ) ? $instance['hours_title'] : __( 'Horaires d\'ouverture', 'boss-seo' );
        $layout = isset( $instance['layout'] ) ? $instance['layout'] : 'table';

        // Récupérer les emplacements
        $locations = get_posts( array(
            'post_type'      => 'boss_location',
            'post_status'    => 'publish',
            'posts_per_page' => -1,
            'orderby'        => 'title',
            'order'          => 'ASC',
        ) );
        ?>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>"><?php esc_html_e( 'Titre du widget :', 'boss-seo' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'title' ) ); ?>" type="text" value="<?php echo esc_attr( $title ); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'location_id' ) ); ?>"><?php esc_html_e( 'Emplacement :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'location_id' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'location_id' ) ); ?>">
                <option value="0"><?php esc_html_e( '-- Sélectionner un emplacement --', 'boss-seo' ); ?></option>
                <?php foreach ( $locations as $location ) : ?>
                    <option value="<?php echo esc_attr( $location->ID ); ?>" <?php selected( $location_id, $location->ID ); ?>><?php echo esc_html( $location->post_title ); ?></option>
                <?php endforeach; ?>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'show_title' ) ); ?>"><?php esc_html_e( 'Afficher le titre des horaires :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'show_title' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'show_title' ) ); ?>">
                <option value="yes" <?php selected( $show_title, 'yes' ); ?>><?php esc_html_e( 'Oui', 'boss-seo' ); ?></option>
                <option value="no" <?php selected( $show_title, 'no' ); ?>><?php esc_html_e( 'Non', 'boss-seo' ); ?></option>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'hours_title' ) ); ?>"><?php esc_html_e( 'Titre des horaires :', 'boss-seo' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'hours_title' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'hours_title' ) ); ?>" type="text" value="<?php echo esc_attr( $hours_title ); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr( $this->get_field_id( 'layout' ) ); ?>"><?php esc_html_e( 'Mise en page :', 'boss-seo' ); ?></label>
            <select class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'layout' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'layout' ) ); ?>">
                <option value="table" <?php selected( $layout, 'table' ); ?>><?php esc_html_e( 'Tableau', 'boss-seo' ); ?></option>
                <option value="list" <?php selected( $layout, 'list' ); ?>><?php esc_html_e( 'Liste', 'boss-seo' ); ?></option>
            </select>
        </p>
        <?php
    }

    /**
     * Traite les options du widget lors de l'enregistrement.
     *
     * @since    1.2.0
     * @param    array    $new_instance    Les nouvelles options.
     * @param    array    $old_instance    Les anciennes options.
     * @return   array                     Les options mises à jour.
     */
    public function update( $new_instance, $old_instance ) {
        $instance = array();
        $instance['title'] = ! empty( $new_instance['title'] ) ? sanitize_text_field( $new_instance['title'] ) : '';
        $instance['location_id'] = ! empty( $new_instance['location_id'] ) ? absint( $new_instance['location_id'] ) : 0;
        $instance['show_title'] = ! empty( $new_instance['show_title'] ) ? sanitize_text_field( $new_instance['show_title'] ) : 'yes';
        $instance['hours_title'] = ! empty( $new_instance['hours_title'] ) ? sanitize_text_field( $new_instance['hours_title'] ) : '';
        $instance['layout'] = ! empty( $new_instance['layout'] ) ? sanitize_text_field( $new_instance['layout'] ) : 'table';

        return $instance;
    }
}