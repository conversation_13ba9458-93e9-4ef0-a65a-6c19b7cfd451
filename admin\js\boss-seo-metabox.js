/**
 * Scripts pour la meta box Boss SEO
 */

(function($) {
    'use strict';

    $(function() {
        // Compteur de caractères pour la méta-description
        const metaDescriptionField = $('#boss_seo_meta_description');
        const metaDescriptionCounter = $('#boss_seo_meta_description_counter');
        const metaDescriptionCounterCurrent = metaDescriptionCounter.find('.boss-seo-counter-current');

        // Mettre à jour le compteur au chargement
        updateMetaDescriptionCounter();

        // Mettre à jour le compteur lors de la saisie
        metaDescriptionField.on('input', updateMetaDescriptionCounter);

        function updateMetaDescriptionCounter() {
            const length = metaDescriptionField.val().length;
            metaDescriptionCounterCurrent.text(length);

            // Mettre à jour la classe en fonction de la longueur
            metaDescriptionCounterCurrent.removeClass('too-short too-long good');

            if (length < 120) {
                metaDescriptionCounterCurrent.addClass('too-short');
            } else if (length > 160) {
                metaDescriptionCounterCurrent.addClass('too-long');
            } else {
                metaDescriptionCounterCurrent.addClass('good');
            }
        }

        // Gestion des mots-clés
        const keywordsContainer = $('.boss-seo-keywords-tags');
        const keywordsInput = $('#boss_seo_keywords_input');
        const focusKeywordInput = $('#boss_seo_focus_keyword');
        const keywordSuggestions = $('.boss-seo-keyword-suggestion');

        // Tableau pour stocker les mots-clés
        let keywords = [];

        // Initialiser les mots-clés à partir du champ caché
        if (focusKeywordInput.val()) {
            // Ajouter le mot-clé principal s'il existe
            addKeyword(focusKeywordInput.val(), true);

            // Ajouter les mots-clés secondaires s'ils existent
            const secondaryKeywords = $('#boss_seo_secondary_keywords').val();
            if (secondaryKeywords) {
                secondaryKeywords.split(',').forEach(keyword => {
                    if (keyword.trim() && keyword.trim() !== focusKeywordInput.val()) {
                        addKeyword(keyword.trim(), false);
                    }
                });
            }
        }

        // Ajouter un mot-clé lorsque l'utilisateur appuie sur Entrée
        keywordsInput.on('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const keyword = keywordsInput.val().trim();
                if (keyword) {
                    addKeyword(keyword);
                    keywordsInput.val('');
                }
            }
        });

        // Ajouter un mot-clé à partir des suggestions
        keywordSuggestions.on('click', function() {
            const keyword = $(this).text().trim();
            addKeyword(keyword);
        });

        // Fonction pour ajouter un mot-clé
        function addKeyword(keyword, isPrimary = false) {
            // Vérifier si le mot-clé existe déjà
            if (keywords.includes(keyword)) {
                // Si le mot-clé existe déjà et qu'on veut le définir comme principal
                if (isPrimary) {
                    // Supprimer l'ancien mot-clé principal
                    $('.boss-seo-keyword-tag.primary').removeClass('primary');

                    // Définir ce mot-clé comme principal
                    $(`.boss-seo-keyword-tag[data-keyword="${keyword}"]`).addClass('primary');

                    // Mettre à jour le champ caché
                    focusKeywordInput.val(keyword);
                }
                return;
            }

            // Ajouter le mot-clé au tableau
            keywords.push(keyword);

            // Créer l'élément HTML pour le mot-clé
            const tagHtml = `
                <div class="boss-seo-keyword-tag ${isPrimary ? 'primary' : ''}" data-keyword="${keyword}">
                    ${keyword}
                    <span class="dashicons dashicons-${isPrimary ? 'star-filled' : 'plus'}"></span>
                </div>
            `;

            // Ajouter l'élément au conteneur
            keywordsContainer.append(tagHtml);

            // Si c'est le mot-clé principal, mettre à jour le champ caché
            if (isPrimary) {
                focusKeywordInput.val(keyword);
            } else {
                // Mettre à jour le champ des mots-clés secondaires
                updateSecondaryKeywords();
            }

            // Ajouter les événements aux boutons
            attachKeywordEvents();
        }

        // Fonction pour attacher les événements aux mots-clés
        function attachKeywordEvents() {
            // Supprimer les événements existants
            $('.boss-seo-keyword-tag .dashicons').off('click');

            // Ajouter les nouveaux événements
            $('.boss-seo-keyword-tag .dashicons').on('click', function(e) {
                e.stopPropagation();
                const tag = $(this).parent();
                const keyword = tag.data('keyword');

                // Si c'est déjà le mot-clé principal, le supprimer
                if (tag.hasClass('primary')) {
                    removeKeyword(keyword);
                    focusKeywordInput.val('');
                }
                // Si c'est un mot-clé secondaire avec l'icône plus, le définir comme principal
                else if ($(this).hasClass('dashicons-plus')) {
                    // Supprimer l'ancien mot-clé principal
                    $('.boss-seo-keyword-tag.primary').removeClass('primary')
                        .find('.dashicons').removeClass('dashicons-star-filled').addClass('dashicons-plus');

                    // Définir ce mot-clé comme principal
                    tag.addClass('primary');
                    $(this).removeClass('dashicons-plus').addClass('dashicons-star-filled');

                    // Mettre à jour le champ caché
                    focusKeywordInput.val(keyword);
                    updateSecondaryKeywords();
                }
                // Sinon, supprimer le mot-clé
                else {
                    removeKeyword(keyword);
                }
            });

            // Ajouter un événement de clic sur le tag lui-même
            $('.boss-seo-keyword-tag').off('click').on('click', function() {
                const keyword = $(this).data('keyword');

                // Si ce n'est pas déjà le mot-clé principal, le définir comme tel
                if (!$(this).hasClass('primary')) {
                    // Supprimer l'ancien mot-clé principal
                    $('.boss-seo-keyword-tag.primary').removeClass('primary')
                        .find('.dashicons').removeClass('dashicons-star-filled').addClass('dashicons-plus');

                    // Définir ce mot-clé comme principal
                    $(this).addClass('primary');
                    $(this).find('.dashicons').removeClass('dashicons-plus').addClass('dashicons-star-filled');

                    // Mettre à jour le champ caché
                    focusKeywordInput.val(keyword);
                    updateSecondaryKeywords();
                }
            });
        }

        // Fonction pour supprimer un mot-clé
        function removeKeyword(keyword) {
            // Supprimer le mot-clé du tableau
            keywords = keywords.filter(k => k !== keyword);

            // Supprimer l'élément HTML
            $(`.boss-seo-keyword-tag[data-keyword="${keyword}"]`).remove();

            // Mettre à jour les champs cachés
            updateSecondaryKeywords();
        }

        // Fonction pour mettre à jour le champ des mots-clés secondaires
        function updateSecondaryKeywords() {
            const primaryKeyword = focusKeywordInput.val();
            const secondaryKeywords = keywords.filter(k => k !== primaryKeyword);
            $('#boss_seo_secondary_keywords').val(secondaryKeywords.join(','));
        }

        // Bouton d'analyse
        const analyzeButton = $('#boss_seo_analyze_button');

        analyzeButton.on('click', function() {
            // Demander confirmation avant d'analyser
            if (!confirm(bossSeoMetabox.confirmAnalyze)) {
                return;
            }

            // Récupérer l'ID du post
            const postId = $('#post_ID').val();

            // Désactiver le bouton et afficher l'état de chargement
            analyzeButton.prop('disabled', true).addClass('boss-seo-loading');
            const originalText = analyzeButton.text();
            analyzeButton.html('<span class="boss-seo-loading-spinner"></span>' + bossSeoMetabox.analyzing);

            // Appeler l'API pour analyser le contenu
            $.ajax({
                url: bossSeoMetabox.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'boss_seo_analyze_content',
                    post_id: postId,
                    nonce: bossSeoMetabox.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Mettre à jour les éléments de la page sans recharger
                        updateScoreAndRecommendations(response.data);

                        // Afficher un message de succès
                        alert(bossSeoMetabox.analyzeSuccess);

                        // Réactiver le bouton
                        analyzeButton.prop('disabled', false).removeClass('boss-seo-loading').text(originalText);
                    } else {
                        alert(response.data.message || bossSeoMetabox.error);
                        analyzeButton.prop('disabled', false).removeClass('boss-seo-loading').text(originalText);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erreur AJAX:', status, error);
                    alert(bossSeoMetabox.error + (error ? ': ' + error : ''));
                    analyzeButton.prop('disabled', false).removeClass('boss-seo-loading').text(originalText);
                }
            });
        });

        // Bouton d'optimisation
        const optimizeButton = $('#boss_seo_optimize_button');

        optimizeButton.on('click', function() {
            // Demander confirmation avant d'optimiser
            if (!confirm(bossSeoMetabox.confirmOptimize)) {
                return;
            }

            // Récupérer l'ID du post
            const postId = $('#post_ID').val();

            // Désactiver le bouton et afficher l'état de chargement
            optimizeButton.prop('disabled', true).addClass('boss-seo-loading');
            const originalText = optimizeButton.text();
            optimizeButton.html('<span class="boss-seo-loading-spinner"></span>' + bossSeoMetabox.optimizing);

            // Appeler l'API pour optimiser le contenu
            $.ajax({
                url: bossSeoMetabox.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'boss_seo_optimize_content',
                    post_id: postId,
                    nonce: bossSeoMetabox.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Mettre à jour le contenu de l'éditeur avec le contenu optimisé
                        if (response.data.content) {
                            updateEditorContent(response.data.content);
                        }

                        // Mettre à jour les métadonnées
                        if (response.data.meta_description) {
                            $('#boss_seo_meta_description').val(response.data.meta_description);
                            updateMetaDescriptionCounter();
                        }

                        if (response.data.focus_keyword) {
                            $('#boss_seo_focus_keyword').val(response.data.focus_keyword);
                        }

                        // Mettre à jour les optimisations si disponibles
                        if (response.data.optimizations) {
                            const optimizations = response.data.optimizations;

                            if (optimizations.title) {
                                $('#boss_seo_title').val(optimizations.title);
                            }

                            if (optimizations.meta_description) {
                                $('#boss_seo_meta_description').val(optimizations.meta_description);
                                updateMetaDescriptionCounter();
                            }

                            if (optimizations.focus_keyword) {
                                $('#boss_seo_focus_keyword').val(optimizations.focus_keyword);
                                console.log('Mot-clé principal appliqué:', optimizations.focus_keyword);
                            }

                            // Gérer les mots-clés secondaires (format array ou chaîne)
                            if (optimizations.secondary_keywords) {
                                let secondaryValue = '';

                                if (Array.isArray(optimizations.secondary_keywords)) {
                                    // Format array: ["mot1", "mot2", "mot3"]
                                    secondaryValue = optimizations.secondary_keywords.join(', ');
                                    console.log('Mots-clés secondaires (array):', optimizations.secondary_keywords);
                                } else if (typeof optimizations.secondary_keywords === 'string') {
                                    // Format chaîne: "mot1, mot2, mot3" (déjà au bon format)
                                    secondaryValue = optimizations.secondary_keywords;
                                    console.log('Mots-clés secondaires (chaîne):', optimizations.secondary_keywords);
                                }

                                $('#boss_seo_secondary_keywords').val(secondaryValue);
                                console.log('Mots-clés secondaires appliqués:', secondaryValue);
                            }
                        }

                        // Mettre à jour le score et les recommandations
                        if (response.data.score) {
                            updateScoreAndRecommendations(response.data);
                        }

                        // Afficher un message de succès
                        alert(response.data.message || bossSeoMetabox.optimizeSuccess);

                        // Réactiver le bouton
                        optimizeButton.prop('disabled', false).removeClass('boss-seo-loading').text(originalText);
                    } else {
                        alert(response.data.message || bossSeoMetabox.error);
                        optimizeButton.prop('disabled', false).removeClass('boss-seo-loading').text(originalText);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erreur AJAX:', status, error);
                    alert(bossSeoMetabox.error + (error ? ': ' + error : ''));
                    optimizeButton.prop('disabled', false).removeClass('boss-seo-loading').text(originalText);
                }
            });
        });
        // Fonction pour mettre à jour le contenu de l'éditeur WordPress
        function updateEditorContent(content) {
            // Vérifier si l'éditeur classique est utilisé
            if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor && !tinyMCE.activeEditor.isHidden()) {
                tinyMCE.activeEditor.setContent(content);
            }
            // Vérifier si l'éditeur Gutenberg est utilisé
            else if (typeof wp !== 'undefined' && wp.data && wp.data.select('core/editor')) {
                const { editPost } = wp.data.dispatch('core/editor');
                editPost({ content: content });
            }
            // Fallback pour l'éditeur texte
            else {
                $('#content').val(content);
            }
        }

        // Fonction pour mettre à jour le score SEO et les recommandations
        function updateScoreAndRecommendations(data) {
            // Mettre à jour le score SEO
            if (data.score) {
                const scoreValue = $('.boss-seo-score-value');
                const scoreIndicator = $('.boss-seo-score-indicator');

                scoreValue.text(parseInt(data.score));

                // Mettre à jour la classe du score
                scoreIndicator.removeClass('boss-seo-score-good boss-seo-score-ok boss-seo-score-poor boss-seo-score-bad');

                if (data.score >= 80) {
                    scoreIndicator.addClass('boss-seo-score-good');
                } else if (data.score >= 60) {
                    scoreIndicator.addClass('boss-seo-score-ok');
                } else if (data.score >= 40) {
                    scoreIndicator.addClass('boss-seo-score-poor');
                } else {
                    scoreIndicator.addClass('boss-seo-score-bad');
                }

                // Mettre à jour la date d'analyse
                if (data.analysis_date) {
                    $('.boss-seo-score-details p strong').text(data.analysis_date);
                }
            }

            // Mettre à jour les recommandations
            if (data.recommendations && data.recommendations.length > 0) {
                const recommendationsContainer = $('.boss-seo-recommendations');

                // Vider le conteneur
                recommendationsContainer.empty();

                // Ajouter les nouvelles recommandations
                data.recommendations.forEach(function(recommendation) {
                    const icon = getRecommendationIcon(recommendation.type);

                    const recommendationHtml = `
                        <div class="boss-seo-recommendation boss-seo-recommendation-${recommendation.type}">
                            <span class="boss-seo-recommendation-icon dashicons dashicons-${icon}"></span>
                            <span class="boss-seo-recommendation-text">${recommendation.text}</span>
                        </div>
                    `;

                    recommendationsContainer.append(recommendationHtml);
                });

                // Afficher la section des recommandations si elle était cachée
                recommendationsContainer.closest('.boss-seo-metabox-section').show();
            }
        }

        // Fonction pour obtenir l'icône en fonction du type de recommandation
        function getRecommendationIcon(type) {
            switch (type) {
                case 'critical':
                    return 'warning';
                case 'warning':
                    return 'flag';
                case 'info':
                    return 'info';
                default:
                    return 'info';
            }
        }
    });
})(jQuery);
