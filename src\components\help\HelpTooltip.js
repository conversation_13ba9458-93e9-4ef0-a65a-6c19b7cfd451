import { useState, useRef, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { Tooltip } from '@wordpress/components';

/**
 * Composant de tooltip d'aide avancé
 * 
 * @param {Object} props - Propriétés du composant
 * @param {string} props.content - Contenu du tooltip
 * @param {string} props.position - Position du tooltip (top, bottom, left, right)
 * @param {boolean} props.showIcon - Afficher l'icône d'aide
 * @param {string} props.iconSize - Taille de l'icône (small, normal, large)
 * @param {string} props.className - Classes CSS additionnelles
 * @param {React.ReactNode} props.children - Contenu enfant
 * @returns {React.ReactElement} Composant HelpTooltip
 */
const HelpTooltip = ({ 
  content, 
  position = 'top', 
  showIcon = true, 
  iconSize = 'normal',
  className = '',
  children 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const tooltipRef = useRef(null);
  
  // Déterminer la classe de taille de l'icône
  const getIconSizeClass = () => {
    switch (iconSize) {
      case 'small':
        return 'boss-text-xs';
      case 'large':
        return 'boss-text-lg';
      default:
        return 'boss-text-sm';
    }
  };
  
  // Gérer le clic en dehors du tooltip pour le fermer
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target)) {
        setIsVisible(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  return (
    <div className={`boss-inline-flex boss-items-center boss-relative ${className}`} ref={tooltipRef}>
      {children}
      
      {showIcon && (
        <span 
          className={`dashicons dashicons-editor-help boss-ml-1 boss-text-boss-blue-500 boss-cursor-pointer boss-transition-all boss-duration-200 boss-ease-in-out boss-hover:boss-text-boss-blue-700 ${getIconSizeClass()}`}
          onClick={() => setIsVisible(!isVisible)}
          onMouseEnter={() => setIsVisible(true)}
          onMouseLeave={() => setIsVisible(false)}
          aria-label={__('Aide', 'boss-seo')}
        />
      )}
      
      {isVisible && (
        <Tooltip position={position} className="boss-z-50">
          <div className="boss-max-w-xs boss-text-sm boss-p-2">
            {content}
          </div>
        </Tooltip>
      )}
    </div>
  );
};

export default HelpTooltip;
