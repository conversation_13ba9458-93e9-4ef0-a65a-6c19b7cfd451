import { __ } from '@wordpress/i18n';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Dashicon } from '@wordpress/components';

/**
 * Composant pour afficher les statistiques d'optimisation
 */
const OptimizationStats = ({ stats }) => {
  return (
    <Card className="boss-card boss-h-full">
      <CardHeader>
        <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Statistiques d\'optimisation', 'boss-seo')}
        </h3>
      </CardHeader>
      <CardBody>
        <div className="boss-grid boss-grid-cols-2 boss-gap-4">
          {stats.map((stat, index) => (
            <div 
              key={index} 
              className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-transition-all boss-duration-200 boss-hover:boss-shadow-md"
            >
              <div className="boss-flex boss-items-center boss-mb-3">
                <div className={`boss-w-10 boss-h-10 boss-rounded-full boss-flex boss-items-center boss-justify-center boss-mr-3 ${stat.bgColor}`}>
                  <Dashicon icon={stat.icon} className={stat.iconColor} />
                </div>
                <div className="boss-flex-1">
                  <h4 className="boss-text-sm boss-font-medium boss-text-boss-gray">{stat.title}</h4>
                  <div className="boss-flex boss-items-baseline">
                    <span className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mr-2">
                      {stat.value}
                    </span>
                    {stat.total && (
                      <span className="boss-text-sm boss-text-boss-gray">
                        / {stat.total}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Barre de progression si applicable */}
              {stat.percentage !== undefined && (
                <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2 boss-mb-2">
                  <div
                    className={`boss-h-2 boss-rounded-full ${stat.progressColor}`}
                    style={{ width: `${stat.percentage}%` }}
                  ></div>
                </div>
              )}
              
              {/* Tendance si applicable */}
              {stat.trend && (
                <div className="boss-flex boss-items-center boss-justify-between boss-mt-2">
                  <span className="boss-text-xs boss-text-boss-gray">
                    {stat.description}
                  </span>
                  <span className={`boss-text-xs boss-font-medium boss-flex boss-items-center ${
                    stat.trend > 0 ? 'boss-text-boss-success' : 'boss-text-boss-error'
                  }`}>
                    <Dashicon 
                      icon={stat.trend > 0 ? 'arrow-up-alt' : 'arrow-down-alt'} 
                      className="boss-mr-1 boss-text-xs" 
                    />
                    {Math.abs(stat.trend)}%
                  </span>
                </div>
              )}
              
              {/* Description simple si pas de tendance */}
              {!stat.trend && stat.description && (
                <div className="boss-text-xs boss-text-boss-gray boss-mt-2">
                  {stat.description}
                </div>
              )}
            </div>
          ))}
        </div>
      </CardBody>
      <CardFooter>
        <Button
          isSecondary
          className="boss-w-full boss-justify-center"
        >
          {__('Voir toutes les statistiques', 'boss-seo')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default OptimizationStats;
