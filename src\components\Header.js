import { __ } from '@wordpress/i18n';
import { Button, Dashicon, Dropdown, NavigableMenu } from '@wordpress/components';
import { useState } from '@wordpress/element';

/**
 * Composant Header pour le tableau de bord
 */
const Header = ({ seoScore, notifications = [] }) => {
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  
  // Nombre de notifications non lues
  const unreadCount = notifications.filter(notif => !notif.read).length;
  
  return (
    <div className="boss-bg-white boss-border-b boss-border-gray-200 boss-px-6 boss-py-4 boss-mb-6">
      <div className="boss-flex boss-justify-between boss-items-center">
        {/* Logo et titre */}
        <div className="boss-flex boss-items-center">
          <div className="boss-w-10 boss-h-10 boss-bg-boss-primary boss-rounded-lg boss-flex boss-items-center boss-justify-center boss-mr-3">
            <Dashicon icon="chart-area" className="boss-text-white boss-text-2xl" />
          </div>
          <div>
            <h1 className="boss-text-xl boss-font-bold boss-text-boss-dark">
              {__('Boss SEO', 'boss-seo')}
            </h1>
            <p className="boss-text-sm boss-text-boss-gray">
              {__('Tableau de bord', 'boss-seo')}
            </p>
          </div>
        </div>
        
        {/* Navigation principale */}
        <div className="boss-hidden md:boss-flex boss-items-center boss-space-x-6">
          <Button
            className="boss-text-boss-gray boss-hover:boss-text-boss-primary boss-font-medium"
          >
            {__('Tableau de bord', 'boss-seo')}
          </Button>
          <Button
            className="boss-text-boss-gray boss-hover:boss-text-boss-primary boss-font-medium"
          >
            {__('Rapports', 'boss-seo')}
          </Button>
          <Button
            className="boss-text-boss-gray boss-hover:boss-text-boss-primary boss-font-medium"
          >
            {__('Outils', 'boss-seo')}
          </Button>
          <Button
            className="boss-text-boss-gray boss-hover:boss-text-boss-primary boss-font-medium"
          >
            {__('Paramètres', 'boss-seo')}
          </Button>
        </div>
        
        {/* Indicateurs et actions */}
        <div className="boss-flex boss-items-center boss-space-x-4">
          {/* Score SEO */}
          <div className="boss-hidden md:boss-flex boss-items-center boss-bg-gray-100 boss-rounded-full boss-px-3 boss-py-1">
            <div className="boss-w-6 boss-h-6 boss-rounded-full boss-bg-boss-primary boss-flex boss-items-center boss-justify-center boss-mr-2">
              <span className="boss-text-xs boss-font-bold boss-text-white">{seoScore}</span>
            </div>
            <span className="boss-text-sm boss-font-medium boss-text-boss-dark">
              {__('Score SEO', 'boss-seo')}
            </span>
          </div>
          
          {/* Notifications */}
          <Dropdown
            className="boss-relative"
            contentClassName="boss-mt-2 boss-absolute boss-right-0 boss-w-80 boss-bg-white boss-rounded-lg boss-shadow-lg boss-border boss-border-gray-200 boss-z-10"
            renderToggle={({ isOpen, onToggle }) => (
              <Button
                className="boss-relative boss-p-2 boss-rounded-full boss-text-boss-gray boss-hover:boss-text-boss-primary boss-hover:boss-bg-gray-100"
                onClick={() => {
                  onToggle();
                  setIsNotificationsOpen(!isOpen);
                }}
                aria-expanded={isOpen}
              >
                <Dashicon icon="bell" />
                {unreadCount > 0 && (
                  <span className="boss-absolute boss-top-0 boss-right-0 boss-transform boss-translate-x-1/2 boss-translate-y-[-25%] boss-bg-boss-error boss-text-white boss-rounded-full boss-w-5 boss-h-5 boss-flex boss-items-center boss-justify-center boss-text-xs boss-font-bold">
                    {unreadCount}
                  </span>
                )}
              </Button>
            )}
            renderContent={() => (
              <div>
                <div className="boss-p-3 boss-border-b boss-border-gray-200">
                  <div className="boss-flex boss-justify-between boss-items-center">
                    <h3 className="boss-text-sm boss-font-semibold boss-text-boss-dark">
                      {__('Notifications', 'boss-seo')}
                    </h3>
                    {unreadCount > 0 && (
                      <Button
                        isLink
                        className="boss-text-xs boss-text-boss-primary"
                      >
                        {__('Tout marquer comme lu', 'boss-seo')}
                      </Button>
                    )}
                  </div>
                </div>
                <div className="boss-max-h-80 boss-overflow-y-auto">
                  {notifications.length === 0 ? (
                    <div className="boss-p-4 boss-text-center boss-text-boss-gray boss-text-sm">
                      {__('Aucune notification', 'boss-seo')}
                    </div>
                  ) : (
                    <ul className="boss-divide-y boss-divide-gray-200">
                      {notifications.map((notification, index) => (
                        <li 
                          key={index} 
                          className={`boss-p-3 boss-transition-colors boss-duration-200 boss-hover:boss-bg-gray-50 ${
                            !notification.read ? 'boss-bg-blue-50' : ''
                          }`}
                        >
                          <div className="boss-flex boss-items-start">
                            <div className={`boss-w-8 boss-h-8 boss-rounded-full boss-flex boss-items-center boss-justify-center boss-mr-3 ${
                              notification.type === 'success' ? 'boss-bg-green-100 boss-text-green-600' :
                              notification.type === 'warning' ? 'boss-bg-yellow-100 boss-text-yellow-600' :
                              notification.type === 'error' ? 'boss-bg-red-100 boss-text-red-600' :
                              'boss-bg-blue-100 boss-text-blue-600'
                            }`}>
                              <Dashicon icon={
                                notification.type === 'success' ? 'yes-alt' :
                                notification.type === 'warning' ? 'warning' :
                                notification.type === 'error' ? 'dismiss' :
                                'info'
                              } />
                            </div>
                            <div className="boss-flex-1">
                              <p className="boss-text-sm boss-font-medium boss-text-boss-dark">
                                {notification.title}
                              </p>
                              <p className="boss-text-xs boss-text-boss-gray boss-mt-1">
                                {notification.message}
                              </p>
                              <div className="boss-flex boss-justify-between boss-items-center boss-mt-2">
                                <span className="boss-text-xs boss-text-boss-gray">
                                  {notification.time}
                                </span>
                                {notification.actionLabel && (
                                  <Button
                                    isSmall
                                    isSecondary
                                    className="boss-text-xs"
                                  >
                                    {notification.actionLabel}
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
                <div className="boss-p-3 boss-border-t boss-border-gray-200 boss-text-center">
                  <Button
                    isLink
                    className="boss-text-boss-primary boss-text-sm"
                  >
                    {__('Voir toutes les notifications', 'boss-seo')}
                  </Button>
                </div>
              </div>
            )}
          />
          
          {/* Bouton d'action principal */}
          <Button
            isPrimary
            className="boss-hidden md:boss-flex boss-items-center"
          >
            <Dashicon icon="plus-alt" className="boss-mr-1" />
            {__('Nouvelle analyse', 'boss-seo')}
          </Button>
          
          {/* Menu mobile */}
          <Dropdown
            className="boss-md:boss-hidden"
            contentClassName="boss-mt-2 boss-absolute boss-right-0 boss-w-48 boss-bg-white boss-rounded-lg boss-shadow-lg boss-border boss-border-gray-200 boss-z-10"
            renderToggle={({ isOpen, onToggle }) => (
              <Button
                className="boss-p-2 boss-rounded-full boss-text-boss-gray boss-hover:boss-text-boss-primary boss-hover:boss-bg-gray-100"
                onClick={onToggle}
                aria-expanded={isOpen}
              >
                <Dashicon icon="menu" />
              </Button>
            )}
            renderContent={() => (
              <NavigableMenu>
                <Button
                  className="boss-w-full boss-text-left boss-px-4 boss-py-2 boss-hover:boss-bg-gray-100"
                >
                  {__('Tableau de bord', 'boss-seo')}
                </Button>
                <Button
                  className="boss-w-full boss-text-left boss-px-4 boss-py-2 boss-hover:boss-bg-gray-100"
                >
                  {__('Rapports', 'boss-seo')}
                </Button>
                <Button
                  className="boss-w-full boss-text-left boss-px-4 boss-py-2 boss-hover:boss-bg-gray-100"
                >
                  {__('Outils', 'boss-seo')}
                </Button>
                <Button
                  className="boss-w-full boss-text-left boss-px-4 boss-py-2 boss-hover:boss-bg-gray-100"
                >
                  {__('Paramètres', 'boss-seo')}
                </Button>
                <div className="boss-border-t boss-border-gray-200 boss-my-1"></div>
                <Button
                  className="boss-w-full boss-text-left boss-px-4 boss-py-2 boss-hover:boss-bg-gray-100 boss-text-boss-primary"
                >
                  <Dashicon icon="plus-alt" className="boss-mr-1" />
                  {__('Nouvelle analyse', 'boss-seo')}
                </Button>
              </NavigableMenu>
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default Header;
