<?php
/**
 * La classe pour le module d'optimisation de contenu multistep.
 *
 * Cette classe gère le module d'optimisation de contenu multistep.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin
 */

/**
 * La classe pour le module d'optimisation de contenu multistep.
 *
 * Cette classe gère le module d'optimisation de contenu multistep.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Content_Multistep {
    /**
     * L'ID unique de ce plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $plugin_name    L'ID unique de ce plugin.
     */
    private $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $version    La version actuelle du plugin.
     */
    private $version;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name    L'ID unique de ce plugin.
     * @param    string    $version        La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Nous n'ajoutons pas de nouveau menu car nous allons remplacer l'existant
    }

    /**
     * Ajoute le menu du module d'optimisation de contenu multistep.
     * Remplace l'ancien module d'optimisation de contenu.
     *
     * @since    1.1.0
     */
    public function add_menu() {
        // Nous n'ajoutons pas de nouveau menu car nous allons remplacer l'existant
        // Cette méthode est conservée pour compatibilité mais n'est pas utilisée
    }

    /**
     * Affiche la page du module d'optimisation de contenu multistep.
     *
     * @since    1.1.0
     */
    public function render_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="boss-seo-content-app"></div>
        </div>
        <?php
    }

    /**
     * Enregistre les scripts et les styles du module d'optimisation de contenu multistep.
     *
     * @since    1.1.0
     */
    public function enqueue_scripts() {
        // Vérifier si nous sommes sur la page du module d'optimisation de contenu
        $screen = get_current_screen();
        if ( $screen->id !== 'boss-seo_page_boss-seo-content' ) {
            return;
        }

        // Enqueue des scripts WordPress natifs
        wp_enqueue_script( 'wp-element' );
        wp_enqueue_script( 'wp-components' );
        wp_enqueue_script( 'wp-i18n' );
        wp_enqueue_script( 'wp-api-fetch' );

        // Styles des composants WordPress
        wp_enqueue_style( 'wp-components' );
        wp_enqueue_style( $this->plugin_name, plugin_dir_url( __FILE__ ) . '../assets/css/boss-seo-admin.css', array(), $this->version, 'all' );

        // Charger le script principal du dashboard (compilé)
        wp_enqueue_script( 'boss-seo-dashboard', plugin_dir_url( __FILE__ ) . '../assets/js/dashboard.js', array( 'wp-element', 'wp-components', 'wp-i18n' ), $this->version, true );
        wp_enqueue_style( 'boss-seo-dashboard-style', plugin_dir_url( __FILE__ ) . '../assets/css/dashboard.css', array( 'wp-components' ), $this->version, 'all' );

        // Localiser le script avec des données spécifiques
        wp_localize_script( 'boss-seo-dashboard', 'bossSeoData', array(
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'boss_seo_content_multistep_nonce' ),
            'api_url' => rest_url( 'boss-seo/v1/' ),
            'site_url' => site_url(),
            'admin_url' => admin_url(),
            'plugin_url' => plugin_dir_url( __FILE__ ) . '../',
            'current_user' => wp_get_current_user()->user_login,
            'current_user_id' => get_current_user_id(),
            'current_user_roles' => wp_get_current_user()->roles,
            'is_admin' => current_user_can( 'manage_options' ),
            'can_edit_posts' => current_user_can( 'edit_posts' ),
            'can_upload_files' => current_user_can( 'upload_files' ),
            'post_types' => $this->get_post_types(),
            'categories' => $this->get_categories(),
            'tags' => $this->get_tags(),
            'users' => $this->get_users(),
            'settings' => $this->get_settings(),
        ) );
    }

    /**
     * Récupère les types de publication disponibles.
     *
     * @since    1.1.0
     * @return   array    Les types de publication disponibles.
     */
    private function get_post_types() {
        $post_types = array();
        $args = array(
            'public' => true,
            'show_ui' => true,
        );
        $post_types_objects = get_post_types( $args, 'objects' );
        foreach ( $post_types_objects as $post_type ) {
            $post_types[] = array(
                'value' => $post_type->name,
                'label' => $post_type->labels->singular_name,
            );
        }
        return $post_types;
    }

    /**
     * Récupère les catégories disponibles.
     *
     * @since    1.1.0
     * @return   array    Les catégories disponibles.
     */
    private function get_categories() {
        $categories = array();
        $args = array(
            'hide_empty' => false,
        );
        $categories_objects = get_categories( $args );
        foreach ( $categories_objects as $category ) {
            $categories[] = array(
                'value' => $category->term_id,
                'label' => $category->name,
            );
        }
        return $categories;
    }

    /**
     * Récupère les tags disponibles.
     *
     * @since    1.1.0
     * @return   array    Les tags disponibles.
     */
    private function get_tags() {
        $tags = array();
        $args = array(
            'hide_empty' => false,
        );
        $tags_objects = get_tags( $args );
        foreach ( $tags_objects as $tag ) {
            $tags[] = array(
                'value' => $tag->term_id,
                'label' => $tag->name,
            );
        }
        return $tags;
    }

    /**
     * Récupère les utilisateurs disponibles.
     *
     * @since    1.1.0
     * @return   array    Les utilisateurs disponibles.
     */
    private function get_users() {
        $users = array();
        $args = array(
            'role__in' => array( 'administrator', 'editor', 'author', 'contributor' ),
        );
        $users_objects = get_users( $args );
        foreach ( $users_objects as $user ) {
            $users[] = array(
                'value' => $user->ID,
                'label' => $user->display_name,
            );
        }
        return $users;
    }

    /**
     * Récupère les paramètres du module d'optimisation de contenu multistep.
     *
     * @since    1.1.0
     * @return   array    Les paramètres du module d'optimisation de contenu multistep.
     */
    private function get_settings() {
        $settings = get_option( 'boss_seo_content_multistep_settings', array() );
        return $settings;
    }
}
