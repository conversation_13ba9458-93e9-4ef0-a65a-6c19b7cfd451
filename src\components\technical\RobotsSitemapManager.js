import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { Tab<PERSON><PERSON><PERSON>, Spinner, Notice } from '@wordpress/components';

// Importer les composants
import RobotsEditor from './RobotsEditor';
import AdvancedSitemapConfig from './AdvancedSitemapConfig';

// Importer le service
import RobotsSitemapService from '../../services/RobotsSitemapService';

/**
 * Composant principal pour la gestion des robots.txt et sitemaps
 */
const RobotsSitemapManager = () => {
  const [activeTab, setActiveTab] = useState('robots');
  const [robotsContent, setRobotsContent] = useState('');
  const [sitemapSettings, setSitemapSettings] = useState({
    enabled: false,
    includedPostTypes: ['post', 'page'],
    includedTaxonomies: ['category', 'post_tag'],
    defaultChangeFreq: 'weekly',
    defaultPriority: 0.7,
    includeImages: true,
    includeLastMod: true,
    enableAutoUpdate: false,
    autoUpdateFrequency: 'daily',
    enableImageSitemap: false,
    enableVideoSitemap: false,
    enableStoriesSitemap: false,
    enableNewsSitemap: false,
    enableCustomSitemap: false,
    enableTaxonomySitemaps: false,
    newsPostTypes: ['post'],
    customUrls: []
  });
  const [contentTypes, setContentTypes] = useState([]);
  const [taxonomies, setTaxonomies] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Charger les données initiales
  useEffect(() => {
    const loadInitialData = async () => {
      setIsLoading(true);
      try {
        // Charger le contenu du robots.txt
        const robotsResponse = await RobotsSitemapService.getRobotsContent();
        setRobotsContent(robotsResponse.content || '');

        // Charger les paramètres du sitemap
        const sitemapResponse = await RobotsSitemapService.getAdvancedSitemapSettings();
        if (sitemapResponse.settings) {
          setSitemapSettings(prevSettings => ({
            ...prevSettings,
            ...sitemapResponse.settings
          }));
        }

        // Charger les types de contenu
        const contentTypesResponse = await RobotsSitemapService.getContentTypes();
        setContentTypes(contentTypesResponse.contentTypes || []);

        // Charger les taxonomies
        const taxonomiesResponse = await RobotsSitemapService.getTaxonomies();
        setTaxonomies(taxonomiesResponse.taxonomies || []);
      } catch (error) {
        console.error('Erreur lors du chargement des données initiales:', error);
        setError(__('Erreur lors du chargement des données. Veuillez rafraîchir la page.', 'boss-seo'));
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Enregistrer le contenu du robots.txt
  const saveRobotsContent = async (content) => {
    setIsSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await RobotsSitemapService.saveRobotsContent(content);
      setSuccess(__('Le fichier robots.txt a été enregistré avec succès.', 'boss-seo'));
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du robots.txt:', error);
      setError(__('Erreur lors de l\'enregistrement du fichier robots.txt.', 'boss-seo'));
    } finally {
      setIsSaving(false);
    }
  };

  // Enregistrer les paramètres du sitemap
  const saveSitemapSettings = async (settings) => {
    setIsSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await RobotsSitemapService.saveAdvancedSitemapSettings(settings);
      setSitemapSettings(settings);
      setSuccess(__('Les paramètres du sitemap ont été enregistrés avec succès.', 'boss-seo'));
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres du sitemap:', error);
      setError(__('Erreur lors de l\'enregistrement des paramètres du sitemap.', 'boss-seo'));
    } finally {
      setIsSaving(false);
    }
  };

  // Afficher un spinner pendant le chargement
  if (isLoading) {
    return (
      <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="boss-p-6">
      <h1 className="boss-text-2xl boss-font-bold boss-mb-6 boss-text-boss-dark">
        {__('Robots.txt & Sitemap', 'boss-seo')}
      </h1>

      {error && (
        <Notice status="error" isDismissible={false} className="boss-mb-6">
          {error}
        </Notice>
      )}

      {success && (
        <Notice status="success" isDismissible={false} className="boss-mb-6">
          {success}
        </Notice>
      )}

      <TabPanel
        className="boss-mb-6"
        activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
        onSelect={(tabName) => setActiveTab(tabName)}
        tabs={[
          {
            name: 'robots',
            title: __('Robots.txt', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'sitemap',
            title: __('Sitemap XML', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          }
        ]}
      >
        {(tab) => {
          if (tab.name === 'robots') {
            return (
              <RobotsEditor
                robotsContent={robotsContent}
                setRobotsContent={setRobotsContent}
                onSave={saveRobotsContent}
                isSaving={isSaving}
              />
            );
          } else if (tab.name === 'sitemap') {
            return (
              <AdvancedSitemapConfig
                sitemapSettings={sitemapSettings}
                setSitemapSettings={setSitemapSettings}
                onSave={saveSitemapSettings}
                isSaving={isSaving}
                contentTypes={contentTypes}
                taxonomies={taxonomies}
              />
            );
          }
          return null;
        }}
      </TabPanel>
    </div>
  );
};

export default RobotsSitemapManager;
