<?php
/**
 * Classe pour le générateur IA de fiches produit.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour le générateur IA de fiches produit.
 *
 * Cette classe gère le générateur IA de fiches produit.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Product_Generator {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_product_generator_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_generate_product_content', array( $this, 'ajax_generate_product_content' ) );
        add_action( 'wp_ajax_boss_seo_apply_generated_content', array( $this, 'ajax_apply_generated_content' ) );
        add_action( 'wp_ajax_boss_seo_get_generation_history', array( $this, 'ajax_get_generation_history' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/products/(?P<id>\d+)/generate-content',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'generate_product_content' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/products/(?P<id>\d+)/apply-generated-content',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'apply_generated_content' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/products/(?P<id>\d+)/generation-history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_generation_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour générer du contenu de produit.
     *
     * @since    1.2.0
     */
    public function ajax_generate_product_content() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );
        $style = isset( $_POST['style'] ) ? sanitize_text_field( $_POST['style'] ) : 'descriptif';
        $tone = isset( $_POST['tone'] ) ? sanitize_text_field( $_POST['tone'] ) : 'professionnel';
        $length = isset( $_POST['length'] ) ? sanitize_text_field( $_POST['length'] ) : 'moyen';

        // Générer le contenu
        $result = $this->generate_product_content_data( $product_id, $style, $tone, $length );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Contenu généré avec succès.', 'boss-seo' ),
            'content'  => $result['content'],
            'history'  => $result['history'],
        ) );
    }

    /**
     * Gère les requêtes AJAX pour appliquer du contenu généré.
     *
     * @since    1.2.0
     */
    public function ajax_apply_generated_content() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        if ( ! isset( $_POST['content'] ) || empty( $_POST['content'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Le contenu est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );
        $content = wp_kses_post( $_POST['content'] );
        $content_type = isset( $_POST['content_type'] ) ? sanitize_text_field( $_POST['content_type'] ) : 'description';

        // Appliquer le contenu
        $result = $this->apply_generated_content_data( $product_id, $content, $content_type );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Contenu appliqué avec succès.', 'boss-seo' ),
            'product' => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer l'historique de génération.
     *
     * @since    1.2.0
     */
    public function ajax_get_generation_history() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );

        // Récupérer l'historique
        $history = $this->get_generation_history_data( $product_id );

        wp_send_json_success( array(
            'message' => __( 'Historique récupéré avec succès.', 'boss-seo' ),
            'history' => $history,
        ) );
    }

    /**
     * Génère du contenu de produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function generate_product_content( $request ) {
        $product_id = $request['id'];
        $params = $request->get_params();

        $style = isset( $params['style'] ) ? sanitize_text_field( $params['style'] ) : 'descriptif';
        $tone = isset( $params['tone'] ) ? sanitize_text_field( $params['tone'] ) : 'professionnel';
        $length = isset( $params['length'] ) ? sanitize_text_field( $params['length'] ) : 'moyen';

        $result = $this->generate_product_content_data( $product_id, $style, $tone, $length );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Contenu généré avec succès.', 'boss-seo' ),
            'content' => $result['content'],
            'history' => $result['history'],
        ) );
    }

    /**
     * Applique du contenu généré via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function apply_generated_content( $request ) {
        $product_id = $request['id'];
        $params = $request->get_params();

        if ( ! isset( $params['content'] ) || empty( $params['content'] ) ) {
            return new WP_Error( 'missing_content', __( 'Le contenu est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $content = wp_kses_post( $params['content'] );
        $content_type = isset( $params['content_type'] ) ? sanitize_text_field( $params['content_type'] ) : 'description';

        $result = $this->apply_generated_content_data( $product_id, $content, $content_type );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Contenu appliqué avec succès.', 'boss-seo' ),
            'product' => $result,
        ) );
    }

    /**
     * Récupère l'historique de génération via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_generation_history( $request ) {
        $product_id = $request['id'];

        $history = $this->get_generation_history_data( $product_id );

        return rest_ensure_response( array(
            'message' => __( 'Historique récupéré avec succès.', 'boss-seo' ),
            'history' => $history,
        ) );
    }

    /**
     * Génère du contenu de produit.
     *
     * @since    1.2.0
     * @param    int       $product_id    L'ID du produit.
     * @param    string    $style         Le style du contenu.
     * @param    string    $tone          Le ton du contenu.
     * @param    string    $length        La longueur du contenu.
     * @return   array|WP_Error           Le contenu généré ou une erreur.
     */
    private function generate_product_content_data( $product_id, $style, $tone, $length ) {
        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );

        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ) );
        }

        // Récupérer les informations du produit
        $product_name = $product->get_name();
        $product_description = $product->get_description();
        $product_short_description = $product->get_short_description();
        $product_categories = array();

        foreach ( $product->get_category_ids() as $category_id ) {
            $term = get_term( $category_id, 'product_cat' );

            if ( $term && ! is_wp_error( $term ) ) {
                $product_categories[] = $term->name;
            }
        }

        $product_tags = array();

        foreach ( $product->get_tag_ids() as $tag_id ) {
            $term = get_term( $tag_id, 'product_tag' );

            if ( $term && ! is_wp_error( $term ) ) {
                $product_tags[] = $term->name;
            }
        }

        $product_attributes = array();

        foreach ( $product->get_attributes() as $attribute ) {
            if ( $attribute->is_taxonomy() ) {
                $attribute_name = wc_attribute_label( $attribute->get_name() );
                $attribute_values = array();

                foreach ( $attribute->get_terms() as $term ) {
                    $attribute_values[] = $term->name;
                }

                $product_attributes[] = array(
                    'name'   => $attribute_name,
                    'values' => $attribute_values,
                );
            } else {
                $product_attributes[] = array(
                    'name'   => $attribute->get_name(),
                    'values' => $attribute->get_options(),
                );
            }
        }

        // Préparer les paramètres pour l'API
        $api_params = array(
            'product_name'        => $product_name,
            'product_description' => $product_description,
            'product_categories'  => $product_categories,
            'product_tags'        => $product_tags,
            'product_attributes'  => $product_attributes,
            'style'               => $style,
            'tone'                => $tone,
            'length'              => $length,
        );

        // Appeler l'API pour générer le contenu
        $content = $this->call_ai_api( $api_params );

        if ( is_wp_error( $content ) ) {
            return $content;
        }

        // Enregistrer le contenu généré dans l'historique
        $history = $this->save_generated_content( $product_id, $content, $style, $tone, $length );

        return array(
            'content' => $content,
            'history' => $history,
        );
    }

    /**
     * Appelle l'API d'IA pour générer du contenu.
     *
     * @since    1.2.0
     * @param    array     $params    Les paramètres pour l'API.
     * @return   string|WP_Error      Le contenu généré ou une erreur.
     */
    private function call_ai_api( $params ) {
        // Récupérer les paramètres de l'API
        $api_key = get_option( $this->option_prefix . 'api_key', '' );

        if ( empty( $api_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API manquante.', 'boss-seo' ) );
        }

        // Simuler un appel à l'API d'IA
        // Dans une implémentation réelle, vous feriez un appel à une API comme OpenAI, etc.

        // Générer un contenu de démonstration
        $product_name = $params['product_name'];
        $product_categories = implode( ', ', $params['product_categories'] );
        $product_tags = implode( ', ', $params['product_tags'] );
        $style = $params['style'];
        $tone = $params['tone'];
        $length = $params['length'];

        $content = '';

        // Générer un titre
        $content .= '<h2>' . sprintf( __( 'Découvrez le %s', 'boss-seo' ), $product_name ) . '</h2>';

        // Générer une introduction
        switch ( $tone ) {
            case 'professionnel':
                $content .= '<p>' . sprintf( __( 'Le %s est un produit de haute qualité conçu pour répondre aux besoins des professionnels les plus exigeants. Appartenant à la catégorie %s, ce produit se distingue par ses caractéristiques exceptionnelles et sa fiabilité à toute épreuve.', 'boss-seo' ), $product_name, $product_categories ) . '</p>';
                break;

            case 'décontracté':
                $content .= '<p>' . sprintf( __( 'Salut ! Laisse-moi te présenter le %s, un super produit qui va te faciliter la vie. Si tu cherches quelque chose dans la catégorie %s, tu vas adorer ce que ce produit a à offrir !', 'boss-seo' ), $product_name, $product_categories ) . '</p>';
                break;

            case 'enthousiaste':
                $content .= '<p>' . sprintf( __( 'Wow ! Le %s est absolument incroyable ! C\'est LE produit qu\'il vous faut dans la catégorie %s. Vous allez être époustouflé par ses performances et sa qualité exceptionnelle !', 'boss-seo' ), $product_name, $product_categories ) . '</p>';
                break;

            default:
                $content .= '<p>' . sprintf( __( 'Le %s est un excellent choix pour ceux qui recherchent un produit de qualité dans la catégorie %s. Avec ses nombreuses fonctionnalités et sa conception soignée, il saura répondre à vos attentes.', 'boss-seo' ), $product_name, $product_categories ) . '</p>';
        }

        // Générer une description des caractéristiques
        $content .= '<h3>' . __( 'Caractéristiques principales', 'boss-seo' ) . '</h3>';
        $content .= '<ul>';
        $content .= '<li>' . __( 'Conception robuste et durable', 'boss-seo' ) . '</li>';
        $content .= '<li>' . __( 'Matériaux de haute qualité', 'boss-seo' ) . '</li>';
        $content .= '<li>' . __( 'Facile à utiliser et à entretenir', 'boss-seo' ) . '</li>';
        $content .= '<li>' . __( 'Design élégant et moderne', 'boss-seo' ) . '</li>';
        $content .= '<li>' . __( 'Excellent rapport qualité-prix', 'boss-seo' ) . '</li>';
        $content .= '</ul>';

        // Générer une description des avantages
        $content .= '<h3>' . __( 'Avantages', 'boss-seo' ) . '</h3>';
        $content .= '<p>' . sprintf( __( 'Le %s vous offre de nombreux avantages qui en font un choix idéal pour les utilisateurs exigeants. Sa conception soignée et ses fonctionnalités avancées vous permettront de profiter d\'une expérience utilisateur exceptionnelle.', 'boss-seo' ), $product_name ) . '</p>';

        // Ajouter du contenu supplémentaire en fonction de la longueur
        if ( $length === 'long' ) {
            $content .= '<h3>' . __( 'Applications', 'boss-seo' ) . '</h3>';
            $content .= '<p>' . sprintf( __( 'Le %s est parfaitement adapté pour une utilisation dans divers contextes. Que vous soyez un professionnel ou un particulier, vous trouverez de nombreuses façons d\'utiliser ce produit pour améliorer votre quotidien.', 'boss-seo' ), $product_name ) . '</p>';

            $content .= '<h3>' . __( 'Témoignages', 'boss-seo' ) . '</h3>';
            $content .= '<blockquote>' . __( 'Ce produit a complètement transformé ma façon de travailler. Je ne peux plus m\'en passer !', 'boss-seo' ) . '</blockquote>';
            $content .= '<blockquote>' . __( 'Excellent rapport qualité-prix. Je recommande vivement ce produit à tous mes collègues.', 'boss-seo' ) . '</blockquote>';

            $content .= '<h3>' . __( 'FAQ', 'boss-seo' ) . '</h3>';
            $content .= '<p><strong>' . __( 'Q: Quelle est la durée de vie de ce produit ?', 'boss-seo' ) . '</strong></p>';
            $content .= '<p>' . __( 'R: Avec un entretien régulier, ce produit peut durer de nombreuses années.', 'boss-seo' ) . '</p>';
            $content .= '<p><strong>' . __( 'Q: Est-il facile à utiliser ?', 'boss-seo' ) . '</strong></p>';
            $content .= '<p>' . __( 'R: Oui, ce produit a été conçu pour être intuitif et facile à utiliser, même pour les débutants.', 'boss-seo' ) . '</p>';
        }

        // Générer une conclusion
        $content .= '<h3>' . __( 'Conclusion', 'boss-seo' ) . '</h3>';
        $content .= '<p>' . sprintf( __( 'En résumé, le %s est un excellent investissement pour tous ceux qui recherchent un produit de qualité dans la catégorie %s. Avec ses nombreuses fonctionnalités et sa conception soignée, il saura répondre à vos attentes et vous offrir une expérience utilisateur exceptionnelle.', 'boss-seo' ), $product_name, $product_categories ) . '</p>';

        return $content;
    }

    /**
     * Enregistre le contenu généré dans l'historique.
     *
     * @since    1.2.0
     * @param    int       $product_id    L'ID du produit.
     * @param    string    $content       Le contenu généré.
     * @param    string    $style         Le style du contenu.
     * @param    string    $tone          Le ton du contenu.
     * @param    string    $length        La longueur du contenu.
     * @return   array                    L'historique de génération.
     */
    private function save_generated_content( $product_id, $content, $style, $tone, $length ) {
        // Récupérer l'historique existant
        $history = get_post_meta( $product_id, '_boss_generated_content_history', true );

        if ( empty( $history ) || ! is_array( $history ) ) {
            $history = array();
        }

        // Ajouter le nouveau contenu à l'historique
        $history[] = array(
            'content' => $content,
            'style'   => $style,
            'tone'    => $tone,
            'length'  => $length,
            'date'    => current_time( 'mysql' ),
        );

        // Limiter l'historique à 10 entrées
        if ( count( $history ) > 10 ) {
            $history = array_slice( $history, -10 );
        }

        // Enregistrer l'historique
        update_post_meta( $product_id, '_boss_generated_content_history', $history );

        return $history;
    }

    /**
     * Récupère l'historique de génération.
     *
     * @since    1.2.0
     * @param    int       $product_id    L'ID du produit.
     * @return   array                    L'historique de génération.
     */
    private function get_generation_history_data( $product_id ) {
        // Récupérer l'historique
        $history = get_post_meta( $product_id, '_boss_generated_content_history', true );

        if ( empty( $history ) || ! is_array( $history ) ) {
            return array();
        }

        return $history;
    }

    /**
     * Applique du contenu généré à un produit.
     *
     * @since    1.2.0
     * @param    int       $product_id     L'ID du produit.
     * @param    string    $content        Le contenu à appliquer.
     * @param    string    $content_type   Le type de contenu (description, short_description).
     * @return   array|WP_Error            Le produit mis à jour ou une erreur.
     */
    private function apply_generated_content_data( $product_id, $content, $content_type ) {
        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );

        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ) );
        }

        // Mettre à jour le produit
        $product_data = array(
            'ID' => $product_id,
        );

        switch ( $content_type ) {
            case 'description':
                $product_data['post_content'] = $content;
                break;

            case 'short_description':
                $product_data['post_excerpt'] = $content;
                break;

            default:
                return new WP_Error( 'invalid_content_type', __( 'Type de contenu non valide.', 'boss-seo' ) );
        }

        // Mettre à jour le produit
        $result = wp_update_post( $product_data, true );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        // Récupérer le produit mis à jour
        $updated_product = wc_get_product( $product_id );

        return array(
            'id'                => $product_id,
            'title'             => $updated_product->get_name(),
            'permalink'         => get_permalink( $product_id ),
            'description'       => $updated_product->get_description(),
            'short_description' => $updated_product->get_short_description(),
        );
    }
}
