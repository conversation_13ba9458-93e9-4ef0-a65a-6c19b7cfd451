import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  SelectControl,
  ToggleControl,
  CheckboxControl,
  RadioControl,
  RangeControl,
  ColorPicker,
  Notice,
  Spinner
} from '@wordpress/components';

// Importer le service des paramètres
import SettingsService from '../../services/SettingsService';

const UserPreferences = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [preferences, setPreferences] = useState({
    dashboard: {
      layout: 'default',
      showWelcomeMessage: true,
      defaultTab: 'overview',
      cardsPerRow: 3,
      expandedCards: []
    },
    notifications: {
      enabled: true,
      showInAdminBar: true,
      emailNotifications: false,
      emailFrequency: 'weekly',
      notifyOnRankChanges: true,
      notifyOnErrors: true,
      notifyOnUpdates: true,
      notifyOnReports: true
    },
    display: {
      theme: 'light',
      primaryColor: '#3b82f6',
      fontSize: 'medium',
      compactMode: false,
      showThumbnails: true,
      tableRowsPerPage: 20,
      dateFormat: 'Y-m-d'
    },
    editor: {
      enableSeoPanel: true,
      showInSidebar: true,
      autoAnalyzeContent: true,
      highlightIssues: true,
      showScoreInAdminColumns: true,
      showMetaInfoInAdminColumns: true,
      enableAiSuggestions: true
    }
  });

  // Charger les données
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        setIsLoading(true);

        // Charger les préférences utilisateur
        const userPreferences = await SettingsService.getUserPreferences();

        setPreferences(userPreferences);
      } catch (error) {
        console.error('Erreur lors du chargement des préférences utilisateur:', error);
        // En cas d'erreur, utiliser des valeurs par défaut
        const defaultPreferences = {
          dashboard: {
            layout: 'default',
            showWelcomeMessage: true,
            defaultTab: 'overview',
            cardsPerRow: 3,
            expandedCards: ['seo-score', 'recent-content', 'keywords']
          },
          notifications: {
            enabled: true,
            showInAdminBar: true,
            emailNotifications: false,
            emailFrequency: 'weekly',
            notifyOnRankChanges: true,
            notifyOnErrors: true,
            notifyOnUpdates: true,
            notifyOnReports: true
          },
          display: {
            theme: 'light',
            primaryColor: '#3b82f6',
            fontSize: 'medium',
            compactMode: false,
            showThumbnails: true,
            tableRowsPerPage: 20,
            dateFormat: 'Y-m-d'
          },
          editor: {
            enableSeoPanel: true,
            showInSidebar: true,
            autoAnalyzeContent: true,
            highlightIssues: true,
            showScoreInAdminColumns: true,
            showMetaInfoInAdminColumns: true,
            enableAiSuggestions: true
          }
        };

        setPreferences(defaultPreferences);
      } finally {
        setIsLoading(false);
      }
    };

    loadPreferences();
  }, []);

  // Fonction pour mettre à jour les préférences
  const updatePreferences = (category, key, value) => {
    setPreferences({
      ...preferences,
      [category]: {
        ...preferences[category],
        [key]: value
      }
    });
  };

  // Fonction pour enregistrer les préférences
  const handleSavePreferences = async () => {
    setIsSaving(true);

    try {
      // Enregistrer les préférences utilisateur
      await SettingsService.saveUserPreferences(preferences);

      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des préférences utilisateur:', error);
      // Afficher un message d'erreur
      alert(__('Une erreur est survenue lors de l\'enregistrement des préférences. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsSaving(false);
    }
  };

  // Fonction pour réinitialiser les préférences
  const handleResetPreferences = async () => {
    if (confirm(__('Êtes-vous sûr de vouloir réinitialiser toutes vos préférences ? Cette action est irréversible.', 'boss-seo'))) {
      setIsSaving(true);

      try {
        // Réinitialiser les préférences utilisateur
        const response = await SettingsService.resetUserPreferences();

        // Mettre à jour les préférences avec les valeurs par défaut
        setPreferences(response.preferences);

        setShowSuccess(true);

        // Masquer le message de succès après 3 secondes
        setTimeout(() => {
          setShowSuccess(false);
        }, 3000);
      } catch (error) {
        console.error('Erreur lors de la réinitialisation des préférences utilisateur:', error);
        // Afficher un message d'erreur
        alert(__('Une erreur est survenue lors de la réinitialisation des préférences. Veuillez réessayer.', 'boss-seo'));
      } finally {
        setIsSaving(false);
      }
    }
  };

  // Fonction pour gérer la sélection/désélection d'une carte
  const handleCardToggle = (cardId) => {
    const expandedCards = [...preferences.dashboard.expandedCards];

    if (expandedCards.includes(cardId)) {
      // Supprimer la carte
      const index = expandedCards.indexOf(cardId);
      expandedCards.splice(index, 1);
    } else {
      // Ajouter la carte
      expandedCards.push(cardId);
    }

    updatePreferences('dashboard', 'expandedCards', expandedCards);
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {__('Préférences enregistrées avec succès !', 'boss-seo')}
            </Notice>
          )}

          <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-2 boss-gap-6">
            <Card className="boss-mb-6">
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Préférences du tableau de bord', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-space-y-4">
                  <SelectControl
                    label={__('Disposition du tableau de bord', 'boss-seo')}
                    value={preferences.dashboard.layout}
                    options={[
                      { label: __('Par défaut', 'boss-seo'), value: 'default' },
                      { label: __('Compact', 'boss-seo'), value: 'compact' },
                      { label: __('Étendu', 'boss-seo'), value: 'expanded' },
                      { label: __('Personnalisé', 'boss-seo'), value: 'custom' }
                    ]}
                    onChange={(value) => updatePreferences('dashboard', 'layout', value)}
                  />

                  <ToggleControl
                    label={__('Afficher le message de bienvenue', 'boss-seo')}
                    checked={preferences.dashboard.showWelcomeMessage}
                    onChange={(value) => updatePreferences('dashboard', 'showWelcomeMessage', value)}
                  />

                  <SelectControl
                    label={__('Onglet par défaut', 'boss-seo')}
                    value={preferences.dashboard.defaultTab}
                    options={[
                      { label: __('Vue d\'ensemble', 'boss-seo'), value: 'overview' },
                      { label: __('Performances', 'boss-seo'), value: 'performance' },
                      { label: __('Contenu', 'boss-seo'), value: 'content' },
                      { label: __('Mots-clés', 'boss-seo'), value: 'keywords' },
                      { label: __('Technique', 'boss-seo'), value: 'technical' }
                    ]}
                    onChange={(value) => updatePreferences('dashboard', 'defaultTab', value)}
                  />

                  <RangeControl
                    label={__('Cartes par ligne', 'boss-seo')}
                    value={preferences.dashboard.cardsPerRow}
                    onChange={(value) => updatePreferences('dashboard', 'cardsPerRow', value)}
                    min={1}
                    max={4}
                    step={1}
                  />

                  <div className="boss-mt-6">
                    <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                      {__('Cartes développées par défaut', 'boss-seo')}
                    </h3>

                    <div className="boss-space-y-2">
                      <CheckboxControl
                        label={__('Score SEO', 'boss-seo')}
                        checked={preferences.dashboard.expandedCards.includes('seo-score')}
                        onChange={() => handleCardToggle('seo-score')}
                      />

                      <CheckboxControl
                        label={__('Contenu récent', 'boss-seo')}
                        checked={preferences.dashboard.expandedCards.includes('recent-content')}
                        onChange={() => handleCardToggle('recent-content')}
                      />

                      <CheckboxControl
                        label={__('Mots-clés', 'boss-seo')}
                        checked={preferences.dashboard.expandedCards.includes('keywords')}
                        onChange={() => handleCardToggle('keywords')}
                      />

                      <CheckboxControl
                        label={__('Problèmes techniques', 'boss-seo')}
                        checked={preferences.dashboard.expandedCards.includes('technical-issues')}
                        onChange={() => handleCardToggle('technical-issues')}
                      />

                      <CheckboxControl
                        label={__('Performances', 'boss-seo')}
                        checked={preferences.dashboard.expandedCards.includes('performance')}
                        onChange={() => handleCardToggle('performance')}
                      />

                      <CheckboxControl
                        label={__('Trafic', 'boss-seo')}
                        checked={preferences.dashboard.expandedCards.includes('traffic')}
                        onChange={() => handleCardToggle('traffic')}
                      />
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            <Card className="boss-mb-6">
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Notifications', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-space-y-4">
                  <ToggleControl
                    label={__('Activer les notifications', 'boss-seo')}
                    checked={preferences.notifications.enabled}
                    onChange={(value) => updatePreferences('notifications', 'enabled', value)}
                  />

                  <ToggleControl
                    label={__('Afficher les notifications dans la barre d\'administration', 'boss-seo')}
                    checked={preferences.notifications.showInAdminBar}
                    onChange={(value) => updatePreferences('notifications', 'showInAdminBar', value)}
                    disabled={!preferences.notifications.enabled}
                  />

                  <ToggleControl
                    label={__('Recevoir des notifications par email', 'boss-seo')}
                    checked={preferences.notifications.emailNotifications}
                    onChange={(value) => updatePreferences('notifications', 'emailNotifications', value)}
                    disabled={!preferences.notifications.enabled}
                  />

                  {preferences.notifications.emailNotifications && (
                    <SelectControl
                      label={__('Fréquence des emails', 'boss-seo')}
                      value={preferences.notifications.emailFrequency}
                      options={[
                        { label: __('Immédiate', 'boss-seo'), value: 'immediate' },
                        { label: __('Quotidienne', 'boss-seo'), value: 'daily' },
                        { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' }
                      ]}
                      onChange={(value) => updatePreferences('notifications', 'emailFrequency', value)}
                      disabled={!preferences.notifications.enabled || !preferences.notifications.emailNotifications}
                    />
                  )}

                  <div className="boss-mt-6">
                    <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                      {__('Me notifier pour', 'boss-seo')}
                    </h3>

                    <div className="boss-space-y-3">
                      <CheckboxControl
                        label={__('Changements de classement', 'boss-seo')}
                        checked={preferences.notifications.notifyOnRankChanges}
                        onChange={(value) => updatePreferences('notifications', 'notifyOnRankChanges', value)}
                        disabled={!preferences.notifications.enabled}
                      />

                      <CheckboxControl
                        label={__('Erreurs techniques', 'boss-seo')}
                        checked={preferences.notifications.notifyOnErrors}
                        onChange={(value) => updatePreferences('notifications', 'notifyOnErrors', value)}
                        disabled={!preferences.notifications.enabled}
                      />

                      <CheckboxControl
                        label={__('Mises à jour du plugin', 'boss-seo')}
                        checked={preferences.notifications.notifyOnUpdates}
                        onChange={(value) => updatePreferences('notifications', 'notifyOnUpdates', value)}
                        disabled={!preferences.notifications.enabled}
                      />

                      <CheckboxControl
                        label={__('Rapports générés', 'boss-seo')}
                        checked={preferences.notifications.notifyOnReports}
                        onChange={(value) => updatePreferences('notifications', 'notifyOnReports', value)}
                        disabled={!preferences.notifications.enabled}
                      />
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            <Card className="boss-mb-6">
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Affichage', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-space-y-4">
                  <RadioControl
                    label={__('Thème', 'boss-seo')}
                    selected={preferences.display.theme}
                    options={[
                      { label: __('Clair', 'boss-seo'), value: 'light' },
                      { label: __('Sombre', 'boss-seo'), value: 'dark' },
                      { label: __('Système', 'boss-seo'), value: 'system' }
                    ]}
                    onChange={(value) => updatePreferences('display', 'theme', value)}
                  />

                  <div>
                    <label className="boss-block boss-mb-2 boss-text-sm boss-font-medium boss-text-boss-dark">
                      {__('Couleur principale', 'boss-seo')}
                    </label>
                    <ColorPicker
                      color={preferences.display.primaryColor}
                      onChange={(value) => updatePreferences('display', 'primaryColor', value)}
                      enableAlpha={false}
                    />
                  </div>

                  <SelectControl
                    label={__('Taille de police', 'boss-seo')}
                    value={preferences.display.fontSize}
                    options={[
                      { label: __('Petite', 'boss-seo'), value: 'small' },
                      { label: __('Moyenne', 'boss-seo'), value: 'medium' },
                      { label: __('Grande', 'boss-seo'), value: 'large' }
                    ]}
                    onChange={(value) => updatePreferences('display', 'fontSize', value)}
                  />

                  <ToggleControl
                    label={__('Mode compact', 'boss-seo')}
                    help={__('Réduit l\'espacement et la taille des éléments pour afficher plus de contenu', 'boss-seo')}
                    checked={preferences.display.compactMode}
                    onChange={(value) => updatePreferences('display', 'compactMode', value)}
                  />

                  <ToggleControl
                    label={__('Afficher les miniatures', 'boss-seo')}
                    checked={preferences.display.showThumbnails}
                    onChange={(value) => updatePreferences('display', 'showThumbnails', value)}
                  />

                  <SelectControl
                    label={__('Lignes par page dans les tableaux', 'boss-seo')}
                    value={preferences.display.tableRowsPerPage}
                    options={[
                      { label: '10', value: 10 },
                      { label: '20', value: 20 },
                      { label: '50', value: 50 },
                      { label: '100', value: 100 }
                    ]}
                    onChange={(value) => updatePreferences('display', 'tableRowsPerPage', parseInt(value))}
                  />

                  <SelectControl
                    label={__('Format de date', 'boss-seo')}
                    value={preferences.display.dateFormat}
                    options={[
                      { label: 'YYYY-MM-DD', value: 'Y-m-d' },
                      { label: 'DD/MM/YYYY', value: 'd/m/Y' },
                      { label: 'MM/DD/YYYY', value: 'm/d/Y' },
                      { label: 'DD.MM.YYYY', value: 'd.m.Y' }
                    ]}
                    onChange={(value) => updatePreferences('display', 'dateFormat', value)}
                  />
                </div>
              </CardBody>
            </Card>

            <Card className="boss-mb-6">
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Intégration avec l\'éditeur', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-space-y-4">
                  <ToggleControl
                    label={__('Activer le panneau SEO', 'boss-seo')}
                    checked={preferences.editor.enableSeoPanel}
                    onChange={(value) => updatePreferences('editor', 'enableSeoPanel', value)}
                  />

                  <ToggleControl
                    label={__('Afficher dans la barre latérale', 'boss-seo')}
                    help={__('Affiche le panneau SEO dans la barre latérale plutôt que sous l\'éditeur', 'boss-seo')}
                    checked={preferences.editor.showInSidebar}
                    onChange={(value) => updatePreferences('editor', 'showInSidebar', value)}
                    disabled={!preferences.editor.enableSeoPanel}
                  />

                  <ToggleControl
                    label={__('Analyser automatiquement le contenu', 'boss-seo')}
                    help={__('Analyse le contenu en temps réel pendant la rédaction', 'boss-seo')}
                    checked={preferences.editor.autoAnalyzeContent}
                    onChange={(value) => updatePreferences('editor', 'autoAnalyzeContent', value)}
                    disabled={!preferences.editor.enableSeoPanel}
                  />

                  <ToggleControl
                    label={__('Surligner les problèmes dans le contenu', 'boss-seo')}
                    help={__('Surligne les problèmes SEO directement dans l\'éditeur', 'boss-seo')}
                    checked={preferences.editor.highlightIssues}
                    onChange={(value) => updatePreferences('editor', 'highlightIssues', value)}
                    disabled={!preferences.editor.enableSeoPanel || !preferences.editor.autoAnalyzeContent}
                  />

                  <ToggleControl
                    label={__('Afficher le score SEO dans les colonnes d\'administration', 'boss-seo')}
                    checked={preferences.editor.showScoreInAdminColumns}
                    onChange={(value) => updatePreferences('editor', 'showScoreInAdminColumns', value)}
                  />

                  <ToggleControl
                    label={__('Afficher les méta-informations dans les colonnes d\'administration', 'boss-seo')}
                    checked={preferences.editor.showMetaInfoInAdminColumns}
                    onChange={(value) => updatePreferences('editor', 'showMetaInfoInAdminColumns', value)}
                  />

                  <ToggleControl
                    label={__('Activer les suggestions IA', 'boss-seo')}
                    help={__('Affiche des suggestions d\'amélioration générées par IA', 'boss-seo')}
                    checked={preferences.editor.enableAiSuggestions}
                    onChange={(value) => updatePreferences('editor', 'enableAiSuggestions', value)}
                  />
                </div>
              </CardBody>
            </Card>
          </div>

          <div className="boss-mt-6 boss-flex boss-justify-between">
            <Button
              isSecondary
              isDestructive
              onClick={handleResetPreferences}
              disabled={isSaving}
            >
              {__('Réinitialiser les préférences', 'boss-seo')}
            </Button>

            <Button
              isPrimary
              onClick={handleSavePreferences}
              isBusy={isSaving}
              disabled={isSaving}
              className="boss-px-6"
            >
              {isSaving ? __('Enregistrement...', 'boss-seo') : __('Enregistrer les préférences', 'boss-seo')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserPreferences;
