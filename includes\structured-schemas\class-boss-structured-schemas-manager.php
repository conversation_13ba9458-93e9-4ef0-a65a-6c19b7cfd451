<?php
/**
 * La classe qui gère les schémas structurés.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 */

/**
 * La classe qui gère les schémas structurés.
 *
 * Cette classe gère la création, la modification, la suppression et la récupération des schémas.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 * <AUTHOR> SEO Team
 */
class Boss_Structured_Schemas_Manager {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Types de schémas disponibles.
     *
     * @since    1.2.0
     * @access   protected
     * @var      array    $schema_types    Les types de schémas disponibles.
     */
    protected $schema_types;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string                   $plugin_name    Le nom du plugin.
     * @param    string                   $version        La version du plugin.
     * @param    Boss_Optimizer_Settings  $settings       Instance de la classe de paramètres (peut être null).
     */
    public function __construct( $plugin_name, $version, $settings = null ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;
        $this->init_schema_types();
    }

    /**
     * Initialise les types de schémas disponibles.
     *
     * @since    1.2.0
     * @access   private
     */
    private function init_schema_types() {
        $this->schema_types = array(
            'Article' => array(
                'label' => __( 'Article', 'boss-seo' ),
                'description' => __( 'Pour les articles de blog, actualités, etc.', 'boss-seo' ),
                'properties' => $this->get_article_properties()
            ),
            'Product' => array(
                'label' => __( 'Produit', 'boss-seo' ),
                'description' => __( 'Pour les produits e-commerce.', 'boss-seo' ),
                'properties' => $this->get_product_properties()
            ),
            'LocalBusiness' => array(
                'label' => __( 'Entreprise locale', 'boss-seo' ),
                'description' => __( 'Pour les entreprises ayant une présence physique.', 'boss-seo' ),
                'properties' => $this->get_local_business_properties()
            ),
            'Organization' => array(
                'label' => __( 'Organisation', 'boss-seo' ),
                'description' => __( 'Pour les entreprises et organisations.', 'boss-seo' ),
                'properties' => $this->get_organization_properties()
            ),
            'Person' => array(
                'label' => __( 'Personne', 'boss-seo' ),
                'description' => __( 'Pour les profils de personnes.', 'boss-seo' ),
                'properties' => $this->get_person_properties()
            ),
            'Event' => array(
                'label' => __( 'Événement', 'boss-seo' ),
                'description' => __( 'Pour les événements.', 'boss-seo' ),
                'properties' => $this->get_event_properties()
            ),
            'Recipe' => array(
                'label' => __( 'Recette', 'boss-seo' ),
                'description' => __( 'Pour les recettes de cuisine.', 'boss-seo' ),
                'properties' => $this->get_recipe_properties()
            ),
            'FAQPage' => array(
                'label' => __( 'FAQ', 'boss-seo' ),
                'description' => __( 'Pour les pages de questions fréquentes.', 'boss-seo' ),
                'properties' => $this->get_faq_properties()
            ),
            'HowTo' => array(
                'label' => __( 'Guide pratique', 'boss-seo' ),
                'description' => __( 'Pour les tutoriels et guides étape par étape.', 'boss-seo' ),
                'properties' => $this->get_howto_properties()
            ),
            'WebPage' => array(
                'label' => __( 'Page web', 'boss-seo' ),
                'description' => __( 'Pour les pages web génériques.', 'boss-seo' ),
                'properties' => $this->get_webpage_properties()
            ),
            'Custom' => array(
                'label' => __( 'Personnalisé', 'boss-seo' ),
                'description' => __( 'Créez votre propre schéma personnalisé.', 'boss-seo' ),
                'properties' => array()
            )
        );
    }

    /**
     * Récupère les types de schémas disponibles.
     *
     * @since    1.2.0
     * @return   array    Les types de schémas disponibles.
     */
    public function get_schema_types() {
        return $this->schema_types;
    }

    /**
     * Récupère les propriétés pour le type de schéma Article.
     *
     * @since    1.2.0
     * @return   array    Les propriétés du schéma Article.
     */
    private function get_article_properties() {
        return array(
            'headline' => array(
                'label' => __( 'Titre', 'boss-seo' ),
                'description' => __( 'Le titre de l\'article.', 'boss-seo' ),
                'type' => 'text',
                'required' => true,
                'default' => '%post_title%'
            ),
            'description' => array(
                'label' => __( 'Description', 'boss-seo' ),
                'description' => __( 'Une courte description de l\'article.', 'boss-seo' ),
                'type' => 'textarea',
                'required' => false,
                'default' => '%post_excerpt%'
            ),
            'author' => array(
                'label' => __( 'Auteur', 'boss-seo' ),
                'description' => __( 'L\'auteur de l\'article.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Person'
                    ),
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => '%author_name%'
                    ),
                    'url' => array(
                        'label' => __( 'URL', 'boss-seo' ),
                        'type' => 'url',
                        'required' => false,
                        'default' => '%author_url%'
                    )
                )
            ),
            'publisher' => array(
                'label' => __( 'Éditeur', 'boss-seo' ),
                'description' => __( 'L\'organisation qui a publié l\'article.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Organization'
                    ),
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => get_bloginfo( 'name' )
                    ),
                    'logo' => array(
                        'label' => __( 'Logo', 'boss-seo' ),
                        'description' => __( 'URL du logo de l\'organisation.', 'boss-seo' ),
                        'type' => 'object',
                        'properties' => array(
                            '@type' => array(
                                'type' => 'hidden',
                                'default' => 'ImageObject'
                            ),
                            'url' => array(
                                'label' => __( 'URL', 'boss-seo' ),
                                'type' => 'url',
                                'required' => true,
                                'default' => function_exists( 'get_custom_logo' ) ? wp_get_attachment_image_url( get_theme_mod( 'custom_logo' ), 'full' ) : ''
                            )
                        )
                    )
                )
            ),
            'datePublished' => array(
                'label' => __( 'Date de publication', 'boss-seo' ),
                'description' => __( 'La date de publication de l\'article.', 'boss-seo' ),
                'type' => 'datetime',
                'required' => true,
                'default' => '%post_date%'
            ),
            'dateModified' => array(
                'label' => __( 'Date de modification', 'boss-seo' ),
                'description' => __( 'La date de dernière modification de l\'article.', 'boss-seo' ),
                'type' => 'datetime',
                'required' => true,
                'default' => '%post_modified%'
            ),
            'mainEntityOfPage' => array(
                'label' => __( 'URL de la page', 'boss-seo' ),
                'description' => __( 'L\'URL de la page contenant l\'article.', 'boss-seo' ),
                'type' => 'url',
                'required' => true,
                'default' => '%permalink%'
            ),
            'image' => array(
                'label' => __( 'Image', 'boss-seo' ),
                'description' => __( 'L\'image principale de l\'article.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'ImageObject'
                    ),
                    'url' => array(
                        'label' => __( 'URL', 'boss-seo' ),
                        'type' => 'url',
                        'required' => true,
                        'default' => '%featured_image%'
                    )
                )
            ),
            'articleSection' => array(
                'label' => __( 'Section', 'boss-seo' ),
                'description' => __( 'La section de l\'article (catégorie).', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => '%tax_category%'
            ),
            'keywords' => array(
                'label' => __( 'Mots-clés', 'boss-seo' ),
                'description' => __( 'Les mots-clés de l\'article (étiquettes).', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => '%tax_post_tag%'
            )
        );
    }

    /**
     * Récupère les propriétés pour le type de schéma Product.
     *
     * @since    1.2.0
     * @return   array    Les propriétés du schéma Product.
     */
    private function get_product_properties() {
        return array(
            'name' => array(
                'label' => __( 'Nom', 'boss-seo' ),
                'description' => __( 'Le nom du produit.', 'boss-seo' ),
                'type' => 'text',
                'required' => true,
                'default' => '%post_title%'
            ),
            'description' => array(
                'label' => __( 'Description', 'boss-seo' ),
                'description' => __( 'La description du produit.', 'boss-seo' ),
                'type' => 'textarea',
                'required' => false,
                'default' => '%post_excerpt%'
            ),
            'image' => array(
                'label' => __( 'Image', 'boss-seo' ),
                'description' => __( 'L\'image principale du produit.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => '%featured_image%'
            ),
            'brand' => array(
                'label' => __( 'Marque', 'boss-seo' ),
                'description' => __( 'La marque du produit.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Brand'
                    ),
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    )
                )
            ),
            'offers' => array(
                'label' => __( 'Offre', 'boss-seo' ),
                'description' => __( 'L\'offre pour le produit.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Offer'
                    ),
                    'price' => array(
                        'label' => __( 'Prix', 'boss-seo' ),
                        'type' => 'number',
                        'required' => true,
                        'default' => '%meta_price%'
                    ),
                    'priceCurrency' => array(
                        'label' => __( 'Devise', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => 'EUR'
                    ),
                    'availability' => array(
                        'label' => __( 'Disponibilité', 'boss-seo' ),
                        'type' => 'select',
                        'options' => array(
                            'https://schema.org/InStock' => __( 'En stock', 'boss-seo' ),
                            'https://schema.org/OutOfStock' => __( 'Rupture de stock', 'boss-seo' ),
                            'https://schema.org/PreOrder' => __( 'Précommande', 'boss-seo' )
                        ),
                        'required' => true,
                        'default' => 'https://schema.org/InStock'
                    ),
                    'url' => array(
                        'label' => __( 'URL', 'boss-seo' ),
                        'type' => 'url',
                        'required' => false,
                        'default' => '%permalink%'
                    )
                )
            ),
            'aggregateRating' => array(
                'label' => __( 'Évaluation moyenne', 'boss-seo' ),
                'description' => __( 'L\'évaluation moyenne du produit.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'AggregateRating'
                    ),
                    'ratingValue' => array(
                        'label' => __( 'Note moyenne', 'boss-seo' ),
                        'type' => 'number',
                        'required' => true,
                        'default' => '%meta_rating%'
                    ),
                    'reviewCount' => array(
                        'label' => __( 'Nombre d\'avis', 'boss-seo' ),
                        'type' => 'number',
                        'required' => true,
                        'default' => '%meta_review_count%'
                    )
                )
            ),
            'sku' => array(
                'label' => __( 'SKU', 'boss-seo' ),
                'description' => __( 'Le code SKU du produit.', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => '%meta_sku%'
            ),
            'mpn' => array(
                'label' => __( 'MPN', 'boss-seo' ),
                'description' => __( 'Le code MPN du produit.', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => '%meta_mpn%'
            ),
            'gtin13' => array(
                'label' => __( 'GTIN-13 (EAN)', 'boss-seo' ),
                'description' => __( 'Le code GTIN-13 (EAN) du produit.', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => '%meta_gtin13%'
            )
        );
    }



    /**
     * Récupère les propriétés pour le type de schéma LocalBusiness.
     *
     * @since    1.2.0
     * @return   array    Les propriétés du schéma LocalBusiness.
     */
    private function get_local_business_properties() {
        return array(
            'name' => array(
                'label' => __( 'Nom', 'boss-seo' ),
                'description' => __( 'Le nom de l\'entreprise.', 'boss-seo' ),
                'type' => 'text',
                'required' => true,
                'default' => get_bloginfo( 'name' )
            ),
            'description' => array(
                'label' => __( 'Description', 'boss-seo' ),
                'description' => __( 'La description de l\'entreprise.', 'boss-seo' ),
                'type' => 'textarea',
                'required' => false,
                'default' => get_bloginfo( 'description' )
            ),
            'image' => array(
                'label' => __( 'Image', 'boss-seo' ),
                'description' => __( 'L\'image principale de l\'entreprise.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => function_exists( 'get_custom_logo' ) ? wp_get_attachment_image_url( get_theme_mod( 'custom_logo' ), 'full' ) : ''
            ),
            'address' => array(
                'label' => __( 'Adresse', 'boss-seo' ),
                'description' => __( 'L\'adresse de l\'entreprise.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'PostalAddress'
                    ),
                    'streetAddress' => array(
                        'label' => __( 'Rue', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'addressLocality' => array(
                        'label' => __( 'Ville', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'postalCode' => array(
                        'label' => __( 'Code postal', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'addressCountry' => array(
                        'label' => __( 'Pays', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => 'FR'
                    )
                )
            ),
            'telephone' => array(
                'label' => __( 'Téléphone', 'boss-seo' ),
                'description' => __( 'Le numéro de téléphone de l\'entreprise.', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => ''
            ),
            'email' => array(
                'label' => __( 'Email', 'boss-seo' ),
                'description' => __( 'L\'adresse email de l\'entreprise.', 'boss-seo' ),
                'type' => 'email',
                'required' => false,
                'default' => get_bloginfo( 'admin_email' )
            ),
            'url' => array(
                'label' => __( 'Site web', 'boss-seo' ),
                'description' => __( 'L\'URL du site web de l\'entreprise.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => get_bloginfo( 'url' )
            ),
        );
    }

    /**
     * Récupère les propriétés pour le type de schéma Organization.
     *
     * @since    1.2.0
     * @return   array    Les propriétés du schéma Organization.
     */
    private function get_organization_properties() {
        return array(
            'name' => array(
                'label' => __( 'Nom', 'boss-seo' ),
                'description' => __( 'Le nom de l\'organisation.', 'boss-seo' ),
                'type' => 'text',
                'required' => true,
                'default' => get_bloginfo( 'name' )
            ),
            'description' => array(
                'label' => __( 'Description', 'boss-seo' ),
                'description' => __( 'La description de l\'organisation.', 'boss-seo' ),
                'type' => 'textarea',
                'required' => false,
                'default' => get_bloginfo( 'description' )
            ),
            'url' => array(
                'label' => __( 'Site web', 'boss-seo' ),
                'description' => __( 'L\'URL du site web de l\'organisation.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => get_bloginfo( 'url' )
            ),
            'logo' => array(
                'label' => __( 'Logo', 'boss-seo' ),
                'description' => __( 'Le logo de l\'organisation.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => function_exists( 'get_custom_logo' ) ? wp_get_attachment_image_url( get_theme_mod( 'custom_logo' ), 'full' ) : ''
            ),
            'contactPoint' => array(
                'label' => __( 'Point de contact', 'boss-seo' ),
                'description' => __( 'Les informations de contact de l\'organisation.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'ContactPoint'
                    ),
                    'telephone' => array(
                        'label' => __( 'Téléphone', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'email' => array(
                        'label' => __( 'Email', 'boss-seo' ),
                        'type' => 'email',
                        'required' => false,
                        'default' => get_bloginfo( 'admin_email' )
                    ),
                    'contactType' => array(
                        'label' => __( 'Type de contact', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => 'customer service'
                    )
                )
            ),
            'sameAs' => array(
                'label' => __( 'Profils sociaux', 'boss-seo' ),
                'description' => __( 'Les profils sociaux de l\'organisation.', 'boss-seo' ),
                'type' => 'array',
                'items' => array(
                    'type' => 'url'
                ),
                'required' => false,
                'default' => array()
            ),
            'address' => array(
                'label' => __( 'Adresse', 'boss-seo' ),
                'description' => __( 'L\'adresse de l\'organisation.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'PostalAddress'
                    ),
                    'streetAddress' => array(
                        'label' => __( 'Rue', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'addressLocality' => array(
                        'label' => __( 'Ville', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'postalCode' => array(
                        'label' => __( 'Code postal', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'addressCountry' => array(
                        'label' => __( 'Pays', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => 'FR'
                    )
                )
            ),
        );
    }

    /**
     * Récupère les propriétés pour le type de schéma Event.
     *
     * @since    1.2.0
     * @return   array    Les propriétés du schéma Event.
     */
    private function get_event_properties() {
        return array(
            'name' => array(
                'label' => __( 'Nom', 'boss-seo' ),
                'description' => __( 'Le nom de l\'événement.', 'boss-seo' ),
                'type' => 'text',
                'required' => true,
                'default' => '%post_title%'
            ),
            'description' => array(
                'label' => __( 'Description', 'boss-seo' ),
                'description' => __( 'La description de l\'événement.', 'boss-seo' ),
                'type' => 'textarea',
                'required' => false,
                'default' => '%post_excerpt%'
            ),
            'startDate' => array(
                'label' => __( 'Date de début', 'boss-seo' ),
                'description' => __( 'La date et l\'heure de début de l\'événement.', 'boss-seo' ),
                'type' => 'datetime',
                'required' => true,
                'default' => ''
            ),
            'endDate' => array(
                'label' => __( 'Date de fin', 'boss-seo' ),
                'description' => __( 'La date et l\'heure de fin de l\'événement.', 'boss-seo' ),
                'type' => 'datetime',
                'required' => false,
                'default' => ''
            ),
            'location' => array(
                'label' => __( 'Lieu', 'boss-seo' ),
                'description' => __( 'Le lieu de l\'événement.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Place'
                    ),
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'address' => array(
                        'label' => __( 'Adresse', 'boss-seo' ),
                        'type' => 'object',
                        'properties' => array(
                            '@type' => array(
                                'type' => 'hidden',
                                'default' => 'PostalAddress'
                            ),
                            'streetAddress' => array(
                                'label' => __( 'Rue', 'boss-seo' ),
                                'type' => 'text',
                                'required' => true,
                                'default' => ''
                            ),
                            'addressLocality' => array(
                                'label' => __( 'Ville', 'boss-seo' ),
                                'type' => 'text',
                                'required' => true,
                                'default' => ''
                            ),
                            'postalCode' => array(
                                'label' => __( 'Code postal', 'boss-seo' ),
                                'type' => 'text',
                                'required' => true,
                                'default' => ''
                            ),
                            'addressCountry' => array(
                                'label' => __( 'Pays', 'boss-seo' ),
                                'type' => 'text',
                                'required' => true,
                                'default' => 'FR'
                            )
                        )
                    )
                )
            ),
            'image' => array(
                'label' => __( 'Image', 'boss-seo' ),
                'description' => __( 'L\'image de l\'événement.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => '%featured_image%'
            ),
            'performer' => array(
                'label' => __( 'Intervenant', 'boss-seo' ),
                'description' => __( 'L\'intervenant de l\'événement.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Person'
                    ),
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    )
                )
            ),
            'organizer' => array(
                'label' => __( 'Organisateur', 'boss-seo' ),
                'description' => __( 'L\'organisateur de l\'événement.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Organization'
                    ),
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => get_bloginfo( 'name' )
                    ),
                    'url' => array(
                        'label' => __( 'URL', 'boss-seo' ),
                        'type' => 'url',
                        'required' => false,
                        'default' => get_bloginfo( 'url' )
                    )
                )
            ),
            'offers' => array(
                'label' => __( 'Offre', 'boss-seo' ),
                'description' => __( 'L\'offre pour l\'événement.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Offer'
                    ),
                    'price' => array(
                        'label' => __( 'Prix', 'boss-seo' ),
                        'type' => 'number',
                        'required' => true,
                        'default' => ''
                    ),
                    'priceCurrency' => array(
                        'label' => __( 'Devise', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => 'EUR'
                    ),
                    'availability' => array(
                        'label' => __( 'Disponibilité', 'boss-seo' ),
                        'type' => 'select',
                        'options' => array(
                            'https://schema.org/InStock' => __( 'Disponible', 'boss-seo' ),
                            'https://schema.org/SoldOut' => __( 'Épuisé', 'boss-seo' ),
                            'https://schema.org/PreOrder' => __( 'Précommande', 'boss-seo' )
                        ),
                        'required' => true,
                        'default' => 'https://schema.org/InStock'
                    ),
                    'url' => array(
                        'label' => __( 'URL', 'boss-seo' ),
                        'type' => 'url',
                        'required' => false,
                        'default' => '%permalink%'
                    )
                )
            ),
            'url' => array(
                'label' => __( 'URL', 'boss-seo' ),
                'description' => __( 'L\'URL de l\'événement.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => '%permalink%'
            )
        );
    }

    /**
     * Récupère les propriétés pour le type de schéma Recipe.
     *
     * @since    1.2.0
     * @return   array    Les propriétés du schéma Recipe.
     */
    private function get_recipe_properties() {
        return array(
            'name' => array(
                'label' => __( 'Nom', 'boss-seo' ),
                'description' => __( 'Le nom de la recette.', 'boss-seo' ),
                'type' => 'text',
                'required' => true,
                'default' => '%post_title%'
            ),
            'description' => array(
                'label' => __( 'Description', 'boss-seo' ),
                'description' => __( 'La description de la recette.', 'boss-seo' ),
                'type' => 'textarea',
                'required' => false,
                'default' => '%post_excerpt%'
            ),
            'image' => array(
                'label' => __( 'Image', 'boss-seo' ),
                'description' => __( 'L\'image de la recette.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => '%featured_image%'
            ),
            'author' => array(
                'label' => __( 'Auteur', 'boss-seo' ),
                'description' => __( 'L\'auteur de la recette.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Person'
                    ),
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => '%author_name%'
                    )
                )
            ),
            'datePublished' => array(
                'label' => __( 'Date de publication', 'boss-seo' ),
                'description' => __( 'La date de publication de la recette.', 'boss-seo' ),
                'type' => 'datetime',
                'required' => false,
                'default' => '%post_date%'
            ),
            'prepTime' => array(
                'label' => __( 'Temps de préparation', 'boss-seo' ),
                'description' => __( 'Le temps de préparation de la recette (format ISO 8601).', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => 'PT30M'
            ),
            'cookTime' => array(
                'label' => __( 'Temps de cuisson', 'boss-seo' ),
                'description' => __( 'Le temps de cuisson de la recette (format ISO 8601).', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => 'PT1H'
            ),
            'totalTime' => array(
                'label' => __( 'Temps total', 'boss-seo' ),
                'description' => __( 'Le temps total de la recette (format ISO 8601).', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => 'PT1H30M'
            ),
            'recipeYield' => array(
                'label' => __( 'Nombre de portions', 'boss-seo' ),
                'description' => __( 'Le nombre de portions de la recette.', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => '4'
            ),
            'recipeIngredient' => array(
                'label' => __( 'Ingrédients', 'boss-seo' ),
                'description' => __( 'Les ingrédients de la recette.', 'boss-seo' ),
                'type' => 'array',
                'items' => array(
                    'type' => 'text'
                ),
                'required' => false,
                'default' => array()
            ),
            'recipeInstructions' => array(
                'label' => __( 'Instructions', 'boss-seo' ),
                'description' => __( 'Les instructions de la recette.', 'boss-seo' ),
                'type' => 'array',
                'items' => array(
                    'type' => 'text'
                ),
                'required' => false,
                'default' => array()
            ),
            'nutrition' => array(
                'label' => __( 'Nutrition', 'boss-seo' ),
                'description' => __( 'Les informations nutritionnelles de la recette.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'NutritionInformation'
                    ),
                    'calories' => array(
                        'label' => __( 'Calories', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    )
                )
            ),
            'keywords' => array(
                'label' => __( 'Mots-clés', 'boss-seo' ),
                'description' => __( 'Les mots-clés de la recette.', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => '%tax_post_tag%'
            )
        );
    }

    /**
     * Récupère les propriétés pour le type de schéma FAQ.
     *
     * @since    1.2.0
     * @return   array    Les propriétés du schéma FAQ.
     */
    private function get_faq_properties() {
        return array(
            'mainEntity' => array(
                'label' => __( 'Questions et réponses', 'boss-seo' ),
                'description' => __( 'Les questions et réponses de la FAQ.', 'boss-seo' ),
                'type' => 'array',
                'items' => array(
                    'type' => 'object',
                    'properties' => array(
                        '@type' => array(
                            'type' => 'hidden',
                            'default' => 'Question'
                        ),
                        'name' => array(
                            'label' => __( 'Question', 'boss-seo' ),
                            'type' => 'text',
                            'required' => true,
                            'default' => ''
                        ),
                        'acceptedAnswer' => array(
                            'label' => __( 'Réponse', 'boss-seo' ),
                            'type' => 'object',
                            'properties' => array(
                                '@type' => array(
                                    'type' => 'hidden',
                                    'default' => 'Answer'
                                ),
                                'text' => array(
                                    'label' => __( 'Texte', 'boss-seo' ),
                                    'type' => 'textarea',
                                    'required' => true,
                                    'default' => ''
                                )
                            )
                        )
                    )
                ),
                'required' => true,
                'default' => array()
            )
        );
    }

    /**
     * Récupère les propriétés pour le type de schéma Person.
     *
     * @since    1.2.0
     * @return   array    Les propriétés du schéma Person.
     */
    private function get_person_properties() {
        return array(
            'name' => array(
                'label' => __( 'Nom', 'boss-seo' ),
                'description' => __( 'Le nom de la personne.', 'boss-seo' ),
                'type' => 'text',
                'required' => true,
                'default' => ''
            ),
            'description' => array(
                'label' => __( 'Description', 'boss-seo' ),
                'description' => __( 'La description de la personne.', 'boss-seo' ),
                'type' => 'textarea',
                'required' => false,
                'default' => ''
            ),
            'image' => array(
                'label' => __( 'Image', 'boss-seo' ),
                'description' => __( 'L\'image de la personne.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => ''
            ),
            'jobTitle' => array(
                'label' => __( 'Titre du poste', 'boss-seo' ),
                'description' => __( 'Le titre du poste de la personne.', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => ''
            ),
            'worksFor' => array(
                'label' => __( 'Employeur', 'boss-seo' ),
                'description' => __( 'L\'organisation pour laquelle la personne travaille.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Organization'
                    ),
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => get_bloginfo( 'name' )
                    )
                )
            ),
            'url' => array(
                'label' => __( 'Site web', 'boss-seo' ),
                'description' => __( 'L\'URL du site web de la personne.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => ''
            ),
            'sameAs' => array(
                'label' => __( 'Profils sociaux', 'boss-seo' ),
                'description' => __( 'Les profils sociaux de la personne.', 'boss-seo' ),
                'type' => 'array',
                'items' => array(
                    'type' => 'url'
                ),
                'required' => false,
                'default' => array()
            ),
            'address' => array(
                'label' => __( 'Adresse', 'boss-seo' ),
                'description' => __( 'L\'adresse de la personne.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'PostalAddress'
                    ),
                    'streetAddress' => array(
                        'label' => __( 'Rue', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'addressLocality' => array(
                        'label' => __( 'Ville', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'postalCode' => array(
                        'label' => __( 'Code postal', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    ),
                    'addressCountry' => array(
                        'label' => __( 'Pays', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => 'FR'
                    )
                )
            ),
            'email' => array(
                'label' => __( 'Email', 'boss-seo' ),
                'description' => __( 'L\'adresse email de la personne.', 'boss-seo' ),
                'type' => 'email',
                'required' => false,
                'default' => ''
            ),
            'telephone' => array(
                'label' => __( 'Téléphone', 'boss-seo' ),
                'description' => __( 'Le numéro de téléphone de la personne.', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => ''
            )
        );
    }

    /**
     * Récupère les propriétés pour le type de schéma HowTo.
     *
     * @since    1.2.0
     * @return   array    Les propriétés du schéma HowTo.
     */
    private function get_howto_properties() {
        return array(
            'name' => array(
                'label' => __( 'Nom', 'boss-seo' ),
                'description' => __( 'Le nom du guide pratique.', 'boss-seo' ),
                'type' => 'text',
                'required' => true,
                'default' => '%post_title%'
            ),
            'description' => array(
                'label' => __( 'Description', 'boss-seo' ),
                'description' => __( 'La description du guide pratique.', 'boss-seo' ),
                'type' => 'textarea',
                'required' => false,
                'default' => '%post_excerpt%'
            ),
            'image' => array(
                'label' => __( 'Image', 'boss-seo' ),
                'description' => __( 'L\'image du guide pratique.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => '%featured_image%'
            ),
            'totalTime' => array(
                'label' => __( 'Temps total', 'boss-seo' ),
                'description' => __( 'Le temps total nécessaire pour réaliser le guide pratique (format ISO 8601).', 'boss-seo' ),
                'type' => 'text',
                'required' => false,
                'default' => 'PT1H'
            ),
            'estimatedCost' => array(
                'label' => __( 'Coût estimé', 'boss-seo' ),
                'description' => __( 'Le coût estimé pour réaliser le guide pratique.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'MonetaryAmount'
                    ),
                    'currency' => array(
                        'label' => __( 'Devise', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => 'EUR'
                    ),
                    'value' => array(
                        'label' => __( 'Valeur', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => ''
                    )
                )
            ),
            'supply' => array(
                'label' => __( 'Fournitures', 'boss-seo' ),
                'description' => __( 'Les fournitures nécessaires pour réaliser le guide pratique.', 'boss-seo' ),
                'type' => 'array',
                'items' => array(
                    'type' => 'object',
                    'properties' => array(
                        '@type' => array(
                            'type' => 'hidden',
                            'default' => 'HowToSupply'
                        ),
                        'name' => array(
                            'label' => __( 'Nom', 'boss-seo' ),
                            'type' => 'text',
                            'required' => true,
                            'default' => ''
                        )
                    )
                ),
                'required' => false,
                'default' => array()
            ),
            'tool' => array(
                'label' => __( 'Outils', 'boss-seo' ),
                'description' => __( 'Les outils nécessaires pour réaliser le guide pratique.', 'boss-seo' ),
                'type' => 'array',
                'items' => array(
                    'type' => 'object',
                    'properties' => array(
                        '@type' => array(
                            'type' => 'hidden',
                            'default' => 'HowToTool'
                        ),
                        'name' => array(
                            'label' => __( 'Nom', 'boss-seo' ),
                            'type' => 'text',
                            'required' => true,
                            'default' => ''
                        )
                    )
                ),
                'required' => false,
                'default' => array()
            ),
            'step' => array(
                'label' => __( 'Étapes', 'boss-seo' ),
                'description' => __( 'Les étapes du guide pratique.', 'boss-seo' ),
                'type' => 'array',
                'items' => array(
                    'type' => 'object',
                    'properties' => array(
                        '@type' => array(
                            'type' => 'hidden',
                            'default' => 'HowToStep'
                        ),
                        'name' => array(
                            'label' => __( 'Nom', 'boss-seo' ),
                            'type' => 'text',
                            'required' => true,
                            'default' => ''
                        ),
                        'text' => array(
                            'label' => __( 'Texte', 'boss-seo' ),
                            'type' => 'textarea',
                            'required' => true,
                            'default' => ''
                        ),
                        'image' => array(
                            'label' => __( 'Image', 'boss-seo' ),
                            'type' => 'url',
                            'required' => false,
                            'default' => ''
                        ),
                        'url' => array(
                            'label' => __( 'URL', 'boss-seo' ),
                            'type' => 'url',
                            'required' => false,
                            'default' => ''
                        )
                    )
                ),
                'required' => true,
                'default' => array()
            )
        );
    }

    /**
     * Récupère les propriétés pour le type de schéma WebPage.
     *
     * @since    1.2.0
     * @return   array    Les propriétés du schéma WebPage.
     */
    private function get_webpage_properties() {
        return array(
            'name' => array(
                'label' => __( 'Nom', 'boss-seo' ),
                'description' => __( 'Le nom de la page web.', 'boss-seo' ),
                'type' => 'text',
                'required' => true,
                'default' => '%post_title%'
            ),
            'description' => array(
                'label' => __( 'Description', 'boss-seo' ),
                'description' => __( 'La description de la page web.', 'boss-seo' ),
                'type' => 'textarea',
                'required' => false,
                'default' => '%post_excerpt%'
            ),
            'url' => array(
                'label' => __( 'URL', 'boss-seo' ),
                'description' => __( 'L\'URL de la page web.', 'boss-seo' ),
                'type' => 'url',
                'required' => true,
                'default' => '%permalink%'
            ),
            'author' => array(
                'label' => __( 'Auteur', 'boss-seo' ),
                'description' => __( 'L\'auteur de la page web.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Person'
                    ),
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => '%author_name%'
                    )
                )
            ),
            'datePublished' => array(
                'label' => __( 'Date de publication', 'boss-seo' ),
                'description' => __( 'La date de publication de la page web.', 'boss-seo' ),
                'type' => 'datetime',
                'required' => false,
                'default' => '%post_date%'
            ),
            'dateModified' => array(
                'label' => __( 'Date de modification', 'boss-seo' ),
                'description' => __( 'La date de dernière modification de la page web.', 'boss-seo' ),
                'type' => 'datetime',
                'required' => false,
                'default' => '%post_modified%'
            ),
            'image' => array(
                'label' => __( 'Image', 'boss-seo' ),
                'description' => __( 'L\'image de la page web.', 'boss-seo' ),
                'type' => 'url',
                'required' => false,
                'default' => '%featured_image%'
            ),
            'publisher' => array(
                'label' => __( 'Éditeur', 'boss-seo' ),
                'description' => __( 'L\'éditeur de la page web.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'Organization'
                    ),
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                        'default' => get_bloginfo( 'name' )
                    ),
                    'logo' => array(
                        'label' => __( 'Logo', 'boss-seo' ),
                        'type' => 'object',
                        'properties' => array(
                            '@type' => array(
                                'type' => 'hidden',
                                'default' => 'ImageObject'
                            ),
                            'url' => array(
                                'label' => __( 'URL', 'boss-seo' ),
                                'type' => 'url',
                                'required' => true,
                                'default' => function_exists( 'get_custom_logo' ) ? wp_get_attachment_image_url( get_theme_mod( 'custom_logo' ), 'full' ) : ''
                            )
                        )
                    )
                )
            ),
            'breadcrumb' => array(
                'label' => __( 'Fil d\'Ariane', 'boss-seo' ),
                'description' => __( 'Le fil d\'Ariane de la page web.', 'boss-seo' ),
                'type' => 'object',
                'properties' => array(
                    '@type' => array(
                        'type' => 'hidden',
                        'default' => 'BreadcrumbList'
                    ),
                    'itemListElement' => array(
                        'label' => __( 'Éléments', 'boss-seo' ),
                        'type' => 'array',
                        'items' => array(
                            'type' => 'object',
                            'properties' => array(
                                '@type' => array(
                                    'type' => 'hidden',
                                    'default' => 'ListItem'
                                ),
                                'position' => array(
                                    'label' => __( 'Position', 'boss-seo' ),
                                    'type' => 'number',
                                    'required' => true,
                                    'default' => 1
                                ),
                                'name' => array(
                                    'label' => __( 'Nom', 'boss-seo' ),
                                    'type' => 'text',
                                    'required' => true,
                                    'default' => ''
                                ),
                                'item' => array(
                                    'label' => __( 'URL', 'boss-seo' ),
                                    'type' => 'url',
                                    'required' => true,
                                    'default' => ''
                                )
                            )
                        ),
                        'required' => true,
                        'default' => array()
                    )
                )
            )
        );
    }

    /**
     * Récupère les propriétés d'un type de schéma.
     *
     * @since    1.2.0
     * @param    string    $type    Le type de schéma.
     * @return   array              Les propriétés du type de schéma.
     */
    public function get_schema_properties( $type ) {
        if ( isset( $this->schema_types[ $type ] ) ) {
            return $this->schema_types[ $type ]['properties'];
        }

        return array();
    }

    /**
     * Crée un nouveau schéma.
     *
     * @since    1.2.0
     * @param    array    $schema_data    Les données du schéma.
     * @return   int|WP_Error             L'ID du schéma créé ou une erreur.
     */
    public function create_schema( $schema_data ) {
        // Valider les données
        if ( empty( $schema_data['title'] ) || empty( $schema_data['type'] ) ) {
            return new WP_Error( 'invalid_schema', __( 'Le titre et le type sont obligatoires.', 'boss-seo' ) );
        }

        // Créer le post
        $post_id = wp_insert_post( array(
            'post_title' => sanitize_text_field( $schema_data['title'] ),
            'post_type' => 'boss_schema',
            'post_status' => 'publish',
        ) );

        if ( is_wp_error( $post_id ) ) {
            return $post_id;
        }

        // Enregistrer les métadonnées
        update_post_meta( $post_id, '_schema_type', sanitize_text_field( $schema_data['type'] ) );
        update_post_meta( $post_id, '_schema_properties', $this->sanitize_schema_properties( $schema_data['properties'] ) );
        update_post_meta( $post_id, '_schema_active', isset( $schema_data['active'] ) ? (bool) $schema_data['active'] : true );

        return $post_id;
    }

    /**
     * Met à jour un schéma existant.
     *
     * @since    1.2.0
     * @param    int      $schema_id      L'ID du schéma.
     * @param    array    $schema_data    Les données du schéma.
     * @return   bool|WP_Error            True si la mise à jour a réussi, une erreur sinon.
     */
    public function update_schema( $schema_id, $schema_data ) {
        // Vérifier que le schéma existe
        $schema = get_post( $schema_id );

        if ( ! $schema || $schema->post_type !== 'boss_schema' ) {
            return new WP_Error( 'schema_not_found', __( 'Schéma non trouvé.', 'boss-seo' ) );
        }

        // Mettre à jour le titre si nécessaire
        if ( ! empty( $schema_data['title'] ) ) {
            wp_update_post( array(
                'ID' => $schema_id,
                'post_title' => sanitize_text_field( $schema_data['title'] ),
            ) );
        }

        // Mettre à jour les métadonnées
        if ( isset( $schema_data['type'] ) ) {
            update_post_meta( $schema_id, '_schema_type', sanitize_text_field( $schema_data['type'] ) );
        }

        if ( isset( $schema_data['properties'] ) ) {
            update_post_meta( $schema_id, '_schema_properties', $this->sanitize_schema_properties( $schema_data['properties'] ) );
        }

        if ( isset( $schema_data['active'] ) ) {
            update_post_meta( $schema_id, '_schema_active', (bool) $schema_data['active'] );
        }

        return true;
    }

    /**
     * Supprime un schéma.
     *
     * @since    1.2.0
     * @param    int       $schema_id    L'ID du schéma.
     * @return   bool|WP_Error           True si la suppression a réussi, une erreur sinon.
     */
    public function delete_schema( $schema_id ) {
        // Vérifier que le schéma existe
        $schema = get_post( $schema_id );

        if ( ! $schema || $schema->post_type !== 'boss_schema' ) {
            return new WP_Error( 'schema_not_found', __( 'Schéma non trouvé.', 'boss-seo' ) );
        }

        // Supprimer le schéma
        $result = wp_delete_post( $schema_id, true );

        if ( ! $result ) {
            return new WP_Error( 'delete_failed', __( 'Échec de la suppression du schéma.', 'boss-seo' ) );
        }

        return true;
    }

    /**
     * Récupère un schéma.
     *
     * @since    1.2.0
     * @param    int       $schema_id    L'ID du schéma.
     * @return   array|WP_Error          Les données du schéma ou une erreur.
     */
    public function get_schema( $schema_id ) {
        // Vérifier que le schéma existe
        $schema = get_post( $schema_id );

        if ( ! $schema || $schema->post_type !== 'boss_schema' ) {
            return new WP_Error( 'schema_not_found', __( 'Schéma non trouvé.', 'boss-seo' ) );
        }

        // Récupérer les métadonnées
        $type = get_post_meta( $schema_id, '_schema_type', true );
        $properties = get_post_meta( $schema_id, '_schema_properties', true );
        $active = get_post_meta( $schema_id, '_schema_active', true );

        return array(
            'id' => $schema_id,
            'title' => $schema->post_title,
            'type' => $type,
            'properties' => $properties,
            'active' => (bool) $active,
            'date_created' => $schema->post_date,
            'date_modified' => $schema->post_modified
        );
    }

    /**
     * Récupère tous les schémas.
     *
     * @since    1.2.0
     * @param    array    $args    Arguments de requête.
     * @return   array             Les schémas.
     */
    public function get_schemas( $args = array() ) {
        $defaults = array(
            'type' => '',
            'active' => null,
            'orderby' => 'date',
            'order' => 'DESC',
            'limit' => -1,
            'offset' => 0,
        );

        $args = wp_parse_args( $args, $defaults );

        // Construire les arguments de la requête
        $query_args = array(
            'post_type' => 'boss_schema',
            'post_status' => 'publish',
            'posts_per_page' => $args['limit'],
            'offset' => $args['offset'],
            'orderby' => $args['orderby'],
            'order' => $args['order'],
            'meta_query' => array()
        );

        // Filtrer par type
        if ( ! empty( $args['type'] ) ) {
            $query_args['meta_query'][] = array(
                'key' => '_schema_type',
                'value' => $args['type'],
                'compare' => '='
            );
        }

        // Filtrer par statut actif
        if ( $args['active'] !== null ) {
            $query_args['meta_query'][] = array(
                'key' => '_schema_active',
                'value' => $args['active'] ? '1' : '0',
                'compare' => '='
            );
        }

        // Exécuter la requête
        $query = new WP_Query( $query_args );
        $schemas = array();

        foreach ( $query->posts as $post ) {
            $schemas[] = $this->get_schema( $post->ID );
        }

        return $schemas;
    }

    /**
     * Récupère les schémas applicables pour un post.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID du post.
     * @return   array                 Les schémas applicables.
     */
    public function get_schemas_for_post( $post_id ) {
        $post = get_post( $post_id );

        if ( ! $post ) {
            return array();
        }

        // Récupérer tous les schémas actifs
        $schemas = $this->get_schemas( array( 'active' => true ) );
        $applicable_schemas = array();

        foreach ( $schemas as $schema ) {
            // Vérifier si le schéma est applicable selon les règles
            if ( $this->is_schema_applicable( $schema['id'], $post ) ) {
                $applicable_schemas[] = $this->generate_schema_json( $schema['id'], $post );
            }
        }

        return $applicable_schemas;
    }

    /**
     * Vérifie si un schéma est applicable à un post selon les règles.
     *
     * @since    1.2.0
     * @param    int       $schema_id    L'ID du schéma.
     * @param    WP_Post   $post         Le post.
     * @return   bool                    True si le schéma est applicable, false sinon.
     */
    public function is_schema_applicable( $schema_id, $post ) {
        // Cette méthode sera implémentée par la classe Boss_Structured_Schemas_Rules
        // Pour l'instant, retournons true pour tous les schémas
        return true;
    }

    /**
     * Génère le JSON-LD pour un schéma.
     *
     * @since    1.2.0
     * @param    int       $schema_id    L'ID du schéma.
     * @param    WP_Post   $post         Le post.
     * @return   array                   Le schéma au format JSON-LD.
     */
    public function generate_schema_json( $schema_id, $post ) {
        $schema = $this->get_schema( $schema_id );

        if ( is_wp_error( $schema ) ) {
            return array();
        }

        $json = array(
            '@context' => 'https://schema.org',
            '@type' => $schema['type']
        );

        // Ajouter les propriétés
        if ( ! empty( $schema['properties'] ) && is_array( $schema['properties'] ) ) {
            foreach ( $schema['properties'] as $key => $value ) {
                // Remplacer les variables dynamiques
                $value = $this->replace_dynamic_variables( $value, $post );

                if ( $value !== null && $value !== '' ) {
                    $json[ $key ] = $value;
                }
            }
        }

        return $json;
    }

    /**
     * Remplace les variables dynamiques dans une valeur.
     *
     * @since    1.2.0
     * @param    mixed     $value    La valeur.
     * @param    WP_Post   $post     Le post.
     * @return   mixed               La valeur avec les variables remplacées.
     */
    private function replace_dynamic_variables( $value, $post ) {
        if ( is_string( $value ) ) {
            // Remplacer les variables de base
            $value = str_replace( '%post_title%', $post->post_title, $value );
            $value = str_replace( '%post_content%', wp_strip_all_tags( $post->post_content ), $value );
            $value = str_replace( '%post_excerpt%', get_the_excerpt( $post ), $value );
            $value = str_replace( '%post_date%', get_the_date( 'c', $post ), $value );
            $value = str_replace( '%post_modified%', get_the_modified_date( 'c', $post ), $value );
            $value = str_replace( '%permalink%', get_permalink( $post ), $value );

            // Remplacer les métadonnées
            if ( strpos( $value, '%meta_' ) !== false ) {
                preg_match_all( '/%meta_(.*?)%/', $value, $matches );

                foreach ( $matches[1] as $meta_key ) {
                    $meta_value = get_post_meta( $post->ID, $meta_key, true );
                    $value = str_replace( "%meta_{$meta_key}%", $meta_value, $value );
                }
            }

            // Remplacer les taxonomies
            if ( strpos( $value, '%tax_' ) !== false ) {
                preg_match_all( '/%tax_(.*?)%/', $value, $matches );

                foreach ( $matches[1] as $tax_name ) {
                    $terms = get_the_terms( $post->ID, $tax_name );
                    $term_names = array();

                    if ( $terms && ! is_wp_error( $terms ) ) {
                        foreach ( $terms as $term ) {
                            $term_names[] = $term->name;
                        }
                    }

                    $value = str_replace( "%tax_{$tax_name}%", implode( ', ', $term_names ), $value );
                }
            }
        } elseif ( is_array( $value ) ) {
            foreach ( $value as $k => $v ) {
                $value[ $k ] = $this->replace_dynamic_variables( $v, $post );
            }
        }

        return $value;
    }

    /**
     * Sanitize les propriétés d'un schéma.
     *
     * @since    1.2.0
     * @param    array    $properties    Les propriétés du schéma.
     * @return   array                   Les propriétés sanitizées.
     */
    private function sanitize_schema_properties( $properties ) {
        if ( ! is_array( $properties ) ) {
            return array();
        }

        $sanitized = array();

        foreach ( $properties as $key => $value ) {
            $sanitized_key = sanitize_text_field( $key );

            if ( is_array( $value ) ) {
                $sanitized[ $sanitized_key ] = $this->sanitize_schema_properties( $value );
            } else {
                $sanitized[ $sanitized_key ] = sanitize_text_field( $value );
            }
        }

        return $sanitized;
    }
}
