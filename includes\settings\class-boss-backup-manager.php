<?php
/**
 * Classe pour gérer les sauvegardes du plugin Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.0.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/settings
 */

/**
 * Classe pour gérer les sauvegardes du plugin Boss SEO.
 *
 * Cette classe gère les sauvegardes des paramètres du plugin, la restauration
 * des sauvegardes, et la gestion des sauvegardes automatiques.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/settings
 * <AUTHOR> SEO Team
 */
class Boss_Backup_Manager {

    /**
     * Le nom du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_seo_backup_';

    /**
     * Le répertoire de sauvegarde.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $backup_dir    Le répertoire de sauvegarde.
     */
    protected $backup_dir;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        
        // Définit le répertoire de sauvegarde
        $upload_dir = wp_upload_dir();
        $this->backup_dir = trailingslashit( $upload_dir['basedir'] ) . 'boss-seo-backups';
        
        // Crée le répertoire de sauvegarde s'il n'existe pas
        if ( ! file_exists( $this->backup_dir ) ) {
            wp_mkdir_p( $this->backup_dir );
            
            // Crée un fichier .htaccess pour protéger le répertoire
            $htaccess_file = trailingslashit( $this->backup_dir ) . '.htaccess';
            if ( ! file_exists( $htaccess_file ) ) {
                $htaccess_content = "# Deny access to all files\n";
                $htaccess_content .= "<Files \"*\">\n";
                $htaccess_content .= "    Order Allow,Deny\n";
                $htaccess_content .= "    Deny from all\n";
                $htaccess_content .= "</Files>\n";
                
                file_put_contents( $htaccess_file, $htaccess_content );
            }
            
            // Crée un fichier index.php vide pour éviter le listage des répertoires
            $index_file = trailingslashit( $this->backup_dir ) . 'index.php';
            if ( ! file_exists( $index_file ) ) {
                file_put_contents( $index_file, '<?php // Silence is golden.' );
            }
        }
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.0.0
     */
    public function register_hooks() {
        // Hooks pour les sauvegardes automatiques
        $settings = $this->get_settings();
        
        if ( $settings['backup']['autoBackup'] ) {
            // Planifie la sauvegarde automatique
            if ( ! wp_next_scheduled( 'boss_seo_auto_backup' ) ) {
                wp_schedule_event( time(), $settings['backup']['backupFrequency'], 'boss_seo_auto_backup' );
            }
            
            // Hook pour la sauvegarde automatique
            add_action( 'boss_seo_auto_backup', array( $this, 'auto_backup' ) );
        } else {
            // Supprime la planification si la sauvegarde automatique est désactivée
            $timestamp = wp_next_scheduled( 'boss_seo_auto_backup' );
            if ( $timestamp ) {
                wp_unschedule_event( $timestamp, 'boss_seo_auto_backup' );
            }
        }
        
        // Hook pour la désactivation du plugin
        register_deactivation_hook( plugin_basename( dirname( dirname( dirname( __FILE__ ) ) ) ) . '/boss-seo.php', array( $this, 'deactivation' ) );
    }

    /**
     * Récupère les paramètres de sauvegarde.
     *
     * @since    1.0.0
     * @return   array    Les paramètres de sauvegarde.
     */
    public function get_settings() {
        $default_settings = $this->get_default_settings();
        $settings = get_option( $this->option_prefix . 'settings', $default_settings );
        
        return wp_parse_args( $settings, $default_settings );
    }

    /**
     * Récupère les paramètres par défaut.
     *
     * @since    1.0.0
     * @return   array    Les paramètres par défaut.
     */
    public function get_default_settings() {
        return array(
            'backup' => array(
                'autoBackup' => true,
                'backupFrequency' => 'weekly',
                'maxBackups' => 5,
                'includeSettings' => true,
                'includeData' => true
            )
        );
    }

    /**
     * Enregistre les paramètres de sauvegarde.
     *
     * @since    1.0.0
     * @param    array    $settings    Les paramètres à enregistrer.
     * @return   bool                  True si les paramètres ont été enregistrés, false sinon.
     */
    public function save_settings( $settings ) {
        // Récupère les anciens paramètres
        $old_settings = $this->get_settings();
        
        // Sanitize les paramètres
        $sanitized_settings = $this->sanitize_settings( $settings );
        
        // Enregistre les paramètres
        $result = update_option( $this->option_prefix . 'settings', $sanitized_settings );
        
        // Vérifie si la sauvegarde automatique a été activée ou désactivée
        if ( $old_settings['backup']['autoBackup'] !== $sanitized_settings['backup']['autoBackup'] ) {
            if ( $sanitized_settings['backup']['autoBackup'] ) {
                // Planifie la sauvegarde automatique
                if ( ! wp_next_scheduled( 'boss_seo_auto_backup' ) ) {
                    wp_schedule_event( time(), $sanitized_settings['backup']['backupFrequency'], 'boss_seo_auto_backup' );
                }
            } else {
                // Supprime la planification
                $timestamp = wp_next_scheduled( 'boss_seo_auto_backup' );
                if ( $timestamp ) {
                    wp_unschedule_event( $timestamp, 'boss_seo_auto_backup' );
                }
            }
        }
        
        // Vérifie si la fréquence de sauvegarde a été modifiée
        if ( $old_settings['backup']['backupFrequency'] !== $sanitized_settings['backup']['backupFrequency'] && $sanitized_settings['backup']['autoBackup'] ) {
            // Supprime l'ancienne planification
            $timestamp = wp_next_scheduled( 'boss_seo_auto_backup' );
            if ( $timestamp ) {
                wp_unschedule_event( $timestamp, 'boss_seo_auto_backup' );
            }
            
            // Planifie la nouvelle sauvegarde automatique
            wp_schedule_event( time(), $sanitized_settings['backup']['backupFrequency'], 'boss_seo_auto_backup' );
        }
        
        return $result;
    }

    /**
     * Sanitize les paramètres de sauvegarde.
     *
     * @since    1.0.0
     * @param    array    $settings    Les paramètres à sanitize.
     * @return   array                 Les paramètres sanitized.
     */
    public function sanitize_settings( $settings ) {
        $sanitized = array();
        
        // Sanitize les paramètres de sauvegarde
        if ( isset( $settings['backup'] ) ) {
            $sanitized['backup'] = array(
                'autoBackup' => isset( $settings['backup']['autoBackup'] ) ? (bool) $settings['backup']['autoBackup'] : true,
                'backupFrequency' => isset( $settings['backup']['backupFrequency'] ) ? sanitize_text_field( $settings['backup']['backupFrequency'] ) : 'weekly',
                'maxBackups' => isset( $settings['backup']['maxBackups'] ) ? absint( $settings['backup']['maxBackups'] ) : 5,
                'includeSettings' => isset( $settings['backup']['includeSettings'] ) ? (bool) $settings['backup']['includeSettings'] : true,
                'includeData' => isset( $settings['backup']['includeData'] ) ? (bool) $settings['backup']['includeData'] : true
            );
            
            // Vérifie que la fréquence de sauvegarde est valide
            $valid_frequencies = array( 'daily', 'weekly', 'monthly' );
            if ( ! in_array( $sanitized['backup']['backupFrequency'], $valid_frequencies ) ) {
                $sanitized['backup']['backupFrequency'] = 'weekly';
            }
        }
        
        return $sanitized;
    }

    /**
     * Crée une sauvegarde des paramètres du plugin.
     *
     * @since    1.0.0
     * @param    string    $name       Le nom de la sauvegarde (optionnel).
     * @param    bool      $include_settings    Si les paramètres doivent être inclus.
     * @param    bool      $include_data        Si les données doivent être incluses.
     * @return   array|WP_Error                 Les informations sur la sauvegarde ou une erreur.
     */
    public function create_backup( $name = '', $include_settings = true, $include_data = true ) {
        // Génère un nom de sauvegarde si aucun n'est fourni
        if ( empty( $name ) ) {
            $name = 'backup-' . date( 'Y-m-d-H-i-s' );
        }
        
        // Sanitize le nom de la sauvegarde
        $name = sanitize_file_name( $name );
        
        // Vérifie si le répertoire de sauvegarde existe
        if ( ! file_exists( $this->backup_dir ) ) {
            wp_mkdir_p( $this->backup_dir );
        }
        
        // Vérifie si le répertoire de sauvegarde est accessible en écriture
        if ( ! is_writable( $this->backup_dir ) ) {
            return new WP_Error( 'backup_dir_not_writable', __( 'Le répertoire de sauvegarde n\'est pas accessible en écriture.', 'boss-seo' ) );
        }
        
        // Récupère les données à sauvegarder
        $data = array(
            'version' => $this->version,
            'date' => current_time( 'mysql' ),
            'name' => $name,
            'settings' => array(),
            'data' => array()
        );
        
        // Inclut les paramètres si demandé
        if ( $include_settings ) {
            $data['settings'] = array(
                'general' => get_option( 'boss_seo_general_settings', array() ),
                'advanced' => get_option( 'boss_seo_advanced_settings', array() ),
                'backup' => get_option( 'boss_seo_backup_settings', array() ),
                'user_preferences' => get_option( 'boss_seo_user_preferences', array() )
            );
        }
        
        // Inclut les données si demandé
        if ( $include_data ) {
            // Récupère toutes les options commençant par 'boss_seo_'
            global $wpdb;
            $options = $wpdb->get_results( "SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE 'boss_seo_%' AND option_name NOT IN ('boss_seo_general_settings', 'boss_seo_advanced_settings', 'boss_seo_backup_settings', 'boss_seo_user_preferences')" );
            
            foreach ( $options as $option ) {
                $data['data'][ $option->option_name ] = maybe_unserialize( $option->option_value );
            }
        }
        
        // Crée le fichier de sauvegarde
        $backup_file = trailingslashit( $this->backup_dir ) . $name . '.json';
        $result = file_put_contents( $backup_file, wp_json_encode( $data ) );
        
        if ( false === $result ) {
            return new WP_Error( 'backup_file_not_writable', __( 'Impossible d\'écrire le fichier de sauvegarde.', 'boss-seo' ) );
        }
        
        // Enregistre la sauvegarde dans la liste des sauvegardes
        $backups = $this->get_backups();
        $backups[] = array(
            'name' => $name,
            'file' => $backup_file,
            'date' => current_time( 'mysql' ),
            'size' => filesize( $backup_file ),
            'include_settings' => $include_settings,
            'include_data' => $include_data
        );
        
        // Trie les sauvegardes par date (la plus récente en premier)
        usort( $backups, function( $a, $b ) {
            return strtotime( $b['date'] ) - strtotime( $a['date'] );
        } );
        
        // Limite le nombre de sauvegardes
        $settings = $this->get_settings();
        $max_backups = $settings['backup']['maxBackups'];
        
        if ( count( $backups ) > $max_backups ) {
            $backups_to_delete = array_slice( $backups, $max_backups );
            
            foreach ( $backups_to_delete as $backup ) {
                if ( file_exists( $backup['file'] ) ) {
                    unlink( $backup['file'] );
                }
            }
            
            $backups = array_slice( $backups, 0, $max_backups );
        }
        
        // Enregistre la liste des sauvegardes
        update_option( $this->option_prefix . 'list', $backups );
        
        return array(
            'name' => $name,
            'file' => $backup_file,
            'date' => current_time( 'mysql' ),
            'size' => filesize( $backup_file ),
            'include_settings' => $include_settings,
            'include_data' => $include_data
        );
    }

    /**
     * Restaure une sauvegarde.
     *
     * @since    1.0.0
     * @param    string    $backup_name    Le nom de la sauvegarde à restaurer.
     * @return   bool|WP_Error             True si la sauvegarde a été restaurée, une erreur sinon.
     */
    public function restore_backup( $backup_name ) {
        // Récupère la liste des sauvegardes
        $backups = $this->get_backups();
        
        // Recherche la sauvegarde
        $backup = null;
        foreach ( $backups as $b ) {
            if ( $b['name'] === $backup_name ) {
                $backup = $b;
                break;
            }
        }
        
        if ( null === $backup ) {
            return new WP_Error( 'backup_not_found', __( 'La sauvegarde demandée n\'existe pas.', 'boss-seo' ) );
        }
        
        // Vérifie si le fichier de sauvegarde existe
        if ( ! file_exists( $backup['file'] ) ) {
            return new WP_Error( 'backup_file_not_found', __( 'Le fichier de sauvegarde n\'existe pas.', 'boss-seo' ) );
        }
        
        // Lit le fichier de sauvegarde
        $backup_data = file_get_contents( $backup['file'] );
        if ( false === $backup_data ) {
            return new WP_Error( 'backup_file_not_readable', __( 'Impossible de lire le fichier de sauvegarde.', 'boss-seo' ) );
        }
        
        // Décode les données
        $data = json_decode( $backup_data, true );
        if ( null === $data ) {
            return new WP_Error( 'backup_file_invalid', __( 'Le fichier de sauvegarde est invalide.', 'boss-seo' ) );
        }
        
        // Restaure les paramètres
        if ( isset( $data['settings'] ) && is_array( $data['settings'] ) ) {
            foreach ( $data['settings'] as $option_name => $option_value ) {
                update_option( 'boss_seo_' . $option_name . '_settings', $option_value );
            }
        }
        
        // Restaure les données
        if ( isset( $data['data'] ) && is_array( $data['data'] ) ) {
            foreach ( $data['data'] as $option_name => $option_value ) {
                update_option( $option_name, $option_value );
            }
        }
        
        return true;
    }

    /**
     * Supprime une sauvegarde.
     *
     * @since    1.0.0
     * @param    string    $backup_name    Le nom de la sauvegarde à supprimer.
     * @return   bool|WP_Error             True si la sauvegarde a été supprimée, une erreur sinon.
     */
    public function delete_backup( $backup_name ) {
        // Récupère la liste des sauvegardes
        $backups = $this->get_backups();
        
        // Recherche la sauvegarde
        $backup_index = -1;
        foreach ( $backups as $index => $backup ) {
            if ( $backup['name'] === $backup_name ) {
                $backup_index = $index;
                break;
            }
        }
        
        if ( -1 === $backup_index ) {
            return new WP_Error( 'backup_not_found', __( 'La sauvegarde demandée n\'existe pas.', 'boss-seo' ) );
        }
        
        // Supprime le fichier de sauvegarde
        if ( file_exists( $backups[ $backup_index ]['file'] ) ) {
            unlink( $backups[ $backup_index ]['file'] );
        }
        
        // Supprime la sauvegarde de la liste
        array_splice( $backups, $backup_index, 1 );
        
        // Enregistre la liste des sauvegardes
        update_option( $this->option_prefix . 'list', $backups );
        
        return true;
    }

    /**
     * Récupère la liste des sauvegardes.
     *
     * @since    1.0.0
     * @return   array    La liste des sauvegardes.
     */
    public function get_backups() {
        $backups = get_option( $this->option_prefix . 'list', array() );
        
        // Vérifie que les fichiers de sauvegarde existent toujours
        foreach ( $backups as $index => $backup ) {
            if ( ! file_exists( $backup['file'] ) ) {
                unset( $backups[ $index ] );
            }
        }
        
        // Réindexe le tableau
        $backups = array_values( $backups );
        
        return $backups;
    }

    /**
     * Crée une sauvegarde automatique.
     *
     * @since    1.0.0
     */
    public function auto_backup() {
        $settings = $this->get_settings();
        
        // Crée une sauvegarde automatique
        $this->create_backup(
            'auto-backup-' . date( 'Y-m-d-H-i-s' ),
            $settings['backup']['includeSettings'],
            $settings['backup']['includeData']
        );
    }

    /**
     * Actions à effectuer lors de la désactivation du plugin.
     *
     * @since    1.0.0
     */
    public function deactivation() {
        // Supprime la planification de sauvegarde automatique
        $timestamp = wp_next_scheduled( 'boss_seo_auto_backup' );
        if ( $timestamp ) {
            wp_unschedule_event( $timestamp, 'boss_seo_auto_backup' );
        }
    }
}
