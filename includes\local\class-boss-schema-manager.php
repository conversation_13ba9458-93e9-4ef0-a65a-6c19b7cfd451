<?php
/**
 * Classe pour gérer les schémas structurés.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour gérer les schémas structurés.
 *
 * Cette classe gère les schémas structurés pour le référencement local.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Schema_Manager {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_schema_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_schema_types', array( $this, 'ajax_get_schema_types' ) );
        add_action( 'wp_ajax_boss_seo_get_schema', array( $this, 'ajax_get_schema' ) );
        add_action( 'wp_ajax_boss_seo_save_schema', array( $this, 'ajax_save_schema' ) );
        add_action( 'wp_ajax_boss_seo_delete_schema', array( $this, 'ajax_delete_schema' ) );
        add_action( 'wp_ajax_boss_seo_test_schema', array( $this, 'ajax_test_schema' ) );

        // Ajouter les schémas structurés
        add_action( 'wp_head', array( $this, 'insert_schemas' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/schemas/types',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_schema_types' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/schemas',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_schemas' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'create_schema' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/schemas/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_schema' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'update_schema' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_schema' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/schemas/(?P<id>\d+)/test',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'test_schema' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les types de schémas disponibles.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_schema_types( $request ) {
        $schema_types = $this->get_available_schema_types();
        return rest_ensure_response( $schema_types );
    }

    /**
     * Récupère la liste des schémas.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_schemas( $request ) {
        $schemas = $this->get_all_schemas();
        return rest_ensure_response( $schemas );
    }

    /**
     * Récupère un schéma spécifique.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_schema( $request ) {
        $schema_id = $request['id'];
        $schema = $this->get_schema_by_id( $schema_id );
        
        if ( ! $schema ) {
            return new WP_Error( 'schema_not_found', __( 'Schéma non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }
        
        return rest_ensure_response( $schema );
    }

    /**
     * Crée un nouveau schéma.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function create_schema( $request ) {
        $params = $request->get_params();
        
        if ( ! isset( $params['schema'] ) || ! is_array( $params['schema'] ) ) {
            return new WP_Error( 'missing_schema', __( 'Les données du schéma sont requises.', 'boss-seo' ), array( 'status' => 400 ) );
        }
        
        $schema = $this->save_schema( $params['schema'] );
        
        if ( is_wp_error( $schema ) ) {
            return $schema;
        }
        
        return rest_ensure_response( array(
            'message' => __( 'Schéma créé avec succès.', 'boss-seo' ),
            'schema'  => $schema,
        ) );
    }

    /**
     * Met à jour un schéma existant.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_schema( $request ) {
        $schema_id = $request['id'];
        $params = $request->get_params();
        
        if ( ! isset( $params['schema'] ) || ! is_array( $params['schema'] ) ) {
            return new WP_Error( 'missing_schema', __( 'Les données du schéma sont requises.', 'boss-seo' ), array( 'status' => 400 ) );
        }
        
        $existing_schema = $this->get_schema_by_id( $schema_id );
        
        if ( ! $existing_schema ) {
            return new WP_Error( 'schema_not_found', __( 'Schéma non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }
        
        $params['schema']['id'] = $schema_id;
        $schema = $this->save_schema( $params['schema'] );
        
        if ( is_wp_error( $schema ) ) {
            return $schema;
        }
        
        return rest_ensure_response( array(
            'message' => __( 'Schéma mis à jour avec succès.', 'boss-seo' ),
            'schema'  => $schema,
        ) );
    }

    /**
     * Supprime un schéma.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_schema( $request ) {
        $schema_id = $request['id'];
        $result = $this->delete_schema_by_id( $schema_id );
        
        if ( is_wp_error( $result ) ) {
            return $result;
        }
        
        return rest_ensure_response( array(
            'message' => __( 'Schéma supprimé avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Teste un schéma.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function test_schema( $request ) {
        $schema_id = $request['id'];
        $schema = $this->get_schema_by_id( $schema_id );
        
        if ( ! $schema ) {
            return new WP_Error( 'schema_not_found', __( 'Schéma non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }
        
        $result = $this->test_schema_with_google( $schema );
        
        if ( is_wp_error( $result ) ) {
            return $result;
        }
        
        return rest_ensure_response( array(
            'message' => __( 'Schéma testé avec succès.', 'boss-seo' ),
            'result'  => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les types de schémas.
     *
     * @since    1.2.0
     */
    public function ajax_get_schema_types() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }
        
        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }
        
        // Récupérer les types de schémas
        $schema_types = $this->get_available_schema_types();
        
        wp_send_json_success( array(
            'message'      => __( 'Types de schémas récupérés avec succès.', 'boss-seo' ),
            'schema_types' => $schema_types,
        ) );
    }

    /**
     * Récupère les types de schémas disponibles.
     *
     * @since    1.2.0
     * @return   array    Les types de schémas disponibles.
     */
    private function get_available_schema_types() {
        return array(
            'LocalBusiness' => array(
                'label' => __( 'Entreprise locale', 'boss-seo' ),
                'description' => __( 'Une entreprise physique ou un lieu d\'activité.', 'boss-seo' ),
                'properties' => array(
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                    ),
                    'description' => array(
                        'label' => __( 'Description', 'boss-seo' ),
                        'type' => 'textarea',
                        'required' => false,
                    ),
                    'url' => array(
                        'label' => __( 'URL', 'boss-seo' ),
                        'type' => 'url',
                        'required' => false,
                    ),
                    'telephone' => array(
                        'label' => __( 'Téléphone', 'boss-seo' ),
                        'type' => 'text',
                        'required' => false,
                    ),
                    'address' => array(
                        'label' => __( 'Adresse', 'boss-seo' ),
                        'type' => 'address',
                        'required' => true,
                    ),
                    'openingHours' => array(
                        'label' => __( 'Heures d\'ouverture', 'boss-seo' ),
                        'type' => 'opening_hours',
                        'required' => false,
                    ),
                    'priceRange' => array(
                        'label' => __( 'Gamme de prix', 'boss-seo' ),
                        'type' => 'text',
                        'required' => false,
                    ),
                ),
            ),
            'Organization' => array(
                'label' => __( 'Organisation', 'boss-seo' ),
                'description' => __( 'Une organisation comme une école, une ONG, une entreprise, etc.', 'boss-seo' ),
                'properties' => array(
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                    ),
                    'description' => array(
                        'label' => __( 'Description', 'boss-seo' ),
                        'type' => 'textarea',
                        'required' => false,
                    ),
                    'url' => array(
                        'label' => __( 'URL', 'boss-seo' ),
                        'type' => 'url',
                        'required' => false,
                    ),
                    'logo' => array(
                        'label' => __( 'Logo', 'boss-seo' ),
                        'type' => 'image',
                        'required' => false,
                    ),
                    'contactPoint' => array(
                        'label' => __( 'Point de contact', 'boss-seo' ),
                        'type' => 'contact_point',
                        'required' => false,
                    ),
                    'sameAs' => array(
                        'label' => __( 'Profils sociaux', 'boss-seo' ),
                        'type' => 'social_profiles',
                        'required' => false,
                    ),
                ),
            ),
            'Person' => array(
                'label' => __( 'Personne', 'boss-seo' ),
                'description' => __( 'Une personne (vivante, décédée, fictive).', 'boss-seo' ),
                'properties' => array(
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                    ),
                    'description' => array(
                        'label' => __( 'Description', 'boss-seo' ),
                        'type' => 'textarea',
                        'required' => false,
                    ),
                    'image' => array(
                        'label' => __( 'Image', 'boss-seo' ),
                        'type' => 'image',
                        'required' => false,
                    ),
                    'jobTitle' => array(
                        'label' => __( 'Titre du poste', 'boss-seo' ),
                        'type' => 'text',
                        'required' => false,
                    ),
                    'worksFor' => array(
                        'label' => __( 'Travaille pour', 'boss-seo' ),
                        'type' => 'text',
                        'required' => false,
                    ),
                    'sameAs' => array(
                        'label' => __( 'Profils sociaux', 'boss-seo' ),
                        'type' => 'social_profiles',
                        'required' => false,
                    ),
                ),
            ),
            'Event' => array(
                'label' => __( 'Événement', 'boss-seo' ),
                'description' => __( 'Un événement qui a lieu à un certain moment et à un certain endroit.', 'boss-seo' ),
                'properties' => array(
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                    ),
                    'description' => array(
                        'label' => __( 'Description', 'boss-seo' ),
                        'type' => 'textarea',
                        'required' => false,
                    ),
                    'startDate' => array(
                        'label' => __( 'Date de début', 'boss-seo' ),
                        'type' => 'datetime',
                        'required' => true,
                    ),
                    'endDate' => array(
                        'label' => __( 'Date de fin', 'boss-seo' ),
                        'type' => 'datetime',
                        'required' => false,
                    ),
                    'location' => array(
                        'label' => __( 'Lieu', 'boss-seo' ),
                        'type' => 'place',
                        'required' => true,
                    ),
                    'offers' => array(
                        'label' => __( 'Offres', 'boss-seo' ),
                        'type' => 'offers',
                        'required' => false,
                    ),
                    'performer' => array(
                        'label' => __( 'Artiste', 'boss-seo' ),
                        'type' => 'text',
                        'required' => false,
                    ),
                ),
            ),
            'Product' => array(
                'label' => __( 'Produit', 'boss-seo' ),
                'description' => __( 'Un produit est tout ce qui est fabriqué, vendu ou consommé.', 'boss-seo' ),
                'properties' => array(
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                    ),
                    'description' => array(
                        'label' => __( 'Description', 'boss-seo' ),
                        'type' => 'textarea',
                        'required' => false,
                    ),
                    'image' => array(
                        'label' => __( 'Image', 'boss-seo' ),
                        'type' => 'image',
                        'required' => false,
                    ),
                    'brand' => array(
                        'label' => __( 'Marque', 'boss-seo' ),
                        'type' => 'text',
                        'required' => false,
                    ),
                    'offers' => array(
                        'label' => __( 'Offres', 'boss-seo' ),
                        'type' => 'offers',
                        'required' => false,
                    ),
                    'sku' => array(
                        'label' => __( 'SKU', 'boss-seo' ),
                        'type' => 'text',
                        'required' => false,
                    ),
                ),
            ),
            'Article' => array(
                'label' => __( 'Article', 'boss-seo' ),
                'description' => __( 'Un article, comme un article de presse ou de blog.', 'boss-seo' ),
                'properties' => array(
                    'headline' => array(
                        'label' => __( 'Titre', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                    ),
                    'description' => array(
                        'label' => __( 'Description', 'boss-seo' ),
                        'type' => 'textarea',
                        'required' => false,
                    ),
                    'image' => array(
                        'label' => __( 'Image', 'boss-seo' ),
                        'type' => 'image',
                        'required' => false,
                    ),
                    'author' => array(
                        'label' => __( 'Auteur', 'boss-seo' ),
                        'type' => 'person',
                        'required' => false,
                    ),
                    'datePublished' => array(
                        'label' => __( 'Date de publication', 'boss-seo' ),
                        'type' => 'date',
                        'required' => false,
                    ),
                    'publisher' => array(
                        'label' => __( 'Éditeur', 'boss-seo' ),
                        'type' => 'organization',
                        'required' => false,
                    ),
                ),
            ),
            'FAQPage' => array(
                'label' => __( 'Page FAQ', 'boss-seo' ),
                'description' => __( 'Une page FAQ avec des questions et réponses.', 'boss-seo' ),
                'properties' => array(
                    'mainEntity' => array(
                        'label' => __( 'Questions et réponses', 'boss-seo' ),
                        'type' => 'faq',
                        'required' => true,
                    ),
                ),
            ),
            'HowTo' => array(
                'label' => __( 'Comment faire', 'boss-seo' ),
                'description' => __( 'Instructions étape par étape pour accomplir une tâche.', 'boss-seo' ),
                'properties' => array(
                    'name' => array(
                        'label' => __( 'Nom', 'boss-seo' ),
                        'type' => 'text',
                        'required' => true,
                    ),
                    'description' => array(
                        'label' => __( 'Description', 'boss-seo' ),
                        'type' => 'textarea',
                        'required' => false,
                    ),
                    'image' => array(
                        'label' => __( 'Image', 'boss-seo' ),
                        'type' => 'image',
                        'required' => false,
                    ),
                    'step' => array(
                        'label' => __( 'Étapes', 'boss-seo' ),
                        'type' => 'steps',
                        'required' => true,
                    ),
                ),
            ),
        );
    }
}
