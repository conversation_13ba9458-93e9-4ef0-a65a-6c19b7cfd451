import { __ } from '@wordpress/i18n';
import { Card, CardBody, Card<PERSON>ooter, Button, Dashicon } from '@wordpress/components';

const ModuleCard = ({ title, description, icon, color, actionId, onNavigate }) => {
  // Fonction pour gérer le clic sur le bouton
  const handleClick = () => {
    if (onNavigate && actionId) {
      onNavigate(actionId);
    }
  };

  return (
    <Card className="boss-card boss-h-full">
      <CardBody>
        <div className="boss-flex boss-items-start">
          <div className={`boss-bg-${color}/10 boss-p-3 boss-rounded-lg boss-mr-4`}>
            <Dashicon icon={icon} className={`boss-text-${color} boss-text-2xl`} />
          </div>
          <div>
            <h3 className="boss-text-lg boss-font-semibold boss-mb-1">{title}</h3>
            <p className="boss-text-boss-gray boss-text-sm">{description}</p>
          </div>
        </div>
      </CardBody>
      <CardFooter>
        <Button
          isPrimary
          className={`boss-w-full boss-bg-${color} hover:boss-bg-${color}/90`}
          onClick={handleClick}
        >
          {__('Accéder', 'boss-seo')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ModuleCard;
