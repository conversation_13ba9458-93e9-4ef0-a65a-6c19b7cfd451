import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  TabPanel,
  <PERSON><PERSON>,
  Spinner
} from '@wordpress/components';
import { motion } from 'framer-motion';

// Composants SEO Local
import LocalDashboard from '../components/local/LocalDashboard';
import LocationManager from '../components/local/LocationManager';
import BusinessInfo from '../components/local/BusinessInfo';
import LocalPageGenerator from '../components/local/LocalPageGenerator';
import LocalSchemaPreview from '../components/local/LocalSchemaPreview';
import LocalRankings from '../components/local/LocalRankings';

// Composants E-commerce
import EcommerceDashboard from '../components/ecommerce/EcommerceDashboard';
import ProductOptimizer from '../components/ecommerce/ProductOptimizer';
import ProductDescriptionGenerator from '../components/ecommerce/ProductDescriptionGenerator';
import ProductSchemaConfig from '../components/ecommerce/ProductSchemaConfig';
import GoogleShoppingPreview from '../components/ecommerce/GoogleShoppingPreview';

// Composant commun
import PerformanceAnalysis from '../components/common/PerformanceAnalysis';

const LocalEcommerce = () => {
  // États
  const [mainTab, setMainTab] = useState('local'); // 'local' ou 'ecommerce'
  const [localTab, setLocalTab] = useState('dashboard');
  const [ecommerceTab, setEcommerceTab] = useState('dashboard');
  const [isLoading, setIsLoading] = useState(false);
  const [hasWooCommerce, setHasWooCommerce] = useState(true); // Simuler la détection de WooCommerce
  
  // Fonction pour vérifier si WooCommerce est installé
  useState(() => {
    // Simuler une vérification asynchrone
    setIsLoading(true);
    setTimeout(() => {
      setHasWooCommerce(true); // Dans un cas réel, cela serait basé sur une vérification
      setIsLoading(false);
    }, 500);
  }, []);
  
  // Rendu du contenu de l'onglet SEO Local
  const renderLocalContent = () => {
    switch (localTab) {
      case 'dashboard':
        return <LocalDashboard />;
      case 'locations':
        return <LocationManager />;
      case 'business-info':
        return <BusinessInfo />;
      case 'page-generator':
        return <LocalPageGenerator />;
      case 'schema-preview':
        return <LocalSchemaPreview />;
      case 'rankings':
        return <LocalRankings />;
      case 'analysis':
        return <PerformanceAnalysis type="local" />;
      default:
        return <LocalDashboard />;
    }
  };
  
  // Rendu du contenu de l'onglet E-commerce
  const renderEcommerceContent = () => {
    if (!hasWooCommerce) {
      return (
        <Card>
          <CardBody>
            <div className="boss-text-center boss-py-8">
              <div className="boss-text-5xl boss-text-boss-gray boss-mb-4">
                <span className="dashicons dashicons-cart"></span>
              </div>
              <h2 className="boss-text-xl boss-font-bold boss-mb-4">
                {__('WooCommerce non détecté', 'boss-seo')}
              </h2>
              <p className="boss-text-boss-gray boss-mb-6">
                {__('Pour utiliser les fonctionnalités SEO E-commerce, vous devez installer et activer WooCommerce.', 'boss-seo')}
              </p>
              <Button
                isPrimary
                href="https://wordpress.org/plugins/woocommerce/"
                target="_blank"
              >
                {__('Installer WooCommerce', 'boss-seo')}
              </Button>
            </div>
          </CardBody>
        </Card>
      );
    }
    
    switch (ecommerceTab) {
      case 'dashboard':
        return <EcommerceDashboard />;
      case 'product-optimizer':
        return <ProductOptimizer />;
      case 'description-generator':
        return <ProductDescriptionGenerator />;
      case 'schema-config':
        return <ProductSchemaConfig />;
      case 'shopping-preview':
        return <GoogleShoppingPreview />;
      case 'analysis':
        return <PerformanceAnalysis type="ecommerce" />;
      default:
        return <EcommerceDashboard />;
    }
  };

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {mainTab === 'local' 
              ? __('SEO Local', 'boss-seo') 
              : __('SEO E-commerce', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {mainTab === 'local'
              ? __('Optimisez votre présence locale et gérez vos emplacements', 'boss-seo')
              : __('Améliorez le référencement de votre boutique en ligne et de vos produits', 'boss-seo')}
          </p>
        </div>

        {/* Onglets principaux */}
        <div className="boss-border-b boss-border-gray-200 boss-mb-6">
          <div className="boss-flex boss-space-x-8">
            <button
              className={`boss-px-1 boss-py-4 boss-border-b-2 boss-font-medium boss-text-sm boss-leading-5 focus:boss-outline-none ${
                mainTab === 'local'
                  ? 'boss-border-boss-primary boss-text-boss-primary'
                  : 'boss-border-transparent boss-text-boss-gray hover:boss-text-boss-dark hover:boss-border-boss-gray'
              }`}
              onClick={() => setMainTab('local')}
            >
              {__('SEO Local', 'boss-seo')}
            </button>
            <button
              className={`boss-px-1 boss-py-4 boss-border-b-2 boss-font-medium boss-text-sm boss-leading-5 focus:boss-outline-none ${
                mainTab === 'ecommerce'
                  ? 'boss-border-boss-primary boss-text-boss-primary'
                  : 'boss-border-transparent boss-text-boss-gray hover:boss-text-boss-dark hover:boss-border-boss-gray'
              }`}
              onClick={() => setMainTab('ecommerce')}
            >
              {__('SEO E-commerce', 'boss-seo')}
            </button>
          </div>
        </div>

        {isLoading ? (
          <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
            <Spinner />
          </div>
        ) : (
          <div>
            {/* Sous-onglets pour SEO Local */}
            {mainTab === 'local' && (
              <div className="boss-mb-6">
                <div className="boss-border-b boss-border-gray-200">
                  <div className="boss-flex boss-overflow-x-auto">
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        localTab === 'dashboard'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setLocalTab('dashboard')}
                    >
                      {__('Tableau de bord', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        localTab === 'locations'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setLocalTab('locations')}
                    >
                      {__('Emplacements', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        localTab === 'business-info'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setLocalTab('business-info')}
                    >
                      {__('Informations entreprise', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        localTab === 'page-generator'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setLocalTab('page-generator')}
                    >
                      {__('Générateur de pages', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        localTab === 'schema-preview'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setLocalTab('schema-preview')}
                    >
                      {__('Schémas', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        localTab === 'rankings'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setLocalTab('rankings')}
                    >
                      {__('Positions', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        localTab === 'analysis'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setLocalTab('analysis')}
                    >
                      {__('Analyse', 'boss-seo')}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Sous-onglets pour SEO E-commerce */}
            {mainTab === 'ecommerce' && hasWooCommerce && (
              <div className="boss-mb-6">
                <div className="boss-border-b boss-border-gray-200">
                  <div className="boss-flex boss-overflow-x-auto">
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        ecommerceTab === 'dashboard'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setEcommerceTab('dashboard')}
                    >
                      {__('Tableau de bord', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        ecommerceTab === 'product-optimizer'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setEcommerceTab('product-optimizer')}
                    >
                      {__('Optimisation produits', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        ecommerceTab === 'description-generator'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setEcommerceTab('description-generator')}
                    >
                      {__('Générateur IA', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        ecommerceTab === 'schema-config'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setEcommerceTab('schema-config')}
                    >
                      {__('Schémas produits', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        ecommerceTab === 'shopping-preview'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setEcommerceTab('shopping-preview')}
                    >
                      {__('Google Shopping', 'boss-seo')}
                    </button>
                    <button
                      className={`boss-px-4 boss-py-2 boss-border-b-2 boss-font-medium boss-whitespace-nowrap ${
                        ecommerceTab === 'analysis'
                          ? 'boss-border-boss-primary boss-text-boss-primary'
                          : 'boss-border-transparent boss-text-boss-gray'
                      }`}
                      onClick={() => setEcommerceTab('analysis')}
                    >
                      {__('Analyse', 'boss-seo')}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Contenu principal */}
            <div>
              {mainTab === 'local' ? renderLocalContent() : renderEcommerceContent()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LocalEcommerce;
