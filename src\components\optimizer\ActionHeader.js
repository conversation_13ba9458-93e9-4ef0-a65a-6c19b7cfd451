import { __ } from '@wordpress/i18n';
import {
  Button,
  Card,
  CardBody,
  Dashicon,
  Dropdown,
  MenuGroup,
  MenuItem,
  SearchControl
} from '@wordpress/components';

/**
 * Composant pour l'en-tête avec actions
 */
const ActionHeader = ({
  selectedItems,
  onBulkOptimize,
  onBulkAnalyze,
  onAddTags,
  onChangeCategory,
  onChangeAuthor,
  onOptimizeAll,
  onAnalyzeAll,
  onClearSelection,
  searchQuery,
  onSearchChange
}) => {
  const hasSelectedItems = selectedItems.length > 0;

  return (
    <Card className="boss-mb-6">
      <CardBody>
        <div className="boss-flex boss-flex-wrap boss-justify-between boss-items-center boss-gap-4">
          {/* Barre de recherche */}
          <div className="boss-flex-1 boss-min-w-[300px]">
            <SearchControl
              value={searchQuery}
              onChange={onSearchChange}
              label={__('Rechercher des contenus', 'boss-seo')}
              placeholder={__('Rechercher par titre ou contenu...', 'boss-seo')}
              className="boss-w-full"
            />
          </div>

          <div className="boss-flex boss-flex-wrap boss-items-center boss-gap-3">
            {/* Actions pour les éléments sélectionnés */}
            {hasSelectedItems ? (
              <>
                <div className="boss-bg-indigo-50 boss-text-indigo-700 boss-px-3 boss-py-1 boss-rounded-full boss-text-sm boss-font-medium boss-flex boss-items-center">
                  <Dashicon icon="yes-alt" className="boss-mr-1" />
                  <span>
                    {selectedItems.length} {selectedItems.length === 1
                      ? __('élément sélectionné', 'boss-seo')
                      : __('éléments sélectionnés', 'boss-seo')
                    }
                  </span>
                </div>

                <Dropdown
                  className="boss-relative"
                  contentClassName="boss-min-w-[200px]"
                  renderToggle={({ isOpen, onToggle }) => (
                    <Button
                      isSecondary
                      onClick={onToggle}
                      aria-expanded={isOpen}
                      className="boss-flex boss-items-center"
                    >
                      <Dashicon icon="performance" className="boss-mr-1" />
                      {__('Actions groupées', 'boss-seo')}
                      <Dashicon icon="arrow-down" className="boss-ml-1" />
                    </Button>
                  )}
                  renderContent={() => (
                    <MenuGroup>
                      <MenuItem
                        onClick={onBulkOptimize}
                        icon="performance"
                      >
                        {__('Optimiser la sélection', 'boss-seo')}
                      </MenuItem>
                      <MenuItem
                        onClick={onBulkAnalyze}
                        icon="update"
                      >
                        {__('Analyser la sélection', 'boss-seo')}
                      </MenuItem>
                      <MenuItem
                        onClick={onAddTags}
                        icon="tag"
                      >
                        {__('Ajouter des balises', 'boss-seo')}
                      </MenuItem>
                      <MenuItem
                        onClick={onChangeCategory}
                        icon="category"
                      >
                        {__('Modifier la catégorie', 'boss-seo')}
                      </MenuItem>
                      <MenuItem
                        onClick={onChangeAuthor}
                        icon="admin-users"
                      >
                        {__('Changer d\'auteur', 'boss-seo')}
                      </MenuItem>
                    </MenuGroup>
                  )}
                />

                <Button
                  isPrimary
                  onClick={onBulkOptimize}
                  className="boss-flex boss-items-center"
                >
                  <Dashicon icon="performance" className="boss-mr-1" />
                  {__('Optimiser en masse', 'boss-seo')}
                </Button>

                <Button
                  isSecondary
                  onClick={onClearSelection}
                  className="boss-flex boss-items-center"
                >
                  <Dashicon icon="dismiss" className="boss-mr-1" />
                  {__('Annuler la sélection', 'boss-seo')}
                </Button>
              </>
            ) : (
              <>
                {/* Actions par défaut quand aucun élément n'est sélectionné */}
                <Button
                  isPrimary
                  className="boss-flex boss-items-center"
                  onClick={onOptimizeAll}
                >
                  <Dashicon icon="performance" className="boss-mr-1" />
                  {__('Optimiser tout', 'boss-seo')}
                </Button>

                <Button
                  isSecondary
                  className="boss-flex boss-items-center"
                  onClick={onAnalyzeAll}
                >
                  <Dashicon icon="update" className="boss-mr-1" />
                  {__('Analyser tout', 'boss-seo')}
                </Button>

                <Dropdown
                  className="boss-relative"
                  contentClassName="boss-min-w-[200px]"
                  renderToggle={({ isOpen, onToggle }) => (
                    <Button
                      isSecondary
                      onClick={onToggle}
                      aria-expanded={isOpen}
                      className="boss-flex boss-items-center"
                    >
                      <Dashicon icon="admin-generic" className="boss-mr-1" />
                      {__('Plus d\'actions', 'boss-seo')}
                      <Dashicon icon="arrow-down" className="boss-ml-1" />
                    </Button>
                  )}
                  renderContent={() => (
                    <MenuGroup>
                      <MenuItem
                        onClick={() => {}}
                        icon="plus"
                      >
                        {__('Ajouter un nouveau contenu', 'boss-seo')}
                      </MenuItem>
                      <MenuItem
                        onClick={() => {}}
                        icon="download"
                      >
                        {__('Exporter les données', 'boss-seo')}
                      </MenuItem>
                      <MenuItem
                        onClick={() => {}}
                        icon="admin-settings"
                      >
                        {__('Paramètres d\'optimisation', 'boss-seo')}
                      </MenuItem>
                    </MenuGroup>
                  )}
                />
              </>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default ActionHeader;
