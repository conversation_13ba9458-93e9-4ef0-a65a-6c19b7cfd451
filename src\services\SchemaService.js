/**
 * Service pour la gestion des schémas structurés
 * 
 * Gère les communications avec l'API pour les fonctionnalités de schémas structurés
 */

import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

class SchemaService {
  /**
   * Récupère tous les schémas
   * 
   * @param {Object} filters - Filtres à appliquer (type, active, etc.)
   * @param {number} page - Numéro de page
   * @param {number} perPage - Nombre d'éléments par page
   * @param {string} orderby - Champ de tri
   * @param {string} order - Ordre de tri (asc, desc)
   * @returns {Promise} Promesse contenant les schémas
   */
  async getSchemas(filters = {}, page = 1, perPage = 20, orderby = 'date', order = 'DESC') {
    try {
      const queryArgs = {
        ...filters,
        page,
        per_page: perPage,
        orderby,
        order
      };
      
      const path = addQueryArgs('/boss-seo/v1/schemas', queryArgs);
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des schémas:', error);
      throw error;
    }
  }

  /**
   * Récupère un schéma spécifique
   * 
   * @param {number} id - ID du schéma
   * @returns {Promise} Promesse contenant le schéma
   */
  async getSchema(id) {
    try {
      const path = `/boss-seo/v1/schemas/${id}`;
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération du schéma ${id}:`, error);
      throw error;
    }
  }

  /**
   * Crée un nouveau schéma
   * 
   * @param {Object} schemaData - Données du schéma
   * @returns {Promise} Promesse contenant le schéma créé
   */
  async createSchema(schemaData) {
    try {
      const path = '/boss-seo/v1/schemas';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: schemaData
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la création du schéma:', error);
      throw error;
    }
  }

  /**
   * Met à jour un schéma existant
   * 
   * @param {number} id - ID du schéma
   * @param {Object} schemaData - Données du schéma
   * @returns {Promise} Promesse contenant le schéma mis à jour
   */
  async updateSchema(id, schemaData) {
    try {
      const path = `/boss-seo/v1/schemas/${id}`;
      const response = await apiFetch({
        path,
        method: 'PUT',
        data: schemaData
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du schéma ${id}:`, error);
      throw error;
    }
  }

  /**
   * Supprime un schéma
   * 
   * @param {number} id - ID du schéma
   * @returns {Promise} Promesse contenant le résultat de la suppression
   */
  async deleteSchema(id) {
    try {
      const path = `/boss-seo/v1/schemas/${id}`;
      const response = await apiFetch({
        path,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la suppression du schéma ${id}:`, error);
      throw error;
    }
  }

  /**
   * Active ou désactive un schéma
   * 
   * @param {number} id - ID du schéma
   * @param {boolean} active - État d'activation
   * @returns {Promise} Promesse contenant le schéma mis à jour
   */
  async toggleSchema(id, active) {
    try {
      const path = `/boss-seo/v1/schemas/${id}`;
      const response = await apiFetch({
        path,
        method: 'PUT',
        data: { active }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'activation/désactivation du schéma ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les types de schémas disponibles
   * 
   * @returns {Promise} Promesse contenant les types de schémas
   */
  async getSchemaTypes() {
    try {
      const path = '/boss-seo/v1/schemas/types';
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des types de schémas:', error);
      throw error;
    }
  }

  /**
   * Récupère les propriétés d'un type de schéma
   * 
   * @param {string} type - Type de schéma
   * @returns {Promise} Promesse contenant les propriétés du type de schéma
   */
  async getSchemaProperties(type) {
    try {
      const path = `/boss-seo/v1/schemas/properties/${type}`;
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération des propriétés du type de schéma ${type}:`, error);
      throw error;
    }
  }

  /**
   * Récupère toutes les règles
   * 
   * @param {Object} filters - Filtres à appliquer (schema_id, active, etc.)
   * @returns {Promise} Promesse contenant les règles
   */
  async getRules(filters = {}) {
    try {
      const path = addQueryArgs('/boss-seo/v1/rules', filters);
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des règles:', error);
      throw error;
    }
  }

  /**
   * Récupère une règle spécifique
   * 
   * @param {number} id - ID de la règle
   * @returns {Promise} Promesse contenant la règle
   */
  async getRule(id) {
    try {
      const path = `/boss-seo/v1/rules/${id}`;
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération de la règle ${id}:`, error);
      throw error;
    }
  }

  /**
   * Crée une nouvelle règle
   * 
   * @param {Object} ruleData - Données de la règle
   * @returns {Promise} Promesse contenant la règle créée
   */
  async createRule(ruleData) {
    try {
      const path = '/boss-seo/v1/rules';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: ruleData
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la création de la règle:', error);
      throw error;
    }
  }

  /**
   * Met à jour une règle existante
   * 
   * @param {number} id - ID de la règle
   * @param {Object} ruleData - Données de la règle
   * @returns {Promise} Promesse contenant la règle mise à jour
   */
  async updateRule(id, ruleData) {
    try {
      const path = `/boss-seo/v1/rules/${id}`;
      const response = await apiFetch({
        path,
        method: 'PUT',
        data: ruleData
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de la règle ${id}:`, error);
      throw error;
    }
  }

  /**
   * Supprime une règle
   * 
   * @param {number} id - ID de la règle
   * @returns {Promise} Promesse contenant le résultat de la suppression
   */
  async deleteRule(id) {
    try {
      const path = `/boss-seo/v1/rules/${id}`;
      const response = await apiFetch({
        path,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la suppression de la règle ${id}:`, error);
      throw error;
    }
  }

  /**
   * Génère un schéma avec l'IA
   * 
   * @param {number} postId - ID du post
   * @param {string} schemaType - Type de schéma
   * @param {boolean} save - Sauvegarder le schéma généré
   * @returns {Promise} Promesse contenant le schéma généré
   */
  async generateSchemaWithAI(postId, schemaType, save = false) {
    try {
      const path = '/boss-seo/v1/ai/generate';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          post_id: postId,
          schema_type: schemaType,
          save
        }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la génération du schéma avec l\'IA:', error);
      throw error;
    }
  }

  /**
   * Teste un schéma
   * 
   * @param {Object} schema - Schéma à tester
   * @returns {Promise} Promesse contenant le résultat du test
   */
  async testSchema(schema) {
    try {
      const path = '/boss-seo/v1/test';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: schema
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du test du schéma:', error);
      throw error;
    }
  }
}

export default new SchemaService();
