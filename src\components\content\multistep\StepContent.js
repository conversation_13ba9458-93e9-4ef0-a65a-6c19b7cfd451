/**
 * Composant pour l'étape 3 : Génération de contenu SEO
 */
import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Dashicon,
  Spinner,
  TextControl,
  TextareaControl,
  SelectControl,
  ToggleControl,
  Notice,
  RangeControl
} from '@wordpress/components';

// Services
import optimizerService from '../../../services/OptimizerService';

/**
 * Composant pour l'analyse SEO en temps réel
 */
const SeoAnalysis = ({ content, keywords, score }) => {
  // Fonction pour obtenir la classe de couleur en fonction du score
  const getScoreColorClass = (score) => {
    if (score >= 80) return 'boss-text-green-500';
    if (score >= 50) return 'boss-text-yellow-500';
    return 'boss-text-red-500';
  };
  
  // Fonction pour obtenir l'icône en fonction du score
  const getScoreIcon = (score) => {
    if (score >= 80) return 'yes-alt';
    if (score >= 50) return 'warning';
    return 'dismiss';
  };
  
  // Calculer les suggestions
  const getSuggestions = () => {
    const suggestions = [];
    
    // Vérifier le titre
    if (!content.title) {
      suggestions.push({
        id: 'missing_title',
        type: 'error',
        text: __('Ajoutez un titre à votre contenu', 'boss-seo')
      });
    } else if (content.title.length < 20) {
      suggestions.push({
        id: 'short_title',
        type: 'warning',
        text: __('Votre titre est trop court (moins de 20 caractères)', 'boss-seo')
      });
    } else if (content.title.length > 70) {
      suggestions.push({
        id: 'long_title',
        type: 'warning',
        text: __('Votre titre est trop long (plus de 70 caractères)', 'boss-seo')
      });
    }
    
    // Vérifier la méta description
    if (!content.metaDescription) {
      suggestions.push({
        id: 'missing_meta_description',
        type: 'error',
        text: __('Ajoutez une méta description à votre contenu', 'boss-seo')
      });
    } else if (content.metaDescription.length < 120) {
      suggestions.push({
        id: 'short_meta_description',
        type: 'warning',
        text: __('Votre méta description est trop courte (moins de 120 caractères)', 'boss-seo')
      });
    } else if (content.metaDescription.length > 160) {
      suggestions.push({
        id: 'long_meta_description',
        type: 'warning',
        text: __('Votre méta description est trop longue (plus de 160 caractères)', 'boss-seo')
      });
    }
    
    // Vérifier le contenu
    if (!content.content) {
      suggestions.push({
        id: 'missing_content',
        type: 'error',
        text: __('Ajoutez du contenu à votre page', 'boss-seo')
      });
    } else if (content.content.length < 300) {
      suggestions.push({
        id: 'short_content',
        type: 'warning',
        text: __('Votre contenu est trop court (moins de 300 caractères)', 'boss-seo')
      });
    }
    
    // Vérifier les mots-clés
    if (keywords.main && content.content) {
      if (!content.content.toLowerCase().includes(keywords.main.toLowerCase())) {
        suggestions.push({
          id: 'missing_keyword',
          type: 'error',
          text: __('Votre mot-clé principal n\'apparaît pas dans le contenu', 'boss-seo')
        });
      } else if (content.title && !content.title.toLowerCase().includes(keywords.main.toLowerCase())) {
        suggestions.push({
          id: 'missing_keyword_title',
          type: 'warning',
          text: __('Votre mot-clé principal n\'apparaît pas dans le titre', 'boss-seo')
        });
      }
    }
    
    return suggestions;
  };
  
  const suggestions = getSuggestions();
  
  return (
    <div className="boss-space-y-4">
      <div className="boss-flex boss-items-center boss-justify-between boss-mb-4">
        <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Analyse SEO', 'boss-seo')}
        </h3>
        
        <div className={`boss-text-2xl boss-font-bold ${getScoreColorClass(score.overall)}`}>
          {score.overall}/100
        </div>
      </div>
      
      <div className="boss-space-y-2">
        <div className="boss-flex boss-justify-between boss-items-center">
          <div className="boss-text-sm boss-font-medium">{__('Titre', 'boss-seo')}</div>
          <div className={`boss-flex boss-items-center ${getScoreColorClass(score.title)}`}>
            <Dashicon icon={getScoreIcon(score.title)} />
            <span className="boss-ml-1">{score.title}/100</span>
          </div>
        </div>
        
        <div className="boss-flex boss-justify-between boss-items-center">
          <div className="boss-text-sm boss-font-medium">{__('Méta description', 'boss-seo')}</div>
          <div className={`boss-flex boss-items-center ${getScoreColorClass(score.metaDescription)}`}>
            <Dashicon icon={getScoreIcon(score.metaDescription)} />
            <span className="boss-ml-1">{score.metaDescription}/100</span>
          </div>
        </div>
        
        <div className="boss-flex boss-justify-between boss-items-center">
          <div className="boss-text-sm boss-font-medium">{__('Contenu', 'boss-seo')}</div>
          <div className={`boss-flex boss-items-center ${getScoreColorClass(score.content)}`}>
            <Dashicon icon={getScoreIcon(score.content)} />
            <span className="boss-ml-1">{score.content}/100</span>
          </div>
        </div>
        
        <div className="boss-flex boss-justify-between boss-items-center">
          <div className="boss-text-sm boss-font-medium">{__('Mots-clés', 'boss-seo')}</div>
          <div className={`boss-flex boss-items-center ${getScoreColorClass(score.keywords)}`}>
            <Dashicon icon={getScoreIcon(score.keywords)} />
            <span className="boss-ml-1">{score.keywords}/100</span>
          </div>
        </div>
      </div>
      
      <div className="boss-mt-6">
        <h4 className="boss-text-sm boss-font-medium boss-mb-2">
          {__('Suggestions d\'amélioration', 'boss-seo')}
        </h4>
        
        {suggestions.length > 0 ? (
          <div className="boss-space-y-2">
            {suggestions.map((suggestion, index) => (
              <div 
                key={index} 
                className={`boss-p-2 boss-rounded boss-text-sm boss-flex boss-items-start ${
                  suggestion.type === 'error' 
                    ? 'boss-bg-red-50 boss-text-red-700' 
                    : 'boss-bg-yellow-50 boss-text-yellow-700'
                }`}
              >
                <Dashicon 
                  icon={suggestion.type === 'error' ? 'dismiss' : 'warning'} 
                  className="boss-mr-2 boss-mt-0.5" 
                />
                <div>{suggestion.text}</div>
              </div>
            ))}
          </div>
        ) : (
          <div className="boss-p-2 boss-rounded boss-text-sm boss-bg-green-50 boss-text-green-700 boss-flex boss-items-start">
            <Dashicon icon="yes-alt" className="boss-mr-2 boss-mt-0.5" />
            <div>{__('Votre contenu est bien optimisé pour le SEO !', 'boss-seo')}</div>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Composant pour l'aperçu SERP
 */
const SerpPreview = ({ title, description, url }) => {
  return (
    <div className="boss-border boss-border-gray-200 boss-rounded boss-p-4 boss-bg-white">
      <h4 className="boss-font-medium boss-mb-2">
        {__('Aperçu dans les résultats de recherche', 'boss-seo')}
      </h4>
      
      <div className="boss-border boss-border-gray-200 boss-rounded boss-p-4 boss-bg-gray-50">
        <div className="boss-text-xl boss-text-blue-600 boss-font-medium boss-mb-1 boss-truncate">
          {title || __('Titre de votre page', 'boss-seo')}
        </div>
        
        <div className="boss-text-sm boss-text-green-700 boss-mb-1 boss-truncate">
          {url || 'https://example.com/votre-page'}
        </div>
        
        <div className="boss-text-sm boss-text-gray-600 boss-line-clamp-2">
          {description || __('Méta description de votre page. Celle-ci apparaîtra dans les résultats de recherche et incitera les utilisateurs à cliquer sur votre lien.', 'boss-seo')}
        </div>
      </div>
    </div>
  );
};

/**
 * Composant pour l'étape 3 : Génération de contenu SEO
 * 
 * @param {Object} props Propriétés du composant
 * @param {Object} props.data Données de l'étape
 * @param {Function} props.updateData Fonction pour mettre à jour les données
 * @param {Object} props.keywordsData Données de l'étape 2 (Recherche de mots-clés)
 * @param {Object} props.objectiveData Données de l'étape 1 (Objectif SEO)
 */
const StepContent = ({ data, updateData, keywordsData, objectiveData }) => {
  // États pour la génération de contenu
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // États pour les options de génération
  const [contentFormat, setContentFormat] = useState(data.format || 'article');
  const [aiProvider, setAiProvider] = useState('openai');
  const [temperature, setTemperature] = useState(0.7);
  const [generateOptions, setGenerateOptions] = useState({
    generateTitle: true,
    generateMetaDescription: true,
    includeKeywords: true,
    optimizeForSeo: true
  });
  
  // Fonction pour mettre à jour une propriété spécifique
  const updateProperty = (property, value) => {
    updateData({
      ...data,
      [property]: value
    });
  };
  
  // Fonction pour générer du contenu avec l'IA
  const generateContent = async () => {
    if (!keywordsData.main) {
      setError(__('Veuillez d\'abord sélectionner un mot-clé principal à l\'étape 2.', 'boss-seo'));
      return;
    }
    
    setIsGenerating(true);
    setError('');
    setSuccess('');
    
    try {
      // Préparer les options pour la génération
      const options = {
        format: contentFormat,
        provider: aiProvider,
        temperature: temperature,
        keywords: {
          main: keywordsData.main,
          secondary: keywordsData.secondary
        },
        objective: {
          contentType: objectiveData.contentType,
          tone: objectiveData.tone,
          audience: objectiveData.audience
        },
        generate: {
          title: generateOptions.generateTitle,
          metaDescription: generateOptions.generateMetaDescription,
          includeKeywords: generateOptions.includeKeywords,
          optimizeForSeo: generateOptions.optimizeForSeo
        }
      };
      
      // Appeler le service pour générer du contenu
      const response = await optimizerService.generateContent('', options);
      
      if (response.success) {
        // Mettre à jour les données avec le contenu généré
        const newData = { ...data };
        
        if (generateOptions.generateTitle && response.title) {
          newData.title = response.title;
        }
        
        if (generateOptions.generateMetaDescription && response.metaDescription) {
          newData.metaDescription = response.metaDescription;
        }
        
        if (response.content) {
          newData.content = response.content;
        }
        
        updateData(newData);
        setSuccess(__('Contenu généré avec succès !', 'boss-seo'));
        
        // Analyser le contenu pour mettre à jour le score SEO
        analyzeContent(newData);
      } else {
        setError(response.message || __('Erreur lors de la génération du contenu.', 'boss-seo'));
      }
    } catch (error) {
      console.error('Erreur lors de la génération du contenu:', error);
      setError(__('Erreur lors de la génération du contenu. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Fonction pour analyser le contenu et mettre à jour le score SEO
  const analyzeContent = (contentData = data) => {
    // Calculer les scores pour chaque catégorie
    let titleScore = 0;
    let metaDescriptionScore = 0;
    let contentScore = 0;
    let keywordsScore = 0;
    
    // Analyser le titre
    if (contentData.title) {
      if (contentData.title.length >= 20 && contentData.title.length <= 70) {
        titleScore = 100;
      } else if (contentData.title.length < 20) {
        titleScore = 50;
      } else {
        titleScore = 70;
      }
      
      // Bonus si le mot-clé principal est dans le titre
      if (keywordsData.main && contentData.title.toLowerCase().includes(keywordsData.main.toLowerCase())) {
        titleScore = Math.min(100, titleScore + 20);
      }
    }
    
    // Analyser la méta description
    if (contentData.metaDescription) {
      if (contentData.metaDescription.length >= 120 && contentData.metaDescription.length <= 160) {
        metaDescriptionScore = 100;
      } else if (contentData.metaDescription.length < 120) {
        metaDescriptionScore = 50;
      } else {
        metaDescriptionScore = 70;
      }
      
      // Bonus si le mot-clé principal est dans la méta description
      if (keywordsData.main && contentData.metaDescription.toLowerCase().includes(keywordsData.main.toLowerCase())) {
        metaDescriptionScore = Math.min(100, metaDescriptionScore + 20);
      }
    }
    
    // Analyser le contenu
    if (contentData.content) {
      if (contentData.content.length >= 300) {
        contentScore = 100;
      } else {
        contentScore = Math.min(100, Math.round(contentData.content.length / 3));
      }
    }
    
    // Analyser les mots-clés
    if (keywordsData.main && contentData.content) {
      if (contentData.content.toLowerCase().includes(keywordsData.main.toLowerCase())) {
        keywordsScore = 100;
        
        // Bonus pour les mots-clés secondaires
        const secondaryKeywordsFound = keywordsData.secondary.filter(
          keyword => contentData.content.toLowerCase().includes(keyword.toLowerCase())
        ).length;
        
        if (secondaryKeywordsFound > 0 && keywordsData.secondary.length > 0) {
          const secondaryKeywordsRatio = secondaryKeywordsFound / keywordsData.secondary.length;
          keywordsScore = Math.min(100, keywordsScore + Math.round(secondaryKeywordsRatio * 20));
        }
      } else {
        keywordsScore = 0;
      }
    }
    
    // Calculer le score global
    const overallScore = Math.round(
      (titleScore + metaDescriptionScore + contentScore + keywordsScore) / 4
    );
    
    // Mettre à jour le score SEO
    updateProperty('seoScore', {
      overall: overallScore,
      title: titleScore,
      metaDescription: metaDescriptionScore,
      content: contentScore,
      keywords: keywordsScore
    });
  };
  
  // Effet pour analyser le contenu lorsqu'il change
  useEffect(() => {
    analyzeContent();
  }, [data.title, data.metaDescription, data.content, keywordsData.main, keywordsData.secondary]);
  
  return (
    <div className="boss-space-y-6">
      <div className="boss-mb-6">
        <h2 className="boss-text-xl boss-font-semibold boss-text-boss-dark boss-mb-4">
          {__('Étape 3 : Génération de contenu SEO', 'boss-seo')}
        </h2>
        <p className="boss-text-boss-gray">
          {__('Générez du contenu optimisé pour le SEO à l\'aide de l\'IA ou créez votre propre contenu avec des suggestions d\'optimisation en temps réel.', 'boss-seo')}
        </p>
      </div>
      
      {error && (
        <Notice status="error" isDismissible={true} onRemove={() => setError('')} className="boss-mb-4">
          {error}
        </Notice>
      )}
      
      {success && (
        <Notice status="success" isDismissible={true} onRemove={() => setSuccess('')} className="boss-mb-4">
          {success}
        </Notice>
      )}
      
      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="admin-tools" className="boss-mr-2" />
            {__('Options de génération', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mb-4">
            <SelectControl
              label={__('Format de contenu', 'boss-seo')}
              value={contentFormat}
              options={[
                { label: __('Article de blog', 'boss-seo'), value: 'article' },
                { label: __('Description produit', 'boss-seo'), value: 'product' },
                { label: __('FAQ', 'boss-seo'), value: 'faq' },
                { label: __('Landing page', 'boss-seo'), value: 'landing' },
                { label: __('Guide pratique', 'boss-seo'), value: 'guide' },
                { label: __('Étude de cas', 'boss-seo'), value: 'case-study' },
              ]}
              onChange={(value) => {
                setContentFormat(value);
                updateProperty('format', value);
              }}
            />
            
            <SelectControl
              label={__('Fournisseur d\'IA', 'boss-seo')}
              value={aiProvider}
              options={[
                { label: __('OpenAI (GPT-4)', 'boss-seo'), value: 'openai' },
                { label: __('Anthropic (Claude)', 'boss-seo'), value: 'anthropic' },
                { label: __('Google (Gemini)', 'boss-seo'), value: 'gemini' },
              ]}
              onChange={setAiProvider}
            />
          </div>
          
          <RangeControl
            label={__('Créativité (température)', 'boss-seo')}
            value={temperature}
            onChange={setTemperature}
            min={0}
            max={1}
            step={0.1}
            help={__('Une valeur plus élevée rend le contenu plus créatif mais potentiellement moins précis.', 'boss-seo')}
          />
          
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mt-4">
            <ToggleControl
              label={__('Générer le titre', 'boss-seo')}
              checked={generateOptions.generateTitle}
              onChange={(value) => setGenerateOptions({ ...generateOptions, generateTitle: value })}
            />
            
            <ToggleControl
              label={__('Générer la méta description', 'boss-seo')}
              checked={generateOptions.generateMetaDescription}
              onChange={(value) => setGenerateOptions({ ...generateOptions, generateMetaDescription: value })}
            />
            
            <ToggleControl
              label={__('Inclure les mots-clés', 'boss-seo')}
              checked={generateOptions.includeKeywords}
              onChange={(value) => setGenerateOptions({ ...generateOptions, includeKeywords: value })}
            />
            
            <ToggleControl
              label={__('Optimiser pour le SEO', 'boss-seo')}
              checked={generateOptions.optimizeForSeo}
              onChange={(value) => setGenerateOptions({ ...generateOptions, optimizeForSeo: value })}
            />
          </div>
          
          <div className="boss-mt-4 boss-flex boss-justify-center">
            <Button
              isPrimary
              onClick={generateContent}
              disabled={isGenerating}
              isBusy={isGenerating}
              className="boss-px-6"
            >
              {isGenerating ? __('Génération en cours...', 'boss-seo') : __('Générer le contenu', 'boss-seo')}
            </Button>
          </div>
        </CardBody>
      </Card>
      
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-6">
        <div className="boss-col-span-2">
          <Card className="boss-mb-6">
            <CardHeader>
              <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                <Dashicon icon="edit" className="boss-mr-2" />
                {__('Éditeur de contenu', 'boss-seo')}
              </h3>
            </CardHeader>
            <CardBody>
              <TextControl
                label={__('Titre', 'boss-seo')}
                value={data.title}
                onChange={(value) => updateProperty('title', value)}
                className="boss-mb-4"
              />
              
              <TextareaControl
                label={__('Méta description', 'boss-seo')}
                value={data.metaDescription}
                onChange={(value) => updateProperty('metaDescription', value)}
                className="boss-mb-4"
                help={__('La méta description apparaît dans les résultats de recherche. Idéalement entre 120 et 160 caractères.', 'boss-seo')}
              />
              
              <TextareaControl
                label={__('Contenu', 'boss-seo')}
                value={data.content}
                onChange={(value) => updateProperty('content', value)}
                className="boss-mb-4"
                rows={15}
              />
              
              <SerpPreview 
                title={data.title}
                description={data.metaDescription}
                url={data.title ? `https://example.com/${data.title.toLowerCase().replace(/[^a-z0-9]+/g, '-')}` : ''}
              />
            </CardBody>
          </Card>
        </div>
        
        <div className="boss-col-span-1">
          <Card className="boss-mb-6 boss-sticky boss-top-4">
            <CardHeader>
              <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                <Dashicon icon="chart-bar" className="boss-mr-2" />
                {__('Analyse SEO', 'boss-seo')}
              </h3>
            </CardHeader>
            <CardBody>
              <SeoAnalysis 
                content={data}
                keywords={keywordsData}
                score={data.seoScore || {
                  overall: 0,
                  title: 0,
                  metaDescription: 0,
                  content: 0,
                  keywords: 0
                }}
              />
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default StepContent;
