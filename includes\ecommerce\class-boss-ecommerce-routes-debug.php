<?php
/**
 * Classe pour déboguer les routes REST API du module e-commerce.
 *
 * Cette classe aide à déboguer les routes REST API du module e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour déboguer les routes REST API du module e-commerce.
 *
 * Cette classe aide à déboguer les routes REST API du module e-commerce.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Ecommerce_Routes_Debug {

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     */
    public function __construct() {
        add_action( 'rest_api_init', array( $this, 'register_rest_routes' ), 999 );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/debug',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'debug_routes' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Enregistrer directement les routes critiques
        $this->register_critical_routes();
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Débogue les routes REST API.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function debug_routes( $request ) {
        global $wp_rest_server;

        $routes = $wp_rest_server->get_routes();
        $boss_routes = array();

        foreach ( $routes as $route => $handlers ) {
            if ( strpos( $route, 'boss-seo/v1' ) !== false ) {
                $boss_routes[ $route ] = array();
                foreach ( $handlers as $handler ) {
                    $boss_routes[ $route ][] = array(
                        'methods' => $handler['methods'],
                        'callback' => get_class( $handler['callback'][0] ) . '::' . $handler['callback'][1],
                        'permission_callback' => $handler['permission_callback'] ? get_class( $handler['permission_callback'][0] ) . '::' . $handler['permission_callback'][1] : null,
                    );
                }
            }
        }

        return rest_ensure_response( array(
            'routes' => $boss_routes,
            'critical_routes_registered' => $this->are_critical_routes_registered( $routes ),
        ) );
    }

    /**
     * Vérifie si les routes critiques sont enregistrées.
     *
     * @since    1.2.0
     * @param    array    $routes    Les routes enregistrées.
     * @return   array               Les résultats de la vérification.
     */
    private function are_critical_routes_registered( $routes ) {
        $critical_routes = array(
            'boss-seo/v1/ecommerce/products',
            'boss-seo/v1/ecommerce/products/(?P<id>\d+)',
            'boss-seo/v1/ecommerce/dashboard/top-products',
            'boss-seo/v1/ecommerce/dashboard/top-categories',
            'boss-seo/v1/ecommerce/dashboard/stats',
        );

        $results = array();

        foreach ( $critical_routes as $critical_route ) {
            $found = false;
            foreach ( $routes as $route => $handlers ) {
                if ( strpos( $route, $critical_route ) !== false ) {
                    $found = true;
                    break;
                }
            }
            $results[ $critical_route ] = $found;
        }

        return $results;
    }

    /**
     * Enregistre directement les routes critiques.
     *
     * @since    1.2.0
     */
    private function register_critical_routes() {
        // Enregistrer la route /ecommerce/products
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/products',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_products' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Enregistrer la route /ecommerce/products/{id}
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/products/(?P<id>\d+)',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_product' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Récupère les produits via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_products( $request ) {
        return rest_ensure_response( array(
            'products' => $this->get_mock_products(),
            'total'    => 50,
            'pages'    => 1,
        ) );
    }

    /**
     * Récupère un produit spécifique via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_product( $request ) {
        $id = $request->get_param( 'id' );
        
        return rest_ensure_response( array(
            'product' => array(
                'id'          => (int) $id,
                'name'        => "Produit $id",
                'category'    => 'Électronique',
                'categoryId'  => 1,
                'price'       => '99.99',
                'stock'       => 50,
                'score'       => 85,
                'status'      => 'optimized',
                'issues'      => array(),
                'hasSchema'   => true,
                'lastUpdated' => date( 'Y-m-d' ),
                'description' => 'Description du produit générée automatiquement pour le débogage.',
                'images'      => array(
                    array( 'id' => 1, 'url' => 'https://via.placeholder.com/600x400', 'alt' => 'Image du produit' )
                ),
                'meta'        => array(
                    'title'       => "Produit $id - Titre SEO",
                    'description' => 'Meta description du produit pour le référencement.',
                    'keywords'    => 'produit, exemple, débogage'
                )
            )
        ) );
    }

    /**
     * Génère des produits fictifs pour le débogage.
     *
     * @since    1.2.0
     * @return   array    Les produits fictifs.
     */
    private function get_mock_products() {
        $mock_categories = array(
            array( 'id' => 1, 'name' => 'Électronique' ),
            array( 'id' => 2, 'name' => 'Vêtements' ),
            array( 'id' => 3, 'name' => 'Maison' ),
            array( 'id' => 4, 'name' => 'Sports' ),
            array( 'id' => 5, 'name' => 'Livres' )
        );

        $mock_products = array();

        for ( $i = 1; $i <= 50; $i++ ) {
            $category_index = rand( 0, count( $mock_categories ) - 1 );
            $category = $mock_categories[ $category_index ];

            $mock_products[] = array(
                'id'          => $i,
                'name'        => "Produit $i - {$category['name']}",
                'category'    => $category['name'],
                'categoryId'  => $category['id'],
                'price'       => round( rand( 1000, 10000 ) / 100, 2 ),
                'stock'       => rand( 0, 100 ),
                'score'       => rand( 40, 100 ),
                'status'      => rand( 0, 100 ) < 50 ? 'optimized' : (rand( 0, 100 ) < 80 ? 'needs_attention' : 'critical'),
                'issues'      => array(),
                'hasSchema'   => rand( 0, 100 ) < 60,
                'lastUpdated' => date( 'Y-m-d', strtotime( '-' . rand( 1, 30 ) . ' days' ) )
            );
        }

        return $mock_products;
    }
}
