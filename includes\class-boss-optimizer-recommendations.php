<?php
/**
 * La classe de gestion des recommandations du module Boss Optimizer.
 *
 * Cette classe gère la génération et le stockage des recommandations d'optimisation.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * La classe de gestion des recommandations du module Boss Optimizer.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_Recommendations {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string                   $plugin_name    Le nom du plugin.
     * @param    Boss_Optimizer_Settings  $settings       Instance de la classe de paramètres.
     */
    public function __construct( $plugin_name, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->settings = $settings;
    }

    /**
     * Récupère les recommandations pour un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du contenu.
     * @return   array                 Les recommandations.
     */
    public function get_recommendations( $post_id ) {
        $recommendations = get_post_meta( $post_id, '_boss_seo_recommendations', true );

        if ( empty( $recommendations ) || ! is_array( $recommendations ) ) {
            return array();
        }

        return $recommendations;
    }

    /**
     * Enregistre les recommandations pour un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id           L'ID du contenu.
     * @param    array     $recommendations   Les recommandations à enregistrer.
     * @return   boolean                      True si les recommandations ont été enregistrées, false sinon.
     */
    public function save_recommendations( $post_id, $recommendations ) {
        if ( ! is_array( $recommendations ) ) {
            return false;
        }

        // Ajouter un horodatage aux recommandations
        $recommendations_with_timestamp = array(
            'date' => current_time( 'mysql' ),
            'items' => $recommendations
        );

        // Enregistrer les recommandations actuelles
        update_post_meta( $post_id, '_boss_seo_recommendations', $recommendations );

        // Enregistrer l'historique des recommandations
        $history = $this->get_recommendations_history( $post_id );
        $history[] = $recommendations_with_timestamp;

        // Limiter l'historique aux 10 dernières analyses
        if ( count( $history ) > 10 ) {
            $history = array_slice( $history, -10 );
        }

        update_post_meta( $post_id, '_boss_seo_recommendations_history', $history );

        return true;
    }

    /**
     * Récupère l'historique des recommandations pour un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du contenu.
     * @return   array                 L'historique des recommandations.
     */
    public function get_recommendations_history( $post_id ) {
        $history = get_post_meta( $post_id, '_boss_seo_recommendations_history', true );

        if ( empty( $history ) || ! is_array( $history ) ) {
            return array();
        }

        return $history;
    }

    /**
     * Génère des recommandations d'optimisation basées sur l'analyse SEO.
     *
     * @since    1.1.0
     * @param    array     $analysis_results    Les résultats de l'analyse SEO.
     * @return   array                          Les recommandations générées.
     */
    public function generate_recommendations( $analysis_results ) {
        if ( ! isset( $analysis_results['recommendations'] ) || ! is_array( $analysis_results['recommendations'] ) ) {
            return array();
        }

        return $analysis_results['recommendations'];
    }

    /**
     * Applique une recommandation à un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id             L'ID du contenu.
     * @param    int       $recommendation_id   L'ID de la recommandation à appliquer.
     * @param    array     $ai_service          Instance du service d'IA (optionnel).
     * @return   array                          Le résultat de l'application de la recommandation.
     */
    public function apply_recommendation( $post_id, $recommendation_id, $ai_service = null ) {
        $post = get_post( $post_id );

        if ( ! $post ) {
            return array(
                'success' => false,
                'message' => __( 'Contenu non trouvé.', 'boss-seo' )
            );
        }

        $recommendations = $this->get_recommendations( $post_id );

        // Trouver la recommandation par son ID
        $recommendation = null;

        foreach ( $recommendations as $rec ) {
            if ( isset( $rec['id'] ) && $rec['id'] == $recommendation_id ) {
                $recommendation = $rec;
                break;
            }
        }

        if ( ! $recommendation ) {
            return array(
                'success' => false,
                'message' => __( 'Recommandation non trouvée.', 'boss-seo' )
            );
        }

        // Appliquer la recommandation en fonction de son type et de l'élément concerné
        $updated_content = $post->post_content;
        $updated_title = $post->post_title;
        $updated_meta_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );
        $update_needed = false;

        // Si aucun mot-clé principal n'est défini, utiliser le titre comme mot-clé par défaut
        if (empty($focus_keyword)) {
            $focus_keyword = $post->post_title;
            update_post_meta( $post_id, '_boss_seo_focus_keyword', $focus_keyword );
        }

        // Si un service d'IA est disponible, l'utiliser pour générer des suggestions
        if ( $ai_service && $ai_service->is_available() ) {
            switch ( $recommendation['element'] ) {
                case 'title':
                    if ( $this->settings->get( 'ai', 'use_ai_for_titles', true ) ) {
                        $prompt = sprintf(
                            __( 'Optimiser ce titre pour le SEO avec le mot-clé "%s": %s', 'boss-seo' ),
                            $focus_keyword,
                            $updated_title
                        );

                        $ai_response = $ai_service->generate_content( $prompt );

                        if ( $ai_response['success'] && ! empty( $ai_response['content'] ) ) {
                            // Nettoyer le contenu généré (supprimer les guillemets, etc.)
                            $generated_title = trim(preg_replace('/^["\']|["\']$/', '', $ai_response['content']));
                            // Limiter la longueur du titre
                            $updated_title = substr($generated_title, 0, 70);
                            $update_needed = true;
                        }
                    }
                    break;

                case 'meta_description':
                    if ( $this->settings->get( 'ai', 'use_ai_for_descriptions', true ) ) {
                        $prompt = sprintf(
                            __( 'Générer une méta-description SEO de 150 caractères maximum pour un contenu sur "%s" avec le mot-clé "%s"', 'boss-seo' ),
                            $updated_title,
                            $focus_keyword
                        );

                        $ai_response = $ai_service->generate_content( $prompt );

                        if ( $ai_response['success'] && ! empty( $ai_response['content'] ) ) {
                            // Nettoyer le contenu généré (supprimer les guillemets, etc.)
                            $generated_description = trim(preg_replace('/^["\']|["\']$/', '', $ai_response['content']));
                            // Limiter la longueur de la méta-description
                            $updated_meta_description = substr($generated_description, 0, 160);
                            $update_needed = true;
                        }
                    }
                    break;

                case 'content':
                    if ( $this->settings->get( 'ai', 'use_ai_for_content', true ) ) {
                        // Déterminer le type d'optimisation de contenu nécessaire
                        $prompt = '';

                        if ( strpos( $recommendation['text'], 'trop court' ) !== false ) {
                            $prompt = sprintf(
                                __( 'Enrichir ce contenu pour le rendre plus complet et optimisé pour le mot-clé "%s": %s', 'boss-seo' ),
                                $focus_keyword,
                                wp_trim_words( $updated_content, 100 )
                            );
                        } elseif ( strpos( $recommendation['text'], 'n\'apparaît pas' ) !== false ) {
                            $prompt = sprintf(
                                __( 'Optimiser ce contenu en incluant naturellement le mot-clé "%s": %s', 'boss-seo' ),
                                $focus_keyword,
                                wp_trim_words( $updated_content, 100 )
                            );
                        } else {
                            $prompt = sprintf(
                                __( 'Améliorer ce contenu pour le SEO en se concentrant sur le mot-clé "%s": %s', 'boss-seo' ),
                                $focus_keyword,
                                wp_trim_words( $updated_content, 100 )
                            );
                        }

                        $ai_response = $ai_service->generate_content( $prompt, array('max_tokens' => 1000) );

                        if ( $ai_response['success'] && ! empty( $ai_response['content'] ) ) {
                            // Pour le contenu, nous voulons conserver le contenu original et l'enrichir
                            // plutôt que de le remplacer complètement
                            if ( strpos( $recommendation['text'], 'trop court' ) !== false ) {
                                // Si le contenu est trop court, ajouter le nouveau contenu à la fin
                                $updated_content = $updated_content . "\n\n" . $ai_response['content'];
                            } elseif ( strpos( $recommendation['text'], 'n\'apparaît pas' ) !== false ) {
                                // Si le mot-clé n'apparaît pas, remplacer le contenu
                                $updated_content = $ai_response['content'];
                            } else {
                                // Dans les autres cas, essayer d'améliorer le contenu existant
                                // en conservant la structure mais en ajoutant des éléments optimisés
                                $updated_content = $ai_response['content'];
                            }
                            $update_needed = true;
                        }
                    }
                    break;

                // Autres éléments...
            }
        }

        // Si des modifications ont été apportées, mettre à jour le contenu
        if ( $update_needed ) {
            $post_data = array(
                'ID' => $post_id,
                'post_title' => $updated_title,
                'post_content' => $updated_content
            );

            wp_update_post( $post_data );

            if ( $updated_meta_description !== get_post_meta( $post_id, '_boss_seo_meta_description', true ) ) {
                update_post_meta( $post_id, '_boss_seo_meta_description', $updated_meta_description );
            }

            // Enregistrer l'application de la recommandation dans l'historique
            $this->record_applied_recommendation( $post_id, $recommendation );

            return array(
                'success' => true,
                'message' => __( 'Recommandation appliquée avec succès.', 'boss-seo' ),
                'updated_content' => $updated_content,
                'updated_title' => $updated_title,
                'updated_meta_description' => $updated_meta_description
            );
        }

        return array(
            'success' => false,
            'message' => __( 'Aucune modification n\'a été apportée.', 'boss-seo' )
        );
    }

    /**
     * Enregistre l'application d'une recommandation dans l'historique.
     *
     * @since    1.1.0
     * @param    int       $post_id          L'ID du contenu.
     * @param    array     $recommendation   La recommandation appliquée.
     * @return   boolean                     True si l'enregistrement a réussi, false sinon.
     */
    protected function record_applied_recommendation( $post_id, $recommendation ) {
        $applied_recommendations = get_post_meta( $post_id, '_boss_seo_applied_recommendations', true );

        if ( ! is_array( $applied_recommendations ) ) {
            $applied_recommendations = array();
        }

        $applied_recommendations[] = array(
            'id' => $recommendation['id'],
            'type' => $recommendation['type'],
            'text' => $recommendation['text'],
            'element' => $recommendation['element'],
            'date' => current_time( 'mysql' )
        );

        update_post_meta( $post_id, '_boss_seo_applied_recommendations', $applied_recommendations );

        return true;
    }
}
