import { __ } from '@wordpress/i18n';
import { <PERSON>, CardBody, CardHeader, Card<PERSON>ooter, Button, Dashicon, SelectControl } from '@wordpress/components';
import { useState } from '@wordpress/element';

/**
 * Composant pour afficher les tendances de trafic
 */
const TrafficTrends = ({ data }) => {
  const [period, setPeriod] = useState('30days');
  
  // Filtrer les données en fonction de la période sélectionnée
  const filteredData = data[period] || [];
  
  // Trouver les valeurs min et max pour l'échelle du graphique
  const values = filteredData.map(item => item.value);
  const maxValue = Math.max(...values, 0);
  const minValue = Math.min(...values, 0);
  const range = maxValue - minValue;
  
  // Calculer la tendance
  const firstValue = filteredData[0]?.value || 0;
  const lastValue = filteredData[filteredData.length - 1]?.value || 0;
  const trend = lastValue - firstValue;
  const trendPercentage = firstValue !== 0 ? Math.round((trend / firstValue) * 100) : 0;
  
  // Hauteur du graphique
  const chartHeight = 150;
  
  return (
    <Card className="boss-card boss-h-full">
      <CardHeader>
        <div className="boss-flex boss-justify-between boss-items-center">
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Tendances de trafic', 'boss-seo')}
          </h3>
          <SelectControl
            value={period}
            options={[
              { label: __('7 derniers jours', 'boss-seo'), value: '7days' },
              { label: __('30 derniers jours', 'boss-seo'), value: '30days' },
              { label: __('3 derniers mois', 'boss-seo'), value: '3months' },
              { label: __('12 derniers mois', 'boss-seo'), value: '12months' },
            ]}
            onChange={setPeriod}
            className="boss-w-40"
          />
        </div>
      </CardHeader>
      <CardBody>
        {/* Statistiques de tendance */}
        <div className="boss-flex boss-justify-between boss-mb-6">
          <div>
            <div className="boss-text-sm boss-text-boss-gray boss-mb-1">
              {__('Visiteurs', 'boss-seo')}
            </div>
            <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
              {lastValue.toLocaleString()}
            </div>
            <div className={`boss-flex boss-items-center boss-text-sm boss-mt-1 ${
              trend > 0 ? 'boss-text-boss-success' : trend < 0 ? 'boss-text-boss-error' : 'boss-text-boss-gray'
            }`}>
              <Dashicon icon={trend > 0 ? 'arrow-up-alt' : trend < 0 ? 'arrow-down-alt' : 'minus'} />
              <span>{Math.abs(trendPercentage)}% {__('depuis la période précédente', 'boss-seo')}</span>
            </div>
          </div>
          <div className="boss-text-right">
            <div className="boss-text-sm boss-text-boss-gray boss-mb-1">
              {__('Moyenne', 'boss-seo')}
            </div>
            <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
              {Math.round(values.reduce((sum, val) => sum + val, 0) / values.length).toLocaleString()}
            </div>
          </div>
        </div>
        
        {/* Graphique */}
        <div className="boss-relative boss-h-40">
          <div className="boss-absolute boss-inset-0 boss-flex boss-items-end boss-justify-between boss-pb-6">
            {filteredData.map((item, index) => {
              // Calculer la hauteur de la barre
              const barHeight = range !== 0 
                ? ((item.value - minValue) / range) * chartHeight 
                : 0;
              
              return (
                <div 
                  key={index} 
                  className="boss-relative boss-flex boss-flex-col boss-items-center boss-group"
                  style={{ height: '100%', width: `${100 / filteredData.length}%` }}
                >
                  {/* Info bulle au survol */}
                  <div className="boss-absolute boss-bottom-full boss-mb-2 boss-opacity-0 boss-group-hover:boss-opacity-100 boss-transition-opacity boss-duration-200 boss-pointer-events-none boss-z-10">
                    <div className="boss-bg-boss-dark boss-text-white boss-rounded boss-py-1 boss-px-2 boss-text-xs boss-whitespace-nowrap">
                      <div className="boss-font-bold">{item.value.toLocaleString()}</div>
                      <div>{item.label}</div>
                    </div>
                    <div className="boss-w-2 boss-h-2 boss-bg-boss-dark boss-transform boss-rotate-45 boss-mx-auto boss-mt-[-4px]"></div>
                  </div>
                  
                  {/* Barre du graphique */}
                  <div 
                    className={`boss-w-6 boss-rounded-t boss-transition-all boss-duration-500 boss-ease-out ${
                      trend > 0 ? 'boss-bg-boss-success' : trend < 0 ? 'boss-bg-boss-error' : 'boss-bg-boss-primary'
                    }`}
                    style={{ height: `${Math.max(barHeight, 2)}px` }}
                  ></div>
                  
                  {/* Étiquette */}
                  <div className="boss-text-xs boss-text-boss-gray boss-mt-2 boss-truncate boss-w-full boss-text-center">
                    {item.shortLabel}
                  </div>
                </div>
              );
            })}
          </div>
          
          {/* Ligne de base */}
          <div className="boss-absolute boss-bottom-6 boss-left-0 boss-right-0 boss-h-px boss-bg-gray-200"></div>
        </div>
      </CardBody>
      <CardFooter>
        <Button
          isSecondary
          className="boss-w-full boss-justify-center"
        >
          {__('Voir les statistiques détaillées', 'boss-seo')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TrafficTrends;
