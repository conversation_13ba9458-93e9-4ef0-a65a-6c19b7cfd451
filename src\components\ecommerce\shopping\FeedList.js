import { __ } from '@wordpress/i18n';
import { 
  <PERSON>, 
  <PERSON>H<PERSON><PERSON>, 
  CardBody, 
  Button, 
  Dashicon 
} from '@wordpress/components';

const FeedList = ({ feeds }) => {
  if (!feeds || feeds.length === 0) {
    return (
      <Card>
        <CardBody>
          <div className="boss-text-center boss-py-6 boss-text-boss-gray">
            {__('Aucun flux Google Shopping n\'a encore été généré.', 'boss-seo')}
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Flux générés', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody className="boss-p-0">
        <div className="boss-divide-y boss-divide-gray-200">
          {feeds.map((feed, index) => (
            <div key={index} className="boss-p-4 boss-hover:boss-bg-gray-50">
              <div className="boss-flex boss-flex-col md:boss-flex-row boss-justify-between boss-items-start boss-gap-4">
                <div>
                  <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">{feed.name}</h3>
                  <div className="boss-flex boss-flex-wrap boss-gap-2 boss-text-sm boss-text-boss-gray boss-mb-2">
                    <span>{feed.country}</span>
                    <span>•</span>
                    <span>{feed.language}</span>
                    <span>•</span>
                    <span>{feed.currency}</span>
                    <span>•</span>
                    <span>{feed.productCount} {__('produits', 'boss-seo')}</span>
                  </div>
                  <div className="boss-flex boss-items-center">
                    <span className={`boss-px-2 boss-py-1 boss-text-xs boss-rounded-full ${
                      feed.status === 'active'
                        ? 'boss-bg-green-100 boss-text-green-800'
                        : 'boss-bg-yellow-100 boss-text-yellow-800'
                    }`}>
                      {feed.status === 'active'
                        ? __('Actif', 'boss-seo')
                        : __('En attente', 'boss-seo')}
                    </span>
                    <span className="boss-text-sm boss-text-boss-gray boss-ml-2">
                      {__('Mis à jour le', 'boss-seo')} {feed.lastUpdated}
                    </span>
                  </div>
                </div>
                <div className="boss-flex boss-space-x-2">
                  <Button
                    isSecondary
                    isSmall
                    href={feed.url}
                    target="_blank"
                  >
                    <Dashicon icon="external" className="boss-mr-1" />
                    {__('Voir', 'boss-seo')}
                  </Button>
                  <Button
                    isSecondary
                    isSmall
                  >
                    <Dashicon icon="update" className="boss-mr-1" />
                    {__('Mettre à jour', 'boss-seo')}
                  </Button>
                  <Button
                    isDestructive
                    isSmall
                  >
                    <Dashicon icon="trash" className="boss-mr-1" />
                    {__('Supprimer', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardBody>
    </Card>
  );
};

export default FeedList;
