/**
 * Composant pour l'étape 1 : Objectif SEO
 */
import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  SelectControl,
  TextareaControl,
  Card,
  CardBody,
  CardHeader,
  Dashicon,
  Button,
  FormTokenField
} from '@wordpress/components';

/**
 * Composant pour l'étape 1 : Objectif SEO
 * 
 * @param {Object} props Propriétés du composant
 * @param {Object} props.data Données de l'étape
 * @param {Function} props.updateData Fonction pour mettre à jour les données
 */
const StepObjective = ({ data, updateData }) => {
  // État local pour gérer les personas
  const [personas, setPersonas] = useState([]);
  const [selectedPersona, setSelectedPersona] = useState('');
  
  // Fonction pour mettre à jour une propriété spécifique
  const updateProperty = (property, value) => {
    updateData({
      ...data,
      [property]: value
    });
  };
  
  // Fonction pour appliquer un persona sélectionné
  const applyPersona = () => {
    if (selectedPersona) {
      // Dans une implémentation réelle, vous récupéreriez les détails du persona depuis une API
      // Pour cet exemple, nous utilisons des données fictives
      const personaDetails = {
        'Professionnel B2B': 'Décideurs d\'entreprise, 35-55 ans, recherchant des solutions professionnelles pour optimiser leurs processus métier.',
        'Consommateur': 'Consommateurs grand public, 25-45 ans, intéressés par des produits de qualité et un bon rapport qualité-prix.',
        'Étudiant': 'Étudiants universitaires, 18-25 ans, à la recherche d\'informations et de ressources pour leurs études.',
        'Parent': 'Parents, 30-45 ans, cherchant des conseils et des produits pour leurs enfants.',
        'Senior': 'Personnes âgées de 60 ans et plus, à la recherche d\'informations claires et de produits adaptés à leurs besoins.'
      }[selectedPersona] || '';
      
      updateProperty('audience', personaDetails);
    }
  };
  
  return (
    <div className="boss-space-y-6">
      <div className="boss-mb-6">
        <h2 className="boss-text-xl boss-font-semibold boss-text-boss-dark boss-mb-4">
          {__('Étape 1 : Définissez votre objectif SEO', 'boss-seo')}
        </h2>
        <p className="boss-text-boss-gray">
          {__('Commencez par définir le type de contenu que vous souhaitez créer, le ton à adopter et votre public cible.', 'boss-seo')}
        </p>
      </div>
      
      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="admin-site-alt3" className="boss-mr-2" />
            {__('Type de contenu', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <p className="boss-text-boss-gray boss-mb-4">
            {__('Sélectionnez le type de contenu que vous souhaitez créer. Cela aidera l\'IA à générer un contenu adapté à votre objectif.', 'boss-seo')}
          </p>
          
          <SelectControl
            label={__('Type de contenu', 'boss-seo')}
            value={data.contentType}
            options={[
              { label: __('Article de blog', 'boss-seo'), value: 'article' },
              { label: __('Page', 'boss-seo'), value: 'page' },
              { label: __('Fiche produit', 'boss-seo'), value: 'product' },
              { label: __('Landing page', 'boss-seo'), value: 'landing' },
              { label: __('FAQ', 'boss-seo'), value: 'faq' },
              { label: __('Guide pratique', 'boss-seo'), value: 'guide' },
              { label: __('Étude de cas', 'boss-seo'), value: 'case-study' },
            ]}
            onChange={(value) => updateProperty('contentType', value)}
          />
        </CardBody>
      </Card>
      
      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="admin-customizer" className="boss-mr-2" />
            {__('Ton et style', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <p className="boss-text-boss-gray boss-mb-4">
            {__('Choisissez le ton et le style d\'écriture pour votre contenu. Cela influencera la façon dont l\'IA génère le texte.', 'boss-seo')}
          </p>
          
          <SelectControl
            label={__('Ton', 'boss-seo')}
            value={data.tone}
            options={[
              { label: __('Professionnel', 'boss-seo'), value: 'professional' },
              { label: __('Décontracté', 'boss-seo'), value: 'casual' },
              { label: __('Informatif', 'boss-seo'), value: 'informative' },
              { label: __('Persuasif', 'boss-seo'), value: 'persuasive' },
              { label: __('Éducatif', 'boss-seo'), value: 'educational' },
              { label: __('Enthousiaste', 'boss-seo'), value: 'enthusiastic' },
              { label: __('Formel', 'boss-seo'), value: 'formal' },
              { label: __('Conversationnel', 'boss-seo'), value: 'conversational' },
            ]}
            onChange={(value) => updateProperty('tone', value)}
          />
        </CardBody>
      </Card>
      
      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="groups" className="boss-mr-2" />
            {__('Public cible', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <p className="boss-text-boss-gray boss-mb-4">
            {__('Décrivez votre public cible ou sélectionnez un persona prédéfini. Cela aidera l\'IA à adapter le contenu à votre audience.', 'boss-seo')}
          </p>
          
          <div className="boss-mb-4">
            <SelectControl
              label={__('Sélectionner un persona', 'boss-seo')}
              value={selectedPersona}
              options={[
                { label: __('Sélectionner...', 'boss-seo'), value: '' },
                { label: __('Professionnel B2B', 'boss-seo'), value: 'Professionnel B2B' },
                { label: __('Consommateur', 'boss-seo'), value: 'Consommateur' },
                { label: __('Étudiant', 'boss-seo'), value: 'Étudiant' },
                { label: __('Parent', 'boss-seo'), value: 'Parent' },
                { label: __('Senior', 'boss-seo'), value: 'Senior' },
              ]}
              onChange={setSelectedPersona}
            />
            
            <Button
              isSecondary
              onClick={applyPersona}
              disabled={!selectedPersona}
              className="boss-mt-2"
            >
              {__('Appliquer ce persona', 'boss-seo')}
            </Button>
          </div>
          
          <TextareaControl
            label={__('Description du public cible', 'boss-seo')}
            help={__('Décrivez votre public cible en détail : âge, intérêts, niveau de connaissance, besoins, etc.', 'boss-seo')}
            value={data.audience}
            onChange={(value) => updateProperty('audience', value)}
            rows={5}
          />
        </CardBody>
      </Card>
    </div>
  );
};

export default StepObjective;
