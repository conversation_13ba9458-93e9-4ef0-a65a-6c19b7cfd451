<?php
/**
 * Plugin Name: Boss SEO
 * Plugin URI: https://bossseo.com
 * Description: Un plugin SEO avancé avec intelligence artificielle pour WordPress
 * Version: 1.1.0
 * Author: Boss SEO Team
 * Author URI: https://bossseo.com
 * Text Domain: boss-seo
 * Domain Path: /languages
 */

// Si ce fichier est appelé directement, on sort.
if (!defined('WPINC')) {
    die;
}

// Définition des constantes
define('BOSS_SEO_VERSION', '1.1.0');
define('BOSS_SEO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BOSS_SEO_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * La fonction exécutée lors de l'activation du plugin.
 */
function activate_boss_seo() {
    require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-seo-activator.php';
    Boss_SEO_Activator::activate();
}

/**
 * La fonction exécutée lors de la désactivation du plugin.
 */
function deactivate_boss_seo() {
    require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-seo-deactivator.php';
    Boss_SEO_Deactivator::deactivate();
}

register_activation_hook(__FILE__, 'activate_boss_seo');
register_deactivation_hook(__FILE__, 'deactivate_boss_seo');

/**
 * Ajouter le menu admin.
 */
function boss_seo_add_admin_menu() {
    add_menu_page(
        'Boss SEO',
        'Boss SEO',
        'manage_options',
        'boss-seo',
        'boss_seo_admin_page',
        'dashicons-search',
        30
    );
}

/**
 * Page d'administration.
 */
function boss_seo_admin_page() {
    ?>
    <div class="wrap">
        <h1>Boss SEO</h1>
        <div class="notice notice-success">
            <p><strong>Boss SEO est activé avec succès !</strong></p>
        </div>
        <div class="card">
            <h2>Informations du Plugin</h2>
            <p><strong>Version:</strong> <?php echo BOSS_SEO_VERSION; ?></p>
            <p><strong>Statut:</strong> Actif</p>
            <p><strong>Répertoire:</strong> <?php echo BOSS_SEO_PLUGIN_DIR; ?></p>
        </div>
        <div class="card">
            <h2>Prochaines Étapes</h2>
            <p>Le plugin est maintenant actif. Les fonctionnalités complètes seront chargées progressivement.</p>
            <ul>
                <li>✅ Plugin activé</li>
                <li>✅ Menu admin créé</li>
                <li>🔄 Modules en cours de chargement...</li>
            </ul>
        </div>
    </div>
    <?php
}

/**
 * Initialisation du plugin.
 */
function run_boss_seo() {
    // Ajouter le menu admin
    add_action( 'admin_menu', 'boss_seo_add_admin_menu' );

    // Ajouter un notice d'activation
    add_action( 'admin_notices', 'boss_seo_activation_notice' );
}

/**
 * Notice d'activation.
 */
function boss_seo_activation_notice() {
    if ( get_transient( 'boss_seo_activation_notice' ) ) {
        ?>
        <div class="notice notice-success is-dismissible">
            <p><strong>Boss SEO</strong> a été activé avec succès ! <a href="<?php echo admin_url('admin.php?page=boss-seo'); ?>">Voir les paramètres</a></p>
        </div>
        <?php
        delete_transient( 'boss_seo_activation_notice' );
    }
}

// Démarrer le plugin
run_boss_seo();
