<?php
/**
 * Classe API unifiée pour le module E-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

// Charger les méthodes utilitaires
require_once plugin_dir_path( __FILE__ ) . 'class-boss-ecommerce-api-helpers.php';

/**
 * Classe API unifiée pour le module E-commerce.
 *
 * Cette classe centralise toutes les routes API pour le module e-commerce
 * et corrige les incohérences entre frontend et backend.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Ecommerce_API {

    use Boss_Ecommerce_API_Helpers;

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance du gestionnaire WooCommerce.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_WooCommerce_Manager    $woo_manager    Gère l'intégration WooCommerce.
     */
    protected $woo_manager;

    /**
     * Instance de la classe d'IA.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_AI    $ai    Gère l'intégration avec l'IA.
     */
    protected $ai;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres.
     */
    protected $settings;

    /**
     * Instance du cache.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Ecommerce_Cache    $cache    Gère le cache e-commerce.
     */
    protected $cache;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string                   $plugin_name    Le nom du plugin.
     * @param    string                   $version        La version du plugin.
     * @param    Boss_Optimizer_AI        $ai             Instance de la classe d'IA.
     * @param    Boss_Optimizer_Settings  $settings       Instance de la classe de paramètres.
     */
    public function __construct( $plugin_name, $version, $ai, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->ai = $ai;
        $this->settings = $settings;

        // Charger les dépendances
        $this->load_dependencies();
    }

    /**
     * Charge les dépendances nécessaires.
     *
     * @since    1.2.0
     */
    private function load_dependencies() {
        // Charger le gestionnaire WooCommerce
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-woocommerce-manager.php';
        $this->woo_manager = Boss_WooCommerce_Manager::get_instance();

        // Charger le cache e-commerce
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-ecommerce-cache.php';
        $this->cache = Boss_Ecommerce_Cache::get_instance();
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Routes du dashboard
        $this->register_dashboard_routes();

        // Routes des produits
        $this->register_product_routes();

        // Routes d'optimisation
        $this->register_optimization_routes();

        // Routes de génération de contenu
        $this->register_generation_routes();

        // Routes des schémas
        $this->register_schema_routes();

        // Routes Google Shopping
        $this->register_shopping_routes();
    }

    /**
     * Enregistre les routes du dashboard.
     *
     * @since    1.2.0
     */
    private function register_dashboard_routes() {
        // Dashboard principal
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/dashboard',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_dashboard_data' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Statistiques du dashboard
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/dashboard/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_dashboard_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Produits les plus performants
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/dashboard/top-products',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_top_products' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Catégories les plus performantes
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/dashboard/top-categories',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_top_categories' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Enregistre les routes des produits.
     *
     * @since    1.2.0
     */
    private function register_product_routes() {
        // Liste des produits
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/products',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_products' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Produit spécifique
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/products/(?P<id>\d+)',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_product' ),
                'permission_callback' => array( $this, 'check_product_permissions' ),
            )
        );

        // Mise à jour d'un produit
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/products/(?P<id>\d+)',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'update_product' ),
                'permission_callback' => array( $this, 'check_product_edit_permissions' ),
            )
        );
    }

    /**
     * Enregistre les routes d'optimisation.
     *
     * @since    1.2.0
     */
    private function register_optimization_routes() {
        // Optimisation d'un produit
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/optimize-product',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'optimize_product' ),
                'permission_callback' => array( $this, 'check_product_edit_permissions' ),
            )
        );

        // Optimisation en masse
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/bulk-optimize-products',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'bulk_optimize_products' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Analyse d'un produit
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/analyze-product',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'analyze_product' ),
                'permission_callback' => array( $this, 'check_product_permissions' ),
            )
        );

        // Analyse de tous les produits
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/analyze/all',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'analyze_all_products' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de base.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Vérifie les permissions pour un produit spécifique.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   bool                          True si l'utilisateur a les permissions, false sinon.
     */
    public function check_product_permissions( $request ) {
        $product_id = $request->get_param( 'id' );

        if ( empty( $product_id ) ) {
            $product_id = $request->get_param( 'product_id' );
        }

        if ( empty( $product_id ) ) {
            return current_user_can( 'manage_options' );
        }

        return current_user_can( 'read_post', $product_id ) || current_user_can( 'manage_options' );
    }

    /**
     * Vérifie les permissions d'édition pour un produit spécifique.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   bool                          True si l'utilisateur a les permissions, false sinon.
     */
    public function check_product_edit_permissions( $request ) {
        $product_id = $request->get_param( 'id' );

        if ( empty( $product_id ) ) {
            $product_id = $request->get_param( 'product_id' );
        }

        if ( empty( $product_id ) ) {
            return current_user_can( 'manage_options' );
        }

        return current_user_can( 'edit_post', $product_id ) || current_user_can( 'manage_options' );
    }

    /**
     * Enregistre les routes de génération de contenu.
     *
     * @since    1.2.0
     */
    private function register_generation_routes() {
        // Génération de description
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/generate-description',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'generate_product_description' ),
                'permission_callback' => array( $this, 'check_product_edit_permissions' ),
            )
        );

        // Application du contenu généré
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/apply-generated-content',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'apply_generated_content' ),
                'permission_callback' => array( $this, 'check_product_edit_permissions' ),
            )
        );
    }

    /**
     * Enregistre les routes des schémas.
     *
     * @since    1.2.0
     */
    private function register_schema_routes() {
        // Génération de schéma
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/generate-schema',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'generate_product_schema' ),
                'permission_callback' => array( $this, 'check_product_edit_permissions' ),
            )
        );

        // Sauvegarde de schéma
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/save-schema',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'save_product_schema' ),
                'permission_callback' => array( $this, 'check_product_edit_permissions' ),
            )
        );
    }

    /**
     * Enregistre les routes Google Shopping.
     *
     * @since    1.2.0
     */
    private function register_shopping_routes() {
        // Liste des flux
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/shopping-feeds',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_shopping_feeds' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'create_shopping_feed' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Flux spécifique
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/shopping-feeds/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_shopping_feed' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'update_shopping_feed' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_shopping_feed' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );
    }

    /**
     * Récupère les données du dashboard e-commerce.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_dashboard_data( $request ) {
        try {
            // Vérifier si WooCommerce est disponible
            if ( ! $this->woo_manager->is_woocommerce_active() ) {
                return new WP_Error(
                    'woocommerce_not_available',
                    __( 'WooCommerce n\'est pas installé ou activé.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            // Vérifier le cache
            $cache_key = 'dashboard_data';
            $cached_data = $this->cache->get( $cache_key );

            if ( $cached_data !== false ) {
                return rest_ensure_response( $cached_data );
            }

            // Récupérer les statistiques
            $stats = $this->woo_manager->get_product_stats();

            // Récupérer l'activité récente
            $recent_activity = $this->woo_manager->get_recent_activity();

            // Récupérer les performances
            $performance = $this->woo_manager->get_performance_data();

            $data = array(
                'stats' => $stats,
                'recent_activity' => $recent_activity,
                'performance' => $performance,
                'timestamp' => current_time( 'timestamp' )
            );

            // Mettre en cache pour 5 minutes
            $this->cache->set( $cache_key, $data, 300 );

            return rest_ensure_response( $data );

        } catch ( Exception $e ) {
            return new WP_Error(
                'dashboard_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère les statistiques du dashboard.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_dashboard_stats( $request ) {
        try {
            if ( ! $this->woo_manager->is_woocommerce_active() ) {
                return new WP_Error(
                    'woocommerce_not_available',
                    __( 'WooCommerce n\'est pas installé ou activé.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            $period = $request->get_param( 'period' ) ?: '30days';
            $cache_key = "dashboard_stats_{$period}";

            $cached_data = $this->cache->get( $cache_key );
            if ( $cached_data !== false ) {
                return rest_ensure_response( $cached_data );
            }

            $stats = $this->woo_manager->get_product_stats();
            $this->cache->set( $cache_key, $stats, 600 ); // 10 minutes

            return rest_ensure_response( array( 'stats' => $stats ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'stats_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère les produits les plus performants.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_top_products( $request ) {
        try {
            if ( ! $this->woo_manager->is_woocommerce_active() ) {
                return new WP_Error(
                    'woocommerce_not_available',
                    __( 'WooCommerce n\'est pas installé ou activé.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            $period = $request->get_param( 'period' ) ?: '30days';
            $cache_key = "top_products_{$period}";

            $cached_data = $this->cache->get( $cache_key );
            if ( $cached_data !== false ) {
                return rest_ensure_response( $cached_data );
            }

            // Récupérer les produits avec les meilleurs scores SEO
            $products = wc_get_products( array(
                'limit' => 10,
                'status' => 'publish',
                'orderby' => 'date',
                'order' => 'DESC'
            ) );

            $top_products = array();
            foreach ( $products as $product ) {
                $score = $this->woo_manager->calculate_seo_score( $product );
                $categories = wp_get_post_terms( $product->get_id(), 'product_cat' );
                $category_name = ! empty( $categories ) ? $categories[0]->name : __( 'Non catégorisé', 'boss-seo' );

                $top_products[] = array(
                    'id' => $product->get_id(),
                    'name' => $product->get_name(),
                    'category' => $category_name,
                    'score' => $score,
                    'clicks' => rand( 500, 2000 ), // Simulé pour l'instant
                    'conversions' => rand( 20, 100 ) // Simulé pour l'instant
                );
            }

            // Trier par score décroissant
            usort( $top_products, function( $a, $b ) {
                return $b['score'] - $a['score'];
            } );

            $data = array( 'products' => array_slice( $top_products, 0, 5 ) );
            $this->cache->set( $cache_key, $data, 600 ); // 10 minutes

            return rest_ensure_response( $data );

        } catch ( Exception $e ) {
            return new WP_Error(
                'top_products_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère les catégories les plus performantes.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_top_categories( $request ) {
        try {
            if ( ! $this->woo_manager->is_woocommerce_active() ) {
                return new WP_Error(
                    'woocommerce_not_available',
                    __( 'WooCommerce n\'est pas installé ou activé.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            $period = $request->get_param( 'period' ) ?: '30days';
            $cache_key = "top_categories_{$period}";

            $cached_data = $this->cache->get( $cache_key );
            if ( $cached_data !== false ) {
                return rest_ensure_response( $cached_data );
            }

            // Récupérer les catégories de produits
            $categories = get_terms( array(
                'taxonomy' => 'product_cat',
                'hide_empty' => true,
                'number' => 10
            ) );

            $top_categories = array();
            foreach ( $categories as $category ) {
                $products_count = $category->count;

                $top_categories[] = array(
                    'name' => $category->name,
                    'clicks' => rand( 1000, 5000 ), // Simulé pour l'instant
                    'conversions' => rand( 50, 300 ), // Simulé pour l'instant
                    'conversionRate' => rand( 500, 800 ) / 100 // Simulé pour l'instant
                );
            }

            // Trier par clics décroissants
            usort( $top_categories, function( $a, $b ) {
                return $b['clicks'] - $a['clicks'];
            } );

            $data = array( 'categories' => array_slice( $top_categories, 0, 5 ) );
            $this->cache->set( $cache_key, $data, 600 ); // 10 minutes

            return rest_ensure_response( $data );

        } catch ( Exception $e ) {
            return new WP_Error(
                'top_categories_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère la liste des produits.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_products( $request ) {
        try {
            if ( ! $this->woo_manager->is_woocommerce_active() ) {
                return new WP_Error(
                    'woocommerce_not_available',
                    __( 'WooCommerce n\'est pas installé ou activé.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            if ( ! $this->woo_manager->has_products() ) {
                return new WP_Error(
                    'no_products_available',
                    __( 'Aucun produit n\'est disponible dans WooCommerce.', 'boss-seo' ),
                    array( 'status' => 404 )
                );
            }

            // Paramètres de la requête
            $page = max( 1, intval( $request->get_param( 'page' ) ?: 1 ) );
            $per_page = max( 1, min( 100, intval( $request->get_param( 'per_page' ) ?: 20 ) ) );
            $category = $request->get_param( 'category' );
            $status = $request->get_param( 'status' );
            $search = $request->get_param( 'search' );

            $cache_key = "products_" . md5( serialize( array( $page, $per_page, $category, $status, $search ) ) );
            $cached_data = $this->cache->get( $cache_key );

            if ( $cached_data !== false ) {
                return rest_ensure_response( $cached_data );
            }

            // Arguments pour WooCommerce
            $args = array(
                'limit' => $per_page,
                'offset' => ( $page - 1 ) * $per_page,
                'status' => 'publish',
                'orderby' => 'date',
                'order' => 'DESC'
            );

            // Filtrer par catégorie
            if ( ! empty( $category ) && $category !== 'all' ) {
                $args['category'] = array( $category );
            }

            // Filtrer par recherche
            if ( ! empty( $search ) ) {
                $args['s'] = $search;
            }

            // Récupérer les produits
            $products = wc_get_products( $args );
            $total_products = wc_get_products( array_merge( $args, array( 'limit' => -1, 'offset' => 0, 'return' => 'ids' ) ) );
            $total = count( $total_products );

            $formatted_products = array();
            foreach ( $products as $product ) {
                $score = $this->woo_manager->calculate_seo_score( $product );
                $categories = wp_get_post_terms( $product->get_id(), 'product_cat' );
                $category_name = ! empty( $categories ) ? $categories[0]->name : __( 'Non catégorisé', 'boss-seo' );
                $category_id = ! empty( $categories ) ? $categories[0]->term_id : 0;

                // Déterminer le statut basé sur le score
                if ( $score >= 80 ) {
                    $product_status = 'optimized';
                } elseif ( $score >= 60 ) {
                    $product_status = 'needs_attention';
                } else {
                    $product_status = 'critical';
                }

                // Générer les problèmes basés sur le score
                $issues = $this->get_product_issues( $product, $score );

                $formatted_products[] = array(
                    'id' => $product->get_id(),
                    'name' => $product->get_name(),
                    'category' => $category_name,
                    'categoryId' => $category_id,
                    'price' => $product->get_price(),
                    'stock' => $product->get_stock_quantity(),
                    'score' => $score,
                    'status' => $product_status,
                    'issues' => $issues,
                    'hasSchema' => $this->has_product_schema( $product->get_id() ),
                    'lastUpdated' => $product->get_date_modified()->format( 'Y-m-d' )
                );
            }

            // Filtrer par statut si demandé
            if ( ! empty( $status ) && $status !== 'all' ) {
                $formatted_products = array_filter( $formatted_products, function( $product ) use ( $status ) {
                    return $product['status'] === $status;
                } );
                $formatted_products = array_values( $formatted_products ); // Réindexer
            }

            $data = array(
                'products' => $formatted_products,
                'total_items' => $total,
                'total_pages' => ceil( $total / $per_page ),
                'current_page' => $page,
                'per_page' => $per_page
            );

            $this->cache->set( $cache_key, $data, 300 ); // 5 minutes

            return rest_ensure_response( $data );

        } catch ( Exception $e ) {
            return new WP_Error(
                'products_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère un produit spécifique.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_product( $request ) {
        try {
            $product_id = intval( $request->get_param( 'id' ) );

            if ( empty( $product_id ) ) {
                return new WP_Error(
                    'invalid_product_id',
                    __( 'ID de produit invalide.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            if ( ! $this->woo_manager->is_woocommerce_active() ) {
                return new WP_Error(
                    'woocommerce_not_available',
                    __( 'WooCommerce n\'est pas installé ou activé.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            $cache_key = "product_{$product_id}";
            $cached_data = $this->cache->get( $cache_key );

            if ( $cached_data !== false ) {
                return rest_ensure_response( $cached_data );
            }

            $product = wc_get_product( $product_id );

            if ( ! $product ) {
                return new WP_Error(
                    'product_not_found',
                    __( 'Produit non trouvé.', 'boss-seo' ),
                    array( 'status' => 404 )
                );
            }

            $score = $this->woo_manager->calculate_seo_score( $product );
            $categories = wp_get_post_terms( $product->get_id(), 'product_cat' );
            $category_name = ! empty( $categories ) ? $categories[0]->name : __( 'Non catégorisé', 'boss-seo' );
            $category_id = ! empty( $categories ) ? $categories[0]->term_id : 0;

            // Déterminer le statut basé sur le score
            if ( $score >= 80 ) {
                $product_status = 'optimized';
            } elseif ( $score >= 60 ) {
                $product_status = 'needs_attention';
            } else {
                $product_status = 'critical';
            }

            $issues = $this->get_product_issues( $product, $score );
            $images = $this->get_product_images( $product );

            $data = array(
                'product' => array(
                    'id' => $product->get_id(),
                    'name' => $product->get_name(),
                    'category' => $category_name,
                    'categoryId' => $category_id,
                    'price' => $product->get_price(),
                    'stock' => $product->get_stock_quantity(),
                    'score' => $score,
                    'status' => $product_status,
                    'issues' => $issues,
                    'hasSchema' => $this->has_product_schema( $product->get_id() ),
                    'lastUpdated' => $product->get_date_modified()->format( 'Y-m-d' ),
                    'description' => $product->get_description(),
                    'short_description' => $product->get_short_description(),
                    'images' => $images,
                    'meta' => array(
                        'title' => get_post_meta( $product->get_id(), '_yoast_wpseo_title', true ) ?: $product->get_name(),
                        'description' => get_post_meta( $product->get_id(), '_yoast_wpseo_metadesc', true ) ?: $product->get_short_description(),
                        'keywords' => get_post_meta( $product->get_id(), '_yoast_wpseo_focuskw', true ) ?: ''
                    )
                )
            );

            $this->cache->set( $cache_key, $data, 600 ); // 10 minutes

            return rest_ensure_response( $data );

        } catch ( Exception $e ) {
            return new WP_Error(
                'product_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Optimise un produit.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function optimize_product( $request ) {
        try {
            $product_id = intval( $request->get_param( 'product_id' ) );
            $settings = $request->get_param( 'settings' ) ?: array();

            if ( empty( $product_id ) ) {
                return new WP_Error(
                    'invalid_product_id',
                    __( 'ID de produit invalide.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            if ( ! $this->woo_manager->is_woocommerce_active() ) {
                return new WP_Error(
                    'woocommerce_not_available',
                    __( 'WooCommerce n\'est pas installé ou activé.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            $product = wc_get_product( $product_id );
            if ( ! $product ) {
                return new WP_Error(
                    'product_not_found',
                    __( 'Produit non trouvé.', 'boss-seo' ),
                    array( 'status' => 404 )
                );
            }

            // Optimiser le produit selon les paramètres
            $optimization_result = $this->perform_product_optimization( $product, $settings );

            // Invalider le cache du produit
            $this->cache->invalidate_product_cache( $product_id );

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Produit optimisé avec succès.', 'boss-seo' ),
                'product' => $optimization_result
            ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'optimization_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Optimise plusieurs produits en masse.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function bulk_optimize_products( $request ) {
        try {
            $product_ids = $request->get_param( 'product_ids' ) ?: array();
            $settings = $request->get_param( 'settings' ) ?: array();

            if ( empty( $product_ids ) || ! is_array( $product_ids ) ) {
                return new WP_Error(
                    'invalid_product_ids',
                    __( 'IDs de produits invalides.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            if ( ! $this->woo_manager->is_woocommerce_active() ) {
                return new WP_Error(
                    'woocommerce_not_available',
                    __( 'WooCommerce n\'est pas installé ou activé.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            $results = array();
            $success_count = 0;
            $error_count = 0;

            foreach ( $product_ids as $product_id ) {
                $product_id = intval( $product_id );
                $product = wc_get_product( $product_id );

                if ( ! $product ) {
                    $results[] = array(
                        'id' => $product_id,
                        'success' => false,
                        'message' => __( 'Produit non trouvé.', 'boss-seo' )
                    );
                    $error_count++;
                    continue;
                }

                try {
                    $optimization_result = $this->perform_product_optimization( $product, $settings );
                    $this->cache->invalidate_product_cache( $product_id );

                    $results[] = array(
                        'id' => $product_id,
                        'success' => true,
                        'message' => __( 'Optimisé avec succès.', 'boss-seo' ),
                        'product' => $optimization_result
                    );
                    $success_count++;

                } catch ( Exception $e ) {
                    $results[] = array(
                        'id' => $product_id,
                        'success' => false,
                        'message' => $e->getMessage()
                    );
                    $error_count++;
                }
            }

            return rest_ensure_response( array(
                'success' => true,
                'message' => sprintf(
                    __( '%d produits optimisés avec succès, %d erreurs.', 'boss-seo' ),
                    $success_count,
                    $error_count
                ),
                'summary' => array(
                    'total' => count( $product_ids ),
                    'success' => $success_count,
                    'errors' => $error_count
                ),
                'results' => $results
            ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'bulk_optimization_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Génère une description de produit avec l'IA.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function generate_product_description( $request ) {
        try {
            $product_id = intval( $request->get_param( 'product_id' ) );
            $settings = $request->get_param( 'settings' ) ?: array();

            if ( empty( $product_id ) ) {
                return new WP_Error(
                    'invalid_product_id',
                    __( 'ID de produit invalide.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            if ( ! $this->ai->is_available() ) {
                return new WP_Error(
                    'ai_not_available',
                    __( 'Les services d\'IA ne sont pas configurés.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            $product = wc_get_product( $product_id );
            if ( ! $product ) {
                return new WP_Error(
                    'product_not_found',
                    __( 'Produit non trouvé.', 'boss-seo' ),
                    array( 'status' => 404 )
                );
            }

            // Construire le prompt pour l'IA
            $prompt = $this->build_description_prompt( $product, $settings );

            // Générer le contenu avec l'IA
            $ai_result = $this->ai->generate_content( $prompt, array(
                'max_tokens' => 1000
            ) );

            if ( ! $ai_result['success'] ) {
                return new WP_Error(
                    'ai_generation_error',
                    $ai_result['message'],
                    array( 'status' => 500 )
                );
            }

            // Parser le contenu généré
            $generated_content = $this->parse_generated_content( $ai_result['content'] );

            return rest_ensure_response( array(
                'success' => true,
                'content' => $generated_content,
                'provider' => $ai_result['provider'],
                'model' => $ai_result['model']
            ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'generation_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère les flux Google Shopping via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_shopping_feeds( $request ) {
        try {
            // Vérifier si WooCommerce est actif
            if ( ! $this->woo_manager->is_woocommerce_active() ) {
                return new WP_Error(
                    'woocommerce_not_available',
                    __( 'WooCommerce n\'est pas installé ou activé.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            // Récupérer les flux depuis la base de données ou créer des données par défaut
            $feeds = $this->get_shopping_feeds_data();

            return rest_ensure_response( array(
                'success' => true,
                'feeds' => $feeds
            ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'shopping_feeds_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Crée un nouveau flux Google Shopping via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function create_shopping_feed( $request ) {
        try {
            $params = $request->get_params();
            $feed_data = isset( $params['feed'] ) ? $params['feed'] : array();

            // Vérifier les paramètres requis
            if ( empty( $feed_data['name'] ) ) {
                return new WP_Error(
                    'missing_feed_name',
                    __( 'Le nom du flux est requis.', 'boss-seo' ),
                    array( 'status' => 400 )
                );
            }

            // Créer le flux
            $result = $this->create_shopping_feed_data( $feed_data );

            if ( is_wp_error( $result ) ) {
                return $result;
            }

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Flux créé avec succès.', 'boss-seo' ),
                'feed' => $result
            ) );

        } catch ( Exception $e ) {
            return new WP_Error(
                'create_shopping_feed_error',
                $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère les données des flux Google Shopping.
     *
     * @since    1.2.0
     * @return   array    Les flux Google Shopping.
     */
    private function get_shopping_feeds_data() {
        // Pour l'instant, retourner des données simulées
        // Dans une version future, cela pourrait être récupéré depuis la base de données
        return array(
            array(
                'id' => 1,
                'name' => __( 'Flux principal', 'boss-seo' ),
                'status' => 'active',
                'products_count' => 150,
                'last_updated' => current_time( 'mysql' ),
                'url' => home_url( '/feed/google-shopping/' ),
            ),
            array(
                'id' => 2,
                'name' => __( 'Flux promotions', 'boss-seo' ),
                'status' => 'inactive',
                'products_count' => 45,
                'last_updated' => current_time( 'mysql' ),
                'url' => home_url( '/feed/google-shopping-promo/' ),
            ),
        );
    }

    /**
     * Crée un nouveau flux Google Shopping.
     *
     * @since    1.2.0
     * @param    array    $feed_data    Les données du flux.
     * @return   array|WP_Error         Le flux créé ou une erreur.
     */
    private function create_shopping_feed_data( $feed_data ) {
        // Simuler la création d'un flux
        $new_feed = array(
            'id' => rand( 100, 999 ),
            'name' => sanitize_text_field( $feed_data['name'] ),
            'status' => 'active',
            'products_count' => 0,
            'last_updated' => current_time( 'mysql' ),
            'url' => home_url( '/feed/google-shopping-' . sanitize_title( $feed_data['name'] ) . '/' ),
        );

        return $new_feed;
    }
}
