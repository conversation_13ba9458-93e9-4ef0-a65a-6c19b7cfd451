import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  TextControl,
  TextareaControl,
  SelectControl,
  Button,
  Dashicon,
  Tooltip,
  Popover
} from '@wordpress/components';

const SchemaPropertyField = ({ structure, properties, onChange, variables, path = '' }) => {
  // États
  const [showVariables, setShowVariables] = useState({});

  // Fonction pour afficher/masquer le sélecteur de variables
  const toggleVariableSelector = (propertyPath) => {
    setShowVariables({
      ...showVariables,
      [propertyPath]: !showVariables[propertyPath]
    });
  };

  // Fonction pour insérer une variable
  const insertVariable = (propertyPath, variableId) => {
    onChange(propertyPath, `{{${variableId}}}`);
    toggleVariableSelector(propertyPath);
  };

  // Fonction pour rendre un champ en fonction de son type
  const renderField = (key, value, currentPath) => {
    const propertyPath = currentPath ? `${currentPath}.${key}` : key;
    const currentValue = properties[key] || '';
    
    // Si c'est un objet imbriqué (pas un type simple)
    if (typeof value === 'object' && !value.type) {
      return (
        <div key={propertyPath} className="boss-mb-4 boss-border-l-2 boss-border-gray-300 boss-pl-4">
          <div className="boss-font-medium boss-mb-2">
            {key} {value['@type'] && <span className="boss-text-boss-gray boss-text-sm">({value['@type']})</span>}
          </div>
          <div className="boss-pl-2">
            {Object.entries(value).map(([subKey, subValue]) => {
              if (subKey === '@type') return null;
              return renderField(subKey, subValue, propertyPath);
            })}
          </div>
        </div>
      );
    }
    
    // C'est un type simple
    const fieldLabel = (
      <div className="boss-flex boss-items-center">
        <span>{key}</span>
        {value.required && (
          <span className="boss-text-boss-error boss-ml-1">*</span>
        )}
      </div>
    );
    
    switch (value.type) {
      case 'string':
        return (
          <div key={propertyPath} className="boss-mb-4 boss-relative">
            <TextControl
              label={fieldLabel}
              value={currentValue}
              onChange={(newValue) => onChange(propertyPath, newValue)}
            />
            <Tooltip text={__('Insérer une variable', 'boss-seo')}>
              <Button
                icon="admin-generic"
                className="boss-absolute boss-right-0 boss-top-7"
                onClick={() => toggleVariableSelector(propertyPath)}
              />
            </Tooltip>
            {showVariables[propertyPath] && (
              <Popover
                position="bottom right"
                onClose={() => toggleVariableSelector(propertyPath)}
                className="boss-variable-selector"
              >
                <div className="boss-p-3 boss-max-h-60 boss-overflow-y-auto">
                  <h3 className="boss-font-medium boss-mb-2">
                    {__('Variables disponibles', 'boss-seo')}
                  </h3>
                  <div className="boss-space-y-2">
                    {variables.map((variable) => (
                      <Button
                        key={variable.id}
                        isSecondary
                        isSmall
                        className="boss-w-full boss-justify-start boss-text-left"
                        onClick={() => insertVariable(propertyPath, variable.id)}
                      >
                        <span className="boss-font-medium">{variable.label}</span>
                        <span className="boss-text-xs boss-text-boss-gray boss-ml-2">
                          {variable.example}
                        </span>
                      </Button>
                    ))}
                  </div>
                </div>
              </Popover>
            )}
          </div>
        );
        
      case 'url':
        return (
          <div key={propertyPath} className="boss-mb-4 boss-relative">
            <TextControl
              label={fieldLabel}
              value={currentValue}
              onChange={(newValue) => onChange(propertyPath, newValue)}
              type="url"
            />
            <Tooltip text={__('Insérer une variable', 'boss-seo')}>
              <Button
                icon="admin-generic"
                className="boss-absolute boss-right-0 boss-top-7"
                onClick={() => toggleVariableSelector(propertyPath)}
              />
            </Tooltip>
            {showVariables[propertyPath] && (
              <Popover
                position="bottom right"
                onClose={() => toggleVariableSelector(propertyPath)}
                className="boss-variable-selector"
              >
                <div className="boss-p-3 boss-max-h-60 boss-overflow-y-auto">
                  <h3 className="boss-font-medium boss-mb-2">
                    {__('Variables disponibles', 'boss-seo')}
                  </h3>
                  <div className="boss-space-y-2">
                    {variables.map((variable) => (
                      <Button
                        key={variable.id}
                        isSecondary
                        isSmall
                        className="boss-w-full boss-justify-start boss-text-left"
                        onClick={() => insertVariable(propertyPath, variable.id)}
                      >
                        <span className="boss-font-medium">{variable.label}</span>
                        <span className="boss-text-xs boss-text-boss-gray boss-ml-2">
                          {variable.example}
                        </span>
                      </Button>
                    ))}
                  </div>
                </div>
              </Popover>
            )}
          </div>
        );
        
      case 'number':
        return (
          <div key={propertyPath} className="boss-mb-4">
            <TextControl
              label={fieldLabel}
              value={currentValue}
              onChange={(newValue) => onChange(propertyPath, newValue)}
              type="number"
            />
          </div>
        );
        
      case 'date':
        return (
          <div key={propertyPath} className="boss-mb-4 boss-relative">
            <TextControl
              label={fieldLabel}
              value={currentValue}
              onChange={(newValue) => onChange(propertyPath, newValue)}
              type="date"
            />
            <Tooltip text={__('Insérer une variable', 'boss-seo')}>
              <Button
                icon="admin-generic"
                className="boss-absolute boss-right-0 boss-top-7"
                onClick={() => toggleVariableSelector(propertyPath)}
              />
            </Tooltip>
            {showVariables[propertyPath] && (
              <Popover
                position="bottom right"
                onClose={() => toggleVariableSelector(propertyPath)}
                className="boss-variable-selector"
              >
                <div className="boss-p-3 boss-max-h-60 boss-overflow-y-auto">
                  <h3 className="boss-font-medium boss-mb-2">
                    {__('Variables disponibles', 'boss-seo')}
                  </h3>
                  <div className="boss-space-y-2">
                    {variables.filter(v => v.id.includes('date')).map((variable) => (
                      <Button
                        key={variable.id}
                        isSecondary
                        isSmall
                        className="boss-w-full boss-justify-start boss-text-left"
                        onClick={() => insertVariable(propertyPath, variable.id)}
                      >
                        <span className="boss-font-medium">{variable.label}</span>
                        <span className="boss-text-xs boss-text-boss-gray boss-ml-2">
                          {variable.example}
                        </span>
                      </Button>
                    ))}
                  </div>
                </div>
              </Popover>
            )}
          </div>
        );
        
      case 'datetime':
        return (
          <div key={propertyPath} className="boss-mb-4 boss-relative">
            <TextControl
              label={fieldLabel}
              value={currentValue}
              onChange={(newValue) => onChange(propertyPath, newValue)}
              type="datetime-local"
            />
            <Tooltip text={__('Insérer une variable', 'boss-seo')}>
              <Button
                icon="admin-generic"
                className="boss-absolute boss-right-0 boss-top-7"
                onClick={() => toggleVariableSelector(propertyPath)}
              />
            </Tooltip>
            {showVariables[propertyPath] && (
              <Popover
                position="bottom right"
                onClose={() => toggleVariableSelector(propertyPath)}
                className="boss-variable-selector"
              >
                <div className="boss-p-3 boss-max-h-60 boss-overflow-y-auto">
                  <h3 className="boss-font-medium boss-mb-2">
                    {__('Variables disponibles', 'boss-seo')}
                  </h3>
                  <div className="boss-space-y-2">
                    {variables.filter(v => v.id.includes('date')).map((variable) => (
                      <Button
                        key={variable.id}
                        isSecondary
                        isSmall
                        className="boss-w-full boss-justify-start boss-text-left"
                        onClick={() => insertVariable(propertyPath, variable.id)}
                      >
                        <span className="boss-font-medium">{variable.label}</span>
                        <span className="boss-text-xs boss-text-boss-gray boss-ml-2">
                          {variable.example}
                        </span>
                      </Button>
                    ))}
                  </div>
                </div>
              </Popover>
            )}
          </div>
        );
        
      default:
        return (
          <div key={propertyPath} className="boss-mb-4">
            <TextControl
              label={fieldLabel}
              value={currentValue}
              onChange={(newValue) => onChange(propertyPath, newValue)}
            />
          </div>
        );
    }
  };

  return (
    <div>
      {Object.entries(structure).map(([key, value]) => {
        if (key === '@type') return null;
        return renderField(key, value, path);
      })}
    </div>
  );
};

export default SchemaPropertyField;
