import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  TextControl,
  SelectControl,
  Notice,
  Spinner
} from '@wordpress/components';

// Importer les composants pour les graphiques
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Enregistrer les composants ChartJS
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const LicenseManager = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isActivating, setIsActivating] = useState(false);
  const [licenseKey, setLicenseKey] = useState('');
  const [licenseData, setLicenseData] = useState(null);
  const [aiCreditsData, setAiCreditsData] = useState(null);
  const [usageHistory, setUsageHistory] = useState([]);
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  
  // Charger les données
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour la licence
      const mockLicenseData = {
        status: 'active', // active, inactive, expired
        key: 'BOSS-SEO-PRO-XXXX-XXXX-XXXX',
        plan: 'Pro',
        expiryDate: '2024-12-31',
        registeredTo: '<EMAIL>',
        domains: ['example.com'],
        features: {
          aiOptimization: true,
          contentAnalysis: true,
          keywordResearch: true,
          technicalSeo: true,
          localSeo: true,
          ecommerce: true,
          reporting: true
        }
      };
      
      // Données fictives pour les crédits IA
      const mockAiCreditsData = {
        total: 10000,
        used: 6240,
        remaining: 3760,
        resetDate: '2023-07-31',
        usageByFeature: {
          contentOptimization: 3200,
          titleGeneration: 850,
          descriptionGeneration: 1100,
          altTextGeneration: 450,
          keywordResearch: 640
        }
      };
      
      // Données fictives pour l'historique d'utilisation
      const mockUsageHistory = [
        { date: '2023-01', credits: 420 },
        { date: '2023-02', credits: 580 },
        { date: '2023-03', credits: 490 },
        { date: '2023-04', credits: 750 },
        { date: '2023-05', credits: 1200 },
        { date: '2023-06', credits: 1800 },
        { date: '2023-07', credits: 1000 }
      ];
      
      setLicenseData(mockLicenseData);
      setAiCreditsData(mockAiCreditsData);
      setUsageHistory(mockUsageHistory);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  // Fonction pour activer la licence
  const handleActivateLicense = () => {
    if (!licenseKey) {
      alert(__('Veuillez entrer une clé de licence valide.', 'boss-seo'));
      return;
    }
    
    setIsActivating(true);
    
    // Simuler l'activation
    setTimeout(() => {
      setIsActivating(false);
      
      // Mettre à jour les données de licence
      setLicenseData({
        ...licenseData,
        status: 'active',
        key: licenseKey
      });
      
      setSuccessMessage(__('Licence activée avec succès !', 'boss-seo'));
      setShowSuccess(true);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1500);
  };
  
  // Fonction pour désactiver la licence
  const handleDeactivateLicense = () => {
    if (confirm(__('Êtes-vous sûr de vouloir désactiver votre licence ? Vous perdrez l\'accès aux fonctionnalités premium.', 'boss-seo'))) {
      setIsActivating(true);
      
      // Simuler la désactivation
      setTimeout(() => {
        setIsActivating(false);
        
        // Mettre à jour les données de licence
        setLicenseData({
          ...licenseData,
          status: 'inactive'
        });
        
        setSuccessMessage(__('Licence désactivée avec succès.', 'boss-seo'));
        setShowSuccess(true);
        
        // Masquer le message de succès après 3 secondes
        setTimeout(() => {
          setShowSuccess(false);
        }, 3000);
      }, 1500);
    }
  };
  
  // Fonction pour acheter des crédits supplémentaires
  const handleBuyCredits = (amount) => {
    window.open(`https://example.com/buy-credits?amount=${amount}`, '_blank');
  };
  
  // Configuration du graphique d'utilisation
  const usageChartData = {
    labels: usageHistory.map(item => item.date),
    datasets: [
      {
        label: __('Crédits IA utilisés', 'boss-seo'),
        data: usageHistory.map(item => item.credits),
        fill: true,
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 1)',
        tension: 0.4
      }
    ]
  };
  
  const usageChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        mode: 'index',
        intersect: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: __('Crédits utilisés', 'boss-seo')
        }
      },
      x: {
        title: {
          display: true,
          text: __('Mois', 'boss-seo')
        }
      }
    }
  };
  
  // Configuration du graphique de répartition
  const featureChartData = {
    labels: aiCreditsData ? Object.keys(aiCreditsData.usageByFeature).map(key => {
      switch (key) {
        case 'contentOptimization':
          return __('Optimisation de contenu', 'boss-seo');
        case 'titleGeneration':
          return __('Génération de titres', 'boss-seo');
        case 'descriptionGeneration':
          return __('Génération de descriptions', 'boss-seo');
        case 'altTextGeneration':
          return __('Génération de textes alt', 'boss-seo');
        case 'keywordResearch':
          return __('Recherche de mots-clés', 'boss-seo');
        default:
          return key;
      }
    }) : [],
    datasets: [
      {
        data: aiCreditsData ? Object.values(aiCreditsData.usageByFeature) : [],
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(139, 92, 246, 0.8)'
        ],
        borderWidth: 1
      }
    ]
  };
  
  const featureChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right'
      }
    }
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColorClass = (status) => {
    switch (status) {
      case 'active':
        return 'boss-text-green-600';
      case 'inactive':
        return 'boss-text-yellow-600';
      case 'expired':
        return 'boss-text-red-600';
      default:
        return 'boss-text-boss-gray';
    }
  };
  
  // Fonction pour obtenir le texte du statut
  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return __('Active', 'boss-seo');
      case 'inactive':
        return __('Inactive', 'boss-seo');
      case 'expired':
        return __('Expirée', 'boss-seo');
      default:
        return status;
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {successMessage}
            </Notice>
          )}
          
          <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
            {/* Panneau de gauche */}
            <div className="lg:boss-col-span-2">
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Gestion de licence', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  {licenseData.status === 'active' ? (
                    <div className="boss-space-y-4">
                      <div className="boss-p-4 boss-bg-green-50 boss-rounded-lg boss-mb-4">
                        <div className="boss-flex boss-items-start">
                          <div className="boss-flex-shrink-0 boss-mr-3">
                            <span className="dashicons dashicons-yes-alt boss-text-green-600"></span>
                          </div>
                          <div>
                            <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                              {__('Licence active', 'boss-seo')}
                            </h3>
                            <p className="boss-text-sm boss-text-boss-gray">
                              {__('Votre licence Boss SEO Pro est active et valide jusqu\'au', 'boss-seo')} {licenseData.expiryDate}.
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
                        <div>
                          <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                            {__('Clé de licence', 'boss-seo')}
                          </h3>
                          <p className="boss-text-boss-gray boss-font-mono">
                            {licenseData.key}
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                            {__('Plan', 'boss-seo')}
                          </h3>
                          <p className="boss-text-boss-gray">
                            Boss SEO {licenseData.plan}
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                            {__('Enregistrée pour', 'boss-seo')}
                          </h3>
                          <p className="boss-text-boss-gray">
                            {licenseData.registeredTo}
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                            {__('Domaines autorisés', 'boss-seo')}
                          </h3>
                          <p className="boss-text-boss-gray">
                            {licenseData.domains.join(', ')}
                          </p>
                        </div>
                      </div>
                      
                      <div className="boss-mt-6">
                        <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                          {__('Fonctionnalités incluses', 'boss-seo')}
                        </h3>
                        
                        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-2">
                          {Object.entries(licenseData.features).map(([feature, enabled]) => (
                            <div key={feature} className="boss-flex boss-items-center">
                              <span className={`dashicons ${enabled ? 'dashicons-yes-alt boss-text-green-600' : 'dashicons-no-alt boss-text-red-600'} boss-mr-2`}></span>
                              <span className="boss-text-boss-gray">
                                {feature === 'aiOptimization' && __('Optimisation IA', 'boss-seo')}
                                {feature === 'contentAnalysis' && __('Analyse de contenu', 'boss-seo')}
                                {feature === 'keywordResearch' && __('Recherche de mots-clés', 'boss-seo')}
                                {feature === 'technicalSeo' && __('SEO technique', 'boss-seo')}
                                {feature === 'localSeo' && __('SEO local', 'boss-seo')}
                                {feature === 'ecommerce' && __('E-commerce', 'boss-seo')}
                                {feature === 'reporting' && __('Rapports avancés', 'boss-seo')}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div className="boss-mt-6 boss-flex boss-justify-end">
                        <Button
                          isDestructive
                          onClick={handleDeactivateLicense}
                          isBusy={isActivating}
                          disabled={isActivating}
                        >
                          {__('Désactiver la licence', 'boss-seo')}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="boss-space-y-4">
                      <div className="boss-p-4 boss-bg-yellow-50 boss-rounded-lg boss-mb-4">
                        <div className="boss-flex boss-items-start">
                          <div className="boss-flex-shrink-0 boss-mr-3">
                            <span className="dashicons dashicons-warning boss-text-yellow-600"></span>
                          </div>
                          <div>
                            <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                              {licenseData.status === 'inactive' 
                                ? __('Licence inactive', 'boss-seo')
                                : __('Licence expirée', 'boss-seo')}
                            </h3>
                            <p className="boss-text-sm boss-text-boss-gray">
                              {licenseData.status === 'inactive'
                                ? __('Votre licence Boss SEO n\'est pas active. Veuillez l\'activer pour accéder à toutes les fonctionnalités.', 'boss-seo')
                                : __('Votre licence Boss SEO a expiré. Veuillez la renouveler pour continuer à utiliser toutes les fonctionnalités.', 'boss-seo')}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="boss-flex boss-space-x-2">
                        <TextControl
                          label={__('Clé de licence', 'boss-seo')}
                          value={licenseKey}
                          onChange={setLicenseKey}
                          placeholder="BOSS-SEO-PRO-XXXX-XXXX-XXXX"
                          className="boss-flex-1"
                        />
                        <div className="boss-flex boss-items-end boss-mb-2">
                          <Button
                            isPrimary
                            onClick={handleActivateLicense}
                            isBusy={isActivating}
                            disabled={isActivating || !licenseKey}
                          >
                            {__('Activer', 'boss-seo')}
                          </Button>
                        </div>
                      </div>
                      
                      <div className="boss-mt-4 boss-text-center">
                        <p className="boss-text-boss-gray boss-mb-4">
                          {__('Vous n\'avez pas encore de licence ?', 'boss-seo')}
                        </p>
                        <Button
                          isPrimary
                          href="https://example.com/buy-boss-seo"
                          target="_blank"
                        >
                          {__('Acheter Boss SEO Pro', 'boss-seo')}
                        </Button>
                      </div>
                    </div>
                  )}
                </CardBody>
              </Card>
              
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Historique d\'utilisation des crédits IA', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-h-64">
                    <Line data={usageChartData} options={usageChartOptions} />
                  </div>
                </CardBody>
              </Card>
            </div>
            
            {/* Panneau de droite */}
            <div>
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Crédits IA', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <div className="boss-flex boss-justify-center boss-mb-4">
                      <div className="boss-w-48 boss-h-48">
                        <Doughnut 
                          data={{
                            labels: [__('Utilisés', 'boss-seo'), __('Restants', 'boss-seo')],
                            datasets: [
                              {
                                data: [aiCreditsData.used, aiCreditsData.remaining],
                                backgroundColor: ['rgba(59, 130, 246, 0.8)', 'rgba(229, 231, 235, 0.8)'],
                                borderWidth: 0
                              }
                            ]
                          }}
                          options={{
                            cutout: '70%',
                            plugins: {
                              legend: {
                                position: 'bottom'
                              }
                            }
                          }}
                        />
                      </div>
                    </div>
                    
                    <div className="boss-text-center boss-mb-4">
                      <div className="boss-text-3xl boss-font-bold boss-text-boss-dark boss-mb-1">
                        {aiCreditsData.remaining}
                      </div>
                      <div className="boss-text-sm boss-text-boss-gray">
                        {__('crédits restants sur', 'boss-seo')} {aiCreditsData.total}
                      </div>
                      <div className="boss-text-xs boss-text-boss-gray boss-mt-1">
                        {__('Réinitialisation le', 'boss-seo')} {aiCreditsData.resetDate}
                      </div>
                    </div>
                    
                    <div className="boss-mt-6">
                      <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3 boss-text-center">
                        {__('Répartition par fonctionnalité', 'boss-seo')}
                      </h3>
                      
                      <div className="boss-h-48 boss-mb-4">
                        <Doughnut data={featureChartData} options={featureChartOptions} />
                      </div>
                    </div>
                    
                    <div className="boss-mt-6">
                      <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3 boss-text-center">
                        {__('Acheter des crédits supplémentaires', 'boss-seo')}
                      </h3>
                      
                      <div className="boss-grid boss-grid-cols-3 boss-gap-2">
                        <Button
                          isSecondary
                          onClick={() => handleBuyCredits(1000)}
                          className="boss-text-center"
                        >
                          <div>
                            <div className="boss-font-bold">1000</div>
                            <div className="boss-text-xs">9,99 €</div>
                          </div>
                        </Button>
                        
                        <Button
                          isPrimary
                          onClick={() => handleBuyCredits(5000)}
                          className="boss-text-center"
                        >
                          <div>
                            <div className="boss-font-bold">5000</div>
                            <div className="boss-text-xs">39,99 €</div>
                          </div>
                        </Button>
                        
                        <Button
                          isSecondary
                          onClick={() => handleBuyCredits(10000)}
                          className="boss-text-center"
                        >
                          <div>
                            <div className="boss-font-bold">10000</div>
                            <div className="boss-text-xs">69,99 €</div>
                          </div>
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
              
              <Card>
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Support', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <p className="boss-text-boss-gray">
                      {__('Besoin d\'aide avec votre licence ou vos crédits IA ?', 'boss-seo')}
                    </p>
                    
                    <div className="boss-grid boss-grid-cols-2 boss-gap-2">
                      <Button
                        isSecondary
                        href="https://example.com/documentation"
                        target="_blank"
                      >
                        {__('Documentation', 'boss-seo')}
                      </Button>
                      
                      <Button
                        isPrimary
                        href="https://example.com/support"
                        target="_blank"
                      >
                        {__('Contacter le support', 'boss-seo')}
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LicenseManager;
