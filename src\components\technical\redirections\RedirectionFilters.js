import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  SelectControl,
  TextControl
} from '@wordpress/components';

const RedirectionFilters = ({ 
  searchQuery, 
  setSearchQuery, 
  filterType, 
  setFilterType, 
  filterStatus, 
  setFilterStatus 
}) => {
  return (
    <Card className="boss-mb-6">
      <CardBody>
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4">
          <TextControl
            placeholder={__('Rechercher une redirection...', 'boss-seo')}
            value={searchQuery}
            onChange={setSearchQuery}
          />
          
          <SelectControl
            label=""
            value={filterType}
            options={[
              { label: __('Tous les types', 'boss-seo'), value: 'all' },
              { label: __('301 - Permanente', 'boss-seo'), value: '301' },
              { label: __('302 - Temporaire', 'boss-seo'), value: '302' },
              { label: __('307 - Temporaire', 'boss-seo'), value: '307' },
              { label: __('410 - Contenu supprimé', 'boss-seo'), value: '410' },
              { label: __('451 - Indisponible pour raisons légales', 'boss-seo'), value: '451' }
            ]}
            onChange={setFilterType}
          />
          
          <SelectControl
            label=""
            value={filterStatus}
            options={[
              { label: __('Tous les statuts', 'boss-seo'), value: 'all' },
              { label: __('Actives', 'boss-seo'), value: 'active' },
              { label: __('Inactives', 'boss-seo'), value: 'inactive' }
            ]}
            onChange={setFilterStatus}
          />
        </div>
      </CardBody>
    </Card>
  );
};

export default RedirectionFilters;
