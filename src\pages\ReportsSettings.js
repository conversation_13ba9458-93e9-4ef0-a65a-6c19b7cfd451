import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  TabPanel,
  Spinner
} from '@wordpress/components';

// Importer les composants
import ReportsLibrary from '../components/reports/ReportsLibrary';
import ReportCreator from '../components/reports/ReportCreator';
import ReportsHistory from '../components/reports/ReportsHistory';
import GeneralSettings from '../components/settings/GeneralSettings';
import ApiSettings from '../components/settings/ApiSettings';
import LicenseManager from '../components/settings/LicenseManager';
import UserPreferences from '../components/settings/UserPreferences';
import ExternalServicesSettings from '../components/settings/ExternalServicesSettings';

const ReportsSettings = () => {
  // États
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('reports-library');
  const [activeSettingsTab, setActiveSettingsTab] = useState('general');

  // Fonction pour gérer le changement d'onglet
  const handleTabChange = (tabName) => {
    setActiveTab(tabName);
  };

  // Fonction pour gérer le changement d'onglet des paramètres
  const handleSettingsTabChange = (tabName) => {
    setActiveSettingsTab(tabName);
  };

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Rapports et paramètres', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Gérez vos rapports SEO, configurez le plugin et personnalisez vos préférences.', 'boss-seo')}
          </p>
        </div>

        <TabPanel
          className="boss-mb-6"
          activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
          onSelect={handleTabChange}
          tabs={[
            {
              name: 'reports-library',
              title: __('Bibliothèque de rapports', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'report-creator',
              title: __('Créer un rapport', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'reports-history',
              title: __('Historique', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            },
            {
              name: 'settings',
              title: __('Paramètres', 'boss-seo'),
              className: 'boss-font-medium boss-px-4 boss-py-2'
            }
          ]}
        >
          {(tab) => {
            if (isLoading) {
              return (
                <Card>
                  <CardBody>
                    <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
                      <Spinner />
                    </div>
                  </CardBody>
                </Card>
              );
            }

            switch (tab.name) {
              case 'reports-library':
                return <ReportsLibrary />;
              case 'report-creator':
                return <ReportCreator />;
              case 'reports-history':
                return <ReportsHistory />;
              case 'settings':
                return (
                  <div>
                    <TabPanel
                      className="boss-mb-6"
                      activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
                      onSelect={handleSettingsTabChange}
                      tabs={[
                        {
                          name: 'general',
                          title: __('Général', 'boss-seo'),
                          className: 'boss-font-medium boss-px-4 boss-py-2'
                        },
                        {
                          name: 'api',
                          title: __('API', 'boss-seo'),
                          className: 'boss-font-medium boss-px-4 boss-py-2'
                        },
                        {
                          name: 'external',
                          title: __('Services externes', 'boss-seo'),
                          className: 'boss-font-medium boss-px-4 boss-py-2'
                        },
                        {
                          name: 'license',
                          title: __('Licence & Crédits', 'boss-seo'),
                          className: 'boss-font-medium boss-px-4 boss-py-2'
                        },
                        {
                          name: 'preferences',
                          title: __('Préférences', 'boss-seo'),
                          className: 'boss-font-medium boss-px-4 boss-py-2'
                        }
                      ]}
                    >
                      {(settingsTab) => {
                        switch (settingsTab.name) {
                          case 'general':
                            return <GeneralSettings />;
                          case 'api':
                            return <ApiSettings />;
                          case 'external':
                            return <ExternalServicesSettings />;
                          case 'license':
                            return <LicenseManager />;
                          case 'preferences':
                            return <UserPreferences />;
                          default:
                            return <GeneralSettings />;
                        }
                      }}
                    </TabPanel>
                  </div>
                );
              default:
                return <ReportsLibrary />;
            }
          }}
        </TabPanel>

        {/* Informations contextuelles */}
        <Card>
          <CardBody>
            <div className="boss-flex boss-items-start boss-gap-4">
              <div className="boss-flex-shrink-0 boss-w-8 boss-h-8 boss-bg-blue-100 boss-rounded-full boss-flex boss-items-center boss-justify-center">
                <span className="dashicons dashicons-info boss-text-blue-600"></span>
              </div>
              <div>
                <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-2">
                  {activeTab === 'reports-library' && __('À propos de la bibliothèque de rapports', 'boss-seo')}
                  {activeTab === 'report-creator' && __('À propos de la création de rapports', 'boss-seo')}
                  {activeTab === 'reports-history' && __('À propos de l\'historique des rapports', 'boss-seo')}
                  {activeTab === 'settings' && __('À propos des paramètres', 'boss-seo')}
                </h3>
                <div className="boss-text-boss-gray">
                  {activeTab === 'reports-library' && (
                    <p>
                      {__('La bibliothèque de rapports vous propose une collection de modèles prêts à l\'emploi pour analyser et présenter vos données SEO. Parcourez les différentes catégories, prévisualisez les rapports et générez-les en quelques clics.', 'boss-seo')}
                    </p>
                  )}
                  {activeTab === 'report-creator' && (
                    <p>
                      {__('Le créateur de rapports vous permet de concevoir des rapports personnalisés en sélectionnant les métriques, la période et le format qui vous conviennent. Vous pouvez également planifier l\'envoi automatique de rapports par email.', 'boss-seo')}
                    </p>
                  )}
                  {activeTab === 'reports-history' && (
                    <p>
                      {__('L\'historique des rapports conserve tous les rapports que vous avez générés. Vous pouvez les consulter, les télécharger à nouveau ou les partager avec vos collaborateurs.', 'boss-seo')}
                    </p>
                  )}
                  {activeTab === 'settings' && (
                    <p>
                      {__('Les paramètres vous permettent de configurer le plugin selon vos besoins, gérer vos clés API, vérifier l\'état de votre licence et personnaliser votre expérience utilisateur.', 'boss-seo')}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default ReportsSettings;
