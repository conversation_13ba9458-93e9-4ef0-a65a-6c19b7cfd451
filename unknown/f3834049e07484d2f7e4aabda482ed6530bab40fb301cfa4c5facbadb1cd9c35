<?php
/**
 * La classe principale du module Boss Optimizer.
 *
 * Cette classe sert de point d'entrée pour le module Boss Optimizer.
 * Elle initialise les sous-modules et enregistre les hooks nécessaires.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * La classe principale du module Boss Optimizer.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer {

    /**
     * Le loader qui est responsable de maintenir et d'enregistrer tous les hooks du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_SEO_Loader    $loader    Maintient et enregistre tous les hooks pour le plugin.
     */
    protected $loader;

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de gestion des contenus.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Content    $content    Gère les contenus.
     */
    protected $content;

    /**
     * Instance de la classe d'analyse SEO.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Analysis    $analysis    Effectue les analyses SEO.
     */
    protected $analysis;

    /**
     * Instance de la classe de recommandations.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Recommendations    $recommendations    Gère les recommandations.
     */
    protected $recommendations;

    /**
     * Instance de la classe d'intégration IA.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_AI    $ai    Gère l'intégration avec les services d'IA.
     */
    protected $ai;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Instance de la classe API.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_API    $api    Gère les endpoints API REST.
     */
    protected $api;

    /**
     * Instance de la classe de meta box.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Metabox    $metabox    Gère les meta boxes SEO.
     */
    protected $metabox;

    /**
     * Instance de la classe AJAX.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_SEO_Ajax    $ajax    Gère les requêtes AJAX.
     */
    protected $ajax;

    /**
     * Instance de la classe d'analyse technique.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Technical_Analysis    $technical_analysis    Gère l'analyse technique.
     */
    protected $technical_analysis;

    /**
     * Instance de la classe de performance.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Performance    $performance    Gère les performances.
     */
    protected $performance;

    /**
     * Instance de la classe de services externes.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_External_Services    $external_services    Gère les services externes.
     */
    protected $external_services;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     * @param    Boss_SEO_Loader    $loader   Le loader du plugin.
     */
    public function __construct( $plugin_name, $version, $loader ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->loader = $loader;

        $this->load_dependencies();
        $this->define_hooks();
    }

    /**
     * Charge les dépendances nécessaires pour ce module.
     *
     * @since    1.1.0
     * @access   private
     */
    private function load_dependencies() {
        /**
         * La classe responsable de la gestion des paramètres du module.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-settings.php';
        $this->settings = new Boss_Optimizer_Settings( $this->plugin_name );

        /**
         * La classe responsable de la gestion des contenus.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-content.php';
        $this->content = new Boss_Optimizer_Content( $this->plugin_name, $this->settings );

        /**
         * La classe responsable de l'analyse SEO.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-analysis.php';
        $this->analysis = new Boss_Optimizer_Analysis( $this->plugin_name, $this->settings );

        /**
         * La classe responsable des recommandations.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-recommendations.php';
        $this->recommendations = new Boss_Optimizer_Recommendations( $this->plugin_name, $this->settings );

        /**
         * La classe responsable de l'intégration avec les services d'IA.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-ai.php';
        $this->ai = new Boss_Optimizer_AI( $this->plugin_name, $this->settings );

        /**
         * La classe responsable de l'analyse technique.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-technical-analysis.php';
        $this->technical_analysis = Boss_Optimizer_Technical_Analysis::get_instance();

        /**
         * La classe responsable des performances.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-performance.php';
        $this->performance = Boss_Optimizer_Performance::get_instance();

        /**
         * La classe responsable des services externes.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-external-services.php';
        $this->external_services = Boss_Optimizer_External_Services::get_instance();

        /**
         * La classe responsable des endpoints API REST.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-api.php';
        $this->api = new Boss_Optimizer_API(
            $this->plugin_name,
            $this->version,
            $this->content,
            $this->analysis,
            $this->recommendations,
            $this->ai,
            $this->settings
        );

        /**
         * La classe responsable des meta boxes SEO.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-metabox.php';

        /**
         * La classe responsable des meta boxes SEO sécurisées (nouvelle version).
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-metabox-secure.php';

        /**
         * La classe responsable des meta boxes SEO avec interface à onglets (version moderne).
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-metabox-tabbed.php';

        // UTILISER UNIQUEMENT LA VERSION AVEC ONGLETS (version moderne et stable)
        $this->metabox = new Boss_Optimizer_Metabox_Tabbed( $this->plugin_name, $this->version, $this->settings );

        // Enregistrer les hooks de la metabox
        $this->metabox->register_hooks();

        /**
         * La classe responsable des requêtes AJAX.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/class-boss-seo-ajax.php';
        $this->ajax = new Boss_SEO_Ajax( $this->plugin_name, $this->version, $this->analysis, $this->recommendations, $this->ai );
    }

    /**
     * Définit les hooks liés à ce module.
     *
     * @since    1.1.0
     * @access   private
     */
    private function define_hooks() {
        // Enregistrement des endpoints API REST
        $this->loader->add_action( 'rest_api_init', $this->api, 'register_routes' );

        // NOTE: Les hooks des metaboxes sont maintenant enregistrés directement dans la classe metabox
        // pour éviter les doublons et les conflits

        // Enregistrement des hooks AJAX
        $this->loader->add_action( 'wp_ajax_boss_seo_analyze_content', $this->ajax, 'analyze_content' );
        $this->loader->add_action( 'wp_ajax_boss_seo_optimize_content', $this->ajax, 'optimize_content' );

        // Enregistrement des hooks pour l'analyse technique
        $this->loader->add_action( 'wp_ajax_boss_seo_run_technical_analysis', $this->technical_analysis, 'run_analysis' );
        $this->loader->add_action( 'wp_ajax_boss_seo_get_technical_analysis', $this->technical_analysis, 'get_analysis' );

        // Enregistrement des hooks pour les services externes
        $this->loader->add_action( 'wp_ajax_boss_optimizer_google_oauth_callback', $this->external_services, 'handle_google_oauth_callback' );
    }

    // Méthodes de détection supprimées - nous utilisons maintenant uniquement la version avec onglets
}
