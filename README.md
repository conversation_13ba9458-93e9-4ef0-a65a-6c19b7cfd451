# Boss SEO

Un plugin SEO avancé avec intelligence artificielle pour WordPress.

## Description

Boss SEO est un plugin WordPress qui offre des fonctionnalités avancées d'optimisation pour les moteurs de recherche, avec l'aide de l'intelligence artificielle. Il permet d'améliorer le référencement de votre site web grâce à des outils d'analyse et d'optimisation.

## Fonctionnalités

- **Tableau de bord** : Vue d'ensemble de l'état SEO de votre site
- **Boss Optimizer** : Optimisation IA de vos contenus et mots-clés
- **Analyse technique** : Audit technique complet de votre site
- **Optimisation de contenu** : Amélioration de vos contenus pour le SEO
- **Schémas structurés** : Ajout de données structurées à votre site
- **Intégrations analytics** : Connexion avec vos outils d'analyse
- **SEO local & e-commerce** : Optimisation de votre présence locale et e-commerce
- **Rapports** : Suivi de vos performances SEO

## Installation

1. Téléchargez le plugin et décompressez-le
2. Uploadez le dossier `boss-seo` dans le répertoire `/wp-content/plugins/` de votre site WordPress
3. Activez le plugin via le menu 'Extensions' dans WordPress
4. Accédez au tableau de bord Boss SEO depuis le menu principal de WordPress

## Développement

### Technologies utilisées

- **PHP** : Langage principal pour l'intégration avec WordPress
- **JavaScript** : Pour l'interface utilisateur interactive
- **Tailwind CSS** : Framework CSS pour le style
- **WordPress Components** : Composants natifs de WordPress pour l'interface

### Architecture du plugin

Le plugin suit une architecture modulaire pour faciliter la maintenance et l'extension :

```
boss-seo/
├── admin/              # Fonctionnalités d'administration
├── assets/             # Ressources compilées (CSS, JS, images)
├── includes/           # Classes principales du plugin
├── languages/          # Fichiers de traduction
└── boss-seo.php        # Point d'entrée du plugin
```

### Approche de développement

Pour maintenir un plugin léger et performant, nous avons adopté l'approche suivante :

1. Utilisation de Tailwind CSS pour le style, compilé en CSS statique
2. Utilisation des composants WordPress natifs via `wp-components`
3. Pas de dépendances NPM dans le plugin final
4. JavaScript compilé pour une meilleure performance

### Compilation des assets

Pour compiler les assets (CSS et JavaScript), nous utilisons un script de build temporaire :

```bash
# Rendre le script exécutable
chmod +x build.sh

# Exécuter le script
./build.sh
```

Ce script installe temporairement les dépendances NPM, compile les assets, puis nettoie les fichiers temporaires.

## Licence

Ce plugin est sous licence GPL v2 ou ultérieure.

## Crédits

Développé par l'équipe Boss SEO.
