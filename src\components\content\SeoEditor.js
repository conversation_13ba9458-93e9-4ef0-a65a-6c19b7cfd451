import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Dashicon,
  Notice,
  Panel,
  PanelBody,
  PanelRow,
  TextControl,
  TextareaControl,
  ToggleControl,
  Tooltip
} from '@wordpress/components';

/**
 * Composant pour afficher la prévisualisation SERP
 */
const SerpPreview = ({ title, description, url }) => {
  // Formater l'URL pour l'affichage SERP
  const formatUrl = (url) => {
    if (!url) return 'example.com › page';
    
    // Supprimer le protocole et les paramètres
    let formattedUrl = url.replace(/^https?:\/\//, '');
    formattedUrl = formattedUrl.split('?')[0];
    
    // Limiter la longueur
    if (formattedUrl.length > 40) {
      formattedUrl = formattedUrl.substring(0, 40) + '...';
    }
    
    return formattedUrl;
  };
  
  // Formater le titre pour l'affichage SERP
  const formatTitle = (title) => {
    if (!title) return __('Titre de la page', 'boss-seo');
    
    // Limiter la longueur
    if (title.length > 60) {
      return title.substring(0, 60) + '...';
    }
    
    return title;
  };
  
  // Formater la description pour l'affichage SERP
  const formatDescription = (description) => {
    if (!description) return __('Ajoutez une méta description pour afficher un aperçu de votre page dans les résultats de recherche. Une bonne méta description peut améliorer votre taux de clics.', 'boss-seo');
    
    // Limiter la longueur
    if (description.length > 160) {
      return description.substring(0, 160) + '...';
    }
    
    return description;
  };
  
  return (
    <div className="boss-mb-6">
      <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-2">
        {__('Prévisualisation SERP', 'boss-seo')}
      </h3>
      <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-bg-white">
        <div className="boss-text-xl boss-font-medium boss-text-blue-600 boss-mb-1">
          {formatTitle(title)}
        </div>
        <div className="boss-text-sm boss-text-green-700 boss-mb-1">
          {formatUrl(url)}
        </div>
        <div className="boss-text-sm boss-text-gray-600">
          {formatDescription(description)}
        </div>
      </div>
    </div>
  );
};

/**
 * Composant pour afficher le score SEO
 */
const SeoScoreIndicator = ({ score }) => {
  // Fonction pour obtenir la classe de couleur en fonction du score
  const getScoreColorClass = (score) => {
    if (score >= 90) return 'boss-text-green-600';
    if (score >= 70) return 'boss-text-yellow-600';
    if (score >= 50) return 'boss-text-orange-600';
    return 'boss-text-red-600';
  };

  // Fonction pour obtenir la couleur de fond en fonction du score
  const getScoreBackgroundClass = (score) => {
    if (score >= 90) return 'boss-bg-green-100';
    if (score >= 70) return 'boss-bg-yellow-100';
    if (score >= 50) return 'boss-bg-orange-100';
    return 'boss-bg-red-100';
  };

  // Fonction pour obtenir le texte d'évaluation en fonction du score
  const getScoreLabel = (score) => {
    if (score >= 90) return __('Excellent', 'boss-seo');
    if (score >= 70) return __('Bon', 'boss-seo');
    if (score >= 50) return __('Moyen', 'boss-seo');
    return __('À améliorer', 'boss-seo');
  };
  
  return (
    <div className="boss-flex boss-items-center boss-mb-4">
      <div className="boss-relative boss-w-16 boss-h-16 boss-mr-4">
        {/* Cercle de fond */}
        <svg className="boss-w-full boss-h-full boss-transform boss-rotate-[-90deg]" viewBox="0 0 100 100">
          <circle
            cx="50"
            cy="50"
            r="45"
            fill="transparent"
            stroke="#e5e7eb"
            strokeWidth="10"
          />
          {/* Cercle de progression */}
          <circle
            cx="50"
            cy="50"
            r="45"
            fill="transparent"
            stroke={
              score >= 90 ? '#10B981' :
              score >= 70 ? '#FBBF24' :
              score >= 50 ? '#F97316' :
              '#EF4444'
            }
            strokeWidth="10"
            strokeDasharray={`${2 * Math.PI * 45 * score / 100} ${2 * Math.PI * 45 * (1 - score / 100)}`}
            strokeLinecap="round"
            className="boss-transition-all boss-duration-1000 boss-ease-out"
          />
        </svg>
        {/* Score au centre */}
        <div className="boss-absolute boss-inset-0 boss-flex boss-flex-col boss-items-center boss-justify-center">
          <span className={`boss-text-2xl boss-font-bold ${getScoreColorClass(score)}`}>
            {score}
          </span>
        </div>
      </div>
      
      <div>
        <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-1">
          {__('Score SEO', 'boss-seo')}
        </h3>
        <div className={`boss-inline-block boss-px-3 boss-py-1 boss-rounded-full boss-text-sm boss-font-medium ${getScoreBackgroundClass(score)} ${getScoreColorClass(score)}`}>
          {getScoreLabel(score)}
        </div>
      </div>
    </div>
  );
};

/**
 * Composant pour afficher les suggestions d'optimisation
 */
const SeoSuggestions = ({ suggestions }) => {
  // Fonction pour obtenir l'icône en fonction du type de suggestion
  const getSuggestionIcon = (type) => {
    switch (type) {
      case 'error':
        return 'warning';
      case 'warning':
        return 'flag';
      case 'info':
        return 'info';
      case 'success':
        return 'yes-alt';
      default:
        return 'info';
    }
  };

  // Fonction pour obtenir la classe de couleur en fonction du type de suggestion
  const getSuggestionColorClass = (type) => {
    switch (type) {
      case 'error':
        return 'boss-text-red-600';
      case 'warning':
        return 'boss-text-yellow-600';
      case 'info':
        return 'boss-text-blue-600';
      case 'success':
        return 'boss-text-green-600';
      default:
        return 'boss-text-gray-600';
    }
  };

  // Fonction pour obtenir la classe de fond en fonction du type de suggestion
  const getSuggestionBgClass = (type) => {
    switch (type) {
      case 'error':
        return 'boss-bg-red-50';
      case 'warning':
        return 'boss-bg-yellow-50';
      case 'info':
        return 'boss-bg-blue-50';
      case 'success':
        return 'boss-bg-green-50';
      default:
        return 'boss-bg-gray-50';
    }
  };
  
  return (
    <div className="boss-mb-6">
      <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-2">
        {__('Suggestions d\'optimisation', 'boss-seo')}
      </h3>
      
      {suggestions.length === 0 ? (
        <div className="boss-bg-green-50 boss-border boss-border-green-200 boss-rounded-lg boss-p-4 boss-flex boss-items-center">
          <Dashicon icon="yes-alt" className="boss-text-green-600 boss-mr-3" />
          <p className="boss-text-green-800">
            {__('Aucune suggestion d\'amélioration. Votre contenu est bien optimisé !', 'boss-seo')}
          </p>
        </div>
      ) : (
        <div className="boss-space-y-3">
          {suggestions.map((suggestion) => (
            <div 
              key={suggestion.id} 
              className={`boss-border boss-rounded-lg boss-p-4 boss-flex boss-items-start ${getSuggestionBgClass(suggestion.type)}`}
            >
              <div className={`boss-mr-3 ${getSuggestionColorClass(suggestion.type)}`}>
                <Dashicon icon={getSuggestionIcon(suggestion.type)} />
              </div>
              <div className="boss-flex-1">
                <p className="boss-text-boss-dark boss-font-medium">
                  {suggestion.text}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

/**
 * Composant principal de l'éditeur SEO
 */
const SeoEditor = ({ content, setContent, seoScore, suggestions, savedKeywords }) => {
  // État pour gérer l'affichage du panneau latéral
  const [isPanelOpen, setIsPanelOpen] = useState(true);
  
  // Fonction pour mettre à jour le contenu
  const handleContentChange = (field, value) => {
    setContent({
      ...content,
      [field]: value
    });
  };
  
  return (
    <div className="boss-flex boss-flex-col md:boss-flex-row boss-gap-6">
      {/* Éditeur principal */}
      <div className="boss-flex-1">
        <Card className="boss-mb-6">
          <CardHeader>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Éditeur de contenu', 'boss-seo')}
            </h3>
          </CardHeader>
          <CardBody>
            <TextControl
              label={__('Titre', 'boss-seo')}
              value={content.title}
              onChange={(value) => handleContentChange('title', value)}
              className="boss-mb-4"
            />
            
            <TextControl
              label={__('Slug', 'boss-seo')}
              value={content.slug}
              onChange={(value) => handleContentChange('slug', value)}
              className="boss-mb-4"
            />
            
            <TextControl
              label={__('Mot-clé principal', 'boss-seo')}
              value={content.focusKeyword}
              onChange={(value) => handleContentChange('focusKeyword', value)}
              className="boss-mb-4"
            />
            
            <TextareaControl
              label={__('Méta description', 'boss-seo')}
              value={content.excerpt}
              onChange={(value) => handleContentChange('excerpt', value)}
              className="boss-mb-4"
              help={__('La méta description apparaît dans les résultats de recherche. Idéalement entre 120 et 160 caractères.', 'boss-seo')}
            />
            
            <TextareaControl
              label={__('Contenu', 'boss-seo')}
              value={content.content}
              onChange={(value) => handleContentChange('content', value)}
              className="boss-mb-4"
              rows={15}
            />
            
            <SerpPreview 
              title={content.title}
              description={content.excerpt}
              url={content.slug ? `https://example.com/${content.slug}/` : ''}
            />
          </CardBody>
          <CardFooter>
            <div className="boss-flex boss-justify-between">
              <Button
                isSecondary
              >
                {__('Enregistrer comme brouillon', 'boss-seo')}
              </Button>
              <Button
                isPrimary
              >
                {__('Publier', 'boss-seo')}
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
      
      {/* Panneau latéral d'analyse */}
      <div className="boss-w-full md:boss-w-80 boss-flex-shrink-0">
        <Card>
          <CardHeader className="boss-flex boss-justify-between boss-items-center">
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Analyse SEO', 'boss-seo')}
            </h3>
            <Button
              isSmall
              isSecondary
              icon={isPanelOpen ? 'arrow-right-alt2' : 'arrow-left-alt2'}
              onClick={() => setIsPanelOpen(!isPanelOpen)}
              label={isPanelOpen ? __('Réduire', 'boss-seo') : __('Développer', 'boss-seo')}
              showTooltip
            />
          </CardHeader>
          <CardBody>
            {isPanelOpen && (
              <>
                <SeoScoreIndicator score={seoScore.overall} />
                
                <div className="boss-mb-6">
                  <h4 className="boss-text-sm boss-font-medium boss-text-boss-gray boss-mb-2">
                    {__('Scores détaillés', 'boss-seo')}
                  </h4>
                  <div className="boss-space-y-2">
                    <div>
                      <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                        <span className="boss-text-sm boss-text-boss-dark">
                          {__('Titre', 'boss-seo')}
                        </span>
                        <span className="boss-text-sm boss-font-medium boss-text-boss-dark">
                          {seoScore.title}/100
                        </span>
                      </div>
                      <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-1.5">
                        <div 
                          className={`boss-h-1.5 boss-rounded-full ${
                            seoScore.title >= 90 ? 'boss-bg-green-500' :
                            seoScore.title >= 70 ? 'boss-bg-yellow-500' :
                            seoScore.title >= 50 ? 'boss-bg-orange-500' :
                            'boss-bg-red-500'
                          }`}
                          style={{ width: `${seoScore.title}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                        <span className="boss-text-sm boss-text-boss-dark">
                          {__('Contenu', 'boss-seo')}
                        </span>
                        <span className="boss-text-sm boss-font-medium boss-text-boss-dark">
                          {seoScore.content}/100
                        </span>
                      </div>
                      <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-1.5">
                        <div 
                          className={`boss-h-1.5 boss-rounded-full ${
                            seoScore.content >= 90 ? 'boss-bg-green-500' :
                            seoScore.content >= 70 ? 'boss-bg-yellow-500' :
                            seoScore.content >= 50 ? 'boss-bg-orange-500' :
                            'boss-bg-red-500'
                          }`}
                          style={{ width: `${seoScore.content}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                        <span className="boss-text-sm boss-text-boss-dark">
                          {__('Mots-clés', 'boss-seo')}
                        </span>
                        <span className="boss-text-sm boss-font-medium boss-text-boss-dark">
                          {seoScore.keywords}/100
                        </span>
                      </div>
                      <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-1.5">
                        <div 
                          className={`boss-h-1.5 boss-rounded-full ${
                            seoScore.keywords >= 90 ? 'boss-bg-green-500' :
                            seoScore.keywords >= 70 ? 'boss-bg-yellow-500' :
                            seoScore.keywords >= 50 ? 'boss-bg-orange-500' :
                            'boss-bg-red-500'
                          }`}
                          style={{ width: `${seoScore.keywords}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <SeoSuggestions suggestions={suggestions} />
                
                {/* Mots-clés enregistrés */}
                <div>
                  <h4 className="boss-text-sm boss-font-medium boss-text-boss-gray boss-mb-2">
                    {__('Mots-clés enregistrés', 'boss-seo')}
                  </h4>
                  
                  {savedKeywords.length === 0 ? (
                    <p className="boss-text-sm boss-text-boss-gray boss-italic">
                      {__('Aucun mot-clé enregistré. Utilisez l\'onglet "Recherche de mots-clés" pour en ajouter.', 'boss-seo')}
                    </p>
                  ) : (
                    <div className="boss-flex boss-flex-wrap boss-gap-2">
                      {savedKeywords.map((keyword, index) => (
                        <div 
                          key={index}
                          className="boss-bg-gray-100 boss-text-boss-dark boss-px-3 boss-py-1 boss-rounded-full boss-text-sm boss-flex boss-items-center"
                        >
                          <span>{keyword.text}</span>
                          <Button
                            isSmall
                            icon="plus"
                            label={__('Utiliser comme mot-clé principal', 'boss-seo')}
                            showTooltip
                            onClick={() => handleContentChange('focusKeyword', keyword.text)}
                            className="boss-ml-1 boss-p-0 boss-h-auto boss-min-w-0"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default SeoEditor;
