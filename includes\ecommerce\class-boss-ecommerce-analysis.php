<?php
/**
 * Classe pour l'analyse SEO e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour l'analyse SEO e-commerce.
 *
 * Cette classe gère l'analyse SEO e-commerce.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Ecommerce_Analysis {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_ecommerce_analysis_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_analyze_store', array( $this, 'ajax_analyze_store' ) );
        add_action( 'wp_ajax_boss_seo_get_store_analysis', array( $this, 'ajax_get_store_analysis' ) );
        add_action( 'wp_ajax_boss_seo_analyze_competitors', array( $this, 'ajax_analyze_competitors' ) );
        add_action( 'wp_ajax_boss_seo_get_competitors_analysis', array( $this, 'ajax_get_competitors_analysis' ) );
        add_action( 'wp_ajax_boss_seo_apply_recommendations', array( $this, 'ajax_apply_recommendations' ) );
        add_action( 'wp_ajax_boss_seo_generate_report', array( $this, 'ajax_generate_report' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/analyze-store',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'analyze_store' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/store-analysis',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_store_analysis' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/analyze-competitors',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'analyze_competitors' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/competitors-analysis',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_competitors_analysis' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/apply-recommendations',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'apply_recommendations' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/generate-report',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'generate_report' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour analyser la boutique.
     *
     * @since    1.2.0
     */
    public function ajax_analyze_store() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Analyser la boutique
        $result = $this->analyze_store_data();

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Boutique analysée avec succès.', 'boss-seo' ),
            'analysis' => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer l'analyse de la boutique.
     *
     * @since    1.2.0
     */
    public function ajax_get_store_analysis() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer l'analyse
        $analysis = $this->get_store_analysis_data();

        wp_send_json_success( array(
            'message'  => __( 'Analyse récupérée avec succès.', 'boss-seo' ),
            'analysis' => $analysis,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour analyser les concurrents.
     *
     * @since    1.2.0
     */
    public function ajax_analyze_competitors() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['competitors'] ) || ! is_array( $_POST['competitors'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Les concurrents sont requis.', 'boss-seo' ) ) );
        }

        $competitors = array_map( 'sanitize_text_field', $_POST['competitors'] );

        // Analyser les concurrents
        $result = $this->analyze_competitors_data( $competitors );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Concurrents analysés avec succès.', 'boss-seo' ),
            'analysis' => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer l'analyse des concurrents.
     *
     * @since    1.2.0
     */
    public function ajax_get_competitors_analysis() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer l'analyse
        $analysis = $this->get_competitors_analysis_data();

        wp_send_json_success( array(
            'message'  => __( 'Analyse récupérée avec succès.', 'boss-seo' ),
            'analysis' => $analysis,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour appliquer les recommandations.
     *
     * @since    1.2.0
     */
    public function ajax_apply_recommendations() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['recommendation_id'] ) || empty( $_POST['recommendation_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de la recommandation est requis.', 'boss-seo' ) ) );
        }

        $recommendation_id = sanitize_text_field( $_POST['recommendation_id'] );

        // Appliquer la recommandation
        $result = $this->apply_recommendation_data( $recommendation_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Recommandation appliquée avec succès.', 'boss-seo' ),
            'result'  => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour générer un rapport.
     *
     * @since    1.2.0
     */
    public function ajax_generate_report() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        $format = isset( $_POST['format'] ) ? sanitize_text_field( $_POST['format'] ) : 'pdf';

        // Générer le rapport
        $result = $this->generate_report_data( $format );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Rapport généré avec succès.', 'boss-seo' ),
            'report'  => $result,
        ) );
    }

    /**
     * Analyse la boutique via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function analyze_store( $request ) {
        $result = $this->analyze_store_data();

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message'  => __( 'Boutique analysée avec succès.', 'boss-seo' ),
            'analysis' => $result,
        ) );
    }

    /**
     * Récupère l'analyse de la boutique via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_store_analysis( $request ) {
        $analysis = $this->get_store_analysis_data();

        return rest_ensure_response( $analysis );
    }

    /**
     * Analyse les concurrents via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function analyze_competitors( $request ) {
        $params = $request->get_params();

        if ( ! isset( $params['competitors'] ) || ! is_array( $params['competitors'] ) ) {
            return new WP_Error( 'missing_competitors', __( 'Les concurrents sont requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $competitors = array_map( 'sanitize_text_field', $params['competitors'] );

        $result = $this->analyze_competitors_data( $competitors );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message'  => __( 'Concurrents analysés avec succès.', 'boss-seo' ),
            'analysis' => $result,
        ) );
    }

    /**
     * Récupère l'analyse des concurrents via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_competitors_analysis( $request ) {
        $analysis = $this->get_competitors_analysis_data();

        return rest_ensure_response( $analysis );
    }

    /**
     * Applique une recommandation via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function apply_recommendations( $request ) {
        $params = $request->get_params();

        if ( ! isset( $params['recommendation_id'] ) || empty( $params['recommendation_id'] ) ) {
            return new WP_Error( 'missing_recommendation_id', __( 'L\'ID de la recommandation est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $recommendation_id = sanitize_text_field( $params['recommendation_id'] );

        $result = $this->apply_recommendation_data( $recommendation_id );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Recommandation appliquée avec succès.', 'boss-seo' ),
            'result'  => $result,
        ) );
    }

    /**
     * Génère un rapport via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function generate_report( $request ) {
        $params = $request->get_params();

        $format = isset( $params['format'] ) ? sanitize_text_field( $params['format'] ) : 'pdf';

        $result = $this->generate_report_data( $format );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Rapport généré avec succès.', 'boss-seo' ),
            'report'  => $result,
        ) );
    }

    /**
     * Analyse la boutique.
     *
     * @since    1.2.0
     * @return   array|WP_Error    Les résultats de l'analyse ou une erreur.
     */
    private function analyze_store_data() {
        // Récupérer les produits
        $products = get_posts( array(
            'post_type'      => 'product',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
        ) );

        if ( empty( $products ) ) {
            return new WP_Error( 'no_products', __( 'Aucun produit trouvé.', 'boss-seo' ) );
        }

        // Initialiser les compteurs
        $total_products = count( $products );
        $optimized_products = 0;
        $critical_products = 0;
        $to_improve_products = 0;
        $total_score = 0;

        // Analyser chaque produit
        foreach ( $products as $product ) {
            $analysis = get_post_meta( $product->ID, '_boss_seo_analysis', true );

            if ( ! empty( $analysis ) && isset( $analysis['score'] ) ) {
                $score = $analysis['score'];
                $total_score += $score;

                if ( $score >= 80 ) {
                    $optimized_products++;
                } elseif ( $score < 50 ) {
                    $critical_products++;
                } else {
                    $to_improve_products++;
                }
            } else {
                // Si le produit n'a pas été analysé, le considérer comme à améliorer
                $to_improve_products++;
            }
        }

        // Calculer le score moyen
        $average_score = $total_products > 0 ? round( $total_score / $total_products ) : 0;

        // Analyser les catégories
        $categories = get_terms( array(
            'taxonomy'   => 'product_cat',
            'hide_empty' => false,
        ) );

        $category_stats = array();

        foreach ( $categories as $category ) {
            // Récupérer les produits de cette catégorie
            $category_products = get_posts( array(
                'post_type'      => 'product',
                'posts_per_page' => -1,
                'tax_query'      => array(
                    array(
                        'taxonomy' => 'product_cat',
                        'field'    => 'term_id',
                        'terms'    => $category->term_id,
                    ),
                ),
            ) );

            $category_total_products = count( $category_products );
            $category_total_score = 0;
            $category_analyzed_products = 0;

            foreach ( $category_products as $product ) {
                $analysis = get_post_meta( $product->ID, '_boss_seo_analysis', true );

                if ( ! empty( $analysis ) && isset( $analysis['score'] ) ) {
                    $category_total_score += $analysis['score'];
                    $category_analyzed_products++;
                }
            }

            $category_average_score = $category_analyzed_products > 0 ? round( $category_total_score / $category_analyzed_products ) : 0;

            $category_stats[] = array(
                'id'             => $category->term_id,
                'name'           => $category->name,
                'total_products' => $category_total_products,
                'analyzed_products' => $category_analyzed_products,
                'average_score'  => $category_average_score,
            );
        }

        // Générer des recommandations
        $recommendations = $this->generate_recommendations( $average_score, $category_stats );

        // Préparer les résultats
        $analysis = array(
            'score'                => $average_score,
            'total_products'       => $total_products,
            'optimized_products'   => $optimized_products,
            'critical_products'    => $critical_products,
            'to_improve_products'  => $to_improve_products,
            'category_stats'       => $category_stats,
            'recommendations'      => $recommendations,
            'date'                 => current_time( 'mysql' ),
        );

        // Enregistrer l'analyse
        update_option( $this->option_prefix . 'store_analysis', $analysis );

        return $analysis;
    }

    /**
     * Récupère l'analyse de la boutique.
     *
     * @since    1.2.0
     * @return   array    L'analyse de la boutique.
     */
    private function get_store_analysis_data() {
        $analysis = get_option( $this->option_prefix . 'store_analysis', array() );

        if ( empty( $analysis ) ) {
            // Si aucune analyse n'existe, en créer une
            $analysis = $this->analyze_store_data();

            if ( is_wp_error( $analysis ) ) {
                return array();
            }
        }

        return $analysis;
    }

    /**
     * Analyse les concurrents.
     *
     * @since    1.2.0
     * @param    array     $competitors    Les concurrents à analyser.
     * @return   array|WP_Error            Les résultats de l'analyse ou une erreur.
     */
    private function analyze_competitors_data( $competitors ) {
        if ( empty( $competitors ) ) {
            return new WP_Error( 'no_competitors', __( 'Aucun concurrent spécifié.', 'boss-seo' ) );
        }

        // Récupérer l'analyse de la boutique
        $store_analysis = $this->get_store_analysis_data();

        if ( empty( $store_analysis ) ) {
            return new WP_Error( 'no_store_analysis', __( 'Aucune analyse de boutique disponible.', 'boss-seo' ) );
        }

        // Simuler l'analyse des concurrents
        $competitors_analysis = array();

        foreach ( $competitors as $competitor ) {
            // Générer un score aléatoire pour la démonstration
            $score = rand( 40, 95 );

            $competitors_analysis[] = array(
                'url'   => $competitor,
                'score' => $score,
                'strengths' => $this->generate_competitor_strengths( $score ),
                'weaknesses' => $this->generate_competitor_weaknesses( $score ),
            );
        }

        // Préparer les résultats
        $analysis = array(
            'store_score'    => $store_analysis['score'],
            'competitors'    => $competitors_analysis,
            'date'           => current_time( 'mysql' ),
        );

        // Enregistrer l'analyse
        update_option( $this->option_prefix . 'competitors_analysis', $analysis );

        return $analysis;
    }

    /**
     * Récupère l'analyse des concurrents.
     *
     * @since    1.2.0
     * @return   array    L'analyse des concurrents.
     */
    private function get_competitors_analysis_data() {
        $analysis = get_option( $this->option_prefix . 'competitors_analysis', array() );

        return $analysis;
    }

    /**
     * Applique une recommandation.
     *
     * @since    1.2.0
     * @param    string    $recommendation_id    L'ID de la recommandation.
     * @return   array|WP_Error                  Le résultat de l'application ou une erreur.
     */
    private function apply_recommendation_data( $recommendation_id ) {
        // Récupérer l'analyse de la boutique
        $analysis = $this->get_store_analysis_data();

        if ( empty( $analysis ) || empty( $analysis['recommendations'] ) ) {
            return new WP_Error( 'no_recommendations', __( 'Aucune recommandation disponible.', 'boss-seo' ) );
        }

        // Trouver la recommandation
        $recommendation = null;

        foreach ( $analysis['recommendations'] as $rec ) {
            if ( $rec['id'] === $recommendation_id ) {
                $recommendation = $rec;
                break;
            }
        }

        if ( ! $recommendation ) {
            return new WP_Error( 'recommendation_not_found', __( 'Recommandation non trouvée.', 'boss-seo' ) );
        }

        // Simuler l'application de la recommandation
        $result = array(
            'id'          => $recommendation_id,
            'title'       => $recommendation['title'],
            'description' => $recommendation['description'],
            'applied'     => true,
            'date'        => current_time( 'mysql' ),
        );

        // Enregistrer l'application de la recommandation
        $applied_recommendations = get_option( $this->option_prefix . 'applied_recommendations', array() );
        $applied_recommendations[] = $result;
        update_option( $this->option_prefix . 'applied_recommendations', $applied_recommendations );

        return $result;
    }

    /**
     * Génère un rapport.
     *
     * @since    1.2.0
     * @param    string    $format    Le format du rapport.
     * @return   array|WP_Error       Le rapport ou une erreur.
     */
    private function generate_report_data( $format ) {
        // Récupérer l'analyse de la boutique
        $analysis = $this->get_store_analysis_data();

        if ( empty( $analysis ) ) {
            return new WP_Error( 'no_analysis', __( 'Aucune analyse disponible.', 'boss-seo' ) );
        }

        // Récupérer l'analyse des concurrents
        $competitors_analysis = $this->get_competitors_analysis_data();

        // Générer un nom de fichier
        $filename = 'boss-seo-ecommerce-report-' . date( 'Y-m-d' ) . '.' . $format;

        // Simuler la génération du rapport
        $report = array(
            'filename'     => $filename,
            'url'          => admin_url( 'admin-ajax.php?action=boss_seo_download_report&filename=' . $filename ),
            'format'       => $format,
            'date'         => current_time( 'mysql' ),
            'store_analysis' => $analysis,
            'competitors_analysis' => $competitors_analysis,
        );

        // Enregistrer le rapport
        $reports = get_option( $this->option_prefix . 'reports', array() );
        $reports[] = $report;
        update_option( $this->option_prefix . 'reports', $reports );

        return $report;
    }

    /**
     * Génère des recommandations.
     *
     * @since    1.2.0
     * @param    int      $score           Le score de la boutique.
     * @param    array    $category_stats  Les statistiques des catégories.
     * @return   array                     Les recommandations.
     */
    private function generate_recommendations( $score, $category_stats ) {
        $recommendations = array();

        // Recommandations basées sur le score global
        if ( $score < 50 ) {
            $recommendations[] = array(
                'id'          => 'optimize_critical_products',
                'title'       => __( 'Optimiser les produits critiques', 'boss-seo' ),
                'description' => __( 'Votre boutique contient de nombreux produits avec un score SEO critique. Optimisez en priorité ces produits pour améliorer votre score global.', 'boss-seo' ),
                'priority'    => 'high',
            );
        }

        if ( $score < 70 ) {
            $recommendations[] = array(
                'id'          => 'improve_product_descriptions',
                'title'       => __( 'Améliorer les descriptions de produits', 'boss-seo' ),
                'description' => __( 'Les descriptions de produits sont souvent trop courtes ou manquent de mots-clés pertinents. Utilisez le générateur IA pour créer des descriptions plus riches et optimisées.', 'boss-seo' ),
                'priority'    => 'medium',
            );
        }

        // Recommandations basées sur les catégories
        foreach ( $category_stats as $category ) {
            if ( $category['average_score'] < 50 && $category['total_products'] > 0 ) {
                $recommendations[] = array(
                    'id'          => 'optimize_category_' . $category['id'],
                    'title'       => sprintf( __( 'Optimiser la catégorie "%s"', 'boss-seo' ), $category['name'] ),
                    'description' => sprintf( __( 'La catégorie "%s" a un score SEO moyen de %d. Optimisez les produits de cette catégorie pour améliorer votre visibilité.', 'boss-seo' ), $category['name'], $category['average_score'] ),
                    'priority'    => 'medium',
                );
            }
        }

        // Recommandations générales
        $recommendations[] = array(
            'id'          => 'setup_google_shopping',
            'title'       => __( 'Configurer Google Shopping', 'boss-seo' ),
            'description' => __( 'Configurez Google Shopping pour augmenter la visibilité de vos produits dans les résultats de recherche Google.', 'boss-seo' ),
            'priority'    => 'medium',
        );

        $recommendations[] = array(
            'id'          => 'add_product_schema',
            'title'       => __( 'Ajouter des schémas de produits', 'boss-seo' ),
            'description' => __( 'Ajoutez des schémas de produits pour améliorer l\'affichage de vos produits dans les résultats de recherche.', 'boss-seo' ),
            'priority'    => 'medium',
        );

        return $recommendations;
    }

    /**
     * Génère les forces d'un concurrent.
     *
     * @since    1.2.0
     * @param    int      $score    Le score du concurrent.
     * @return   array              Les forces du concurrent.
     */
    private function generate_competitor_strengths( $score ) {
        $strengths = array();

        if ( $score > 80 ) {
            $strengths[] = __( 'Descriptions de produits détaillées et optimisées', 'boss-seo' );
            $strengths[] = __( 'Utilisation efficace des schémas de produits', 'boss-seo' );
            $strengths[] = __( 'Bonne structure de catégories', 'boss-seo' );
        }

        if ( $score > 70 ) {
            $strengths[] = __( 'Images de produits bien optimisées', 'boss-seo' );
            $strengths[] = __( 'Bonne utilisation des mots-clés', 'boss-seo' );
        }

        if ( $score > 60 ) {
            $strengths[] = __( 'Présence sur Google Shopping', 'boss-seo' );
        }

        return $strengths;
    }

    /**
     * Génère les faiblesses d'un concurrent.
     *
     * @since    1.2.0
     * @param    int      $score    Le score du concurrent.
     * @return   array              Les faiblesses du concurrent.
     */
    private function generate_competitor_weaknesses( $score ) {
        $weaknesses = array();

        if ( $score < 90 ) {
            $weaknesses[] = __( 'Manque d\'avis clients', 'boss-seo' );
        }

        if ( $score < 80 ) {
            $weaknesses[] = __( 'Optimisation mobile insuffisante', 'boss-seo' );
        }

        if ( $score < 70 ) {
            $weaknesses[] = __( 'Descriptions de produits trop courtes', 'boss-seo' );
            $weaknesses[] = __( 'Manque de schémas de produits', 'boss-seo' );
        }

        if ( $score < 60 ) {
            $weaknesses[] = __( 'Mauvaise structure de catégories', 'boss-seo' );
            $weaknesses[] = __( 'Images de produits non optimisées', 'boss-seo' );
        }

        return $weaknesses;
    }
}
