<?php
/**
 * Classe pour Google Shopping.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour Google Shopping.
 *
 * Cette classe gère Google Shopping.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Google_Shopping {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_google_shopping_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_save_google_shopping_settings', array( $this, 'ajax_save_google_shopping_settings' ) );
        add_action( 'wp_ajax_boss_seo_get_google_shopping_settings', array( $this, 'ajax_get_google_shopping_settings' ) );
        add_action( 'wp_ajax_boss_seo_generate_google_shopping_feed', array( $this, 'ajax_generate_google_shopping_feed' ) );
        add_action( 'wp_ajax_boss_seo_get_google_shopping_feeds', array( $this, 'ajax_get_google_shopping_feeds' ) );
        add_action( 'wp_ajax_boss_seo_delete_google_shopping_feed', array( $this, 'ajax_delete_google_shopping_feed' ) );

        // Ajouter les actions pour les métaboxes
        add_action( 'woocommerce_product_options_general_product_data', array( $this, 'add_google_shopping_fields' ) );
        add_action( 'woocommerce_process_product_meta', array( $this, 'save_google_shopping_fields' ) );

        // Ajouter les actions pour la génération automatique des flux
        add_action( 'boss_seo_generate_google_shopping_feeds', array( $this, 'generate_all_feeds' ) );

        // Planifier la génération automatique des flux
        if ( ! wp_next_scheduled( 'boss_seo_generate_google_shopping_feeds' ) ) {
            wp_schedule_event( time(), 'daily', 'boss_seo_generate_google_shopping_feeds' );
        }
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/google-shopping/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_google_shopping_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_google_shopping_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/google-shopping/feeds',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_google_shopping_feeds' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'generate_google_shopping_feed' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/google-shopping/feeds/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_google_shopping_feed' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour enregistrer les paramètres Google Shopping.
     *
     * @since    1.2.0
     */
    public function ajax_save_google_shopping_settings() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['settings'] ) || ! is_array( $_POST['settings'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Paramètres invalides.', 'boss-seo' ) ) );
        }

        $settings = $_POST['settings'];

        // Enregistrer les paramètres
        $result = $this->save_google_shopping_settings_data( $settings );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Paramètres enregistrés avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les paramètres Google Shopping.
     *
     * @since    1.2.0
     */
    public function ajax_get_google_shopping_settings() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $settings = $this->get_google_shopping_settings_data();

        wp_send_json_success( array(
            'message'  => __( 'Paramètres récupérés avec succès.', 'boss-seo' ),
            'settings' => $settings,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour générer un flux Google Shopping.
     *
     * @since    1.2.0
     */
    public function ajax_generate_google_shopping_feed() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['feed_name'] ) || empty( $_POST['feed_name'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Le nom du flux est requis.', 'boss-seo' ) ) );
        }

        $feed_name = sanitize_text_field( $_POST['feed_name'] );
        $merchant_id = isset( $_POST['merchant_id'] ) ? sanitize_text_field( $_POST['merchant_id'] ) : '';
        $country = isset( $_POST['country'] ) ? sanitize_text_field( $_POST['country'] ) : 'FR';
        $language = isset( $_POST['language'] ) ? sanitize_text_field( $_POST['language'] ) : 'fr';
        $currency = isset( $_POST['currency'] ) ? sanitize_text_field( $_POST['currency'] ) : 'EUR';

        // Générer le flux
        $result = $this->generate_google_shopping_feed_data( $feed_name, $merchant_id, $country, $language, $currency );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Flux généré avec succès.', 'boss-seo' ),
            'feed'    => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les flux Google Shopping.
     *
     * @since    1.2.0
     */
    public function ajax_get_google_shopping_feeds() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les flux
        $feeds = $this->get_google_shopping_feeds_data();

        wp_send_json_success( array(
            'message' => __( 'Flux récupérés avec succès.', 'boss-seo' ),
            'feeds'   => $feeds,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour supprimer un flux Google Shopping.
     *
     * @since    1.2.0
     */
    public function ajax_delete_google_shopping_feed() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['feed_id'] ) || empty( $_POST['feed_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du flux est requis.', 'boss-seo' ) ) );
        }

        $feed_id = absint( $_POST['feed_id'] );

        // Supprimer le flux
        $result = $this->delete_google_shopping_feed_data( $feed_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Flux supprimé avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Ajoute les champs Google Shopping dans l'éditeur de produit.
     *
     * @since    1.2.0
     */
    public function add_google_shopping_fields() {
        global $post;

        echo '<div class="options_group">';

        // Titre Google Shopping
        woocommerce_wp_text_input( array(
            'id'          => '_boss_google_shopping_title',
            'label'       => __( 'Titre Google Shopping', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Le titre du produit pour Google Shopping. Si vide, le titre du produit sera utilisé.', 'boss-seo' ),
        ) );

        // GTIN
        woocommerce_wp_text_input( array(
            'id'          => '_boss_google_shopping_gtin',
            'label'       => __( 'GTIN', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Le code GTIN (Global Trade Item Number) du produit.', 'boss-seo' ),
        ) );

        // MPN
        woocommerce_wp_text_input( array(
            'id'          => '_boss_google_shopping_mpn',
            'label'       => __( 'MPN', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Le code MPN (Manufacturer Part Number) du produit.', 'boss-seo' ),
        ) );

        // Condition
        woocommerce_wp_select( array(
            'id'          => '_boss_google_shopping_condition',
            'label'       => __( 'Condition', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'La condition du produit.', 'boss-seo' ),
            'options'     => array(
                'new'         => __( 'Neuf', 'boss-seo' ),
                'refurbished' => __( 'Reconditionné', 'boss-seo' ),
                'used'        => __( 'Occasion', 'boss-seo' ),
            ),
        ) );

        // Disponibilité
        woocommerce_wp_select( array(
            'id'          => '_boss_google_shopping_availability',
            'label'       => __( 'Disponibilité', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'La disponibilité du produit.', 'boss-seo' ),
            'options'     => array(
                'in_stock'     => __( 'En stock', 'boss-seo' ),
                'out_of_stock' => __( 'Rupture de stock', 'boss-seo' ),
                'preorder'     => __( 'Précommande', 'boss-seo' ),
            ),
        ) );

        // Délai de livraison
        woocommerce_wp_text_input( array(
            'id'          => '_boss_google_shopping_shipping_label',
            'label'       => __( 'Délai de livraison', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Le délai de livraison du produit.', 'boss-seo' ),
        ) );

        // Exclure du flux
        woocommerce_wp_checkbox( array(
            'id'          => '_boss_google_shopping_exclude',
            'label'       => __( 'Exclure du flux Google Shopping', 'boss-seo' ),
            'desc_tip'    => true,
            'description' => __( 'Cochez cette case pour exclure ce produit du flux Google Shopping.', 'boss-seo' ),
        ) );

        echo '</div>';
    }

    /**
     * Enregistre les champs Google Shopping.
     *
     * @since    1.2.0
     * @param    int    $post_id    L'ID du produit.
     */
    public function save_google_shopping_fields( $post_id ) {
        // Vérifier si c'est un produit
        if ( get_post_type( $post_id ) !== 'product' ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Vérifier si c'est une sauvegarde automatique
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }

        // Enregistrer les champs
        $fields = array(
            '_boss_google_shopping_title',
            '_boss_google_shopping_gtin',
            '_boss_google_shopping_mpn',
            '_boss_google_shopping_condition',
            '_boss_google_shopping_availability',
            '_boss_google_shopping_shipping_label',
        );

        foreach ( $fields as $field ) {
            if ( isset( $_POST[ $field ] ) ) {
                update_post_meta( $post_id, $field, sanitize_text_field( $_POST[ $field ] ) );
            }
        }

        // Enregistrer la case à cocher
        $exclude = isset( $_POST['_boss_google_shopping_exclude'] ) ? 'yes' : 'no';
        update_post_meta( $post_id, '_boss_google_shopping_exclude', $exclude );
    }

    /**
     * Récupère les paramètres Google Shopping via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_google_shopping_settings( $request ) {
        $settings = $this->get_google_shopping_settings_data();

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres Google Shopping via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_google_shopping_settings( $request ) {
        $params = $request->get_params();

        if ( ! isset( $params['settings'] ) || ! is_array( $params['settings'] ) ) {
            return new WP_Error( 'invalid_settings', __( 'Paramètres invalides.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $result = $this->save_google_shopping_settings_data( $params['settings'] );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Paramètres enregistrés avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les flux Google Shopping via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_google_shopping_feeds( $request ) {
        $feeds = $this->get_google_shopping_feeds_data();

        return rest_ensure_response( $feeds );
    }

    /**
     * Génère un flux Google Shopping via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function generate_google_shopping_feed( $request ) {
        $params = $request->get_params();

        if ( ! isset( $params['feed_name'] ) || empty( $params['feed_name'] ) ) {
            return new WP_Error( 'missing_feed_name', __( 'Le nom du flux est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $feed_name = sanitize_text_field( $params['feed_name'] );
        $merchant_id = isset( $params['merchant_id'] ) ? sanitize_text_field( $params['merchant_id'] ) : '';
        $country = isset( $params['country'] ) ? sanitize_text_field( $params['country'] ) : 'FR';
        $language = isset( $params['language'] ) ? sanitize_text_field( $params['language'] ) : 'fr';
        $currency = isset( $params['currency'] ) ? sanitize_text_field( $params['currency'] ) : 'EUR';

        $result = $this->generate_google_shopping_feed_data( $feed_name, $merchant_id, $country, $language, $currency );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Flux généré avec succès.', 'boss-seo' ),
            'feed'    => $result,
        ) );
    }

    /**
     * Supprime un flux Google Shopping via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_google_shopping_feed( $request ) {
        $feed_id = $request['id'];

        $result = $this->delete_google_shopping_feed_data( $feed_id );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Flux supprimé avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Génère tous les flux Google Shopping.
     *
     * @since    1.2.0
     */
    public function generate_all_feeds() {
        // Récupérer les flux
        $feeds = $this->get_google_shopping_feeds_data();

        foreach ( $feeds as $feed ) {
            $this->generate_google_shopping_feed_data(
                $feed['name'],
                $feed['merchant_id'],
                $feed['country'],
                $feed['language'],
                $feed['currency']
            );
        }
    }

    /**
     * Récupère les paramètres Google Shopping.
     *
     * @since    1.2.0
     * @return   array    Les paramètres Google Shopping.
     */
    private function get_google_shopping_settings_data() {
        $settings = get_option( $this->option_prefix . 'settings', array() );

        $defaults = array(
            'merchant_id'        => '',
            'default_country'    => 'FR',
            'default_language'   => 'fr',
            'default_currency'   => 'EUR',
            'auto_update'        => 'yes',
            'update_frequency'   => 'daily',
            'include_variations' => 'yes',
            'include_tax'        => 'yes',
            'include_shipping'   => 'yes',
            'default_condition'  => 'new',
            'default_brand'      => get_bloginfo( 'name' ),
        );

        return wp_parse_args( $settings, $defaults );
    }

    /**
     * Enregistre les paramètres Google Shopping.
     *
     * @since    1.2.0
     * @param    array     $settings    Les paramètres à enregistrer.
     * @return   bool|WP_Error          True en cas de succès, WP_Error en cas d'erreur.
     */
    private function save_google_shopping_settings_data( $settings ) {
        // Valider les paramètres
        $valid_settings = array();

        // Merchant ID
        if ( isset( $settings['merchant_id'] ) ) {
            $valid_settings['merchant_id'] = sanitize_text_field( $settings['merchant_id'] );
        }

        // Pays par défaut
        if ( isset( $settings['default_country'] ) ) {
            $valid_settings['default_country'] = sanitize_text_field( $settings['default_country'] );
        }

        // Langue par défaut
        if ( isset( $settings['default_language'] ) ) {
            $valid_settings['default_language'] = sanitize_text_field( $settings['default_language'] );
        }

        // Devise par défaut
        if ( isset( $settings['default_currency'] ) ) {
            $valid_settings['default_currency'] = sanitize_text_field( $settings['default_currency'] );
        }

        // Mise à jour automatique
        if ( isset( $settings['auto_update'] ) ) {
            $valid_settings['auto_update'] = $settings['auto_update'] ? 'yes' : 'no';
        }

        // Fréquence de mise à jour
        if ( isset( $settings['update_frequency'] ) ) {
            $valid_settings['update_frequency'] = in_array( $settings['update_frequency'], array( 'hourly', 'daily', 'weekly' ) ) ? $settings['update_frequency'] : 'daily';
        }

        // Inclure les variations
        if ( isset( $settings['include_variations'] ) ) {
            $valid_settings['include_variations'] = $settings['include_variations'] ? 'yes' : 'no';
        }

        // Inclure les taxes
        if ( isset( $settings['include_tax'] ) ) {
            $valid_settings['include_tax'] = $settings['include_tax'] ? 'yes' : 'no';
        }

        // Inclure les frais de livraison
        if ( isset( $settings['include_shipping'] ) ) {
            $valid_settings['include_shipping'] = $settings['include_shipping'] ? 'yes' : 'no';
        }

        // Condition par défaut
        if ( isset( $settings['default_condition'] ) ) {
            $valid_settings['default_condition'] = in_array( $settings['default_condition'], array( 'new', 'refurbished', 'used' ) ) ? $settings['default_condition'] : 'new';
        }

        // Marque par défaut
        if ( isset( $settings['default_brand'] ) ) {
            $valid_settings['default_brand'] = sanitize_text_field( $settings['default_brand'] );
        }

        // Enregistrer les paramètres
        update_option( $this->option_prefix . 'settings', $valid_settings );

        return true;
    }

    /**
     * Récupère les flux Google Shopping.
     *
     * @since    1.2.0
     * @return   array    Les flux Google Shopping.
     */
    private function get_google_shopping_feeds_data() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'boss_google_shopping_feeds';

        // Vérifier si la table existe
        if ( $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) != $table_name ) {
            // Créer la table
            $this->create_feeds_table();
        }

        // Récupérer les flux
        $feeds = $wpdb->get_results( "SELECT * FROM $table_name ORDER BY id DESC", ARRAY_A );

        return $feeds ? $feeds : array();
    }

    /**
     * Génère un flux Google Shopping.
     *
     * @since    1.2.0
     * @param    string    $feed_name     Le nom du flux.
     * @param    string    $merchant_id   L'ID du marchand.
     * @param    string    $country       Le pays.
     * @param    string    $language      La langue.
     * @param    string    $currency      La devise.
     * @return   array|WP_Error           Le flux généré ou une erreur.
     */
    private function generate_google_shopping_feed_data( $feed_name, $merchant_id, $country, $language, $currency ) {
        global $wpdb;

        // Vérifier les paramètres
        if ( empty( $feed_name ) ) {
            return new WP_Error( 'missing_feed_name', __( 'Le nom du flux est requis.', 'boss-seo' ) );
        }

        // Récupérer les paramètres
        $settings = $this->get_google_shopping_settings_data();

        // Utiliser les paramètres par défaut si nécessaire
        $merchant_id = ! empty( $merchant_id ) ? $merchant_id : $settings['merchant_id'];
        $country = ! empty( $country ) ? $country : $settings['default_country'];
        $language = ! empty( $language ) ? $language : $settings['default_language'];
        $currency = ! empty( $currency ) ? $currency : $settings['default_currency'];

        // Générer le nom du fichier
        $file_name = sanitize_file_name( $feed_name . '-' . $country . '-' . $language . '.xml' );

        // Générer le chemin du fichier
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['basedir'] . '/boss-seo/google-shopping/' . $file_name;
        $file_url = $upload_dir['baseurl'] . '/boss-seo/google-shopping/' . $file_name;

        // Créer le dossier s'il n'existe pas
        wp_mkdir_p( $upload_dir['basedir'] . '/boss-seo/google-shopping/' );

        // Générer le contenu du flux
        $content = $this->generate_feed_content( $merchant_id, $country, $language, $currency );

        // Enregistrer le fichier
        file_put_contents( $file_path, $content );

        // Enregistrer le flux dans la base de données
        $table_name = $wpdb->prefix . 'boss_google_shopping_feeds';

        // Vérifier si la table existe
        if ( $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) != $table_name ) {
            // Créer la table
            $this->create_feeds_table();
        }

        // Vérifier si le flux existe déjà
        $existing_feed = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM $table_name WHERE name = %s AND country = %s AND language = %s", $feed_name, $country, $language ) );

        if ( $existing_feed ) {
            // Mettre à jour le flux
            $wpdb->update(
                $table_name,
                array(
                    'merchant_id'  => $merchant_id,
                    'currency'     => $currency,
                    'file_path'    => $file_path,
                    'file_url'     => $file_url,
                    'last_updated' => current_time( 'mysql' ),
                ),
                array(
                    'id' => $existing_feed->id,
                )
            );

            $feed_id = $existing_feed->id;
        } else {
            // Créer un nouveau flux
            $wpdb->insert(
                $table_name,
                array(
                    'name'         => $feed_name,
                    'merchant_id'  => $merchant_id,
                    'country'      => $country,
                    'language'     => $language,
                    'currency'     => $currency,
                    'file_path'    => $file_path,
                    'file_url'     => $file_url,
                    'created'      => current_time( 'mysql' ),
                    'last_updated' => current_time( 'mysql' ),
                )
            );

            $feed_id = $wpdb->insert_id;
        }

        // Récupérer le flux
        $feed = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM $table_name WHERE id = %d", $feed_id ), ARRAY_A );

        return $feed;
    }

    /**
     * Supprime un flux Google Shopping.
     *
     * @since    1.2.0
     * @param    int       $feed_id    L'ID du flux.
     * @return   bool|WP_Error         True en cas de succès, WP_Error en cas d'erreur.
     */
    private function delete_google_shopping_feed_data( $feed_id ) {
        global $wpdb;

        // Vérifier les paramètres
        if ( empty( $feed_id ) ) {
            return new WP_Error( 'missing_feed_id', __( 'L\'ID du flux est requis.', 'boss-seo' ) );
        }

        $table_name = $wpdb->prefix . 'boss_google_shopping_feeds';

        // Vérifier si la table existe
        if ( $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) != $table_name ) {
            return new WP_Error( 'table_not_found', __( 'La table des flux n\'existe pas.', 'boss-seo' ) );
        }

        // Récupérer le flux
        $feed = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM $table_name WHERE id = %d", $feed_id ) );

        if ( ! $feed ) {
            return new WP_Error( 'feed_not_found', __( 'Flux non trouvé.', 'boss-seo' ) );
        }

        // Supprimer le fichier
        if ( file_exists( $feed->file_path ) ) {
            unlink( $feed->file_path );
        }

        // Supprimer le flux de la base de données
        $wpdb->delete( $table_name, array( 'id' => $feed_id ) );

        return true;
    }

    /**
     * Crée la table des flux.
     *
     * @since    1.2.0
     */
    private function create_feeds_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'boss_google_shopping_feeds';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            merchant_id varchar(255) NOT NULL,
            country varchar(2) NOT NULL,
            language varchar(5) NOT NULL,
            currency varchar(3) NOT NULL,
            file_path varchar(255) NOT NULL,
            file_url varchar(255) NOT NULL,
            created datetime NOT NULL,
            last_updated datetime NOT NULL,
            PRIMARY KEY  (id)
        ) $charset_collate;";

        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );
    }

    /**
     * Génère le contenu du flux.
     *
     * @since    1.2.0
     * @param    string    $merchant_id   L'ID du marchand.
     * @param    string    $country       Le pays.
     * @param    string    $language      La langue.
     * @param    string    $currency      La devise.
     * @return   string                   Le contenu du flux.
     */
    private function generate_feed_content( $merchant_id, $country, $language, $currency ) {
        // Récupérer les paramètres
        $settings = $this->get_google_shopping_settings_data();

        // Récupérer les produits
        $args = array(
            'post_type'      => 'product',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
            'meta_query'     => array(
                array(
                    'key'     => '_boss_google_shopping_exclude',
                    'value'   => 'yes',
                    'compare' => '!=',
                ),
            ),
        );

        $products = get_posts( $args );

        // Générer le contenu XML
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<rss xmlns:g="http://base.google.com/ns/1.0" version="2.0">' . PHP_EOL;
        $xml .= '<channel>' . PHP_EOL;
        $xml .= '<title>' . get_bloginfo( 'name' ) . '</title>' . PHP_EOL;
        $xml .= '<link>' . get_bloginfo( 'url' ) . '</link>' . PHP_EOL;
        $xml .= '<description>' . get_bloginfo( 'description' ) . '</description>' . PHP_EOL;

        foreach ( $products as $post ) {
            $product = wc_get_product( $post );

            if ( ! $product ) {
                continue;
            }

            // Vérifier si le produit est en stock
            if ( $product->get_stock_status() === 'outofstock' && $settings['include_out_of_stock'] !== 'yes' ) {
                continue;
            }

            // Récupérer les informations du produit
            $product_id = $product->get_id();
            $product_title = get_post_meta( $product_id, '_boss_google_shopping_title', true );
            $product_title = ! empty( $product_title ) ? $product_title : $product->get_name();
            $product_description = $product->get_description();
            $product_link = get_permalink( $product_id );
            $product_image = wp_get_attachment_url( $product->get_image_id() );
            $product_price = $product->get_price();
            $product_gtin = get_post_meta( $product_id, '_boss_google_shopping_gtin', true );
            $product_mpn = get_post_meta( $product_id, '_boss_google_shopping_mpn', true );
            $product_condition = get_post_meta( $product_id, '_boss_google_shopping_condition', true );
            $product_condition = ! empty( $product_condition ) ? $product_condition : $settings['default_condition'];
            $product_availability = get_post_meta( $product_id, '_boss_google_shopping_availability', true );
            $product_availability = ! empty( $product_availability ) ? $product_availability : ( $product->get_stock_status() === 'instock' ? 'in_stock' : 'out_of_stock' );
            $product_shipping_label = get_post_meta( $product_id, '_boss_google_shopping_shipping_label', true );

            // Récupérer la marque
            $product_brand = '';
            $brand_taxonomy = 'product_brand';

            if ( taxonomy_exists( $brand_taxonomy ) ) {
                $brands = wp_get_post_terms( $product_id, $brand_taxonomy );

                if ( ! empty( $brands ) && ! is_wp_error( $brands ) ) {
                    $product_brand = $brands[0]->name;
                }
            }

            if ( empty( $product_brand ) ) {
                $product_brand = $settings['default_brand'];
            }

            // Générer l'élément XML
            $xml .= '<item>' . PHP_EOL;
            $xml .= '<g:id>' . $product_id . '</g:id>' . PHP_EOL;
            $xml .= '<g:title>' . htmlspecialchars( $product_title ) . '</g:title>' . PHP_EOL;
            $xml .= '<g:description>' . htmlspecialchars( $product_description ) . '</g:description>' . PHP_EOL;
            $xml .= '<g:link>' . $product_link . '</g:link>' . PHP_EOL;
            $xml .= '<g:image_link>' . $product_image . '</g:image_link>' . PHP_EOL;
            $xml .= '<g:price>' . $product_price . ' ' . $currency . '</g:price>' . PHP_EOL;
            $xml .= '<g:brand>' . htmlspecialchars( $product_brand ) . '</g:brand>' . PHP_EOL;
            $xml .= '<g:condition>' . $product_condition . '</g:condition>' . PHP_EOL;
            $xml .= '<g:availability>' . $product_availability . '</g:availability>' . PHP_EOL;

            if ( ! empty( $product_gtin ) ) {
                $xml .= '<g:gtin>' . $product_gtin . '</g:gtin>' . PHP_EOL;
            }

            if ( ! empty( $product_mpn ) ) {
                $xml .= '<g:mpn>' . $product_mpn . '</g:mpn>' . PHP_EOL;
            }

            if ( ! empty( $product_shipping_label ) ) {
                $xml .= '<g:shipping_label>' . $product_shipping_label . '</g:shipping_label>' . PHP_EOL;
            }

            $xml .= '</item>' . PHP_EOL;
        }

        $xml .= '</channel>' . PHP_EOL;
        $xml .= '</rss>';

        return $xml;
    }
}
