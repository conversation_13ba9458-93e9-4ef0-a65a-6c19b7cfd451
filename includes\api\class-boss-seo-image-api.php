<?php
/**
 * La classe d'intégration avec les API d'images.
 *
 * Cette classe gère l'intégration avec les API d'images comme Pexels, Unsplash et Pixabay.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/api
 */

/**
 * La classe d'intégration avec les API d'images.
 *
 * Cette classe gère l'intégration avec les API d'images comme Pexels, Unsplash et Pixabay.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/api
 * <AUTHOR> SEO Team
 */
class Boss_Seo_Image_API {
    /**
     * La clé API Pexels.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $pexels_api_key    La clé API Pexels.
     */
    private $pexels_api_key;
    
    /**
     * La clé API Unsplash.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $unsplash_api_key    La clé API Unsplash.
     */
    private $unsplash_api_key;
    
    /**
     * La clé API Pixabay.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $pixabay_api_key    La clé API Pixabay.
     */
    private $pixabay_api_key;
    
    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $pexels_api_key     La clé API Pexels.
     * @param    string    $unsplash_api_key   La clé API Unsplash.
     * @param    string    $pixabay_api_key    La clé API Pixabay.
     */
    public function __construct( $pexels_api_key, $unsplash_api_key, $pixabay_api_key ) {
        $this->pexels_api_key = $pexels_api_key;
        $this->unsplash_api_key = $unsplash_api_key;
        $this->pixabay_api_key = $pixabay_api_key;
    }
    
    /**
     * Recherche des images via les API d'images.
     *
     * @since    1.1.0
     * @param    string    $query       La requête de recherche.
     * @param    string    $source      La source d'images (pexels, unsplash, pixabay).
     * @param    int       $per_page    Le nombre d'images par page.
     * @param    int       $page        Le numéro de page.
     * @return   array|WP_Error         Les résultats de la recherche ou une erreur.
     */
    public function search_images( $query, $source = 'pexels', $per_page = 8, $page = 1 ) {
        switch ( $source ) {
            case 'pexels':
                return $this->search_pexels( $query, $per_page, $page );
            case 'unsplash':
                return $this->search_unsplash( $query, $per_page, $page );
            case 'pixabay':
                return $this->search_pixabay( $query, $per_page, $page );
            default:
                return new WP_Error( 'invalid_source', __( 'Source d\'images non valide.', 'boss-seo' ) );
        }
    }
    
    /**
     * Recherche des images via l'API Pexels.
     *
     * @since    1.1.0
     * @access   private
     * @param    string    $query       La requête de recherche.
     * @param    int       $per_page    Le nombre d'images par page.
     * @param    int       $page        Le numéro de page.
     * @return   array|WP_Error         Les résultats de la recherche ou une erreur.
     */
    private function search_pexels( $query, $per_page = 8, $page = 1 ) {
        // Vérifier si la clé API est définie
        if ( empty( $this->pexels_api_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API Pexels non définie.', 'boss-seo' ) );
        }
        
        // Construire l'URL de l'API
        $url = add_query_arg(
            array(
                'query'    => urlencode( $query ),
                'per_page' => $per_page,
                'page'     => $page,
            ),
            'https://api.pexels.com/v1/search'
        );
        
        // Effectuer la requête
        $response = wp_remote_get(
            $url,
            array(
                'headers' => array(
                    'Authorization' => $this->pexels_api_key,
                ),
            )
        );
        
        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        // Récupérer le corps de la réponse
        $body = json_decode( wp_remote_retrieve_body( $response ), true );
        
        // Vérifier si la réponse contient une erreur
        if ( isset( $body['error'] ) ) {
            return new WP_Error( 'pexels_error', $body['error'] );
        }
        
        // Extraire les images
        $images = array();
        
        if ( isset( $body['photos'] ) ) {
            foreach ( $body['photos'] as $photo ) {
                $images[] = array(
                    'id'              => $photo['id'],
                    'url'             => $photo['src']['original'],
                    'thumbnail'       => $photo['src']['medium'],
                    'alt'             => $photo['alt'] ?? $query,
                    'width'           => $photo['width'],
                    'height'          => $photo['height'],
                    'photographer'    => $photo['photographer'],
                    'photographer_url' => $photo['photographer_url'],
                    'source'          => 'pexels',
                );
            }
        }
        
        return $images;
    }
    
    /**
     * Recherche des images via l'API Unsplash.
     *
     * @since    1.1.0
     * @access   private
     * @param    string    $query       La requête de recherche.
     * @param    int       $per_page    Le nombre d'images par page.
     * @param    int       $page        Le numéro de page.
     * @return   array|WP_Error         Les résultats de la recherche ou une erreur.
     */
    private function search_unsplash( $query, $per_page = 8, $page = 1 ) {
        // Vérifier si la clé API est définie
        if ( empty( $this->unsplash_api_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API Unsplash non définie.', 'boss-seo' ) );
        }
        
        // Construire l'URL de l'API
        $url = add_query_arg(
            array(
                'query'     => urlencode( $query ),
                'per_page'  => $per_page,
                'page'      => $page,
                'client_id' => $this->unsplash_api_key,
            ),
            'https://api.unsplash.com/search/photos'
        );
        
        // Effectuer la requête
        $response = wp_remote_get( $url );
        
        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        // Récupérer le corps de la réponse
        $body = json_decode( wp_remote_retrieve_body( $response ), true );
        
        // Vérifier si la réponse contient une erreur
        if ( isset( $body['errors'] ) ) {
            return new WP_Error( 'unsplash_error', implode( ', ', $body['errors'] ) );
        }
        
        // Extraire les images
        $images = array();
        
        if ( isset( $body['results'] ) ) {
            foreach ( $body['results'] as $photo ) {
                $images[] = array(
                    'id'              => $photo['id'],
                    'url'             => $photo['urls']['raw'],
                    'thumbnail'       => $photo['urls']['small'],
                    'alt'             => $photo['alt_description'] ?? $query,
                    'width'           => $photo['width'],
                    'height'          => $photo['height'],
                    'photographer'    => $photo['user']['name'],
                    'photographer_url' => $photo['user']['links']['html'],
                    'source'          => 'unsplash',
                );
            }
        }
        
        return $images;
    }
    
    /**
     * Recherche des images via l'API Pixabay.
     *
     * @since    1.1.0
     * @access   private
     * @param    string    $query       La requête de recherche.
     * @param    int       $per_page    Le nombre d'images par page.
     * @param    int       $page        Le numéro de page.
     * @return   array|WP_Error         Les résultats de la recherche ou une erreur.
     */
    private function search_pixabay( $query, $per_page = 8, $page = 1 ) {
        // Vérifier si la clé API est définie
        if ( empty( $this->pixabay_api_key ) ) {
            return new WP_Error( 'missing_api_key', __( 'Clé API Pixabay non définie.', 'boss-seo' ) );
        }
        
        // Construire l'URL de l'API
        $url = add_query_arg(
            array(
                'q'        => urlencode( $query ),
                'per_page' => $per_page,
                'page'     => $page,
                'key'      => $this->pixabay_api_key,
                'lang'     => 'fr',
                'image_type' => 'photo',
            ),
            'https://pixabay.com/api/'
        );
        
        // Effectuer la requête
        $response = wp_remote_get( $url );
        
        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        // Récupérer le corps de la réponse
        $body = json_decode( wp_remote_retrieve_body( $response ), true );
        
        // Vérifier si la réponse contient une erreur
        if ( isset( $body['error'] ) ) {
            return new WP_Error( 'pixabay_error', $body['error'] );
        }
        
        // Extraire les images
        $images = array();
        
        if ( isset( $body['hits'] ) ) {
            foreach ( $body['hits'] as $photo ) {
                $images[] = array(
                    'id'              => $photo['id'],
                    'url'             => $photo['largeImageURL'],
                    'thumbnail'       => $photo['webformatURL'],
                    'alt'             => $query,
                    'width'           => $photo['imageWidth'],
                    'height'          => $photo['imageHeight'],
                    'photographer'    => $photo['user'],
                    'photographer_url' => "https://pixabay.com/users/{$photo['user']}-{$photo['user_id']}/",
                    'source'          => 'pixabay',
                );
            }
        }
        
        return $images;
    }
    
    /**
     * Importe une image dans la médiathèque WordPress.
     *
     * @since    1.1.0
     * @param    array     $image    Les informations de l'image.
     * @return   array|WP_Error      Les informations de l'image importée ou une erreur.
     */
    public function import_image( $image ) {
        // Vérifier si l'image est valide
        if ( ! isset( $image['url'] ) || ! isset( $image['alt'] ) ) {
            return new WP_Error( 'invalid_image', __( 'Image non valide.', 'boss-seo' ) );
        }
        
        // Télécharger l'image
        $tmp = download_url( $image['url'] );
        
        // Vérifier les erreurs
        if ( is_wp_error( $tmp ) ) {
            return $tmp;
        }
        
        // Préparer le fichier pour l'importation
        $file_array = array(
            'name'     => basename( $image['url'] ),
            'tmp_name' => $tmp,
        );
        
        // Importer l'image dans la médiathèque
        $attachment_id = media_handle_sideload( $file_array, 0, $image['alt'] );
        
        // Supprimer le fichier temporaire
        @unlink( $tmp );
        
        // Vérifier les erreurs
        if ( is_wp_error( $attachment_id ) ) {
            return $attachment_id;
        }
        
        // Mettre à jour les métadonnées de l'image
        wp_update_attachment_metadata(
            $attachment_id,
            wp_generate_attachment_metadata( $attachment_id, get_attached_file( $attachment_id ) )
        );
        
        // Mettre à jour l'attribut alt
        update_post_meta( $attachment_id, '_wp_attachment_image_alt', $image['alt'] );
        
        // Récupérer les informations de l'image importée
        $imported_image = array(
            'id'        => $attachment_id,
            'url'       => wp_get_attachment_url( $attachment_id ),
            'thumbnail' => wp_get_attachment_image_url( $attachment_id, 'medium' ),
            'alt'       => $image['alt'],
            'width'     => get_post_meta( $attachment_id, '_wp_attachment_metadata', true )['width'] ?? 0,
            'height'    => get_post_meta( $attachment_id, '_wp_attachment_metadata', true )['height'] ?? 0,
            'source'    => 'wordpress',
        );
        
        return $imported_image;
    }
}
