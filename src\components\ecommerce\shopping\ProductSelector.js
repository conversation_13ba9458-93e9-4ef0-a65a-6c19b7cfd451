import { __ } from '@wordpress/i18n';
import { 
  Card, 
  CardHeader, 
  CardBody, 
  TextControl, 
  SelectControl, 
  Dashicon 
} from '@wordpress/components';

const ProductSelector = ({ 
  searchQuery, 
  setSearchQuery, 
  selectedCategory, 
  setSelectedCategory, 
  categories, 
  filteredProducts, 
  selectedProduct, 
  setSelectedProduct 
}) => {
  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Sélection du produit', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        <div className="boss-space-y-4">
          <TextControl
            placeholder={__('Rechercher un produit...', 'boss-seo')}
            value={searchQuery}
            onChange={setSearchQuery}
          />
          
          <SelectControl
            label={__('<PERSON><PERSON><PERSON><PERSON>', 'boss-seo')}
            value={selectedCategory}
            options={[
              { label: __('Toutes les catégories', 'boss-seo'), value: 'all' },
              ...categories.map(category => ({
                label: category.name,
                value: category.id.toString()
              }))
            ]}
            onChange={setSelectedCategory}
          />
          
          <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-max-h-96 boss-overflow-y-auto">
            <div className="boss-divide-y boss-divide-gray-200">
              {filteredProducts.length === 0 ? (
                <div className="boss-p-4 boss-text-center boss-text-boss-gray">
                  {__('Aucun produit trouvé.', 'boss-seo')}
                </div>
              ) : (
                filteredProducts.map(product => (
                  <div 
                    key={product.id} 
                    className={`boss-p-4 boss-cursor-pointer boss-transition-colors ${
                      selectedProduct && selectedProduct.id === product.id
                        ? 'boss-bg-blue-50'
                        : 'boss-hover:boss-bg-gray-50'
                    }`}
                    onClick={() => setSelectedProduct(product)}
                  >
                    <div className="boss-flex boss-justify-between boss-items-start">
                      <div>
                        <div className="boss-font-medium boss-text-boss-dark boss-mb-1">{product.name}</div>
                        <div className="boss-text-sm boss-text-boss-gray boss-mb-1">{product.category}</div>
                        <div className="boss-text-sm boss-text-boss-primary">{product.price} €</div>
                      </div>
                      {product.isInShopping && (
                        <span className="boss-text-green-600 boss-flex boss-items-center">
                          <Dashicon icon="cart" className="boss-mr-1" />
                        </span>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default ProductSelector;
