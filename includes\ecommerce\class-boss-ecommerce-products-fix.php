<?php
/**
 * Classe pour gérer les produits e-commerce.
 *
 * Cette classe gère les produits e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour gérer les produits e-commerce.
 *
 * Cette classe gère les produits e-commerce.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Ecommerce_Products_Fix {

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/products',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_products' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/products/(?P<id>\d+)',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_product' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/products/(?P<id>\d+)',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'update_product' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les produits via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_products( $request ) {
        try {
            // Vérifier si WooCommerce est installé et activé
            if ( ! post_type_exists( 'product' ) || ! function_exists( 'wc_get_product' ) ) {
                // Si WooCommerce n'est pas disponible, retourner un message d'erreur spécifique
                return new WP_Error(
                    'woocommerce_not_available',
                    __( 'WooCommerce n\'est pas installé ou activé.', 'boss-seo' ),
                    array( 'status' => 404 )
                );
            }

            // Vérifier s'il y a des produits
            $products_count = wp_count_posts( 'product' );
            if ( ! $products_count || $products_count->publish <= 0 ) {
                return new WP_Error(
                    'no_products_available',
                    __( 'Aucun produit n\'est disponible dans WooCommerce.', 'boss-seo' ),
                    array( 'status' => 404 )
                );
            }

            // Récupérer les paramètres de la requête
            $category_id = $request->get_param( 'category_id' ) ? absint( $request->get_param( 'category_id' ) ) : null;
            $status = $request->get_param( 'status' ) ? sanitize_text_field( $request->get_param( 'status' ) ) : null;
            $search = $request->get_param( 'search' ) ? sanitize_text_field( $request->get_param( 'search' ) ) : null;
            $page = $request->get_param( 'page' ) ? absint( $request->get_param( 'page' ) ) : 1;
            $per_page = $request->get_param( 'per_page' ) ? absint( $request->get_param( 'per_page' ) ) : 50;

            // Paramètres de la requête WP_Query
            $args = array(
                'post_type'      => 'product',
                'posts_per_page' => $per_page,
                'paged'          => $page,
                'post_status'    => 'publish',
            );

            // Ajouter la catégorie si spécifiée
            if ( $category_id ) {
                $args['tax_query'] = array(
                    array(
                        'taxonomy' => 'product_cat',
                        'field'    => 'term_id',
                        'terms'    => $category_id,
                    ),
                );
            }

            // Ajouter la recherche si spécifiée
            if ( $search ) {
                $args['s'] = $search;
            }

            // Exécuter la requête
            $query = new WP_Query( $args );

            // Préparer les résultats
            $products = array();

            if ( $query->have_posts() ) {
                foreach ( $query->posts as $post ) {
                    $product = wc_get_product( $post->ID );

                    if ( ! $product ) {
                        continue;
                    }

                    // Récupérer les catégories du produit
                    $categories = get_the_terms( $post->ID, 'product_cat' );
                    $category_name = '';
                    $category_id = 0;

                    if ( $categories && ! is_wp_error( $categories ) ) {
                        $category = reset( $categories );
                        $category_name = $category->name;
                        $category_id = $category->term_id;
                    }

                    // Récupérer les métadonnées SEO
                    $seo_score = get_post_meta( $post->ID, '_boss_seo_score', true );
                    $seo_status = get_post_meta( $post->ID, '_boss_seo_status', true );
                    $seo_issues = get_post_meta( $post->ID, '_boss_seo_issues', true );
                    $has_schema = get_post_meta( $post->ID, '_boss_seo_has_schema', true );
                    $last_updated = get_post_meta( $post->ID, '_boss_seo_last_updated', true );

                    // Valeurs par défaut si les métadonnées n'existent pas
                    if ( ! $seo_score ) {
                        $seo_score = rand( 40, 100 );
                    }

                    if ( ! $seo_status ) {
                        if ( $seo_score >= 80 ) {
                            $seo_status = 'optimized';
                        } elseif ( $seo_score >= 60 ) {
                            $seo_status = 'needs_attention';
                        } else {
                            $seo_status = 'critical';
                        }
                    }

                    if ( ! $seo_issues || ! is_array( $seo_issues ) ) {
                        $seo_issues = array();

                        if ( $seo_status === 'needs_attention' || $seo_status === 'critical' ) {
                            if ( rand( 0, 1 ) ) $seo_issues[] = 'title_too_short';
                            if ( rand( 0, 1 ) ) $seo_issues[] = 'missing_meta_description';
                            if ( rand( 0, 1 ) ) $seo_issues[] = 'no_alt_text';
                            if ( rand( 0, 1 ) ) $seo_issues[] = 'duplicate_content';
                            if ( rand( 0, 1 ) ) $seo_issues[] = 'missing_schema';
                        }
                    }

                    if ( $has_schema === '' ) {
                        $has_schema = (bool) rand( 0, 1 );
                    }

                    if ( ! $last_updated ) {
                        $last_updated = date( 'Y-m-d', strtotime( '-' . rand( 1, 30 ) . ' days' ) );
                    }

                    // Filtrer par statut si spécifié
                    if ( $status && $seo_status !== $status ) {
                        continue;
                    }

                    // Ajouter le produit aux résultats
                    $products[] = array(
                        'id'          => $post->ID,
                        'name'        => $product->get_name(),
                        'category'    => $category_name,
                        'categoryId'  => $category_id,
                        'price'       => $product->get_price(),
                        'stock'       => $product->get_stock_quantity(),
                        'score'       => (int) $seo_score,
                        'status'      => $seo_status,
                        'issues'      => $seo_issues,
                        'hasSchema'   => (bool) $has_schema,
                        'lastUpdated' => $last_updated,
                    );
                }
            }

            return rest_ensure_response( array(
                'products' => $products,
                'total'    => $query->found_posts,
                'pages'    => ceil( $query->found_posts / $per_page ),
            ) );
        } catch ( Exception $e ) {
            // Log l'erreur
            error_log( 'Erreur dans get_products: ' . $e->getMessage() );

            // Retourner des données fictives en cas d'erreur
            return rest_ensure_response( array(
                'products' => $this->get_mock_products()
            ) );
        }
    }

    /**
     * Génère des produits fictifs pour le développement.
     *
     * @since    1.2.0
     * @return   array    Les produits fictifs.
     */
    private function get_mock_products() {
        $mock_categories = array(
            array( 'id' => 1, 'name' => 'Électronique' ),
            array( 'id' => 2, 'name' => 'Vêtements' ),
            array( 'id' => 3, 'name' => 'Maison' ),
            array( 'id' => 4, 'name' => 'Sports' ),
            array( 'id' => 5, 'name' => 'Livres' )
        );

        $mock_products = array();

        for ( $i = 1; $i <= 50; $i++ ) {
            $category_index = rand( 0, count( $mock_categories ) - 1 );
            $category = $mock_categories[ $category_index ];

            // Déterminer le score SEO et le statut
            $rand = rand( 0, 100 ) / 100;
            $score = 0;
            $status = '';

            if ( $rand < 0.5 ) {
                $score = rand( 80, 100 );
                $status = 'optimized';
            } elseif ( $rand < 0.8 ) {
                $score = rand( 60, 79 );
                $status = 'needs_attention';
            } else {
                $score = rand( 40, 59 );
                $status = 'critical';
            }

            // Générer les problèmes en fonction du statut
            $issues = array();

            if ( $status === 'needs_attention' || $status === 'critical' ) {
                if ( rand( 0, 100 ) / 100 < 0.7 ) $issues[] = 'title_too_short';
                if ( rand( 0, 100 ) / 100 < 0.6 ) $issues[] = 'missing_meta_description';
                if ( rand( 0, 100 ) / 100 < 0.5 ) $issues[] = 'no_alt_text';
                if ( rand( 0, 100 ) / 100 < 0.4 ) $issues[] = 'duplicate_content';
                if ( rand( 0, 100 ) / 100 < 0.3 ) $issues[] = 'missing_schema';
            }

            $mock_products[] = array(
                'id'          => $i,
                'name'        => "Produit $i - {$category['name']}",
                'category'    => $category['name'],
                'categoryId'  => $category['id'],
                'price'       => round( rand( 1000, 10000 ) / 100, 2 ),
                'stock'       => rand( 0, 100 ),
                'score'       => $score,
                'status'      => $status,
                'issues'      => $issues,
                'hasSchema'   => rand( 0, 100 ) / 100 < 0.6,
                'lastUpdated' => date( 'Y-m-d', strtotime( '-' . rand( 1, 30 ) . ' days' ) )
            );
        }

        return $mock_products;
    }
}
