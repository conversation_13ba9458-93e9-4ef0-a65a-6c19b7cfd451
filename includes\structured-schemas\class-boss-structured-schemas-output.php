<?php
/**
 * La classe qui gère l'affichage des schémas structurés.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 */

/**
 * La classe qui gère l'affichage des schémas structurés.
 *
 * Cette classe gère l'affichage des schémas structurés dans le frontend.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 * <AUTHOR> SEO Team
 */
class Boss_Structured_Schemas_Output {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Instance de la classe de gestion des schémas.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Structured_Schemas_Manager    $schema_manager    Gère les schémas.
     */
    protected $schema_manager;

    /**
     * Instance de la classe de gestion des règles.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Structured_Schemas_Rules    $rules_manager    Gère les règles d'application.
     */
    protected $rules_manager;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string                             $plugin_name     Le nom du plugin.
     * @param    string                             $version         La version du plugin.
     * @param    Boss_Optimizer_Settings            $settings        Instance de la classe de paramètres.
     * @param    Boss_Structured_Schemas_Manager    $schema_manager  Instance de la classe de gestion des schémas.
     * @param    Boss_Structured_Schemas_Rules      $rules_manager   Instance de la classe de gestion des règles.
     */
    public function __construct( $plugin_name, $version, $settings, $schema_manager, $rules_manager ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;
        $this->schema_manager = $schema_manager;
        $this->rules_manager = $rules_manager;
        
        $this->register_hooks();
    }

    /**
     * Enregistre les hooks WordPress nécessaires.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        add_action( 'wp_head', array( $this, 'output_schemas' ), 10 );
    }

    /**
     * Génère et affiche les schémas structurés dans le head.
     *
     * @since    1.2.0
     */
    public function output_schemas() {
        global $post;
        
        if ( ! is_singular() || ! $post ) {
            return;
        }
        
        // Obtenir les schémas applicables pour ce post
        $schemas = $this->get_schemas_for_post( $post->ID );
        
        if ( empty( $schemas ) ) {
            return;
        }
        
        foreach ( $schemas as $schema ) {
            echo "<!-- Boss SEO: Schéma structuré -->\n";
            echo "<script type=\"application/ld+json\">\n";
            echo json_encode( $schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE );
            echo "\n</script>\n";
        }
    }

    /**
     * Récupère les schémas applicables pour un post.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID du post.
     * @return   array                 Les schémas applicables.
     */
    public function get_schemas_for_post( $post_id ) {
        $post = get_post( $post_id );
        
        if ( ! $post ) {
            return array();
        }
        
        // Récupérer tous les schémas actifs
        $schemas = $this->schema_manager->get_schemas( array( 'active' => true ) );
        $applicable_schemas = array();
        
        foreach ( $schemas as $schema ) {
            // Vérifier si le schéma est applicable selon les règles
            if ( $this->rules_manager->is_schema_applicable( $schema['id'], $post ) ) {
                $applicable_schemas[] = $this->generate_schema_json( $schema['id'], $post );
            }
        }
        
        return $applicable_schemas;
    }

    /**
     * Génère le JSON-LD pour un schéma.
     *
     * @since    1.2.0
     * @param    int       $schema_id    L'ID du schéma.
     * @param    WP_Post   $post         Le post.
     * @return   array                   Le schéma au format JSON-LD.
     */
    public function generate_schema_json( $schema_id, $post ) {
        $schema = $this->schema_manager->get_schema( $schema_id );
        
        if ( is_wp_error( $schema ) ) {
            return array();
        }
        
        $json = array(
            '@context' => 'https://schema.org',
            '@type' => $schema['type']
        );
        
        // Ajouter les propriétés
        if ( ! empty( $schema['properties'] ) && is_array( $schema['properties'] ) ) {
            foreach ( $schema['properties'] as $key => $value ) {
                // Remplacer les variables dynamiques
                $value = $this->replace_dynamic_variables( $value, $post );
                
                if ( $value !== null && $value !== '' ) {
                    $json[ $key ] = $value;
                }
            }
        }
        
        return $json;
    }

    /**
     * Remplace les variables dynamiques dans une valeur.
     *
     * @since    1.2.0
     * @param    mixed     $value    La valeur.
     * @param    WP_Post   $post     Le post.
     * @return   mixed               La valeur avec les variables remplacées.
     */
    private function replace_dynamic_variables( $value, $post ) {
        if ( is_string( $value ) ) {
            // Remplacer les variables de base
            $value = str_replace( '%post_title%', $post->post_title, $value );
            $value = str_replace( '%post_content%', wp_strip_all_tags( $post->post_content ), $value );
            $value = str_replace( '%post_excerpt%', get_the_excerpt( $post ), $value );
            $value = str_replace( '%post_date%', get_the_date( 'c', $post ), $value );
            $value = str_replace( '%post_modified%', get_the_modified_date( 'c', $post ), $value );
            $value = str_replace( '%permalink%', get_permalink( $post ), $value );
            $value = str_replace( '%author_name%', get_the_author_meta( 'display_name', $post->post_author ), $value );
            $value = str_replace( '%author_url%', get_author_posts_url( $post->post_author ), $value );
            $value = str_replace( '%featured_image%', get_the_post_thumbnail_url( $post, 'full' ), $value );
            
            // Remplacer les métadonnées
            if ( strpos( $value, '%meta_' ) !== false ) {
                preg_match_all( '/%meta_(.*?)%/', $value, $matches );
                
                foreach ( $matches[1] as $meta_key ) {
                    $meta_value = get_post_meta( $post->ID, $meta_key, true );
                    $value = str_replace( "%meta_{$meta_key}%", $meta_value, $value );
                }
            }
            
            // Remplacer les taxonomies
            if ( strpos( $value, '%tax_' ) !== false ) {
                preg_match_all( '/%tax_(.*?)%/', $value, $matches );
                
                foreach ( $matches[1] as $tax_name ) {
                    $terms = get_the_terms( $post->ID, $tax_name );
                    $term_names = array();
                    
                    if ( $terms && ! is_wp_error( $terms ) ) {
                        foreach ( $terms as $term ) {
                            $term_names[] = $term->name;
                        }
                    }
                    
                    $value = str_replace( "%tax_{$tax_name}%", implode( ', ', $term_names ), $value );
                }
            }
        } elseif ( is_array( $value ) ) {
            foreach ( $value as $k => $v ) {
                $value[ $k ] = $this->replace_dynamic_variables( $v, $post );
            }
        }
        
        return $value;
    }
}
