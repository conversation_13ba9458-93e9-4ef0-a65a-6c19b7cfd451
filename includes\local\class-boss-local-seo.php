<?php
/**
 * Classe principale pour le module SEO local.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe principale pour le module SEO local.
 *
 * Cette classe gère toutes les fonctionnalités du module SEO local.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_SEO {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de gestion des emplacements.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Location    $location    Instance de la classe de gestion des emplacements.
     */
    protected $location;

    /**
     * Instance de la classe de gestion des informations d'entreprise.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Business_Info    $business_info    Instance de la classe de gestion des informations d'entreprise.
     */
    protected $business_info;

    /**
     * Instance de la classe de génération de pages locales.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Page_Generator    $page_generator    Instance de la classe de génération de pages locales.
     */
    protected $page_generator;

    /**
     * Instance de la classe de gestion des schémas locaux.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Schema    $schema    Instance de la classe de gestion des schémas locaux.
     */
    protected $schema;

    /**
     * Instance de la classe de suivi des positions locales.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Rankings    $rankings    Instance de la classe de suivi des positions locales.
     */
    protected $rankings;

    /**
     * Instance de la classe d'analyse SEO locale.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Local_Analysis    $analysis    Instance de la classe d'analyse SEO locale.
     */
    protected $analysis;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->init_components();
    }

    /**
     * Initialise les composants du module SEO local.
     *
     * @since    1.2.0
     * @access   private
     */
    private function init_components() {
        $this->location = new Boss_Location( $this->plugin_name, $this->version );
        $this->business_info = new Boss_Business_Info( $this->plugin_name, $this->version );
        $this->page_generator = new Boss_Local_Page_Generator( $this->plugin_name, $this->version );
        $this->schema = new Boss_Local_Schema( $this->plugin_name, $this->version );
        $this->rankings = new Boss_Local_Rankings( $this->plugin_name, $this->version );
        $this->analysis = new Boss_Local_Analysis( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Enregistrer les hooks pour les composants
        $this->location->register_hooks();
        $this->business_info->register_hooks();
        $this->page_generator->register_hooks();
        $this->schema->register_hooks();
        $this->rankings->register_hooks();
        $this->analysis->register_hooks();

        // Ajouter les hooks pour les assets
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_styles' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Enregistrer les routes REST API pour les composants
        $this->location->register_rest_routes();
        $this->business_info->register_rest_routes();
        $this->page_generator->register_rest_routes();
        $this->schema->register_rest_routes();
        $this->rankings->register_rest_routes();
        $this->analysis->register_rest_routes();
    }

    /**
     * Enregistre les styles pour ce module.
     *
     * @since    1.2.0
     */
    public function enqueue_styles() {
        $screen = get_current_screen();
        if ( $screen->id === 'boss-seo_page_boss-seo-local' ) {
            wp_enqueue_style( 'leaflet', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css', array(), '1.7.1' );
            wp_enqueue_style( 'boss-seo-local', plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/css/boss-seo-local.css', array(), $this->version, 'all' );
        }
    }

    /**
     * Enregistre les scripts pour ce module.
     *
     * @since    1.2.0
     */
    public function enqueue_scripts() {
        $screen = get_current_screen();
        if ( $screen->id === 'boss-seo_page_boss-seo-local' ) {
            wp_enqueue_script( 'leaflet', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js', array(), '1.7.1', true );
            wp_enqueue_script( 'boss-seo-local', plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/js/boss-seo-local.js', array( 'jquery', 'leaflet' ), $this->version, true );
            
            // Localiser le script avec les données nécessaires
            wp_localize_script( 'boss-seo-local', 'bossSeoLocalData', array(
                'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'boss_seo_local_nonce' ),
                'restUrl' => rest_url( 'boss-seo/v1' ),
                'restNonce' => wp_create_nonce( 'wp_rest' ),
                'mapboxToken' => get_option( 'boss_seo_mapbox_token', '' ),
                'defaultLat' => get_option( 'boss_seo_default_lat', '48.8566' ),
                'defaultLng' => get_option( 'boss_seo_default_lng', '2.3522' ),
                'defaultZoom' => get_option( 'boss_seo_default_zoom', '13' ),
            ) );
        }
    }
}
