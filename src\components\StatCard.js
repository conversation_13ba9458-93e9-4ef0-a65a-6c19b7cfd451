import { Card, CardBody, Dashicon } from '@wordpress/components';

const StatCard = ({ title, value, icon, color, trend }) => {
  // Déterminer si la tendance est positive ou négative
  const isTrendPositive = trend && trend.startsWith('+');
  const trendColor = isTrendPositive ? 'boss-success' : 'boss-error';

  return (
    <Card className="boss-card">
      <CardBody>
        <div className="boss-flex boss-justify-between boss-items-start">
          <div>
            <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">{title}</h3>
            <div className="boss-flex boss-items-baseline">
              <span className="boss-text-2xl boss-font-bold boss-mr-2">{value}</span>
              {trend && (
                <span className={`boss-text-${trendColor} boss-text-sm boss-font-medium`}>
                  {trend}
                </span>
              )}
            </div>
          </div>
          <div className={`boss-bg-${color}/10 boss-p-2 boss-rounded-lg`}>
            <Dashicon icon={icon} className={`boss-text-${color} boss-text-xl`} />
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default StatCard;
