/**
 * Service pour la gestion des services externes
 *
 * Gère les communications avec l'API pour les fonctionnalités de services externes
 */

import apiFetch from '@wordpress/api-fetch';

class ExternalServicesService {
  /**
   * Récupère les paramètres des services externes
   *
   * @returns {Promise} Promesse contenant les paramètres des services externes
   */
  async getExternalServicesSettings() {
    try {
      const path = '/boss-seo/v1/external-services/settings';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres des services externes:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres des services externes
   *
   * @param {Object} settings - Nouveaux paramètres des services externes
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async saveExternalServicesSettings(settings) {
    try {
      const path = '/boss-seo/v1/external-services/settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { settings }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres des services externes:', error);
      throw error;
    }
  }

  /**
   * Vérifie la validité d'une clé API
   *
   * @param {string} service - Nom du service
   * @param {string} apiKey - Clé API à vérifier
   * @returns {Promise} Promesse contenant le résultat de la vérification
   */
  async verifyApiKey(service, apiKey) {
    try {
      const path = '/boss-seo/v1/external-services/verify-api-key';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          service,
          api_key: apiKey
        }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la vérification de la clé API ${service}:`, error);
      throw error;
    }
  }

  /**
   * Récupère la liste des services disponibles
   *
   * @returns {Promise} Promesse contenant la liste des services disponibles
   */
  async getAvailableServices() {
    try {
      const path = '/boss-seo/v1/external-services/available';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des services disponibles:', error);
      throw error;
    }
  }

  /**
   * Récupère le statut de connexion des services
   *
   * @returns {Promise} Promesse contenant le statut de connexion des services
   */
  async getServicesStatus() {
    try {
      const path = '/boss-seo/v1/external-services/status';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération du statut des services:', error);
      throw error;
    }
  }

  /**
   * Connecte un compte Google Search Console
   *
   * @param {string} authCode - Code d'autorisation OAuth
   * @returns {Promise} Promesse contenant le résultat de la connexion
   */
  async connectGoogleSearchConsole(authCode) {
    try {
      const path = '/boss-seo/v1/external-services/connect-google-search-console';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { auth_code: authCode }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la connexion à Google Search Console:', error);
      throw error;
    }
  }

  /**
   * Déconnecte un compte Google Search Console
   *
   * @returns {Promise} Promesse contenant le résultat de la déconnexion
   */
  async disconnectGoogleSearchConsole() {
    try {
      const path = '/boss-seo/v1/external-services/disconnect-google-search-console';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la déconnexion de Google Search Console:', error);
      throw error;
    }
  }

  /**
   * Récupère l'URL d'autorisation OAuth pour Google
   *
   * @returns {Promise} Promesse contenant l'URL d'autorisation
   */
  async getGoogleAuthUrl() {
    try {
      const path = '/boss-seo/v1/external-services/google-auth-url';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'URL d\'autorisation Google:', error);
      throw error;
    }
  }

  /**
   * Récupère l'URL d'autorisation OAuth pour Google My Business
   *
   * @returns {Promise} Promesse contenant l'URL d'autorisation
   */
  async getGMBAuthUrl() {
    try {
      const path = '/boss-seo/v1/external-services/gmb/auth-url';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'URL d\'autorisation Google My Business:', error);
      throw error;
    }
  }

  /**
   * Connecte un compte Google My Business
   *
   * @param {string} authCode - Code d'autorisation OAuth
   * @returns {Promise} Promesse contenant le résultat de la connexion
   */
  async connectGMB(authCode) {
    try {
      const path = '/boss-seo/v1/external-services/gmb/connect';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { auth_code: authCode }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la connexion à Google My Business:', error);
      throw error;
    }
  }

  /**
   * Déconnecte un compte Google My Business
   *
   * @returns {Promise} Promesse contenant le résultat de la déconnexion
   */
  async disconnectGMB() {
    try {
      const path = '/boss-seo/v1/external-services/gmb/disconnect';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la déconnexion de Google My Business:', error);
      throw error;
    }
  }

  /**
   * Récupère les comptes Google My Business
   *
   * @returns {Promise} Promesse contenant les comptes
   */
  async getGMBAccounts() {
    try {
      const path = '/boss-seo/v1/local/gmb/accounts';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des comptes Google My Business:', error);
      throw error;
    }
  }

  /**
   * Récupère les établissements Google My Business
   *
   * @param {string} accountId - ID du compte Google My Business
   * @returns {Promise} Promesse contenant les établissements
   */
  async getGMBLocations(accountId) {
    try {
      const path = `/boss-seo/v1/local/gmb/locations?account_id=${encodeURIComponent(accountId)}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des établissements Google My Business:', error);
      throw error;
    }
  }

  /**
   * Importe un établissement Google My Business
   *
   * @param {string} accountId - ID du compte Google My Business
   * @param {string} locationId - ID de l'établissement Google My Business
   * @returns {Promise} Promesse contenant le résultat de l'importation
   */
  async importGMBLocation(accountId, locationId) {
    try {
      const path = '/boss-seo/v1/local/gmb/import';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          account_id: accountId,
          location_id: locationId
        }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'importation de l\'établissement Google My Business:', error);
      throw error;
    }
  }

  /**
   * Synchronise un établissement avec Google My Business
   *
   * @param {number} locationId - ID de l'emplacement WordPress
   * @returns {Promise} Promesse contenant le résultat de la synchronisation
   */
  async syncGMBLocation(locationId) {
    try {
      const path = '/boss-seo/v1/local/gmb/sync';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { location_id: locationId }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la synchronisation de l\'établissement avec Google My Business:', error);
      throw error;
    }
  }
}

export default new ExternalServicesService();
