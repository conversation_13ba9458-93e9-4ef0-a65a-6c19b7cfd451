import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Modal,
  Button,
  TextControl,
  TextareaControl,
  SelectControl,
  Notice,
  Spinner,
  Flex,
  FlexItem,
  FlexBlock,
  __experimentalSpacer as Spacer,
  __experimentalHeading as Heading,
  __experimentalText as Text
} from '@wordpress/components';

/**
 * Composant modal pour ajouter/modifier un emplacement
 */
const LocationFormModal = ({ 
  isOpen, 
  onClose, 
  onSave, 
  location = null, 
  isLoading = false,
  error = null 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    city: '',
    postalCode: '',
    country: 'FR',
    phone: '',
    email: '',
    website: '',
    description: '',
    primaryCategory: '',
    status: 'pending'
  });

  const [validationErrors, setValidationErrors] = useState({});

  // Initialiser le formulaire avec les données de l'emplacement
  useEffect(() => {
    if (location) {
      setFormData({
        name: location.name || location.title || '',
        address: location.address || '',
        city: location.city || '',
        postalCode: location.postalCode || location.postal_code || '',
        country: location.country || 'FR',
        phone: location.phone || '',
        email: location.email || '',
        website: location.website || '',
        description: location.description || location.content || '',
        primaryCategory: location.primaryCategory || (location.types && location.types[0]) || '',
        status: location.status || 'pending'
      });
    } else {
      // Réinitialiser pour un nouvel emplacement
      setFormData({
        name: '',
        address: '',
        city: '',
        postalCode: '',
        country: 'FR',
        phone: '',
        email: '',
        website: '',
        description: '',
        primaryCategory: '',
        status: 'pending'
      });
    }
    setValidationErrors({});
  }, [location, isOpen]);

  /**
   * Valide les données du formulaire
   */
  const validateForm = () => {
    const errors = {};

    // Champs requis
    if (!formData.name || formData.name.trim() === '') {
      errors.name = __('Le nom de l\'emplacement est requis.', 'boss-seo');
    }

    if (!formData.address || formData.address.trim() === '') {
      errors.address = __('L\'adresse est requise.', 'boss-seo');
    }

    if (!formData.city || formData.city.trim() === '') {
      errors.city = __('La ville est requise.', 'boss-seo');
    }

    // Validation de l'email
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = __('L\'adresse email n\'est pas valide.', 'boss-seo');
    }

    // Validation du site web
    if (formData.website && !/^https?:\/\/.+/.test(formData.website)) {
      errors.website = __('L\'URL du site web doit commencer par http:// ou https://', 'boss-seo');
    }

    // Validation du téléphone
    if (formData.phone && !/^[\+]?[0-9\s\-\(\)\.]{10,}$/.test(formData.phone)) {
      errors.phone = __('Le numéro de téléphone n\'est pas valide.', 'boss-seo');
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  /**
   * Gère la soumission du formulaire
   */
  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    onSave(formData);
  };

  /**
   * Met à jour les données du formulaire
   */
  const updateFormData = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Supprimer l'erreur de validation pour ce champ
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <Modal
      title={location ? __('Modifier l\'emplacement', 'boss-seo') : __('Ajouter un emplacement', 'boss-seo')}
      onRequestClose={onClose}
      className="boss-location-modal"
      style={{ maxWidth: '600px' }}
    >
      <div className="boss-location-form">
        {/* Erreur globale */}
        {error && (
          <Notice status="error" isDismissible={false}>
            {error}
          </Notice>
        )}

        <Spacer marginY={4} />

        {/* Informations de base */}
        <Heading level={4}>
          {__('Informations de base', 'boss-seo')}
        </Heading>

        <TextControl
          label={__('Nom de l\'emplacement', 'boss-seo')}
          value={formData.name}
          onChange={(value) => updateFormData('name', value)}
          placeholder={__('Ex: Restaurant Le Gourmet', 'boss-seo')}
          help={validationErrors.name || __('Nom qui apparaîtra dans les résultats de recherche', 'boss-seo')}
          className={validationErrors.name ? 'has-error' : ''}
        />

        <TextareaControl
          label={__('Description', 'boss-seo')}
          value={formData.description}
          onChange={(value) => updateFormData('description', value)}
          placeholder={__('Décrivez votre établissement...', 'boss-seo')}
          help={__('Description détaillée pour améliorer le SEO local', 'boss-seo')}
          rows={3}
        />

        <SelectControl
          label={__('Catégorie principale', 'boss-seo')}
          value={formData.primaryCategory}
          onChange={(value) => updateFormData('primaryCategory', value)}
          options={[
            { label: __('Sélectionner une catégorie', 'boss-seo'), value: '' },
            { label: __('Restaurant', 'boss-seo'), value: 'restaurant' },
            { label: __('Boutique', 'boss-seo'), value: 'store' },
            { label: __('Service', 'boss-seo'), value: 'service' },
            { label: __('Hôtel', 'boss-seo'), value: 'lodging' },
            { label: __('Santé', 'boss-seo'), value: 'health' },
            { label: __('Autre', 'boss-seo'), value: 'other' }
          ]}
          help={__('Catégorie principale de votre établissement', 'boss-seo')}
        />

        <Spacer marginY={6} />

        {/* Adresse */}
        <Heading level={4}>
          {__('Adresse', 'boss-seo')}
        </Heading>

        <TextControl
          label={__('Adresse', 'boss-seo')}
          value={formData.address}
          onChange={(value) => updateFormData('address', value)}
          placeholder={__('123 Rue de la Paix', 'boss-seo')}
          help={validationErrors.address || __('Adresse complète de l\'établissement', 'boss-seo')}
          className={validationErrors.address ? 'has-error' : ''}
        />

        <Flex gap={4}>
          <FlexBlock>
            <TextControl
              label={__('Ville', 'boss-seo')}
              value={formData.city}
              onChange={(value) => updateFormData('city', value)}
              placeholder={__('Paris', 'boss-seo')}
              help={validationErrors.city}
              className={validationErrors.city ? 'has-error' : ''}
            />
          </FlexBlock>
          <FlexItem>
            <TextControl
              label={__('Code postal', 'boss-seo')}
              value={formData.postalCode}
              onChange={(value) => updateFormData('postalCode', value)}
              placeholder={__('75001', 'boss-seo')}
              style={{ width: '120px' }}
            />
          </FlexItem>
        </Flex>

        <SelectControl
          label={__('Pays', 'boss-seo')}
          value={formData.country}
          onChange={(value) => updateFormData('country', value)}
          options={[
            { label: __('France', 'boss-seo'), value: 'FR' },
            { label: __('Belgique', 'boss-seo'), value: 'BE' },
            { label: __('Suisse', 'boss-seo'), value: 'CH' },
            { label: __('Canada', 'boss-seo'), value: 'CA' },
            { label: __('Autre', 'boss-seo'), value: 'OTHER' }
          ]}
        />

        <Spacer marginY={6} />

        {/* Contact */}
        <Heading level={4}>
          {__('Informations de contact', 'boss-seo')}
        </Heading>

        <TextControl
          label={__('Téléphone', 'boss-seo')}
          value={formData.phone}
          onChange={(value) => updateFormData('phone', value)}
          placeholder={__('01 42 86 87 88', 'boss-seo')}
          help={validationErrors.phone || __('Numéro de téléphone principal', 'boss-seo')}
          className={validationErrors.phone ? 'has-error' : ''}
        />

        <TextControl
          label={__('Email', 'boss-seo')}
          value={formData.email}
          onChange={(value) => updateFormData('email', value)}
          placeholder={__('<EMAIL>', 'boss-seo')}
          help={validationErrors.email || __('Adresse email de contact', 'boss-seo')}
          className={validationErrors.email ? 'has-error' : ''}
        />

        <TextControl
          label={__('Site web', 'boss-seo')}
          value={formData.website}
          onChange={(value) => updateFormData('website', value)}
          placeholder={__('https://www.exemple.fr', 'boss-seo')}
          help={validationErrors.website || __('URL du site web', 'boss-seo')}
          className={validationErrors.website ? 'has-error' : ''}
        />

        <Spacer marginY={6} />

        {/* Statut */}
        <SelectControl
          label={__('Statut', 'boss-seo')}
          value={formData.status}
          onChange={(value) => updateFormData('status', value)}
          options={[
            { label: __('En attente', 'boss-seo'), value: 'pending' },
            { label: __('Actif', 'boss-seo'), value: 'active' },
            { label: __('Inactif', 'boss-seo'), value: 'inactive' }
          ]}
          help={__('Statut de publication de l\'emplacement', 'boss-seo')}
        />

        <Spacer marginY={6} />

        {/* Boutons d'action */}
        <Flex justify="flex-end" gap={3}>
          <FlexItem>
            <Button
              isSecondary
              onClick={onClose}
              disabled={isLoading}
            >
              {__('Annuler', 'boss-seo')}
            </Button>
          </FlexItem>
          <FlexItem>
            <Button
              isPrimary
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Spinner />
                  {location ? __('Modification...', 'boss-seo') : __('Ajout...', 'boss-seo')}
                </>
              ) : (
                location ? __('Modifier', 'boss-seo') : __('Ajouter', 'boss-seo')
              )}
            </Button>
          </FlexItem>
        </Flex>
      </div>
    </Modal>
  );
};

export default LocationFormModal;
