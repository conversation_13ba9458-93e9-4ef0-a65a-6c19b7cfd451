import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { motion } from 'framer-motion';

// Composants
import Sidebar from './components/Sidebar';
import Dashboard from './pages/Dashboard';
import BossOptimizer from './pages/BossOptimizer';
import TechnicalAnalysis from './pages/TechnicalAnalysis';
import ContentOptimization from './pages/ContentOptimization';
import ContentOptimizationMultistep from './pages/ContentOptimizationMultistep';
import SchemaManager from './pages/SchemaManager';
import AnalyticsManager from './pages/AnalyticsManager';
import LocalEcommerce from './pages/LocalEcommerce';
import ReportsSettings from './pages/ReportsSettings';
import TechnicalManagement from './pages/TechnicalManagement';
import Help from './pages/Help';

const App = ({ initialPage = 'dashboard' }) => {
  const [currentPage, setCurrentPage] = useState(initialPage);

  // Animation pour la transition entre les pages
  const pageVariants = {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 }
  };

  // Rendu de la page active
  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <Dashboard setCurrentPage={setCurrentPage} />
          </motion.div>
        );
      case 'optimizer':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <BossOptimizer />
          </motion.div>
        );
      case 'technical':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <TechnicalAnalysis />
          </motion.div>
        );
      case 'content':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <ContentOptimization />
          </motion.div>
        );
      case 'content_multistep':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <ContentOptimizationMultistep />
          </motion.div>
        );
      case 'schema':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <SchemaManager />
          </motion.div>
        );
      case 'analytics':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <AnalyticsManager />
          </motion.div>
        );
      case 'local':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <LocalEcommerce />
          </motion.div>
        );
      case 'technical_management':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <TechnicalManagement />
          </motion.div>
        );
      case 'reports':
      case 'settings':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <ReportsSettings />
          </motion.div>
        );
      case 'help':
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <Help />
          </motion.div>
        );
      default:
        return (
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            transition={{ duration: 0.3 }}
          >
            <div className="boss-p-8">
              <h2 className="boss-text-2xl boss-font-bold boss-mb-4">
                {__('Page en développement', 'boss-seo')}
              </h2>
              <p className="boss-text-boss-gray">
                {__('Cette fonctionnalité sera bientôt disponible.', 'boss-seo')}
              </p>
            </div>
          </motion.div>
        );
    }
  };

  return (
    <div className="boss-flex boss-h-full boss-bg-boss-light boss-text-boss-dark boss-rounded-xl boss-overflow-hidden boss-shadow-lg boss-my-4">
      {/* Sidebar */}
      <Sidebar currentPage={currentPage} setCurrentPage={setCurrentPage} />

      {/* Contenu principal */}
      <div className="boss-flex-1 boss-overflow-auto">
        {renderPage()}
      </div>
    </div>
  );
};

export default App;
