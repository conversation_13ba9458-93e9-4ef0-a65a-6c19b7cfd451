import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Dashicon,
  SelectControl,
  TextControl,
  ToggleControl,
  Tooltip
} from '@wordpress/components';
import { PagesDistributionChart } from './ChartComponents';

const PopularPages = ({ data, selectedPeriod, onPeriodChange }) => {
  // États
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('pageviews');
  const [sortOrder, setSortOrder] = useState('desc');
  const [filterType, setFilterType] = useState('all');
  
  // Fonction pour filtrer les pages
  const getFilteredPages = () => {
    if (!data) return [];
    
    let filtered = [...data];
    
    // Filtrer par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(page => 
        page.title.toLowerCase().includes(query) || 
        page.url.toLowerCase().includes(query)
      );
    }
    
    // Filtrer par type
    if (filterType !== 'all') {
      filtered = filtered.filter(page => {
        if (filterType === 'blog' && page.url.includes('/blog/')) return true;
        if (filterType === 'product' && page.url.includes('/produits/')) return true;
        if (filterType === 'landing' && (page.url === '/' || page.url.includes('/landing/'))) return true;
        return false;
      });
    }
    
    // Trier les résultats
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'url':
          comparison = a.url.localeCompare(b.url);
          break;
        case 'pageviews':
          comparison = b.pageviews - a.pageviews;
          break;
        case 'avgTimeOnPage':
          comparison = b.avgTimeOnPage - a.avgTimeOnPage;
          break;
        case 'bounceRate':
          comparison = a.bounceRate - b.bounceRate;
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
    
    return filtered;
  };
  
  // Obtenir les pages filtrées
  const filteredPages = getFilteredPages();
  
  // Fonction pour basculer l'ordre de tri
  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };
  
  // Fonction pour définir la colonne de tri
  const handleSort = (column) => {
    if (sortBy === column) {
      toggleSortOrder();
    } else {
      setSortBy(column);
      setSortOrder(column === 'title' || column === 'url' ? 'asc' : 'desc');
    }
  };
  
  // Fonction pour obtenir l'icône de tri
  const getSortIcon = (column) => {
    if (sortBy !== column) return null;
    return sortOrder === 'asc' ? 'arrow-up-alt2' : 'arrow-down-alt2';
  };
  
  // Fonction pour formater le temps
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du taux de rebond
  const getBounceRateColorClass = (rate) => {
    if (rate < 30) return 'boss-text-green-600';
    if (rate < 50) return 'boss-text-blue-600';
    if (rate < 70) return 'boss-text-yellow-600';
    return 'boss-text-red-500';
  };
  
  // Calculer les statistiques des pages
  const calculatePageStats = () => {
    if (!data || data.length === 0) return { 
      totalPageviews: 0, 
      avgTimeOnPage: 0, 
      avgBounceRate: 0 
    };
    
    const totalPageviews = data.reduce((sum, page) => sum + page.pageviews, 0);
    const avgTimeOnPage = data.reduce((sum, page) => sum + page.avgTimeOnPage, 0) / data.length;
    const avgBounceRate = data.reduce((sum, page) => sum + page.bounceRate, 0) / data.length;
    
    return {
      totalPageviews,
      avgTimeOnPage,
      avgBounceRate
    };
  };
  
  const pageStats = calculatePageStats();

  return (
    <div>
      {/* Statistiques des pages */}
      <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4 boss-mb-6">
        <Card className="boss-card">
          <CardBody className="boss-p-4">
            <div className="boss-text-center">
              <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                {__('Pages vues totales', 'boss-seo')}
              </h3>
              <div className="boss-text-2xl boss-font-bold boss-text-boss-primary">
                {pageStats.totalPageviews.toLocaleString()}
              </div>
            </div>
          </CardBody>
        </Card>
        
        <Card className="boss-card">
          <CardBody className="boss-p-4">
            <div className="boss-text-center">
              <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                {__('Temps moyen sur page', 'boss-seo')}
              </h3>
              <div className="boss-text-2xl boss-font-bold boss-text-boss-success">
                {formatTime(pageStats.avgTimeOnPage)}
              </div>
            </div>
          </CardBody>
        </Card>
        
        <Card className="boss-card">
          <CardBody className="boss-p-4">
            <div className="boss-text-center">
              <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                {__('Taux de rebond moyen', 'boss-seo')}
              </h3>
              <div className={`boss-text-2xl boss-font-bold ${getBounceRateColorClass(pageStats.avgBounceRate)}`}>
                {pageStats.avgBounceRate.toFixed(1)}%
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
      
      {/* Graphique de distribution */}
      <Card className="boss-mb-6">
        <CardHeader className="boss-border-b boss-border-gray-200">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Distribution des pages vues', 'boss-seo')}
          </h2>
        </CardHeader>
        <CardBody>
          <PagesDistributionChart data={filteredPages.slice(0, 10)} />
        </CardBody>
      </Card>
      
      {/* Filtres et tableau */}
      <Card>
        <CardHeader className="boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-flex-col md:boss-flex-row boss-justify-between boss-items-start md:boss-items-center boss-gap-4">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Pages populaires', 'boss-seo')}
            </h2>
            
            <div className="boss-flex boss-flex-col sm:boss-flex-row boss-gap-3">
              <TextControl
                placeholder={__('Rechercher une page...', 'boss-seo')}
                value={searchQuery}
                onChange={setSearchQuery}
                className="boss-min-w-[200px]"
              />
              
              <SelectControl
                value={filterType}
                options={[
                  { label: __('Tous les types', 'boss-seo'), value: 'all' },
                  { label: __('Blog', 'boss-seo'), value: 'blog' },
                  { label: __('Produits', 'boss-seo'), value: 'product' },
                  { label: __('Landing pages', 'boss-seo'), value: 'landing' }
                ]}
                onChange={setFilterType}
              />
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="boss-p-0">
          <div className="boss-overflow-x-auto">
            <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
              <thead className="boss-bg-gray-50">
                <tr>
                  <th 
                    className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-cursor-pointer"
                    onClick={() => handleSort('title')}
                  >
                    <div className="boss-flex boss-items-center">
                      {__('Titre', 'boss-seo')}
                      {getSortIcon('title') && <Dashicon icon={getSortIcon('title')} className="boss-ml-1" />}
                    </div>
                  </th>
                  <th 
                    className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-cursor-pointer"
                    onClick={() => handleSort('url')}
                  >
                    <div className="boss-flex boss-items-center">
                      {__('URL', 'boss-seo')}
                      {getSortIcon('url') && <Dashicon icon={getSortIcon('url')} className="boss-ml-1" />}
                    </div>
                  </th>
                  <th 
                    className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-cursor-pointer"
                    onClick={() => handleSort('pageviews')}
                  >
                    <div className="boss-flex boss-items-center">
                      {__('Pages vues', 'boss-seo')}
                      {getSortIcon('pageviews') && <Dashicon icon={getSortIcon('pageviews')} className="boss-ml-1" />}
                    </div>
                  </th>
                  <th 
                    className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-cursor-pointer"
                    onClick={() => handleSort('avgTimeOnPage')}
                  >
                    <div className="boss-flex boss-items-center">
                      {__('Temps moyen', 'boss-seo')}
                      {getSortIcon('avgTimeOnPage') && <Dashicon icon={getSortIcon('avgTimeOnPage')} className="boss-ml-1" />}
                    </div>
                  </th>
                  <th 
                    className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-cursor-pointer"
                    onClick={() => handleSort('bounceRate')}
                  >
                    <div className="boss-flex boss-items-center">
                      {__('Taux de rebond', 'boss-seo')}
                      {getSortIcon('bounceRate') && <Dashicon icon={getSortIcon('bounceRate')} className="boss-ml-1" />}
                    </div>
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-right boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Actions', 'boss-seo')}
                  </th>
                </tr>
              </thead>
              <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                {filteredPages.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="boss-px-6 boss-py-4 boss-text-center boss-text-boss-gray">
                      {__('Aucune page trouvée.', 'boss-seo')}
                    </td>
                  </tr>
                ) : (
                  filteredPages.map((page, index) => (
                    <tr key={index} className="boss-hover:boss-bg-gray-50">
                      <td className="boss-px-6 boss-py-4">
                        <div className="boss-font-medium boss-text-boss-dark">{page.title}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4">
                        <div className="boss-text-boss-gray boss-text-sm">{page.url}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-font-medium boss-text-boss-primary">{page.pageviews.toLocaleString()}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{formatTime(page.avgTimeOnPage)}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className={`boss-font-medium ${getBounceRateColorClass(page.bounceRate)}`}>
                          {page.bounceRate.toFixed(1)}%
                        </div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-right">
                        <div className="boss-flex boss-justify-end boss-space-x-2">
                          <Tooltip text={__('Voir la page', 'boss-seo')}>
                            <Button
                              isSecondary
                              isSmall
                              icon="external"
                            />
                          </Tooltip>
                          <Tooltip text={__('Analyser', 'boss-seo')}>
                            <Button
                              isSecondary
                              isSmall
                              icon="search"
                            />
                          </Tooltip>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default PopularPages;
