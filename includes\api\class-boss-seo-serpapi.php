<?php
/**
 * La classe d'intégration avec SerpAPI.
 *
 * Cette classe gère l'intégration avec SerpAPI pour la recherche de mots-clés.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/api
 */

/**
 * La classe d'intégration avec SerpAPI.
 *
 * Cette classe gère l'intégration avec SerpAPI pour la recherche de mots-clés.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/api
 * <AUTHOR> SEO Team
 */
class Boss_Seo_SerpAPI {
    /**
     * La clé API SerpAPI.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $api_key    La clé API SerpAPI.
     */
    private $api_key;
    
    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $api_key    La clé API SerpAPI.
     */
    public function __construct( $api_key ) {
        $this->api_key = $api_key;
    }
    
    /**
     * Recherche des mots-clés via SerpAPI.
     *
     * @since    1.1.0
     * @param    string    $query      La requête de recherche.
     * @param    string    $language   La langue (fr, en, etc.).
     * @param    string    $country    Le pays (fr, us, etc.).
     * @return   array|WP_Error        Les résultats de la recherche ou une erreur.
     */
    public function search_keywords( $query, $language = 'fr', $country = 'fr' ) {
        // Construire l'URL de l'API
        $url = add_query_arg(
            array(
                'engine'  => 'google',
                'q'       => urlencode( $query ),
                'api_key' => $this->api_key,
                'gl'      => $country,
                'hl'      => $language,
            ),
            'https://serpapi.com/search'
        );
        
        // Effectuer la requête
        $response = wp_remote_get( $url );
        
        // Vérifier les erreurs
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        // Récupérer le corps de la réponse
        $body = json_decode( wp_remote_retrieve_body( $response ), true );
        
        // Vérifier si la réponse contient une erreur
        if ( isset( $body['error'] ) ) {
            return new WP_Error( 'serpapi_error', $body['error'] );
        }
        
        // Extraire les suggestions de mots-clés
        $keywords = array();
        
        // Extraire les recherches associées
        if ( isset( $body['related_searches'] ) ) {
            foreach ( $body['related_searches'] as $related ) {
                $keywords[] = array(
                    'text'       => $related['query'],
                    'volume'     => $this->get_random_volume(), // SerpAPI ne fournit pas le volume, simulé ici
                    'difficulty' => $this->get_random_difficulty(), // SerpAPI ne fournit pas la difficulté, simulée ici
                    'cpc'        => $this->get_random_cpc(), // SerpAPI ne fournit pas le CPC, simulé ici
                );
            }
        }
        
        // Extraire les suggestions de recherche
        if ( isset( $body['suggestions'] ) ) {
            foreach ( $body['suggestions'] as $suggestion ) {
                // Éviter les doublons
                if ( ! $this->keyword_exists( $keywords, $suggestion ) ) {
                    $keywords[] = array(
                        'text'       => $suggestion,
                        'volume'     => $this->get_random_volume(),
                        'difficulty' => $this->get_random_difficulty(),
                        'cpc'        => $this->get_random_cpc(),
                    );
                }
            }
        }
        
        return $keywords;
    }
    
    /**
     * Récupère les mots-clés connexes.
     *
     * @since    1.1.0
     * @param    string    $keyword    Le mot-clé principal.
     * @param    string    $language   La langue (fr, en, etc.).
     * @param    string    $country    Le pays (fr, us, etc.).
     * @return   array|WP_Error        Les mots-clés connexes ou une erreur.
     */
    public function get_related_keywords( $keyword, $language = 'fr', $country = 'fr' ) {
        return $this->search_keywords( $keyword, $language, $country );
    }
    
    /**
     * Récupère les mots-clés à longue traîne.
     *
     * @since    1.1.0
     * @param    string    $keyword    Le mot-clé principal.
     * @param    string    $language   La langue (fr, en, etc.).
     * @param    string    $country    Le pays (fr, us, etc.).
     * @return   array|WP_Error        Les mots-clés à longue traîne ou une erreur.
     */
    public function get_long_tail_keywords( $keyword, $language = 'fr', $country = 'fr' ) {
        // Construire des préfixes et suffixes pour les mots-clés à longue traîne
        $prefixes = array( 'comment', 'pourquoi', 'meilleur', 'top', 'guide', 'tutoriel', 'comparatif', 'avis' );
        $suffixes = array( 'pas cher', 'gratuit', 'en ligne', 'professionnel', 'facile', 'rapide', 'pour débutant', 'avancé' );
        
        $long_tail_keywords = array();
        
        // Générer des mots-clés à longue traîne avec des préfixes
        foreach ( $prefixes as $prefix ) {
            $long_tail_keywords[] = array(
                'text'       => $prefix . ' ' . $keyword,
                'volume'     => $this->get_random_volume( 100, 1000 ), // Volume plus faible pour les mots-clés à longue traîne
                'difficulty' => $this->get_random_difficulty( 10, 40 ), // Difficulté plus faible pour les mots-clés à longue traîne
                'cpc'        => $this->get_random_cpc( 0.1, 0.5 ), // CPC plus faible pour les mots-clés à longue traîne
            );
        }
        
        // Générer des mots-clés à longue traîne avec des suffixes
        foreach ( $suffixes as $suffix ) {
            $long_tail_keywords[] = array(
                'text'       => $keyword . ' ' . $suffix,
                'volume'     => $this->get_random_volume( 100, 1000 ),
                'difficulty' => $this->get_random_difficulty( 10, 40 ),
                'cpc'        => $this->get_random_cpc( 0.1, 0.5 ),
            );
        }
        
        return $long_tail_keywords;
    }
    
    /**
     * Vérifie si un mot-clé existe déjà dans la liste.
     *
     * @since    1.1.0
     * @access   private
     * @param    array     $keywords    La liste des mots-clés.
     * @param    string    $keyword     Le mot-clé à vérifier.
     * @return   boolean                True si le mot-clé existe, false sinon.
     */
    private function keyword_exists( $keywords, $keyword ) {
        foreach ( $keywords as $existing_keyword ) {
            if ( strtolower( $existing_keyword['text'] ) === strtolower( $keyword ) ) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Génère un volume de recherche aléatoire.
     *
     * @since    1.1.0
     * @access   private
     * @param    int       $min    Le volume minimum.
     * @param    int       $max    Le volume maximum.
     * @return   string            Le volume de recherche formaté.
     */
    private function get_random_volume( $min = 1000, $max = 10000 ) {
        $volume = rand( $min, $max );
        
        if ( $volume >= 1000 ) {
            return floor( $volume / 1000 ) . 'K-' . ceil( $volume / 1000 ) . 'K';
        }
        
        return $volume . '-' . ( $volume + 100 );
    }
    
    /**
     * Génère une difficulté aléatoire.
     *
     * @since    1.1.0
     * @access   private
     * @param    int       $min    La difficulté minimum.
     * @param    int       $max    La difficulté maximum.
     * @return   int               La difficulté.
     */
    private function get_random_difficulty( $min = 20, $max = 80 ) {
        return rand( $min, $max );
    }
    
    /**
     * Génère un CPC aléatoire.
     *
     * @since    1.1.0
     * @access   private
     * @param    float     $min    Le CPC minimum.
     * @param    float     $max    Le CPC maximum.
     * @return   string            Le CPC formaté.
     */
    private function get_random_cpc( $min = 0.5, $max = 2.0 ) {
        return number_format( $min + ( $max - $min ) * (float) rand() / (float) getrandmax(), 2 );
    }
}
