<?php
/**
 * Script pour enregistrer manuellement les types de post manquants du plugin Boss SEO.
 */

// Charger WordPress
require_once( dirname( __FILE__ ) . '/wp-load.php' );

// Vérifier si l'utilisateur est connecté et est administrateur
if ( ! current_user_can( 'manage_options' ) ) {
    die( 'Vous devez être connecté en tant qu\'administrateur pour exécuter ce script.' );
}

// Vérifier si le type de post existe déjà
if ( ! post_type_exists( 'boss_local_location' ) ) {
    // Enregistrer le type de publication pour les emplacements
    register_post_type( 'boss_local_location', array(
        'labels'              => array(
            'name'               => __( 'Emplacements', 'boss-seo' ),
            'singular_name'      => __( 'Emplacement', 'boss-seo' ),
            'menu_name'          => __( 'Emplacements', 'boss-seo' ),
            'name_admin_bar'     => __( 'Emplacement', 'boss-seo' ),
            'add_new'            => __( 'Ajouter', 'boss-seo' ),
            'add_new_item'       => __( 'Ajouter un emplacement', 'boss-seo' ),
            'new_item'           => __( 'Nouvel emplacement', 'boss-seo' ),
            'edit_item'          => __( 'Modifier l\'emplacement', 'boss-seo' ),
            'view_item'          => __( 'Voir l\'emplacement', 'boss-seo' ),
            'all_items'          => __( 'Tous les emplacements', 'boss-seo' ),
            'search_items'       => __( 'Rechercher des emplacements', 'boss-seo' ),
            'parent_item_colon'  => __( 'Emplacement parent :', 'boss-seo' ),
            'not_found'          => __( 'Aucun emplacement trouvé.', 'boss-seo' ),
            'not_found_in_trash' => __( 'Aucun emplacement trouvé dans la corbeille.', 'boss-seo' ),
        ),
        'public'              => true,
        'exclude_from_search' => false,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => false,
        'query_var'           => true,
        'rewrite'             => array( 'slug' => 'emplacement' ),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => null,
        'supports'            => array( 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields' ),
        'show_in_rest'        => true,
    ) );
    
    echo 'Le type de post "boss_local_location" a été enregistré avec succès.<br>';
} else {
    echo 'Le type de post "boss_local_location" existe déjà.<br>';
}

// Vérifier si le type de post existe déjà
if ( ! post_type_exists( 'boss_local_page' ) ) {
    // Enregistrer le type de publication pour les pages locales
    register_post_type( 'boss_local_page', array(
        'labels'              => array(
            'name'               => __( 'Pages locales', 'boss-seo' ),
            'singular_name'      => __( 'Page locale', 'boss-seo' ),
            'menu_name'          => __( 'Pages locales', 'boss-seo' ),
            'name_admin_bar'     => __( 'Page locale', 'boss-seo' ),
            'add_new'            => __( 'Ajouter', 'boss-seo' ),
            'add_new_item'       => __( 'Ajouter une page locale', 'boss-seo' ),
            'new_item'           => __( 'Nouvelle page locale', 'boss-seo' ),
            'edit_item'          => __( 'Modifier la page locale', 'boss-seo' ),
            'view_item'          => __( 'Voir la page locale', 'boss-seo' ),
            'all_items'          => __( 'Toutes les pages locales', 'boss-seo' ),
            'search_items'       => __( 'Rechercher des pages locales', 'boss-seo' ),
            'parent_item_colon'  => __( 'Page locale parente :', 'boss-seo' ),
            'not_found'          => __( 'Aucune page locale trouvée.', 'boss-seo' ),
            'not_found_in_trash' => __( 'Aucune page locale trouvée dans la corbeille.', 'boss-seo' ),
        ),
        'public'              => true,
        'exclude_from_search' => false,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => false,
        'query_var'           => true,
        'rewrite'             => array( 'slug' => 'page-locale' ),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => null,
        'supports'            => array( 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields' ),
        'show_in_rest'        => true,
    ) );
    
    echo 'Le type de post "boss_local_page" a été enregistré avec succès.<br>';
} else {
    echo 'Le type de post "boss_local_page" existe déjà.<br>';
}

// Enregistrer la taxonomie pour les types d'emplacements
if ( ! taxonomy_exists( 'boss_local_location_type' ) ) {
    register_taxonomy( 'boss_local_location_type', 'boss_local_location', array(
        'labels'            => array(
            'name'              => __( 'Types d\'emplacements', 'boss-seo' ),
            'singular_name'     => __( 'Type d\'emplacement', 'boss-seo' ),
            'search_items'      => __( 'Rechercher des types d\'emplacements', 'boss-seo' ),
            'all_items'         => __( 'Tous les types d\'emplacements', 'boss-seo' ),
            'parent_item'       => __( 'Type d\'emplacement parent', 'boss-seo' ),
            'parent_item_colon' => __( 'Type d\'emplacement parent :', 'boss-seo' ),
            'edit_item'         => __( 'Modifier le type d\'emplacement', 'boss-seo' ),
            'update_item'       => __( 'Mettre à jour le type d\'emplacement', 'boss-seo' ),
            'add_new_item'      => __( 'Ajouter un type d\'emplacement', 'boss-seo' ),
            'new_item_name'     => __( 'Nouveau type d\'emplacement', 'boss-seo' ),
            'menu_name'         => __( 'Types d\'emplacements', 'boss-seo' ),
        ),
        'hierarchical'      => true,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array( 'slug' => 'type-emplacement' ),
        'show_in_rest'      => true,
    ) );
    
    echo 'La taxonomie "boss_local_location_type" a été enregistrée avec succès.<br>';
} else {
    echo 'La taxonomie "boss_local_location_type" existe déjà.<br>';
}

echo 'Tous les types de post et taxonomies ont été vérifiés et enregistrés si nécessaire.<br>';
echo 'Vous pouvez maintenant <a href="' . admin_url( 'admin.php?page=boss-seo-local' ) . '">retourner à l\'administration</a>.<br>';
echo 'N\'oubliez pas de supprimer ce fichier après utilisation pour des raisons de sécurité.';
