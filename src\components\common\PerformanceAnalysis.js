import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Dashicon,
  SelectControl,
  Spinner
} from '@wordpress/components';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

const PerformanceAnalysis = ({ type }) => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('30days');
  const [selectedMetric, setSelectedMetric] = useState('clicks');
  const [performanceData, setPerformanceData] = useState({
    overview: {},
    timeData: [],
    categoryData: [],
    comparisonData: []
  });
  
  // Charger les données
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour les statistiques de performance
      const mockOverview = {
        clicks: type === 'local' ? 3250 : 8750,
        impressions: type === 'local' ? 45000 : 120000,
        ctr: type === 'local' ? 7.22 : 7.29,
        conversions: type === 'local' ? 145 : 385,
        conversionRate: type === 'local' ? 4.46 : 4.4,
        revenue: type === 'local' ? 7250 : 19500
      };
      
      // Générer des données temporelles fictives
      const mockTimeData = [];
      const today = new Date();
      
      // Déterminer le nombre de jours en fonction de la période sélectionnée
      let days = 30;
      if (selectedPeriod === '7days') days = 7;
      if (selectedPeriod === '90days') days = 90;
      
      // Générer des données pour chaque jour
      for (let i = days; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        
        // Valeurs de base pour les métriques
        const baseClicks = type === 'local' ? 100 : 250;
        const baseImpressions = type === 'local' ? 1500 : 4000;
        const baseConversions = type === 'local' ? 5 : 12;
        const baseRevenue = type === 'local' ? 250 : 650;
        
        // Ajouter une variation aléatoire
        const clicks = Math.floor(baseClicks + (Math.random() * baseClicks * 0.5) - (baseClicks * 0.25));
        const impressions = Math.floor(baseImpressions + (Math.random() * baseImpressions * 0.3) - (baseImpressions * 0.15));
        const conversions = Math.floor(baseConversions + (Math.random() * baseConversions * 0.6) - (baseConversions * 0.3));
        const revenue = Math.floor(baseRevenue + (Math.random() * baseRevenue * 0.4) - (baseRevenue * 0.2));
        
        mockTimeData.push({
          date: date.toISOString().split('T')[0],
          clicks: clicks,
          impressions: impressions,
          ctr: ((clicks / impressions) * 100).toFixed(2),
          conversions: conversions,
          conversionRate: ((conversions / clicks) * 100).toFixed(2),
          revenue: revenue
        });
      }
      
      // Données fictives par catégorie
      const mockCategoryData = [];
      
      if (type === 'local') {
        mockCategoryData.push(
          { name: 'Paris', clicks: 1250, impressions: 18000, conversions: 62, revenue: 3100 },
          { name: 'Lyon', clicks: 850, impressions: 12000, conversions: 43, revenue: 2150 },
          { name: 'Marseille', clicks: 650, impressions: 9000, conversions: 28, revenue: 1400 },
          { name: 'Bordeaux', clicks: 320, impressions: 4000, conversions: 8, revenue: 400 },
          { name: 'Lille', clicks: 180, impressions: 2000, conversions: 4, revenue: 200 }
        );
      } else {
        mockCategoryData.push(
          { name: 'Électronique', clicks: 3200, impressions: 45000, conversions: 160, revenue: 8000 },
          { name: 'Vêtements', clicks: 2800, impressions: 38000, conversions: 140, revenue: 7000 },
          { name: 'Maison', clicks: 1500, impressions: 22000, conversions: 50, revenue: 2500 },
          { name: 'Sports', clicks: 800, impressions: 10000, conversions: 25, revenue: 1250 },
          { name: 'Livres', clicks: 450, impressions: 5000, conversions: 10, revenue: 750 }
        );
      }
      
      // Données fictives de comparaison avec les concurrents
      const mockComparisonData = [
        { name: 'Votre entreprise', score: 78, visibility: 72, engagement: 85 },
        { name: 'Concurrent 1', score: 82, visibility: 85, engagement: 78 },
        { name: 'Concurrent 2', score: 65, visibility: 68, engagement: 62 },
        { name: 'Concurrent 3', score: 70, visibility: 75, engagement: 65 },
        { name: 'Moyenne du secteur', score: 72, visibility: 74, engagement: 70 }
      ];
      
      setPerformanceData({
        overview: mockOverview,
        timeData: mockTimeData,
        categoryData: mockCategoryData,
        comparisonData: mockComparisonData
      });
      
      setIsLoading(false);
    }, 1000);
  }, [type, selectedPeriod]);
  
  // Fonction pour formater les dates pour l'axe X
  const formatXAxis = (tickItem) => {
    const date = new Date(tickItem);
    return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' });
  };
  
  // Fonction pour formater les valeurs pour le tooltip
  const formatTooltipValue = (value, name) => {
    switch (name) {
      case 'ctr':
      case 'conversionRate':
        return [`${value}%`, name];
      case 'revenue':
        return [`${value} €`, name];
      default:
        return [value, name];
    }
  };
  
  // Couleurs pour les graphiques
  const COLORS = ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {/* Sélecteurs */}
          <div className="boss-flex boss-flex-col md:boss-flex-row boss-justify-between boss-items-start md:boss-items-center boss-mb-6 boss-gap-4">
            <h2 className="boss-text-xl boss-font-semibold boss-text-boss-dark">
              {type === 'local' 
                ? __('Analyse de performance SEO local', 'boss-seo') 
                : __('Analyse de performance E-commerce', 'boss-seo')}
            </h2>
            
            <div className="boss-flex boss-space-x-4">
              <SelectControl
                value={selectedPeriod}
                options={[
                  { label: __('7 derniers jours', 'boss-seo'), value: '7days' },
                  { label: __('30 derniers jours', 'boss-seo'), value: '30days' },
                  { label: __('90 derniers jours', 'boss-seo'), value: '90days' }
                ]}
                onChange={setSelectedPeriod}
              />
              
              <Button
                isSecondary
                onClick={() => {
                  // Simuler une actualisation des données
                  setIsLoading(true);
                  setTimeout(() => {
                    setIsLoading(false);
                  }, 1000);
                }}
              >
                <Dashicon icon="update" className="boss-mr-1" />
                {__('Actualiser', 'boss-seo')}
              </Button>
            </div>
          </div>
          
          {/* Statistiques principales */}
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 lg:boss-grid-cols-6 boss-gap-4 boss-mb-6">
            <Card>
              <CardBody className="boss-p-4">
                <div className="boss-text-center">
                  <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                    {__('Clics', 'boss-seo')}
                  </h3>
                  <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
                    {performanceData.overview.clicks.toLocaleString()}
                  </div>
                </div>
              </CardBody>
            </Card>
            
            <Card>
              <CardBody className="boss-p-4">
                <div className="boss-text-center">
                  <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                    {__('Impressions', 'boss-seo')}
                  </h3>
                  <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
                    {performanceData.overview.impressions.toLocaleString()}
                  </div>
                </div>
              </CardBody>
            </Card>
            
            <Card>
              <CardBody className="boss-p-4">
                <div className="boss-text-center">
                  <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                    {__('CTR', 'boss-seo')}
                  </h3>
                  <div className="boss-text-2xl boss-font-bold boss-text-boss-primary">
                    {performanceData.overview.ctr}%
                  </div>
                </div>
              </CardBody>
            </Card>
            
            <Card>
              <CardBody className="boss-p-4">
                <div className="boss-text-center">
                  <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                    {__('Conversions', 'boss-seo')}
                  </h3>
                  <div className="boss-text-2xl boss-font-bold boss-text-boss-success">
                    {performanceData.overview.conversions.toLocaleString()}
                  </div>
                </div>
              </CardBody>
            </Card>
            
            <Card>
              <CardBody className="boss-p-4">
                <div className="boss-text-center">
                  <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                    {__('Taux de conversion', 'boss-seo')}
                  </h3>
                  <div className="boss-text-2xl boss-font-bold boss-text-boss-success">
                    {performanceData.overview.conversionRate}%
                  </div>
                </div>
              </CardBody>
            </Card>
            
            <Card>
              <CardBody className="boss-p-4">
                <div className="boss-text-center">
                  <h3 className="boss-text-boss-gray boss-text-sm boss-font-medium boss-mb-1">
                    {__('Revenus', 'boss-seo')}
                  </h3>
                  <div className="boss-text-2xl boss-font-bold boss-text-boss-dark">
                    {performanceData.overview.revenue.toLocaleString()} €
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
          
          {/* Graphique d'évolution */}
          <Card className="boss-mb-6">
            <CardHeader className="boss-border-b boss-border-gray-200">
              <div className="boss-flex boss-justify-between boss-items-center">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Évolution des performances', 'boss-seo')}
                </h2>
                <SelectControl
                  value={selectedMetric}
                  options={[
                    { label: __('Clics', 'boss-seo'), value: 'clicks' },
                    { label: __('Impressions', 'boss-seo'), value: 'impressions' },
                    { label: __('CTR', 'boss-seo'), value: 'ctr' },
                    { label: __('Conversions', 'boss-seo'), value: 'conversions' },
                    { label: __('Taux de conversion', 'boss-seo'), value: 'conversionRate' },
                    { label: __('Revenus', 'boss-seo'), value: 'revenue' }
                  ]}
                  onChange={setSelectedMetric}
                />
              </div>
            </CardHeader>
            <CardBody>
              <div className="boss-h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={performanceData.timeData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis 
                      dataKey="date" 
                      tickFormatter={formatXAxis} 
                      tick={{ fontSize: 12 }}
                    />
                    <YAxis 
                      tick={{ fontSize: 12 }}
                    />
                    <Tooltip 
                      formatter={formatTooltipValue}
                      labelFormatter={(label) => new Date(label).toLocaleDateString('fr-FR', { 
                        day: 'numeric', 
                        month: 'long', 
                        year: 'numeric' 
                      })}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey={selectedMetric}
                      name={
                        selectedMetric === 'clicks' ? __('Clics', 'boss-seo') :
                        selectedMetric === 'impressions' ? __('Impressions', 'boss-seo') :
                        selectedMetric === 'ctr' ? __('CTR', 'boss-seo') :
                        selectedMetric === 'conversions' ? __('Conversions', 'boss-seo') :
                        selectedMetric === 'conversionRate' ? __('Taux de conversion', 'boss-seo') :
                        __('Revenus', 'boss-seo')
                      }
                      stroke="#4F46E5"
                      activeDot={{ r: 8 }}
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardBody>
          </Card>
          
          {/* Graphiques par catégorie et comparaison */}
          <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-2 boss-gap-6 boss-mb-6">
            {/* Performance par catégorie */}
            <Card>
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {type === 'local' 
                    ? __('Performance par emplacement', 'boss-seo') 
                    : __('Performance par catégorie', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={performanceData.categoryData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="clicks" name={__('Clics', 'boss-seo')} fill="#4F46E5" />
                      <Bar dataKey="conversions" name={__('Conversions', 'boss-seo')} fill="#10B981" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardBody>
            </Card>
            
            {/* Comparaison concurrentielle */}
            <Card>
              <CardHeader className="boss-border-b boss-border-gray-200">
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {__('Comparaison concurrentielle', 'boss-seo')}
                </h2>
              </CardHeader>
              <CardBody>
                <div className="boss-h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={performanceData.comparisonData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis type="number" tick={{ fontSize: 12 }} />
                      <YAxis dataKey="name" type="category" tick={{ fontSize: 12 }} width={120} />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="score" name={__('Score SEO', 'boss-seo')} fill="#4F46E5" />
                      <Bar dataKey="visibility" name={__('Visibilité', 'boss-seo')} fill="#F59E0B" />
                      <Bar dataKey="engagement" name={__('Engagement', 'boss-seo')} fill="#10B981" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardBody>
            </Card>
          </div>
          
          {/* Recommandations */}
          <Card>
            <CardHeader className="boss-border-b boss-border-gray-200">
              <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                {__('Recommandations d\'optimisation', 'boss-seo')}
              </h2>
            </CardHeader>
            <CardBody>
              <div className="boss-space-y-4">
                {type === 'local' ? (
                  <>
                    <div className="boss-flex boss-items-start boss-gap-3">
                      <div className="boss-bg-blue-100 boss-p-2 boss-rounded-lg">
                        <Dashicon icon="location" className="boss-text-blue-600" />
                      </div>
                      <div>
                        <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                          {__('Optimisez vos fiches Google Business', 'boss-seo')}
                        </h3>
                        <p className="boss-text-boss-gray boss-text-sm">
                          {__('Complétez toutes les informations de vos fiches Google Business et ajoutez des photos de qualité pour améliorer votre visibilité locale.', 'boss-seo')}
                        </p>
                      </div>
                    </div>
                    
                    <div className="boss-flex boss-items-start boss-gap-3">
                      <div className="boss-bg-green-100 boss-p-2 boss-rounded-lg">
                        <Dashicon icon="star-filled" className="boss-text-green-600" />
                      </div>
                      <div>
                        <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                          {__('Encouragez les avis clients', 'boss-seo')}
                        </h3>
                        <p className="boss-text-boss-gray boss-text-sm">
                          {__('Sollicitez activement des avis positifs de vos clients satisfaits pour renforcer votre réputation locale.', 'boss-seo')}
                        </p>
                      </div>
                    </div>
                    
                    <div className="boss-flex boss-items-start boss-gap-3">
                      <div className="boss-bg-yellow-100 boss-p-2 boss-rounded-lg">
                        <Dashicon icon="admin-site" className="boss-text-yellow-600" />
                      </div>
                      <div>
                        <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                          {__('Créez des pages locales', 'boss-seo')}
                        </h3>
                        <p className="boss-text-boss-gray boss-text-sm">
                          {__('Développez des pages dédiées à chaque emplacement avec du contenu unique et des mots-clés locaux pertinents.', 'boss-seo')}
                        </p>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="boss-flex boss-items-start boss-gap-3">
                      <div className="boss-bg-blue-100 boss-p-2 boss-rounded-lg">
                        <Dashicon icon="cart" className="boss-text-blue-600" />
                      </div>
                      <div>
                        <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                          {__('Optimisez vos fiches produits', 'boss-seo')}
                        </h3>
                        <p className="boss-text-boss-gray boss-text-sm">
                          {__('Améliorez les titres, descriptions et images de vos produits pour augmenter leur visibilité dans les résultats de recherche.', 'boss-seo')}
                        </p>
                      </div>
                    </div>
                    
                    <div className="boss-flex boss-items-start boss-gap-3">
                      <div className="boss-bg-green-100 boss-p-2 boss-rounded-lg">
                        <Dashicon icon="media-code" className="boss-text-green-600" />
                      </div>
                      <div>
                        <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                          {__('Implémentez des schémas produits', 'boss-seo')}
                        </h3>
                        <p className="boss-text-boss-gray boss-text-sm">
                          {__('Ajoutez des données structurées à vos pages produits pour obtenir des résultats enrichis dans Google.', 'boss-seo')}
                        </p>
                      </div>
                    </div>
                    
                    <div className="boss-flex boss-items-start boss-gap-3">
                      <div className="boss-bg-yellow-100 boss-p-2 boss-rounded-lg">
                        <Dashicon icon="performance" className="boss-text-yellow-600" />
                      </div>
                      <div>
                        <h3 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                          {__('Améliorez la vitesse de chargement', 'boss-seo')}
                        </h3>
                        <p className="boss-text-boss-gray boss-text-sm">
                          {__('Optimisez les performances de votre site e-commerce pour offrir une meilleure expérience utilisateur et améliorer votre classement.', 'boss-seo')}
                        </p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardBody>
          </Card>
        </div>
      )}
    </div>
  );
};

export default PerformanceAnalysis;
