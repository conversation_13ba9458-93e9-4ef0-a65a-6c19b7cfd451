/**
 * Service pour la recherche et la gestion des images
 * 
 * Gère les communications avec l'API pour les fonctionnalités de recherche d'images
 */

import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

class ImageService {
  /**
   * Recherche des images via les API externes (Pexels, Unsplash, Pixabay)
   * 
   * @param {string} query - Requête de recherche
   * @param {string} source - Source des images (pexels, unsplash, pixabay)
   * @param {number} perPage - Nombre d'images par page
   * @param {number} page - Numéro de page
   * @returns {Promise} - Promesse contenant les résultats de la recherche
   */
  async searchImages(query, source = 'pexels', perPage = 8, page = 1) {
    try {
      const path = addQueryArgs('/boss-seo/v1/images/search', {
        query,
        source,
        per_page: perPage,
        page
      });
      
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la recherche d\'images:', error);
      throw error;
    }
  }
  
  /**
   * Recherche des images en fonction des mots-clés
   * 
   * @param {Array} keywords - Liste de mots-clés
   * @param {string} source - Source des images (pexels, unsplash, pixabay)
   * @param {number} perPage - Nombre d'images par page
   * @returns {Promise} - Promesse contenant les résultats de la recherche
   */
  async searchImagesByKeywords(keywords, source = 'pexels', perPage = 8) {
    try {
      // Construire la requête à partir des mots-clés
      const query = keywords.join(' ');
      
      return await this.searchImages(query, source, perPage);
    } catch (error) {
      console.error('Erreur lors de la recherche d\'images par mots-clés:', error);
      throw error;
    }
  }
  
  /**
   * Importe une image dans la médiathèque WordPress
   * 
   * @param {Object} image - Image à importer
   * @returns {Promise} - Promesse contenant les détails de l'image importée
   */
  async importImage(image) {
    try {
      const path = '/boss-seo/v1/images/import';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { image }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'importation de l\'image:', error);
      throw error;
    }
  }
  
  /**
   * Importe plusieurs images dans la médiathèque WordPress
   * 
   * @param {Array} images - Images à importer
   * @returns {Promise} - Promesse contenant les détails des images importées
   */
  async importImages(images) {
    try {
      const path = '/boss-seo/v1/images/import-bulk';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { images }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'importation des images:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les sources d'images disponibles
   * 
   * @returns {Promise} - Promesse contenant les sources d'images disponibles
   */
  async getImageSources() {
    try {
      const path = '/boss-seo/v1/images/sources';
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des sources d\'images:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les paramètres des API d'images
   * 
   * @returns {Promise} - Promesse contenant les paramètres des API d'images
   */
  async getImageApiSettings() {
    try {
      const path = '/boss-seo/v1/images/settings';
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres des API d\'images:', error);
      throw error;
    }
  }
  
  /**
   * Vérifie si une API d'images est configurée
   * 
   * @param {string} source - Source d'images (pexels, unsplash, pixabay)
   * @returns {Promise} - Promesse contenant le statut de configuration
   */
  async checkImageApiStatus(source) {
    try {
      const path = addQueryArgs('/boss-seo/v1/images/check-api', { source });
      const response = await apiFetch({ path });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la vérification de l'API ${source}:`, error);
      throw error;
    }
  }
}

export default new ImageService();
