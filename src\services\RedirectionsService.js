/**
 * Service pour la gestion des redirections
 * 
 * Gère les communications avec l'API pour les fonctionnalités de redirections
 */

import apiFetch from '@wordpress/api-fetch';

class RedirectionsService {
  /**
   * Récupère la liste des redirections
   * 
   * @param {number} page - Numéro de page
   * @param {number} perPage - Nombre d'éléments par page
   * @param {Object} filters - Filtres à appliquer
   * @returns {Promise} Promesse contenant la liste des redirections
   */
  async getRedirections(page = 1, perPage = 20, filters = {}) {
    try {
      let path = `/boss-seo/v1/redirections?page=${page}&per_page=${perPage}`;
      
      // Ajouter les filtres à l'URL
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          path += `&${key}=${encodeURIComponent(filters[key])}`;
        }
      });
      
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des redirections:', error);
      throw error;
    }
  }

  /**
   * Ajoute une nouvelle redirection
   * 
   * @param {Object} redirection - Données de la redirection
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async addRedirection(redirection) {
    try {
      const path = '/boss-seo/v1/redirections';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: redirection
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la redirection:', error);
      throw error;
    }
  }

  /**
   * Met à jour une redirection existante
   * 
   * @param {number} id - ID de la redirection
   * @param {Object} redirection - Nouvelles données de la redirection
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async updateRedirection(id, redirection) {
    try {
      const path = `/boss-seo/v1/redirections/${id}`;
      const response = await apiFetch({
        path,
        method: 'PUT',
        data: redirection
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la redirection:', error);
      throw error;
    }
  }

  /**
   * Supprime une redirection
   * 
   * @param {number} id - ID de la redirection
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async deleteRedirection(id) {
    try {
      const path = `/boss-seo/v1/redirections/${id}`;
      const response = await apiFetch({
        path,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la suppression de la redirection:', error);
      throw error;
    }
  }

  /**
   * Récupère les logs des redirections
   * 
   * @param {number} page - Numéro de page
   * @param {number} perPage - Nombre d'éléments par page
   * @param {Object} filters - Filtres à appliquer
   * @returns {Promise} Promesse contenant les logs des redirections
   */
  async getRedirectionLogs(page = 1, perPage = 20, filters = {}) {
    try {
      let path = `/boss-seo/v1/redirections/logs?page=${page}&per_page=${perPage}`;
      
      // Ajouter les filtres à l'URL
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          path += `&${key}=${encodeURIComponent(filters[key])}`;
        }
      });
      
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des logs de redirections:', error);
      throw error;
    }
  }

  /**
   * Importe des redirections
   * 
   * @param {Array} redirections - Liste des redirections à importer
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async importRedirections(redirections) {
    try {
      const path = '/boss-seo/v1/redirections/import';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { redirections }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'import des redirections:', error);
      throw error;
    }
  }

  /**
   * Exporte les redirections
   * 
   * @param {string} format - Format d'export (json, csv)
   * @returns {Promise} Promesse contenant les redirections exportées
   */
  async exportRedirections(format = 'json') {
    try {
      const path = `/boss-seo/v1/redirections/export?format=${format}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'export des redirections:', error);
      throw error;
    }
  }

  /**
   * Teste une redirection
   * 
   * @param {string} url - URL à tester
   * @returns {Promise} Promesse contenant le résultat du test
   */
  async testRedirection(url) {
    try {
      const path = '/boss-seo/v1/redirections/test';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { url }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du test de la redirection:', error);
      throw error;
    }
  }
}

export default new RedirectionsService();
