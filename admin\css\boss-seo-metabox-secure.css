/**
 * Styles sécurisés et optimisés pour la Meta Box Boss SEO
 * Version responsive avec accessibilité améliorée
 */

/* Variables CSS modernes et colorées */
:root {
    --boss-seo-primary: #667eea;
    --boss-seo-primary-hover: #5a6fd8;
    --boss-seo-primary-light: #f1f3ff;
    --boss-seo-secondary: #764ba2;
    --boss-seo-accent: #f093fb;
    --boss-seo-success: #10b981;
    --boss-seo-success-light: #d1fae5;
    --boss-seo-warning: #f59e0b;
    --boss-seo-warning-light: #fef3c7;
    --boss-seo-error: #ef4444;
    --boss-seo-error-light: #fee2e2;
    --boss-seo-info: #3b82f6;
    --boss-seo-info-light: #dbeafe;
    --boss-seo-border: #e5e7eb;
    --boss-seo-border-focus: #667eea;
    --boss-seo-bg-light: #f8fafc;
    --boss-seo-bg-white: #ffffff;
    --boss-seo-bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --boss-seo-text: #1f2937;
    --boss-seo-text-light: #6b7280;
    --boss-seo-text-muted: #9ca3af;
    --boss-seo-radius: 8px;
    --boss-seo-radius-lg: 12px;
    --boss-seo-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --boss-seo-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --boss-seo-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --boss-seo-transition-fast: all 0.15s ease-out;
}

/* Support du mode sombre */
@media (prefers-color-scheme: dark) {
    :root {
        --boss-seo-bg-light: #1e1e1e;
        --boss-seo-bg-white: #2c2c2c;
        --boss-seo-text: #ffffff;
        --boss-seo-text-light: #cccccc;
        --boss-seo-border: #444;
    }
}

/* Container principal moderne */
.boss-seo-metabox-secure,
.boss-seo-metabox-tabbed {
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
    background: var(--boss-seo-bg-white);
    border-radius: var(--boss-seo-radius-lg);
    overflow: hidden;
    box-shadow: var(--boss-seo-shadow);
    border: 1px solid var(--boss-seo-border);
    position: relative;
}

.boss-seo-metabox-secure::before,
.boss-seo-metabox-tabbed::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--boss-seo-bg-gradient);
    z-index: 1;
}

/* Navigation par onglets moderne */
.boss-seo-tabs-nav {
    display: flex;
    background: var(--boss-seo-bg-light);
    border-bottom: 1px solid var(--boss-seo-border);
    margin: 0;
    padding: 8px 8px 0 8px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    position: relative;
}

.boss-seo-tabs-nav::-webkit-scrollbar {
    display: none;
}

.boss-seo-tab-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 14px 20px;
    background: transparent;
    border: none;
    border-radius: var(--boss-seo-radius) var(--boss-seo-radius) 0 0;
    color: var(--boss-seo-text-light);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--boss-seo-transition);
    white-space: nowrap;
    position: relative;
    min-width: 140px;
    justify-content: center;
    margin-right: 4px;
    transform: translateY(0);
}

.boss-seo-tab-button::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: var(--boss-seo-bg-gradient);
    border-radius: 2px;
    transition: var(--boss-seo-transition);
    transform: translateX(-50%);
}

.boss-seo-tab-button:hover {
    background: var(--boss-seo-primary-light);
    color: var(--boss-seo-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.boss-seo-tab-button:hover::before {
    width: 60%;
}

.boss-seo-tab-button.active {
    background: var(--boss-seo-bg-white);
    color: var(--boss-seo-primary);
    font-weight: 600;
    transform: translateY(-1px);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.boss-seo-tab-button.active::before {
    width: 80%;
    background: var(--boss-seo-bg-gradient);
}

.boss-seo-tab-button .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
    transition: var(--boss-seo-transition-fast);
}

.boss-seo-tab-button:hover .dashicons,
.boss-seo-tab-button.active .dashicons {
    transform: scale(1.1);
}

.boss-seo-tab-button .tab-label {
    display: block;
    font-weight: inherit;
}

/* Badge de score moderne dans l'onglet */
.boss-seo-score-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    height: 22px;
    padding: 0 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    color: white;
    margin-left: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: pulse 2s infinite;
    position: relative;
    overflow: hidden;
}

.boss-seo-score-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.boss-seo-score-badge:hover::before {
    left: 100%;
}

.boss-seo-score-badge.boss-seo-score-good {
    background: linear-gradient(135deg, var(--boss-seo-success) 0%, #059669 100%);
}

.boss-seo-score-badge.boss-seo-score-ok {
    background: linear-gradient(135deg, var(--boss-seo-warning) 0%, #d97706 100%);
}

.boss-seo-score-badge.boss-seo-score-poor {
    background: linear-gradient(135deg, #f56e28 0%, #ea580c 100%);
}

.boss-seo-score-badge.boss-seo-score-bad {
    background: linear-gradient(135deg, var(--boss-seo-error) 0%, #dc2626 100%);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Contenu des onglets */
.boss-seo-tabs-content {
    position: relative;
    min-height: 400px;
}

.boss-seo-tab-panel {
    display: none !important;
    padding: 24px;
    animation: fadeIn 0.3s ease-in-out;
}

.boss-seo-tab-panel.active {
    display: block !important;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.boss-seo-tab-content {
    max-width: none;
}

/* En-têtes de section dans les onglets */
.boss-seo-section-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--boss-seo-border);
}

.boss-seo-section-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--boss-seo-text);
    display: flex;
    align-items: center;
    gap: 10px;
}

.boss-seo-section-header h4 .dashicons {
    color: var(--boss-seo-primary);
    font-size: 20px;
}

/* Sections */
.boss-seo-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--boss-seo-border);
    position: relative;
}

.boss-seo-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.boss-seo-section h4 {
    margin: 0 0 16px 0;
    font-size: 15px;
    font-weight: 600;
    color: var(--boss-seo-text);
    display: flex;
    align-items: center;
    gap: 8px;
}

.boss-seo-section h4 .dashicons {
    color: var(--boss-seo-primary);
    font-size: 18px;
}

/* Groupes de champs */
.boss-seo-field-group {
    margin-bottom: 16px;
}

.boss-seo-field-group:last-child {
    margin-bottom: 0;
}

.boss-seo-field-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    font-size: 13px;
    color: var(--boss-seo-text);
}

/* Labels améliorés */
.boss-seo-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 14px;
    color: var(--boss-seo-text);
}

.boss-seo-label .dashicons {
    color: var(--boss-seo-primary);
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Champs de saisie modernes */
.boss-seo-input,
.boss-seo-textarea,
.boss-seo-select {
    width: 100%;
    padding: 14px 18px;
    border: 2px solid var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    font-size: 14px;
    background: var(--boss-seo-bg-white);
    color: var(--boss-seo-text);
    transition: var(--boss-seo-transition);
    box-sizing: border-box;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.boss-seo-input::placeholder,
.boss-seo-textarea::placeholder {
    color: var(--boss-seo-text-muted);
    font-style: italic;
}

/* Container pour les champs avec compteur */
.boss-seo-input-container {
    position: relative;
}

/* Champ avec bouton */
.boss-seo-input-with-button {
    display: flex;
    gap: 8px;
    align-items: stretch;
}

.boss-seo-input-with-button .boss-seo-input {
    flex: 1;
}

.boss-seo-add-keyword-btn {
    padding: 14px 18px;
    background: var(--boss-seo-bg-gradient);
    color: white;
    border: none;
    border-radius: var(--boss-seo-radius);
    cursor: pointer;
    transition: var(--boss-seo-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.boss-seo-add-keyword-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.boss-seo-add-keyword-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.boss-seo-add-keyword-btn:hover::before {
    left: 100%;
}

.boss-seo-add-keyword-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.boss-seo-add-keyword-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    transition: var(--boss-seo-transition-fast);
}

.boss-seo-add-keyword-btn:hover .dashicons {
    transform: rotate(90deg) scale(1.1);
}

.boss-seo-input:focus,
.boss-seo-textarea:focus,
.boss-seo-select:focus {
    border-color: var(--boss-seo-border-focus);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
    outline: none;
    transform: translateY(-1px);
    background: var(--boss-seo-bg-white);
}

.boss-seo-input:hover,
.boss-seo-textarea:hover,
.boss-seo-select:hover {
    border-color: var(--boss-seo-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.boss-seo-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Grid responsive pour les champs */
.boss-seo-field-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

@media (max-width: 768px) {
    .boss-seo-field-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}

/* Compteurs de caractères */
.boss-seo-counter {
    position: absolute;
    right: 8px;
    bottom: 8px;
    font-size: 11px;
    color: var(--boss-seo-text-light);
    background: var(--boss-seo-bg-white);
    padding: 2px 6px;
    border-radius: var(--boss-seo-radius);
    box-shadow: var(--boss-seo-shadow);
    pointer-events: none;
    z-index: 10;
}

.boss-seo-field-group {
    position: relative;
}

.boss-seo-counter-current.too-short,
.boss-seo-counter-current.too-long {
    color: var(--boss-seo-error);
    font-weight: 600;
}

.boss-seo-counter-current.good {
    color: var(--boss-seo-success);
    font-weight: 600;
}

/* Textes d'aide */
.boss-seo-help {
    font-size: 12px;
    color: var(--boss-seo-text-light);
    margin-top: 4px;
    line-height: 1.4;
}

/* Container des mots-clés amélioré */
.boss-seo-keywords-container {
    margin: 16px 0;
}

.boss-seo-keywords-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 12px 0;
    min-height: 60px;
    padding: 16px;
    border: 2px dashed var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    background: var(--boss-seo-bg-light);
    transition: var(--boss-seo-transition);
}

.boss-seo-keywords-tags:hover {
    border-color: var(--boss-seo-primary);
    background: rgba(0, 115, 170, 0.02);
}

.boss-seo-keywords-tags:empty::before {
    content: "Aucun mot-clé défini. Ajoutez des mots-clés pour optimiser votre contenu.";
    color: var(--boss-seo-text-light);
    font-style: italic;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
}

.boss-seo-keyword-tag {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: var(--boss-seo-bg-white);
    border: 2px solid var(--boss-seo-border);
    border-radius: var(--boss-seo-radius-lg);
    padding: 8px 14px;
    font-size: 13px;
    color: var(--boss-seo-text);
    cursor: pointer;
    transition: var(--boss-seo-transition);
    user-select: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.boss-seo-keyword-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s;
}

.boss-seo-keyword-tag:hover {
    border-color: var(--boss-seo-primary);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
}

.boss-seo-keyword-tag:hover::before {
    left: 100%;
}

.boss-seo-keyword-tag.primary {
    background: var(--boss-seo-bg-gradient);
    border-color: var(--boss-seo-primary);
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.boss-seo-keyword-tag.primary::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.boss-seo-keyword-tag .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    cursor: pointer;
    transition: var(--boss-seo-transition-fast);
}

.boss-seo-keyword-tag:hover .dashicons {
    transform: scale(1.1);
}

.boss-seo-keyword-tag:not(.primary) .dashicons:hover {
    color: var(--boss-seo-error);
    transform: scale(1.2) rotate(90deg);
}

.boss-seo-keyword-tag.primary .dashicons {
    color: #fbbf24;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Suggestions de mots-clés améliorées */
.boss-seo-suggestions {
    margin-top: 20px;
    padding: 16px;
    background: var(--boss-seo-bg-light);
    border-radius: var(--boss-seo-radius);
    border: 1px solid var(--boss-seo-border);
}

.boss-seo-suggestions-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.boss-seo-suggestions-header .dashicons {
    color: var(--boss-seo-warning);
    font-size: 16px;
    margin-right: 6px;
}

.boss-seo-suggestions-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--boss-seo-text);
    display: flex;
    align-items: center;
}

.boss-seo-refresh-suggestions {
    background: transparent;
    border: 1px solid var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    padding: 6px;
    cursor: pointer;
    color: var(--boss-seo-text-light);
    transition: var(--boss-seo-transition);
}

.boss-seo-refresh-suggestions:hover {
    background: var(--boss-seo-primary);
    color: white;
    border-color: var(--boss-seo-primary);
}

.boss-seo-refresh-suggestions .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.boss-seo-suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.boss-seo-suggestion {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: var(--boss-seo-bg-white);
    border: 2px solid var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    padding: 8px 12px;
    font-size: 12px;
    color: var(--boss-seo-text);
    cursor: pointer;
    transition: var(--boss-seo-transition);
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.boss-seo-suggestion::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--boss-seo-bg-gradient);
    transition: left 0.3s;
    z-index: 0;
}

.boss-seo-suggestion:hover {
    border-color: var(--boss-seo-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.boss-seo-suggestion:hover::before {
    left: 0;
}

.boss-seo-suggestion .dashicons,
.boss-seo-suggestion .suggestion-text {
    position: relative;
    z-index: 1;
}

.boss-seo-suggestion .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    transition: var(--boss-seo-transition-fast);
}

.boss-seo-suggestion:hover .dashicons {
    transform: rotate(90deg) scale(1.1);
}

/* Score SEO */
.boss-seo-score-container {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.boss-seo-score-indicator {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.boss-seo-score-indicator::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: conic-gradient(from 0deg, transparent 0%, var(--boss-seo-primary) 100%);
    opacity: 0.1;
}

.boss-seo-score-good {
    background: var(--boss-seo-success);
}

.boss-seo-score-ok {
    background: var(--boss-seo-warning);
}

.boss-seo-score-poor {
    background: #f56e28;
}

.boss-seo-score-bad {
    background: var(--boss-seo-error);
}

.boss-seo-score-none {
    background: var(--boss-seo-text-light);
}

.boss-seo-score-details {
    flex: 1;
    min-width: 200px;
}

.boss-seo-score-details p {
    margin: 0 0 8px 0;
    font-size: 13px;
}

/* Actions et boutons */
.boss-seo-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
}

.boss-seo-actions .button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: var(--boss-seo-transition);
}

.boss-seo-actions .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Recommandations */
.boss-seo-recommendations {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    background: var(--boss-seo-bg-white);
}

.boss-seo-recommendation {
    padding: 12px;
    border-bottom: 1px solid var(--boss-seo-border);
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.boss-seo-recommendation:last-child {
    border-bottom: none;
}

.boss-seo-recommendation-critical {
    background: #fef8f6;
    border-left: 4px solid var(--boss-seo-error);
}

.boss-seo-recommendation-warning {
    background: #fff8e5;
    border-left: 4px solid var(--boss-seo-warning);
}

.boss-seo-recommendation-info {
    background: #f0f6fc;
    border-left: 4px solid var(--boss-seo-info);
}

.boss-seo-recommendation-icon {
    font-size: 18px;
    flex-shrink: 0;
    margin-top: 2px;
}

.boss-seo-recommendation-critical .boss-seo-recommendation-icon {
    color: var(--boss-seo-error);
}

.boss-seo-recommendation-warning .boss-seo-recommendation-icon {
    color: var(--boss-seo-warning);
}

.boss-seo-recommendation-info .boss-seo-recommendation-icon {
    color: var(--boss-seo-info);
}

.boss-seo-recommendation-text {
    flex: 1;
    font-size: 13px;
    line-height: 1.4;
}

/* États de chargement */
.boss-seo-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.boss-seo-loading::after {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--boss-seo-radius);
}

.boss-seo-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-left-color: var(--boss-seo-primary);
    border-radius: 50%;
    animation: boss-seo-spin 1s linear infinite;
}

@keyframes boss-seo-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .boss-seo-metabox-secure {
        padding: 16px 0;
    }

    .boss-seo-section {
        margin-bottom: 20px;
        padding-bottom: 16px;
    }

    .boss-seo-score-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .boss-seo-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .boss-seo-actions .button {
        justify-content: center;
    }
}

/* Accessibilité */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus visible pour l'accessibilité */
.boss-seo-input:focus-visible,
.boss-seo-textarea:focus-visible,
.boss-seo-select:focus-visible,
.boss-seo-suggestion:focus-visible,
.boss-seo-keyword-tag:focus-visible {
    outline: 2px solid var(--boss-seo-primary);
    outline-offset: 2px;
}

/* Amélioration du contraste pour l'accessibilité */
@media (prefers-contrast: high) {
    :root {
        --boss-seo-border: #000;
        --boss-seo-text-light: #333;
    }
}

/* Champs média */
.boss-seo-media-field {
    display: flex;
    gap: 8px;
    align-items: stretch;
}

.boss-seo-media-field .boss-seo-input {
    flex: 1;
}

.boss-seo-media-button {
    padding: 12px 16px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 6px;
}

.boss-seo-media-button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Checkboxes personnalisées */
.boss-seo-checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 14px;
    color: var(--boss-seo-text);
    margin-bottom: 8px;
    position: relative;
}

.boss-seo-checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.boss-seo-checkbox-label .checkmark {
    width: 18px;
    height: 18px;
    background: var(--boss-seo-bg-white);
    border: 2px solid var(--boss-seo-border);
    border-radius: 3px;
    position: relative;
    transition: var(--boss-seo-transition);
}

.boss-seo-checkbox-label:hover .checkmark {
    border-color: var(--boss-seo-primary);
}

.boss-seo-checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--boss-seo-primary);
    border-color: var(--boss-seo-primary);
}

.boss-seo-checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Barre d'actions flottante moderne */
.boss-seo-floating-actions {
    position: sticky;
    bottom: 0;
    background: linear-gradient(135deg, var(--boss-seo-bg-white) 0%, var(--boss-seo-bg-light) 100%);
    border-top: 1px solid var(--boss-seo-border);
    padding: 20px 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    justify-content: space-between;
    z-index: 10;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

.boss-seo-floating-actions .button {
    padding: 12px 20px;
    border-radius: var(--boss-seo-radius);
    font-weight: 600;
    transition: var(--boss-seo-transition);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.boss-seo-floating-actions .button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.boss-seo-floating-actions .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.boss-seo-floating-actions .button:hover::before {
    left: 100%;
}

.boss-seo-floating-actions .button-primary {
    background: var(--boss-seo-bg-gradient);
    border-color: var(--boss-seo-primary);
    color: white;
}

.boss-seo-floating-actions .button:not(.button-primary) {
    background: var(--boss-seo-bg-white);
    border: 2px solid var(--boss-seo-border);
    color: var(--boss-seo-text);
}

.boss-seo-floating-actions .button:not(.button-primary):hover {
    border-color: var(--boss-seo-primary);
    color: var(--boss-seo-primary);
}

.boss-seo-save-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--boss-seo-success);
    font-size: 13px;
    opacity: 0;
    transition: var(--boss-seo-transition);
}

.boss-seo-save-indicator.visible {
    opacity: 1;
}

.boss-seo-save-indicator .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Aperçu SERP */
.boss-seo-serp-preview {
    margin-top: 24px;
    padding: 20px;
    background: var(--boss-seo-bg-light);
    border-radius: var(--boss-seo-radius);
    border: 1px solid var(--boss-seo-border);
}

.boss-seo-serp-preview h4 {
    margin: 0 0 16px 0;
    font-size: 15px;
    font-weight: 600;
    color: var(--boss-seo-text);
    display: flex;
    align-items: center;
    gap: 8px;
}

.boss-seo-serp-result {
    background: white;
    padding: 16px;
    border-radius: var(--boss-seo-radius);
    border: 1px solid var(--boss-seo-border);
    font-family: arial, sans-serif;
}

.boss-seo-serp-result .serp-url {
    color: #006621;
    font-size: 14px;
    margin-bottom: 4px;
}

.boss-seo-serp-result .serp-title {
    color: #1a0dab;
    font-size: 18px;
    font-weight: normal;
    margin-bottom: 4px;
    cursor: pointer;
    text-decoration: underline;
}

.boss-seo-serp-result .serp-description {
    color: #545454;
    font-size: 14px;
    line-height: 1.4;
}

/* Notifications */
.boss-seo-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    background: var(--boss-seo-bg-white);
    border: 1px solid var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    padding: 12px 16px;
    box-shadow: var(--boss-seo-shadow);
    z-index: 100000;
    display: flex;
    align-items: center;
    gap: 10px;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.boss-seo-notification.success {
    border-left: 4px solid var(--boss-seo-success);
}

.boss-seo-notification.warning {
    border-left: 4px solid var(--boss-seo-warning);
}

.boss-seo-notification.error {
    border-left: 4px solid var(--boss-seo-error);
}

.boss-seo-notification.info {
    border-left: 4px solid var(--boss-seo-info);
}

.boss-seo-notification .notification-close {
    background: transparent;
    border: none;
    cursor: pointer;
    color: var(--boss-seo-text-light);
    padding: 4px;
    margin-left: auto;
}

.boss-seo-notification .notification-close:hover {
    color: var(--boss-seo-text);
}

/* Masquer les éléments pour les lecteurs d'écran uniquement */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    clip: auto !important;
    color: #21759b;
    display: block;
    font-size: 14px;
    font-weight: bold;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000;
}
