import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  TextareaControl,
  Dashicon,
  Tooltip,
  Popover,
  MenuGroup,
  MenuItem,
  DropdownMenu
} from '@wordpress/components';

// Importer le service
import RobotsSitemapService from '../../services/RobotsSitemapService';

/**
 * Composant pour l'éditeur de robots.txt amélioré
 */
const RobotsEditor = ({ robotsContent, setRobotsContent, onSave, isSaving }) => {
  const [validationErrors, setValidationErrors] = useState([]);
  const [isValidating, setIsValidating] = useState(false);
  const [showTemplatePopover, setShowTemplatePopover] = useState(false);
  const [showGoogleTestLink, setShowGoogleTestLink] = useState(false);
  const [highlightedContent, setHighlightedContent] = useState('');

  // Valider le contenu du robots.txt
  useEffect(() => {
    const validateRobots = async () => {
      if (!robotsContent) return;

      setIsValidating(true);
      try {
        const result = await RobotsSitemapService.validateRobotsContent(robotsContent);
        setValidationErrors(result.errors || []);
      } catch (error) {
        console.error('Erreur lors de la validation du robots.txt:', error);
      } finally {
        setIsValidating(false);
      }
    };

    // Utiliser un délai pour éviter de valider à chaque frappe
    const timeoutId = setTimeout(() => {
      validateRobots();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [robotsContent]);

  // Mettre à jour le contenu surligné avec les erreurs
  useEffect(() => {
    if (!robotsContent || validationErrors.length === 0) {
      setHighlightedContent(robotsContent);
      return;
    }

    // Créer le contenu surligné
    let content = robotsContent;
    const lines = content.split('\n');
    
    // Trier les erreurs par numéro de ligne (décroissant pour éviter les décalages)
    const sortedErrors = [...validationErrors].sort((a, b) => b.line - a.line);
    
    // Ajouter des marqueurs d'erreur à chaque ligne concernée
    sortedErrors.forEach(error => {
      if (error.line > 0 && error.line <= lines.length) {
        const lineIndex = error.line - 1;
        const errorClass = error.type === 'error' ? 'boss-text-red-600' : 'boss-text-yellow-600';
        lines[lineIndex] = `<span class="${errorClass}">${lines[lineIndex]}</span>`;
      }
    });
    
    setHighlightedContent(lines.join('\n'));
  }, [robotsContent, validationErrors]);

  // Ajouter des règles communes
  const addCommonRules = (type) => {
    RobotsSitemapService.getRobotsRules(type)
      .then(result => {
        const newContent = robotsContent ? `${robotsContent}\n\n${result.rules}` : result.rules;
        setRobotsContent(newContent);
      })
      .catch(error => {
        console.error(`Erreur lors de la récupération des règles ${type}:`, error);
      });
  };

  // Ajouter des règles de blocage automatique
  const addBlockingRules = (type) => {
    RobotsSitemapService.getRobotsRules(type)
      .then(result => {
        const newContent = robotsContent ? `${robotsContent}\n\n${result.rules}` : result.rules;
        setRobotsContent(newContent);
      })
      .catch(error => {
        console.error(`Erreur lors de la récupération des règles de blocage ${type}:`, error);
      });
  };

  // Ouvrir l'outil de test Google robots.txt
  const openGoogleRobotsTest = () => {
    const googleTestUrl = 'https://www.google.com/webmasters/tools/robots-testing-tool';
    window.open(googleTestUrl, '_blank');
  };

  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <div className="boss-flex boss-justify-between boss-items-center">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Éditeur robots.txt', 'boss-seo')}
          </h2>
          <div className="boss-flex boss-space-x-2">
            <DropdownMenu
              label={__('Modèles', 'boss-seo')}
              icon="admin-page"
              toggleProps={{
                isSecondary: true,
              }}
            >
              {() => (
                <MenuGroup label={__('Modèles prédéfinis', 'boss-seo')}>
                  <MenuItem onClick={() => addCommonRules('wordpress-core')}>
                    {__('WordPress Core', 'boss-seo')}
                  </MenuItem>
                  <MenuItem onClick={() => addCommonRules('media')}>
                    {__('Médias', 'boss-seo')}
                  </MenuItem>
                  <MenuItem onClick={() => addCommonRules('sitemap')}>
                    {__('Sitemap', 'boss-seo')}
                  </MenuItem>
                  <MenuItem onClick={() => addCommonRules('ecommerce')}>
                    {__('E-commerce', 'boss-seo')}
                  </MenuItem>
                  <MenuItem onClick={() => addCommonRules('complete')}>
                    {__('Modèle complet', 'boss-seo')}
                  </MenuItem>
                </MenuGroup>
              )}
            </DropdownMenu>

            <DropdownMenu
              label={__('Blocage auto', 'boss-seo')}
              icon="shield"
              toggleProps={{
                isSecondary: true,
              }}
            >
              {() => (
                <MenuGroup label={__('Règles de blocage', 'boss-seo')}>
                  <MenuItem onClick={() => addBlockingRules('preview')}>
                    {__('URLs de prévisualisation', 'boss-seo')}
                  </MenuItem>
                  <MenuItem onClick={() => addBlockingRules('noindex')}>
                    {__('Pages noindex', 'boss-seo')}
                  </MenuItem>
                </MenuGroup>
              )}
            </DropdownMenu>

            <Button
              isSecondary
              icon="external"
              onClick={openGoogleRobotsTest}
            >
              {__('Test Google', 'boss-seo')}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardBody>
        <div className="boss-mb-4">
          <div className="boss-p-4 boss-bg-blue-50 boss-rounded-lg boss-mb-4">
            <div className="boss-flex boss-items-start">
              <div className="boss-flex-shrink-0 boss-mr-3">
                <Dashicon icon="info" className="boss-text-blue-600" />
              </div>
              <div>
                <p className="boss-text-sm boss-text-boss-dark">
                  {__('Le fichier robots.txt est utilisé pour contrôler l\'accès des robots des moteurs de recherche à votre site. Utilisez-le pour empêcher l\'indexation de certaines pages ou répertoires.', 'boss-seo')}
                </p>
                <p className="boss-text-sm boss-text-boss-dark boss-mt-2">
                  {__('Votre fichier robots.txt est disponible à l\'adresse:', 'boss-seo')}
                  <a
                    href={`${window.location.origin}/robots.txt`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="boss-ml-2 boss-text-blue-600 boss-font-medium boss-underline"
                  >
                    {`${window.location.origin}/robots.txt`}
                  </a>
                </p>
              </div>
            </div>
          </div>

          {validationErrors.length > 0 && (
            <div className="boss-p-4 boss-bg-yellow-50 boss-rounded-lg boss-mb-4">
              <div className="boss-flex boss-items-start">
                <div className="boss-flex-shrink-0 boss-mr-3">
                  <Dashicon icon="warning" className="boss-text-yellow-600" />
                </div>
                <div>
                  <p className="boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-2">
                    {__('Problèmes détectés dans votre fichier robots.txt:', 'boss-seo')}
                  </p>
                  <ul className="boss-list-disc boss-pl-5 boss-space-y-1">
                    {validationErrors.map((error, index) => (
                      <li key={index} className={`boss-text-sm ${error.type === 'error' ? 'boss-text-red-600' : 'boss-text-yellow-600'}`}>
                        {error.line > 0 ? `${__('Ligne', 'boss-seo')} ${error.line}: ` : ''}{error.message}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          <TextareaControl
            value={robotsContent}
            onChange={setRobotsContent}
            className="boss-font-mono boss-text-sm"
            rows={15}
          />
        </div>
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200 boss-flex boss-justify-end">
        <Button
          isPrimary
          onClick={() => onSave(robotsContent)}
          isBusy={isSaving}
          disabled={isSaving}
        >
          {__('Enregistrer', 'boss-seo')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default RobotsEditor;
