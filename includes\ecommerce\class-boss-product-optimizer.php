<?php
/**
 * Classe pour l'optimisation des produits.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour l'optimisation des produits.
 *
 * Cette classe gère l'optimisation des produits.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Product_Optimizer {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_product_optimizer_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_products_to_optimize', array( $this, 'ajax_get_products_to_optimize' ) );
        add_action( 'wp_ajax_boss_seo_optimize_product', array( $this, 'ajax_optimize_product' ) );
        add_action( 'wp_ajax_boss_seo_get_optimization_suggestions', array( $this, 'ajax_get_optimization_suggestions' ) );
        add_action( 'wp_ajax_boss_seo_apply_optimization', array( $this, 'ajax_apply_optimization' ) );

        // Ajouter les actions pour l'optimisation des produits
        add_action( 'woocommerce_process_product_meta', array( $this, 'save_product_optimization' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/products/to-optimize',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_products_to_optimize' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/products/(?P<id>\d+)/optimize',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'optimize_product' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/products/(?P<id>\d+)/suggestions',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_optimization_suggestions' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/products/(?P<id>\d+)/apply-optimization',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'apply_optimization' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les produits à optimiser.
     *
     * @since    1.2.0
     */
    public function ajax_get_products_to_optimize() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $page = isset( $_POST['page'] ) ? absint( $_POST['page'] ) : 1;
        $per_page = isset( $_POST['per_page'] ) ? absint( $_POST['per_page'] ) : 10;
        $order_by = isset( $_POST['order_by'] ) ? sanitize_text_field( $_POST['order_by'] ) : 'score';
        $order = isset( $_POST['order'] ) ? sanitize_text_field( $_POST['order'] ) : 'asc';
        $search = isset( $_POST['search'] ) ? sanitize_text_field( $_POST['search'] ) : '';

        // Récupérer les produits à optimiser
        $products = $this->get_products_to_optimize_data( $page, $per_page, $order_by, $order, $search );

        wp_send_json_success( array(
            'message'  => __( 'Produits récupérés avec succès.', 'boss-seo' ),
            'products' => $products['products'],
            'total'    => $products['total'],
            'pages'    => $products['pages'],
        ) );
    }

    /**
     * Gère les requêtes AJAX pour optimiser un produit.
     *
     * @since    1.2.0
     */
    public function ajax_optimize_product() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );
        $title = isset( $_POST['title'] ) ? sanitize_text_field( $_POST['title'] ) : '';
        $description = isset( $_POST['description'] ) ? wp_kses_post( $_POST['description'] ) : '';
        $short_description = isset( $_POST['short_description'] ) ? wp_kses_post( $_POST['short_description'] ) : '';
        $keywords = isset( $_POST['keywords'] ) ? sanitize_text_field( $_POST['keywords'] ) : '';

        // Optimiser le produit
        $result = $this->optimize_product_data( $product_id, $title, $description, $short_description, $keywords );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Produit optimisé avec succès.', 'boss-seo' ),
            'product' => $result,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les suggestions d'optimisation.
     *
     * @since    1.2.0
     */
    public function ajax_get_optimization_suggestions() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );

        // Récupérer les suggestions d'optimisation
        $suggestions = $this->get_optimization_suggestions_data( $product_id );

        if ( is_wp_error( $suggestions ) ) {
            wp_send_json_error( array( 'message' => $suggestions->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'     => __( 'Suggestions récupérées avec succès.', 'boss-seo' ),
            'suggestions' => $suggestions,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour appliquer une optimisation.
     *
     * @since    1.2.0
     */
    public function ajax_apply_optimization() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['product_id'] ) || empty( $_POST['product_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID du produit est requis.', 'boss-seo' ) ) );
        }

        if ( ! isset( $_POST['suggestion_id'] ) || empty( $_POST['suggestion_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de la suggestion est requis.', 'boss-seo' ) ) );
        }

        $product_id = absint( $_POST['product_id'] );
        $suggestion_id = sanitize_text_field( $_POST['suggestion_id'] );

        // Appliquer l'optimisation
        $result = $this->apply_optimization_data( $product_id, $suggestion_id );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Optimisation appliquée avec succès.', 'boss-seo' ),
            'product' => $result,
        ) );
    }

    /**
     * Récupère les produits à optimiser via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_products_to_optimize( $request ) {
        $page = $request->get_param( 'page' ) ? absint( $request->get_param( 'page' ) ) : 1;
        $per_page = $request->get_param( 'per_page' ) ? absint( $request->get_param( 'per_page' ) ) : 10;
        $order_by = $request->get_param( 'order_by' ) ? sanitize_text_field( $request->get_param( 'order_by' ) ) : 'score';
        $order = $request->get_param( 'order' ) ? sanitize_text_field( $request->get_param( 'order' ) ) : 'asc';
        $search = $request->get_param( 'search' ) ? sanitize_text_field( $request->get_param( 'search' ) ) : '';

        $products = $this->get_products_to_optimize_data( $page, $per_page, $order_by, $order, $search );

        return rest_ensure_response( $products );
    }

    /**
     * Optimise un produit via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function optimize_product( $request ) {
        $product_id = $request['id'];
        $params = $request->get_params();

        $title = isset( $params['title'] ) ? sanitize_text_field( $params['title'] ) : '';
        $description = isset( $params['description'] ) ? wp_kses_post( $params['description'] ) : '';
        $short_description = isset( $params['short_description'] ) ? wp_kses_post( $params['short_description'] ) : '';
        $keywords = isset( $params['keywords'] ) ? sanitize_text_field( $params['keywords'] ) : '';

        $result = $this->optimize_product_data( $product_id, $title, $description, $short_description, $keywords );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Produit optimisé avec succès.', 'boss-seo' ),
            'product' => $result,
        ) );
    }

    /**
     * Récupère les suggestions d'optimisation via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_optimization_suggestions( $request ) {
        $product_id = $request['id'];

        $suggestions = $this->get_optimization_suggestions_data( $product_id );

        if ( is_wp_error( $suggestions ) ) {
            return $suggestions;
        }

        return rest_ensure_response( $suggestions );
    }

    /**
     * Applique une optimisation via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function apply_optimization( $request ) {
        $product_id = $request['id'];
        $params = $request->get_params();

        if ( ! isset( $params['suggestion_id'] ) || empty( $params['suggestion_id'] ) ) {
            return new WP_Error( 'missing_suggestion_id', __( 'L\'ID de la suggestion est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $suggestion_id = sanitize_text_field( $params['suggestion_id'] );

        $result = $this->apply_optimization_data( $product_id, $suggestion_id );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Optimisation appliquée avec succès.', 'boss-seo' ),
            'product' => $result,
        ) );
    }

    /**
     * Enregistre l'optimisation d'un produit.
     *
     * @since    1.2.0
     * @param    int    $post_id    L'ID du produit.
     */
    public function save_product_optimization( $post_id ) {
        // Vérifier si c'est un produit
        if ( get_post_type( $post_id ) !== 'product' ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Vérifier si c'est une sauvegarde automatique
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }

        // Vérifier si les champs d'optimisation sont présents
        if ( ! isset( $_POST['boss_seo_product_keywords'] ) ) {
            return;
        }

        // Enregistrer les mots-clés
        $keywords = sanitize_text_field( $_POST['boss_seo_product_keywords'] );
        update_post_meta( $post_id, '_boss_seo_product_keywords', $keywords );

        // Analyser le produit après la sauvegarde
        $this->analyze_product_after_save( $post_id );
    }

    /**
     * Analyse un produit après sa sauvegarde.
     *
     * @since    1.2.0
     * @param    int    $product_id    L'ID du produit.
     */
    private function analyze_product_after_save( $product_id ) {
        // Vérifier si l'analyse automatique est activée
        $settings = get_option( 'boss_ecommerce_settings', array() );

        if ( isset( $settings['auto_analyze'] ) && $settings['auto_analyze'] ) {
            // Analyser le produit
            $analyzer = new Boss_Seo_Analyzer( $this->plugin_name, $this->version );
            $analyzer->analyze_product( $product_id );
        }
    }

    /**
     * Récupère les produits à optimiser.
     *
     * @since    1.2.0
     * @param    int       $page       Le numéro de page.
     * @param    int       $per_page   Le nombre d'éléments par page.
     * @param    string    $order_by   Le champ de tri.
     * @param    string    $order      L'ordre de tri.
     * @param    string    $search     La recherche.
     * @return   array                 Les produits à optimiser.
     */
    private function get_products_to_optimize_data( $page, $per_page, $order_by, $order, $search ) {
        // Paramètres de la requête
        $args = array(
            'post_type'      => 'product',
            'posts_per_page' => $per_page,
            'paged'          => $page,
            'post_status'    => 'publish',
        );

        // Ajouter la recherche
        if ( ! empty( $search ) ) {
            $args['s'] = $search;
        }

        // Exécuter la requête
        $query = new WP_Query( $args );

        // Préparer les résultats
        $products = array();

        foreach ( $query->posts as $post ) {
            $product = wc_get_product( $post );

            if ( ! $product ) {
                continue;
            }

            // Récupérer l'analyse SEO
            $analysis = get_post_meta( $post->ID, '_boss_seo_analysis', true );
            $score = ! empty( $analysis ) && isset( $analysis['score'] ) ? $analysis['score'] : 0;

            // Récupérer les mots-clés
            $keywords = get_post_meta( $post->ID, '_boss_seo_product_keywords', true );

            $products[] = array(
                'id'                => $post->ID,
                'title'             => $product->get_name(),
                'permalink'         => get_permalink( $post->ID ),
                'score'             => $score,
                'keywords'          => $keywords,
                'description'       => $product->get_description(),
                'short_description' => $product->get_short_description(),
                'image'             => $product->get_image_id() ? wp_get_attachment_url( $product->get_image_id() ) : '',
                'price'             => $product->get_price(),
                'regular_price'     => $product->get_regular_price(),
                'sale_price'        => $product->get_sale_price(),
                'sku'               => $product->get_sku(),
                'stock_status'      => $product->get_stock_status(),
                'categories'        => $this->get_product_categories( $product ),
                'tags'              => $this->get_product_tags( $product ),
            );
        }

        // Trier les produits
        if ( $order_by === 'score' ) {
            usort( $products, function( $a, $b ) use ( $order ) {
                if ( $order === 'asc' ) {
                    return $a['score'] - $b['score'];
                } else {
                    return $b['score'] - $a['score'];
                }
            } );
        }

        return array(
            'products' => $products,
            'total'    => $query->found_posts,
            'pages'    => ceil( $query->found_posts / $per_page ),
        );
    }

    /**
     * Récupère les catégories d'un produit.
     *
     * @since    1.2.0
     * @param    WC_Product    $product    Le produit.
     * @return   array                     Les catégories du produit.
     */
    private function get_product_categories( $product ) {
        $categories = array();
        $category_ids = $product->get_category_ids();

        foreach ( $category_ids as $category_id ) {
            $term = get_term( $category_id, 'product_cat' );

            if ( $term && ! is_wp_error( $term ) ) {
                $categories[] = array(
                    'id'   => $term->term_id,
                    'name' => $term->name,
                    'slug' => $term->slug,
                );
            }
        }

        return $categories;
    }

    /**
     * Récupère les tags d'un produit.
     *
     * @since    1.2.0
     * @param    WC_Product    $product    Le produit.
     * @return   array                     Les tags du produit.
     */
    private function get_product_tags( $product ) {
        $tags = array();
        $tag_ids = $product->get_tag_ids();

        foreach ( $tag_ids as $tag_id ) {
            $term = get_term( $tag_id, 'product_tag' );

            if ( $term && ! is_wp_error( $term ) ) {
                $tags[] = array(
                    'id'   => $term->term_id,
                    'name' => $term->name,
                    'slug' => $term->slug,
                );
            }
        }

        return $tags;
    }

    /**
     * Optimise un produit.
     *
     * @since    1.2.0
     * @param    int       $product_id          L'ID du produit.
     * @param    string    $title               Le titre du produit.
     * @param    string    $description         La description du produit.
     * @param    string    $short_description   La description courte du produit.
     * @param    string    $keywords            Les mots-clés du produit.
     * @return   array|WP_Error                 Le produit optimisé ou une erreur.
     */
    private function optimize_product_data( $product_id, $title, $description, $short_description, $keywords ) {
        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );

        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ) );
        }

        // Mettre à jour le produit
        $product_data = array(
            'ID' => $product_id,
        );

        if ( ! empty( $title ) ) {
            $product_data['post_title'] = $title;
        }

        if ( ! empty( $description ) ) {
            $product_data['post_content'] = $description;
        }

        if ( ! empty( $short_description ) ) {
            $product_data['post_excerpt'] = $short_description;
        }

        // Mettre à jour le produit
        $result = wp_update_post( $product_data, true );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        // Mettre à jour les mots-clés
        if ( ! empty( $keywords ) ) {
            update_post_meta( $product_id, '_boss_seo_product_keywords', $keywords );
        }

        // Analyser le produit après l'optimisation
        $this->analyze_product_after_save( $product_id );

        // Récupérer le produit mis à jour
        $updated_product = wc_get_product( $product_id );

        // Récupérer l'analyse SEO
        $analysis = get_post_meta( $product_id, '_boss_seo_analysis', true );
        $score = ! empty( $analysis ) && isset( $analysis['score'] ) ? $analysis['score'] : 0;

        // Récupérer les mots-clés
        $keywords = get_post_meta( $product_id, '_boss_seo_product_keywords', true );

        return array(
            'id'                => $product_id,
            'title'             => $updated_product->get_name(),
            'permalink'         => get_permalink( $product_id ),
            'score'             => $score,
            'keywords'          => $keywords,
            'description'       => $updated_product->get_description(),
            'short_description' => $updated_product->get_short_description(),
            'image'             => $updated_product->get_image_id() ? wp_get_attachment_url( $updated_product->get_image_id() ) : '',
            'price'             => $updated_product->get_price(),
            'regular_price'     => $updated_product->get_regular_price(),
            'sale_price'        => $updated_product->get_sale_price(),
            'sku'               => $updated_product->get_sku(),
            'stock_status'      => $updated_product->get_stock_status(),
            'categories'        => $this->get_product_categories( $updated_product ),
            'tags'              => $this->get_product_tags( $updated_product ),
        );
    }

    /**
     * Récupère les suggestions d'optimisation pour un produit.
     *
     * @since    1.2.0
     * @param    int       $product_id    L'ID du produit.
     * @return   array|WP_Error           Les suggestions d'optimisation ou une erreur.
     */
    private function get_optimization_suggestions_data( $product_id ) {
        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );

        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ) );
        }

        // Récupérer l'analyse SEO
        $analysis = get_post_meta( $product_id, '_boss_seo_analysis', true );

        if ( empty( $analysis ) ) {
            return new WP_Error( 'no_analysis', __( 'Aucune analyse trouvée.', 'boss-seo' ) );
        }

        // Préparer les suggestions
        $suggestions = array();

        // Suggestions pour le titre
        if ( isset( $analysis['title'] ) && ! empty( $analysis['title']['recommendations'] ) ) {
            foreach ( $analysis['title']['recommendations'] as $recommendation ) {
                $suggestions[] = array(
                    'id'             => 'title_' . md5( $recommendation ),
                    'type'           => 'title',
                    'recommendation' => $recommendation,
                    'priority'       => $this->get_suggestion_priority( $analysis['title']['status'] ),
                    'status'         => $analysis['title']['status'],
                );
            }
        }

        // Suggestions pour la description
        if ( isset( $analysis['description'] ) && ! empty( $analysis['description']['recommendations'] ) ) {
            foreach ( $analysis['description']['recommendations'] as $recommendation ) {
                $suggestions[] = array(
                    'id'             => 'description_' . md5( $recommendation ),
                    'type'           => 'description',
                    'recommendation' => $recommendation,
                    'priority'       => $this->get_suggestion_priority( $analysis['description']['status'] ),
                    'status'         => $analysis['description']['status'],
                );
            }
        }

        // Suggestions pour les images
        if ( isset( $analysis['images'] ) && ! empty( $analysis['images']['recommendations'] ) ) {
            foreach ( $analysis['images']['recommendations'] as $recommendation ) {
                $suggestions[] = array(
                    'id'             => 'images_' . md5( $recommendation ),
                    'type'           => 'images',
                    'recommendation' => $recommendation,
                    'priority'       => $this->get_suggestion_priority( $analysis['images']['status'] ),
                    'status'         => $analysis['images']['status'],
                );
            }
        }

        // Suggestions pour les catégories et tags
        if ( isset( $analysis['categories_tags'] ) && ! empty( $analysis['categories_tags']['recommendations'] ) ) {
            foreach ( $analysis['categories_tags']['recommendations'] as $recommendation ) {
                $suggestions[] = array(
                    'id'             => 'categories_tags_' . md5( $recommendation ),
                    'type'           => 'categories_tags',
                    'recommendation' => $recommendation,
                    'priority'       => $this->get_suggestion_priority( $analysis['categories_tags']['status'] ),
                    'status'         => $analysis['categories_tags']['status'],
                );
            }
        }

        // Suggestions pour les attributs
        if ( isset( $analysis['attributes'] ) && ! empty( $analysis['attributes']['recommendations'] ) ) {
            foreach ( $analysis['attributes']['recommendations'] as $recommendation ) {
                $suggestions[] = array(
                    'id'             => 'attributes_' . md5( $recommendation ),
                    'type'           => 'attributes',
                    'recommendation' => $recommendation,
                    'priority'       => $this->get_suggestion_priority( $analysis['attributes']['status'] ),
                    'status'         => $analysis['attributes']['status'],
                );
            }
        }

        // Trier les suggestions par priorité
        usort( $suggestions, function( $a, $b ) {
            return $b['priority'] - $a['priority'];
        } );

        return $suggestions;
    }

    /**
     * Récupère la priorité d'une suggestion.
     *
     * @since    1.2.0
     * @param    string    $status    Le statut de l'analyse.
     * @return   int                  La priorité de la suggestion.
     */
    private function get_suggestion_priority( $status ) {
        switch ( $status ) {
            case 'error':
                return 3;
            case 'warning':
                return 2;
            case 'good':
                return 1;
            case 'excellent':
                return 0;
            default:
                return 0;
        }
    }

    /**
     * Applique une optimisation à un produit.
     *
     * @since    1.2.0
     * @param    int       $product_id      L'ID du produit.
     * @param    string    $suggestion_id   L'ID de la suggestion.
     * @return   array|WP_Error             Le produit optimisé ou une erreur.
     */
    private function apply_optimization_data( $product_id, $suggestion_id ) {
        // Vérifier si le produit existe
        $product = wc_get_product( $product_id );

        if ( ! $product ) {
            return new WP_Error( 'product_not_found', __( 'Produit non trouvé.', 'boss-seo' ) );
        }

        // Récupérer les suggestions d'optimisation
        $suggestions = $this->get_optimization_suggestions_data( $product_id );

        if ( is_wp_error( $suggestions ) ) {
            return $suggestions;
        }

        // Trouver la suggestion
        $suggestion = null;

        foreach ( $suggestions as $s ) {
            if ( $s['id'] === $suggestion_id ) {
                $suggestion = $s;
                break;
            }
        }

        if ( ! $suggestion ) {
            return new WP_Error( 'suggestion_not_found', __( 'Suggestion non trouvée.', 'boss-seo' ) );
        }

        // Appliquer la suggestion
        $product_data = array(
            'ID' => $product_id,
        );

        switch ( $suggestion['type'] ) {
            case 'title':
                // Améliorer le titre
                $title = $product->get_name();
                $product_data['post_title'] = $this->improve_title( $title, $suggestion['recommendation'] );
                break;

            case 'description':
                // Améliorer la description
                $description = $product->get_description();
                $product_data['post_content'] = $this->improve_description( $description, $suggestion['recommendation'] );
                break;

            case 'images':
                // Les suggestions pour les images ne peuvent pas être appliquées automatiquement
                return new WP_Error( 'manual_action_required', __( 'Cette suggestion nécessite une action manuelle.', 'boss-seo' ) );

            case 'categories_tags':
                // Les suggestions pour les catégories et tags ne peuvent pas être appliquées automatiquement
                return new WP_Error( 'manual_action_required', __( 'Cette suggestion nécessite une action manuelle.', 'boss-seo' ) );

            case 'attributes':
                // Les suggestions pour les attributs ne peuvent pas être appliquées automatiquement
                return new WP_Error( 'manual_action_required', __( 'Cette suggestion nécessite une action manuelle.', 'boss-seo' ) );

            default:
                return new WP_Error( 'invalid_suggestion_type', __( 'Type de suggestion non valide.', 'boss-seo' ) );
        }

        // Mettre à jour le produit
        $result = wp_update_post( $product_data, true );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        // Analyser le produit après l'optimisation
        $this->analyze_product_after_save( $product_id );

        // Récupérer le produit mis à jour
        $updated_product = wc_get_product( $product_id );

        // Récupérer l'analyse SEO
        $analysis = get_post_meta( $product_id, '_boss_seo_analysis', true );
        $score = ! empty( $analysis ) && isset( $analysis['score'] ) ? $analysis['score'] : 0;

        // Récupérer les mots-clés
        $keywords = get_post_meta( $product_id, '_boss_seo_product_keywords', true );

        return array(
            'id'                => $product_id,
            'title'             => $updated_product->get_name(),
            'permalink'         => get_permalink( $product_id ),
            'score'             => $score,
            'keywords'          => $keywords,
            'description'       => $updated_product->get_description(),
            'short_description' => $updated_product->get_short_description(),
            'image'             => $updated_product->get_image_id() ? wp_get_attachment_url( $updated_product->get_image_id() ) : '',
            'price'             => $updated_product->get_price(),
            'regular_price'     => $updated_product->get_regular_price(),
            'sale_price'        => $updated_product->get_sale_price(),
            'sku'               => $updated_product->get_sku(),
            'stock_status'      => $updated_product->get_stock_status(),
            'categories'        => $this->get_product_categories( $updated_product ),
            'tags'              => $this->get_product_tags( $updated_product ),
        );
    }

    /**
     * Améliore le titre d'un produit.
     *
     * @since    1.2.0
     * @param    string    $title             Le titre du produit.
     * @param    string    $recommendation    La recommandation.
     * @return   string                       Le titre amélioré.
     */
    private function improve_title( $title, $recommendation ) {
        // Analyser la recommandation
        if ( strpos( $recommendation, 'trop court' ) !== false ) {
            // Le titre est trop court, l'allonger
            $keywords = get_post_meta( get_the_ID(), '_boss_seo_product_keywords', true );

            if ( ! empty( $keywords ) ) {
                $keywords_array = explode( ',', $keywords );
                $keyword = trim( $keywords_array[0] );

                if ( strpos( $title, $keyword ) === false ) {
                    $title .= ' - ' . $keyword;
                }
            }
        } elseif ( strpos( $recommendation, 'trop long' ) !== false ) {
            // Le titre est trop long, le raccourcir
            $title = substr( $title, 0, 70 );
        }

        return $title;
    }

    /**
     * Améliore la description d'un produit.
     *
     * @since    1.2.0
     * @param    string    $description       La description du produit.
     * @param    string    $recommendation    La recommandation.
     * @return   string                       La description améliorée.
     */
    private function improve_description( $description, $recommendation ) {
        // Analyser la recommandation
        if ( strpos( $recommendation, 'trop courte' ) !== false ) {
            // La description est trop courte, l'allonger
            $keywords = get_post_meta( get_the_ID(), '_boss_seo_product_keywords', true );

            if ( ! empty( $keywords ) ) {
                $keywords_array = explode( ',', $keywords );
                $keyword = trim( $keywords_array[0] );

                $description .= '<p>Ce produit est parfait pour ' . $keyword . '. Il offre une excellente qualité et durabilité.</p>';
            } else {
                $description .= '<p>Ce produit offre une excellente qualité et durabilité. Il est parfait pour répondre à vos besoins.</p>';
            }
        } elseif ( strpos( $recommendation, 'listes' ) !== false ) {
            // Ajouter des listes à la description
            $description .= '<h3>Caractéristiques :</h3>';
            $description .= '<ul>';
            $description .= '<li>Haute qualité</li>';
            $description .= '<li>Durable</li>';
            $description .= '<li>Facile à utiliser</li>';
            $description .= '<li>Excellent rapport qualité-prix</li>';
            $description .= '</ul>';
        } elseif ( strpos( $recommendation, 'titres' ) !== false ) {
            // Ajouter des titres à la description
            $description .= '<h3>Pourquoi choisir ce produit ?</h3>';
            $description .= '<p>Ce produit offre une excellente qualité et durabilité. Il est parfait pour répondre à vos besoins.</p>';
        }

        return $description;
    }
}
