/**
 * Composant pour l'étape 4 : Sélection d'images
 */
import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Dashicon,
  Spinner,
  SearchControl,
  SelectControl,
  Notice,
  CheckboxControl
} from '@wordpress/components';

// Services
import imageService from '../../../services/ImageService';

/**
 * Composant pour afficher une image avec option de sélection
 */
const ImageCard = ({ image, onSelect, isSelected, maxSelected, isDisabled }) => {
  return (
    <div 
      className={`boss-relative boss-rounded boss-overflow-hidden boss-border boss-transition-all boss-duration-200 boss-ease-in-out ${
        isSelected 
          ? 'boss-border-boss-primary boss-shadow-md' 
          : 'boss-border-gray-200 boss-hover:boss-shadow-sm'
      } ${isDisabled && !isSelected ? 'boss-opacity-50' : ''}`}
    >
      <div className="boss-aspect-w-16 boss-aspect-h-9 boss-bg-gray-100">
        <img 
          src={image.thumbnail} 
          alt={image.alt} 
          className="boss-object-cover boss-w-full boss-h-full"
        />
      </div>
      
      <div className="boss-absolute boss-top-2 boss-right-2">
        <button
          className={`boss-w-6 boss-h-6 boss-rounded-full boss-flex boss-items-center boss-justify-center boss-transition-all boss-duration-200 boss-ease-in-out ${
            isSelected 
              ? 'boss-bg-boss-primary boss-text-white' 
              : 'boss-bg-white boss-text-boss-gray boss-border boss-border-gray-200'
          }`}
          onClick={() => !isDisabled || isSelected ? onSelect(image) : null}
          disabled={isDisabled && !isSelected}
        >
          {isSelected && <Dashicon icon="yes" size={16} />}
        </button>
      </div>
      
      <div className="boss-p-2 boss-text-xs boss-text-boss-gray boss-flex boss-justify-between boss-items-center">
        <div className="boss-truncate">
          {image.source === 'pexels' && 'Pexels'}
          {image.source === 'unsplash' && 'Unsplash'}
          {image.source === 'pixabay' && 'Pixabay'}
        </div>
        <div className="boss-truncate boss-text-right">
          {image.width}x{image.height}
        </div>
      </div>
    </div>
  );
};

/**
 * Composant pour l'étape 4 : Sélection d'images
 * 
 * @param {Object} props Propriétés du composant
 * @param {Object} props.data Données de l'étape
 * @param {Function} props.updateData Fonction pour mettre à jour les données
 * @param {Object} props.keywordsData Données de l'étape 2 (Recherche de mots-clés)
 */
const StepImages = ({ data, updateData, keywordsData }) => {
  // États pour la recherche
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState('');
  
  // États pour les filtres
  const [imageSource, setImageSource] = useState('pexels');
  const [availableSources, setAvailableSources] = useState([]);
  const [useKeywords, setUseKeywords] = useState(true);
  
  // État pour les images sélectionnées
  const [selectedImages, setSelectedImages] = useState(data.selected || []);
  const maxImages = 4;
  
  // Fonction pour rechercher des images
  const searchImages = async (query = searchQuery) => {
    if (!query && !useKeywords) {
      setError(__('Veuillez entrer un terme de recherche ou utiliser les mots-clés.', 'boss-seo'));
      return;
    }
    
    setIsSearching(true);
    setError('');
    
    try {
      let results;
      
      if (useKeywords) {
        // Utiliser les mots-clés pour la recherche
        const keywords = [keywordsData.main, ...keywordsData.secondary].filter(Boolean);
        
        if (keywords.length === 0) {
          setError(__('Aucun mot-clé disponible. Veuillez ajouter des mots-clés à l\'étape 2 ou désactiver l\'option "Utiliser les mots-clés".', 'boss-seo'));
          setIsSearching(false);
          return;
        }
        
        results = await imageService.searchImagesByKeywords(keywords, imageSource);
      } else {
        // Utiliser la requête de recherche
        results = await imageService.searchImages(query, imageSource);
      }
      
      // Mettre à jour les suggestions d'images
      updateData({
        ...data,
        suggestions: results
      });
    } catch (error) {
      console.error('Erreur lors de la recherche d\'images:', error);
      setError(__('Erreur lors de la recherche d\'images. Veuillez réessayer.', 'boss-seo'));
      
      // Utiliser des données fictives en cas d'erreur
      updateData({
        ...data,
        suggestions: [
          {
            id: '1',
            url: 'https://images.pexels.com/photos/1181271/pexels-photo-1181271.jpeg',
            thumbnail: 'https://images.pexels.com/photos/1181271/pexels-photo-1181271.jpeg?auto=compress&cs=tinysrgb&w=500',
            alt: 'Image 1',
            width: 1920,
            height: 1080,
            photographer: 'Photographer 1',
            photographer_url: 'https://www.pexels.com/@photographer1',
            source: 'pexels',
          },
          {
            id: '2',
            url: 'https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg',
            thumbnail: 'https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg?auto=compress&cs=tinysrgb&w=500',
            alt: 'Image 2',
            width: 1920,
            height: 1080,
            photographer: 'Photographer 2',
            photographer_url: 'https://www.pexels.com/@photographer2',
            source: 'pexels',
          },
          // Ajouter plus d'images fictives si nécessaire
        ]
      });
    } finally {
      setIsSearching(false);
    }
  };
  
  // Fonction pour sélectionner/désélectionner une image
  const toggleImageSelection = (image) => {
    const isAlreadySelected = selectedImages.some(img => img.id === image.id);
    
    if (isAlreadySelected) {
      // Désélectionner l'image
      const newSelectedImages = selectedImages.filter(img => img.id !== image.id);
      setSelectedImages(newSelectedImages);
      updateData({
        ...data,
        selected: newSelectedImages
      });
    } else if (selectedImages.length < maxImages) {
      // Sélectionner l'image
      const newSelectedImages = [...selectedImages, image];
      setSelectedImages(newSelectedImages);
      updateData({
        ...data,
        selected: newSelectedImages
      });
    }
  };
  
  // Fonction pour régénérer les suggestions d'images
  const regenerateSuggestions = () => {
    searchImages();
  };
  
  // Effet pour récupérer les sources d'images disponibles
  useEffect(() => {
    const fetchImageSources = async () => {
      try {
        const sources = await imageService.getImageSources();
        setAvailableSources(sources);
        
        // Si la source actuelle n'est pas disponible, utiliser la première source disponible
        if (sources.length > 0 && !sources.some(source => source.value === imageSource)) {
          setImageSource(sources[0].value);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des sources d\'images:', error);
        
        // Utiliser des sources par défaut en cas d'erreur
        setAvailableSources([
          { label: 'Pexels', value: 'pexels' },
          { label: 'Unsplash', value: 'unsplash' },
          { label: 'Pixabay', value: 'pixabay' },
        ]);
      }
    };
    
    fetchImageSources();
  }, []);
  
  // Effet pour charger les images au chargement du composant
  useEffect(() => {
    // Si des suggestions sont déjà disponibles, ne pas recharger
    if (data.suggestions && data.suggestions.length > 0) {
      return;
    }
    
    // Sinon, rechercher des images
    searchImages();
  }, []);
  
  // Effet pour mettre à jour les images sélectionnées lorsque les données changent
  useEffect(() => {
    setSelectedImages(data.selected || []);
  }, [data.selected]);
  
  return (
    <div className="boss-space-y-6">
      <div className="boss-mb-6">
        <h2 className="boss-text-xl boss-font-semibold boss-text-boss-dark boss-mb-4">
          {__('Étape 4 : Sélection d\'images', 'boss-seo')}
        </h2>
        <p className="boss-text-boss-gray">
          {__('Sélectionnez jusqu\'à 4 images pour illustrer votre contenu. Vous pouvez rechercher des images ou utiliser les suggestions basées sur vos mots-clés.', 'boss-seo')}
        </p>
      </div>
      
      {error && (
        <Notice status="error" isDismissible={true} onRemove={() => setError('')} className="boss-mb-4">
          {error}
        </Notice>
      )}
      
      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="format-image" className="boss-mr-2" />
            {__('Recherche d\'images', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-mb-4">
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-4 boss-mb-4">
              <div className="boss-col-span-2">
                <SearchControl
                  label={__('Rechercher des images', 'boss-seo')}
                  value={searchQuery}
                  onChange={setSearchQuery}
                  placeholder={__('Entrez des mots-clés pour rechercher des images...', 'boss-seo')}
                  disabled={useKeywords}
                  className="boss-mb-4"
                />
              </div>
              
              <SelectControl
                label={__('Source d\'images', 'boss-seo')}
                value={imageSource}
                options={availableSources.length > 0 ? availableSources : [
                  { label: 'Pexels', value: 'pexels' },
                  { label: 'Unsplash', value: 'unsplash' },
                  { label: 'Pixabay', value: 'pixabay' },
                ]}
                onChange={setImageSource}
              />
            </div>
            
            <CheckboxControl
              label={__('Utiliser les mots-clés pour la recherche', 'boss-seo')}
              help={__('Utilise automatiquement vos mots-clés principaux et secondaires pour rechercher des images pertinentes.', 'boss-seo')}
              checked={useKeywords}
              onChange={setUseKeywords}
              className="boss-mb-4"
            />
            
            <div className="boss-flex boss-justify-between boss-items-center">
              <Button
                isPrimary
                onClick={() => searchImages()}
                disabled={(!searchQuery && !useKeywords) || isSearching}
                isBusy={isSearching}
              >
                {__('Rechercher', 'boss-seo')}
              </Button>
              
              <Button
                isSecondary
                onClick={regenerateSuggestions}
                disabled={isSearching}
                isBusy={isSearching}
              >
                {__('Régénérer les suggestions', 'boss-seo')}
              </Button>
            </div>
          </div>
          
          {isSearching ? (
            <div className="boss-text-center boss-py-8">
              <Spinner />
              <p className="boss-mt-2 boss-text-boss-gray">
                {__('Recherche d\'images en cours...', 'boss-seo')}
              </p>
            </div>
          ) : data.suggestions && data.suggestions.length > 0 ? (
            <div>
              <div className="boss-flex boss-justify-between boss-items-center boss-mb-4">
                <h4 className="boss-font-medium">
                  {__('Résultats de recherche', 'boss-seo')}
                </h4>
                
                <div className="boss-text-sm boss-text-boss-gray">
                  {selectedImages.length}/{maxImages} {__('images sélectionnées', 'boss-seo')}
                </div>
              </div>
              
              <div className="boss-grid boss-grid-cols-2 md:boss-grid-cols-4 boss-gap-4">
                {data.suggestions.map((image, index) => (
                  <ImageCard
                    key={index}
                    image={image}
                    onSelect={() => toggleImageSelection(image)}
                    isSelected={selectedImages.some(img => img.id === image.id)}
                    maxSelected={maxImages}
                    isDisabled={selectedImages.length >= maxImages && !selectedImages.some(img => img.id === image.id)}
                  />
                ))}
              </div>
            </div>
          ) : (
            <div className="boss-text-center boss-py-8 boss-text-boss-gray">
              <Dashicon icon="format-image" className="boss-mb-2 boss-text-3xl" />
              <p>{__('Aucune image trouvée. Essayez une autre recherche ou une autre source d\'images.', 'boss-seo')}</p>
            </div>
          )}
        </CardBody>
      </Card>
      
      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="images-alt2" className="boss-mr-2" />
            {__('Images sélectionnées', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          {selectedImages.length > 0 ? (
            <div className="boss-grid boss-grid-cols-2 md:boss-grid-cols-4 boss-gap-4">
              {selectedImages.map((image, index) => (
                <div key={index} className="boss-relative boss-rounded boss-overflow-hidden boss-border boss-border-boss-primary boss-shadow-md">
                  <div className="boss-aspect-w-16 boss-aspect-h-9 boss-bg-gray-100">
                    <img 
                      src={image.thumbnail} 
                      alt={image.alt} 
                      className="boss-object-cover boss-w-full boss-h-full"
                    />
                  </div>
                  
                  <button
                    className="boss-absolute boss-top-2 boss-right-2 boss-w-6 boss-h-6 boss-rounded-full boss-bg-red-500 boss-text-white boss-flex boss-items-center boss-justify-center boss-transition-all boss-duration-200 boss-ease-in-out boss-hover:boss-bg-red-600"
                    onClick={() => toggleImageSelection(image)}
                  >
                    <Dashicon icon="no-alt" size={16} />
                  </button>
                  
                  <div className="boss-p-2 boss-text-xs boss-text-boss-gray boss-flex boss-justify-between boss-items-center">
                    <div className="boss-truncate">
                      {image.source === 'pexels' && 'Pexels'}
                      {image.source === 'unsplash' && 'Unsplash'}
                      {image.source === 'pixabay' && 'Pixabay'}
                    </div>
                    <div className="boss-truncate boss-text-right">
                      {image.width}x{image.height}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="boss-text-center boss-py-8 boss-text-boss-gray">
              <Dashicon icon="images-alt2" className="boss-mb-2 boss-text-3xl" />
              <p>{__('Aucune image sélectionnée. Choisissez jusqu\'à 4 images dans les résultats de recherche.', 'boss-seo')}</p>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default StepImages;
