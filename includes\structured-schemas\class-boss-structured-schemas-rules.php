<?php
/**
 * La classe qui gère les règles d'application des schémas structurés.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 */

/**
 * La classe qui gère les règles d'application des schémas structurés.
 *
 * Cette classe gère la création, la modification, la suppression et l'application des règles.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 * <AUTHOR> SEO Team
 */
class Boss_Structured_Schemas_Rules {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string                   $plugin_name    Le nom du plugin.
     * @param    string                   $version        La version du plugin.
     * @param    Boss_Optimizer_Settings  $settings       Instance de la classe de paramètres (peut être null).
     */
    public function __construct( $plugin_name, $version, $settings = null ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;
    }

    /**
     * Crée une nouvelle règle.
     *
     * @since    1.2.0
     * @param    array    $rule_data    Les données de la règle.
     * @return   int|WP_Error           L'ID de la règle créée ou une erreur.
     */
    public function create_rule( $rule_data ) {
        // Valider les données
        if ( empty( $rule_data['title'] ) || empty( $rule_data['schema_ids'] ) || empty( $rule_data['conditions'] ) ) {
            return new WP_Error( 'invalid_rule', __( 'Le titre, les schémas et les conditions sont obligatoires.', 'boss-seo' ) );
        }

        // Créer le post
        $post_id = wp_insert_post( array(
            'post_title' => sanitize_text_field( $rule_data['title'] ),
            'post_type' => 'boss_schema_rule',
            'post_status' => 'publish',
        ) );

        if ( is_wp_error( $post_id ) ) {
            return $post_id;
        }

        // Enregistrer les métadonnées
        update_post_meta( $post_id, '_rule_schema_ids', $this->sanitize_schema_ids( $rule_data['schema_ids'] ) );
        update_post_meta( $post_id, '_rule_conditions', $this->sanitize_conditions( $rule_data['conditions'] ) );
        update_post_meta( $post_id, '_rule_priority', isset( $rule_data['priority'] ) ? intval( $rule_data['priority'] ) : 10 );
        update_post_meta( $post_id, '_rule_active', isset( $rule_data['active'] ) ? (bool) $rule_data['active'] : true );

        return $post_id;
    }

    /**
     * Met à jour une règle existante.
     *
     * @since    1.2.0
     * @param    int      $rule_id      L'ID de la règle.
     * @param    array    $rule_data    Les données de la règle.
     * @return   bool|WP_Error          True si la mise à jour a réussi, une erreur sinon.
     */
    public function update_rule( $rule_id, $rule_data ) {
        // Vérifier que la règle existe
        $rule = get_post( $rule_id );

        if ( ! $rule || $rule->post_type !== 'boss_schema_rule' ) {
            return new WP_Error( 'rule_not_found', __( 'Règle non trouvée.', 'boss-seo' ) );
        }

        // Mettre à jour le titre si nécessaire
        if ( ! empty( $rule_data['title'] ) ) {
            wp_update_post( array(
                'ID' => $rule_id,
                'post_title' => sanitize_text_field( $rule_data['title'] ),
            ) );
        }

        // Mettre à jour les métadonnées
        if ( isset( $rule_data['schema_ids'] ) ) {
            update_post_meta( $rule_id, '_rule_schema_ids', $this->sanitize_schema_ids( $rule_data['schema_ids'] ) );
        }

        if ( isset( $rule_data['conditions'] ) ) {
            update_post_meta( $rule_id, '_rule_conditions', $this->sanitize_conditions( $rule_data['conditions'] ) );
        }

        if ( isset( $rule_data['priority'] ) ) {
            update_post_meta( $rule_id, '_rule_priority', intval( $rule_data['priority'] ) );
        }

        if ( isset( $rule_data['active'] ) ) {
            update_post_meta( $rule_id, '_rule_active', (bool) $rule_data['active'] );
        }

        return true;
    }

    /**
     * Supprime une règle.
     *
     * @since    1.2.0
     * @param    int       $rule_id    L'ID de la règle.
     * @return   bool|WP_Error         True si la suppression a réussi, une erreur sinon.
     */
    public function delete_rule( $rule_id ) {
        // Vérifier que la règle existe
        $rule = get_post( $rule_id );

        if ( ! $rule || $rule->post_type !== 'boss_schema_rule' ) {
            return new WP_Error( 'rule_not_found', __( 'Règle non trouvée.', 'boss-seo' ) );
        }

        // Supprimer la règle
        $result = wp_delete_post( $rule_id, true );

        if ( ! $result ) {
            return new WP_Error( 'delete_failed', __( 'Échec de la suppression de la règle.', 'boss-seo' ) );
        }

        return true;
    }

    /**
     * Récupère une règle.
     *
     * @since    1.2.0
     * @param    int       $rule_id    L'ID de la règle.
     * @return   array|WP_Error        Les données de la règle ou une erreur.
     */
    public function get_rule( $rule_id ) {
        // Vérifier que la règle existe
        $rule = get_post( $rule_id );

        if ( ! $rule || $rule->post_type !== 'boss_schema_rule' ) {
            return new WP_Error( 'rule_not_found', __( 'Règle non trouvée.', 'boss-seo' ) );
        }

        // Récupérer les métadonnées
        $schema_ids = get_post_meta( $rule_id, '_rule_schema_ids', true );
        $conditions = get_post_meta( $rule_id, '_rule_conditions', true );
        $priority = get_post_meta( $rule_id, '_rule_priority', true );
        $active = get_post_meta( $rule_id, '_rule_active', true );

        return array(
            'id' => $rule_id,
            'title' => $rule->post_title,
            'schema_ids' => $schema_ids,
            'conditions' => $conditions,
            'priority' => intval( $priority ),
            'active' => (bool) $active,
            'date_created' => $rule->post_date,
            'date_modified' => $rule->post_modified
        );
    }

    /**
     * Récupère toutes les règles.
     *
     * @since    1.2.0
     * @param    array    $args    Arguments de requête.
     * @return   array             Les règles.
     */
    public function get_rules( $args = array() ) {
        $defaults = array(
            'schema_id' => '',
            'active' => null,
            'orderby' => 'meta_value_num',
            'meta_key' => '_rule_priority',
            'order' => 'ASC',
            'limit' => -1,
            'offset' => 0,
        );

        $args = wp_parse_args( $args, $defaults );

        // Construire les arguments de la requête
        $query_args = array(
            'post_type' => 'boss_schema_rule',
            'post_status' => 'publish',
            'posts_per_page' => $args['limit'],
            'offset' => $args['offset'],
            'orderby' => $args['orderby'],
            'meta_key' => $args['meta_key'],
            'order' => $args['order'],
            'meta_query' => array()
        );

        // Filtrer par schéma
        if ( ! empty( $args['schema_id'] ) ) {
            $query_args['meta_query'][] = array(
                'key' => '_rule_schema_ids',
                'value' => sprintf( '"%s"', $args['schema_id'] ),
                'compare' => 'LIKE'
            );
        }

        // Filtrer par statut actif
        if ( $args['active'] !== null ) {
            $query_args['meta_query'][] = array(
                'key' => '_rule_active',
                'value' => $args['active'] ? '1' : '0',
                'compare' => '='
            );
        }

        // Exécuter la requête
        $query = new WP_Query( $query_args );
        $rules = array();

        foreach ( $query->posts as $post ) {
            $rules[] = $this->get_rule( $post->ID );
        }

        return $rules;
    }

    /**
     * Vérifie si un schéma est applicable à un post selon les règles.
     *
     * @since    1.2.0
     * @param    int       $schema_id    L'ID du schéma.
     * @param    WP_Post   $post         Le post.
     * @return   bool                    True si le schéma est applicable, false sinon.
     */
    public function is_schema_applicable( $schema_id, $post ) {
        // Récupérer toutes les règles actives pour ce schéma
        $rules = $this->get_rules( array(
            'schema_id' => $schema_id,
            'active' => true
        ) );

        if ( empty( $rules ) ) {
            // Si aucune règle n'est définie, le schéma n'est pas applicable
            return false;
        }

        foreach ( $rules as $rule ) {
            if ( $this->check_conditions( $rule['conditions'], $post ) ) {
                return true;
            }
        }

        return false;
    }

    /**
     * Vérifie si un post correspond aux conditions d'une règle.
     *
     * @since    1.2.0
     * @param    array     $conditions    Les conditions à vérifier.
     * @param    WP_Post   $post          Le post.
     * @return   bool                     True si le post correspond aux conditions, false sinon.
     */
    private function check_conditions( $conditions, $post ) {
        if ( empty( $conditions ) ) {
            return false;
        }

        $match = true;

        foreach ( $conditions as $condition ) {
            $type = isset( $condition['type'] ) ? $condition['type'] : '';
            $operator = isset( $condition['operator'] ) ? $condition['operator'] : 'is';
            $value = isset( $condition['value'] ) ? $condition['value'] : '';

            switch ( $type ) {
                case 'post_type':
                    $post_type = get_post_type( $post );
                    $match = $this->compare_values( $post_type, $value, $operator );
                    break;

                case 'post_id':
                    $match = $this->compare_values( $post->ID, $value, $operator );
                    break;

                case 'taxonomy':
                    $taxonomy = isset( $condition['taxonomy'] ) ? $condition['taxonomy'] : '';
                    $terms = get_the_terms( $post->ID, $taxonomy );

                    if ( is_wp_error( $terms ) || empty( $terms ) ) {
                        $match = $operator === 'is_not';
                    } else {
                        $term_ids = wp_list_pluck( $terms, 'term_id' );
                        $match = $this->compare_values( $term_ids, $value, $operator );
                    }
                    break;

                case 'author':
                    $match = $this->compare_values( $post->post_author, $value, $operator );
                    break;

                case 'post_status':
                    $match = $this->compare_values( $post->post_status, $value, $operator );
                    break;

                case 'page_template':
                    $template = get_page_template_slug( $post->ID );
                    $match = $this->compare_values( $template, $value, $operator );
                    break;
            }

            if ( ! $match ) {
                return false;
            }
        }

        return $match;
    }

    /**
     * Compare des valeurs selon un opérateur.
     *
     * @since    1.2.0
     * @param    mixed     $actual      La valeur actuelle.
     * @param    mixed     $expected    La valeur attendue.
     * @param    string    $operator    L'opérateur de comparaison.
     * @return   bool                   Le résultat de la comparaison.
     */
    private function compare_values( $actual, $expected, $operator ) {
        switch ( $operator ) {
            case 'is':
                if ( is_array( $actual ) ) {
                    return in_array( $expected, $actual );
                }
                return $actual == $expected;

            case 'is_not':
                if ( is_array( $actual ) ) {
                    return ! in_array( $expected, $actual );
                }
                return $actual != $expected;

            case 'contains':
                if ( is_array( $actual ) ) {
                    foreach ( $actual as $value ) {
                        if ( strpos( $value, $expected ) !== false ) {
                            return true;
                        }
                    }
                    return false;
                }
                return strpos( $actual, $expected ) !== false;

            case 'not_contains':
                if ( is_array( $actual ) ) {
                    foreach ( $actual as $value ) {
                        if ( strpos( $value, $expected ) !== false ) {
                            return false;
                        }
                    }
                    return true;
                }
                return strpos( $actual, $expected ) === false;

            default:
                return false;
        }
    }

    /**
     * Sanitize les IDs de schémas.
     *
     * @since    1.2.0
     * @param    array    $schema_ids    Les IDs de schémas.
     * @return   array                   Les IDs de schémas sanitizés.
     */
    private function sanitize_schema_ids( $schema_ids ) {
        if ( ! is_array( $schema_ids ) ) {
            return array();
        }

        return array_map( 'intval', $schema_ids );
    }

    /**
     * Sanitize les conditions.
     *
     * @since    1.2.0
     * @param    array    $conditions    Les conditions.
     * @return   array                   Les conditions sanitizées.
     */
    private function sanitize_conditions( $conditions ) {
        if ( ! is_array( $conditions ) ) {
            return array();
        }

        $sanitized = array();

        foreach ( $conditions as $condition ) {
            $sanitized_condition = array();

            if ( isset( $condition['type'] ) ) {
                $sanitized_condition['type'] = sanitize_text_field( $condition['type'] );
            }

            if ( isset( $condition['operator'] ) ) {
                $sanitized_condition['operator'] = sanitize_text_field( $condition['operator'] );
            }

            if ( isset( $condition['value'] ) ) {
                if ( is_array( $condition['value'] ) ) {
                    $sanitized_condition['value'] = array_map( 'sanitize_text_field', $condition['value'] );
                } else {
                    $sanitized_condition['value'] = sanitize_text_field( $condition['value'] );
                }
            }

            if ( isset( $condition['taxonomy'] ) ) {
                $sanitized_condition['taxonomy'] = sanitize_text_field( $condition['taxonomy'] );
            }

            $sanitized[] = $sanitized_condition;
        }

        return $sanitized;
    }
}
