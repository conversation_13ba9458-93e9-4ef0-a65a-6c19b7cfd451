<?php
/**
 * Classe pour gérer les paramètres des services externes.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/settings
 */

/**
 * Classe pour gérer les paramètres des services externes.
 *
 * Cette classe gère les paramètres des services externes comme Google My Business.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/settings
 * <AUTHOR> SEO Team
 */
class Boss_External_Services {

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_external_services_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     */
    public function __construct() {
        // Charger les dépendances
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'api/class-boss-seo-gmb-api.php';
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_external_services', array( $this, 'ajax_get_external_services' ) );
        add_action( 'wp_ajax_boss_seo_save_external_services', array( $this, 'ajax_save_external_services' ) );
        add_action( 'wp_ajax_boss_seo_connect_gmb', array( $this, 'ajax_connect_gmb' ) );
        add_action( 'wp_ajax_boss_seo_disconnect_gmb', array( $this, 'ajax_disconnect_gmb' ) );
        
        // Ajouter les actions pour la gestion des redirections OAuth
        add_action( 'admin_init', array( $this, 'handle_gmb_auth_callback' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/external-services/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/external-services/gmb/auth-url',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_gmb_auth_url' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/external-services/gmb/disconnect',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'disconnect_gmb' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/external-services/gmb/accounts',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_gmb_accounts' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/external-services/gmb/locations',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_gmb_locations' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les paramètres des services externes.
     *
     * @since    1.2.0
     */
    public function ajax_get_external_services() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $settings = $this->get_settings_data();

        wp_send_json_success( array(
            'message'  => __( 'Paramètres récupérés avec succès.', 'boss-seo' ),
            'settings' => $settings,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour enregistrer les paramètres des services externes.
     *
     * @since    1.2.0
     */
    public function ajax_save_external_services() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['settings'] ) || ! is_array( $_POST['settings'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Paramètres invalides.', 'boss-seo' ) ) );
        }

        $settings = $_POST['settings'];

        // Enregistrer les paramètres
        $result = $this->save_settings_data( $settings );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'Paramètres enregistrés avec succès.', 'boss-seo' ),
            'settings' => $this->get_settings_data(),
        ) );
    }

    /**
     * Gère les requêtes AJAX pour connecter Google My Business.
     *
     * @since    1.2.0
     */
    public function ajax_connect_gmb() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Initialiser l'API GMB
        $gmb_api = new Boss_SEO_GMB_API();

        // Vérifier si l'API est configurée
        if ( ! $gmb_api->is_configured() ) {
            wp_send_json_error( array( 'message' => __( 'L\'API Google My Business n\'est pas configurée.', 'boss-seo' ) ) );
        }

        // Générer l'URL d'authentification
        $auth_url = $gmb_api->get_auth_url();

        if ( empty( $auth_url ) ) {
            wp_send_json_error( array( 'message' => __( 'Impossible de générer l\'URL d\'authentification.', 'boss-seo' ) ) );
        }

        wp_send_json_success( array(
            'message'  => __( 'URL d\'authentification générée avec succès.', 'boss-seo' ),
            'auth_url' => $auth_url,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour déconnecter Google My Business.
     *
     * @since    1.2.0
     */
    public function ajax_disconnect_gmb() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Initialiser l'API GMB
        $gmb_api = new Boss_SEO_GMB_API();

        // Déconnecter l'API
        $result = $gmb_api->disconnect();

        if ( ! $result ) {
            wp_send_json_error( array( 'message' => __( 'Erreur lors de la déconnexion.', 'boss-seo' ) ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Déconnexion réussie.', 'boss-seo' ),
        ) );
    }

    /**
     * Gère le callback d'authentification OAuth pour Google My Business.
     *
     * @since    1.2.0
     */
    public function handle_gmb_auth_callback() {
        // Vérifier si c'est un callback d'authentification GMB
        if ( ! isset( $_GET['page'] ) || $_GET['page'] !== 'boss-seo-settings' || ! isset( $_GET['tab'] ) || $_GET['tab'] !== 'external-services' || ! isset( $_GET['gmb_auth'] ) || ! isset( $_GET['code'] ) ) {
            return;
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) );
        }

        // Récupérer le code d'autorisation
        $code = sanitize_text_field( $_GET['code'] );

        // Initialiser l'API GMB
        $gmb_api = new Boss_SEO_GMB_API();

        // Échanger le code contre des tokens
        $result = $gmb_api->exchange_code_for_tokens( $code );

        // Rediriger vers la page des paramètres
        $redirect_url = admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' );

        if ( is_wp_error( $result ) ) {
            $redirect_url = add_query_arg( 'gmb_error', urlencode( $result->get_error_message() ), $redirect_url );
        } else {
            $redirect_url = add_query_arg( 'gmb_success', '1', $redirect_url );
        }

        wp_redirect( $redirect_url );
        exit;
    }

    /**
     * Récupère les paramètres des services externes via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_settings( $request ) {
        $settings = $this->get_settings_data();

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres des services externes via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_settings( $request ) {
        $settings = $request->get_param( 'settings' );

        if ( empty( $settings ) ) {
            return new WP_Error( 'missing_settings', __( 'Paramètres manquants.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $result = $this->save_settings_data( $settings );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( $this->get_settings_data() );
    }

    /**
     * Récupère l'URL d'authentification Google My Business via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_gmb_auth_url( $request ) {
        // Initialiser l'API GMB
        $gmb_api = new Boss_SEO_GMB_API();

        // Vérifier si l'API est configurée
        if ( ! $gmb_api->is_configured() ) {
            return new WP_Error( 'gmb_not_configured', __( 'L\'API Google My Business n\'est pas configurée.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Générer l'URL d'authentification
        $auth_url = $gmb_api->get_auth_url();

        if ( empty( $auth_url ) ) {
            return new WP_Error( 'gmb_auth_url_error', __( 'Impossible de générer l\'URL d\'authentification.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array( 'auth_url' => $auth_url ) );
    }

    /**
     * Déconnecte Google My Business via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function disconnect_gmb( $request ) {
        // Initialiser l'API GMB
        $gmb_api = new Boss_SEO_GMB_API();

        // Déconnecter l'API
        $result = $gmb_api->disconnect();

        if ( ! $result ) {
            return new WP_Error( 'gmb_disconnect_error', __( 'Erreur lors de la déconnexion.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array( 'success' => true ) );
    }

    /**
     * Récupère les comptes Google My Business via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_gmb_accounts( $request ) {
        // Initialiser l'API GMB
        $gmb_api = new Boss_SEO_GMB_API();

        // Vérifier si l'API est connectée
        if ( ! $gmb_api->is_connected() ) {
            return new WP_Error( 'gmb_not_connected', __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les comptes
        $accounts = $gmb_api->get_accounts();

        if ( is_wp_error( $accounts ) ) {
            return $accounts;
        }

        return rest_ensure_response( $accounts );
    }

    /**
     * Récupère les établissements Google My Business via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_gmb_locations( $request ) {
        // Récupérer l'ID du compte
        $account_id = $request->get_param( 'account_id' );

        if ( empty( $account_id ) ) {
            return new WP_Error( 'missing_account_id', __( 'ID de compte manquant.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Initialiser l'API GMB
        $gmb_api = new Boss_SEO_GMB_API();

        // Vérifier si l'API est connectée
        if ( ! $gmb_api->is_connected() ) {
            return new WP_Error( 'gmb_not_connected', __( 'L\'API Google My Business n\'est pas connectée.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les établissements
        $locations = $gmb_api->get_locations( $account_id );

        if ( is_wp_error( $locations ) ) {
            return $locations;
        }

        return rest_ensure_response( $locations );
    }

    /**
     * Récupère les paramètres des services externes.
     *
     * @since    1.2.0
     * @return   array    Les paramètres des services externes.
     */
    private function get_settings_data() {
        // Récupérer les paramètres
        $settings = get_option( $this->option_prefix . 'settings', array() );

        // Valeurs par défaut
        $default_settings = array(
            'google_my_business' => array(
                'enabled'       => false,
                'client_id'     => '',
                'client_secret' => '',
                'redirect_uri'  => admin_url( 'admin.php?page=boss-seo-settings&tab=external-services&gmb_auth=1' ),
                'connected'     => false,
            ),
        );

        // Fusionner les paramètres avec les valeurs par défaut
        $settings = wp_parse_args( $settings, $default_settings );

        // Vérifier si GMB est connecté
        $gmb_api = new Boss_SEO_GMB_API();
        $settings['google_my_business']['connected'] = $gmb_api->is_connected();

        return $settings;
    }

    /**
     * Enregistre les paramètres des services externes.
     *
     * @since    1.2.0
     * @param    array    $settings    Les paramètres à enregistrer.
     * @return   bool|WP_Error         True en cas de succès, WP_Error en cas d'erreur.
     */
    private function save_settings_data( $settings ) {
        // Récupérer les paramètres actuels
        $current_settings = get_option( $this->option_prefix . 'settings', array() );

        // Fusionner les paramètres
        $new_settings = wp_parse_args( $settings, $current_settings );

        // Sanitize les paramètres
        $sanitized_settings = $this->sanitize_settings( $new_settings );

        // Enregistrer les paramètres
        $result = update_option( $this->option_prefix . 'settings', $sanitized_settings );

        if ( ! $result ) {
            return new WP_Error( 'settings_save_error', __( 'Erreur lors de l\'enregistrement des paramètres.', 'boss-seo' ) );
        }

        return true;
    }

    /**
     * Sanitize les paramètres des services externes.
     *
     * @since    1.2.0
     * @param    array    $settings    Les paramètres à sanitize.
     * @return   array                 Les paramètres sanitized.
     */
    private function sanitize_settings( $settings ) {
        $sanitized = array();

        if ( isset( $settings['google_my_business'] ) ) {
            $sanitized['google_my_business'] = array(
                'enabled'       => isset( $settings['google_my_business']['enabled'] ) ? (bool) $settings['google_my_business']['enabled'] : false,
                'client_id'     => isset( $settings['google_my_business']['client_id'] ) ? sanitize_text_field( $settings['google_my_business']['client_id'] ) : '',
                'client_secret' => isset( $settings['google_my_business']['client_secret'] ) ? sanitize_text_field( $settings['google_my_business']['client_secret'] ) : '',
                'redirect_uri'  => isset( $settings['google_my_business']['redirect_uri'] ) ? esc_url_raw( $settings['google_my_business']['redirect_uri'] ) : admin_url( 'admin.php?page=boss-seo-settings&tab=external-services&gmb_auth=1' ),
            );
        }

        return $sanitized;
    }
}
