import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  TextControl,
  SelectControl,
  ToggleControl,
  Panel,
  PanelBody,
  PanelRow,
  Notice
} from '@wordpress/components';
import SchemaPreview from './SchemaPreview';
import SchemaPropertyField from './SchemaPropertyField';

const SchemaBuilder = ({ schema, schemaTypes, onSave, onCancel }) => {
  // États
  const [currentSchema, setCurrentSchema] = useState(schema || {
    id: null,
    name: '',
    type: '',
    active: true,
    validated: false,
    usage: 0,
    lastUpdated: new Date().toISOString().split('T')[0],
    properties: {},
    rules: {
      postTypes: [],
      taxonomies: []
    }
  });
  
  const [schemaStructure, setSchemaStructure] = useState({});
  const [validationErrors, setValidationErrors] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState('visual'); // 'visual' ou 'code'
  const [availableVariables, setAvailableVariables] = useState([]);

  // Effet pour charger la structure du schéma en fonction du type
  useEffect(() => {
    if (currentSchema.type) {
      setIsLoading(true);
      
      // Simuler le chargement de la structure du schéma
      setTimeout(() => {
        // Structures fictives pour différents types de schémas
        const structures = {
          article: {
            '@type': 'Article',
            headline: { type: 'string', required: true },
            description: { type: 'string', required: false },
            image: { type: 'url', required: false },
            author: { 
              '@type': 'Person',
              name: { type: 'string', required: true }
            },
            publisher: {
              '@type': 'Organization',
              name: { type: 'string', required: true },
              logo: { type: 'url', required: false }
            },
            datePublished: { type: 'date', required: true },
            dateModified: { type: 'date', required: false }
          },
          product: {
            '@type': 'Product',
            name: { type: 'string', required: true },
            description: { type: 'string', required: false },
            image: { type: 'url', required: false },
            brand: {
              '@type': 'Brand',
              name: { type: 'string', required: true }
            },
            offers: {
              '@type': 'Offer',
              price: { type: 'number', required: true },
              priceCurrency: { type: 'string', required: true },
              availability: { type: 'string', required: false },
              url: { type: 'url', required: false }
            },
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: { type: 'number', required: false },
              reviewCount: { type: 'number', required: false }
            }
          },
          'local-business': {
            '@type': 'LocalBusiness',
            name: { type: 'string', required: true },
            description: { type: 'string', required: false },
            image: { type: 'url', required: false },
            address: {
              '@type': 'PostalAddress',
              streetAddress: { type: 'string', required: true },
              addressLocality: { type: 'string', required: true },
              addressRegion: { type: 'string', required: false },
              postalCode: { type: 'string', required: true },
              addressCountry: { type: 'string', required: true }
            },
            telephone: { type: 'string', required: false },
            openingHours: { type: 'string', required: false }
          },
          faq: {
            '@type': 'FAQPage',
            mainEntity: {
              '@type': 'Question',
              name: { type: 'string', required: true },
              acceptedAnswer: {
                '@type': 'Answer',
                text: { type: 'string', required: true }
              }
            }
          },
          event: {
            '@type': 'Event',
            name: { type: 'string', required: true },
            description: { type: 'string', required: false },
            startDate: { type: 'datetime', required: true },
            endDate: { type: 'datetime', required: false },
            location: {
              '@type': 'Place',
              name: { type: 'string', required: true },
              address: {
                '@type': 'PostalAddress',
                streetAddress: { type: 'string', required: true },
                addressLocality: { type: 'string', required: true },
                addressRegion: { type: 'string', required: false },
                postalCode: { type: 'string', required: true },
                addressCountry: { type: 'string', required: true }
              }
            },
            organizer: {
              '@type': 'Organization',
              name: { type: 'string', required: true },
              url: { type: 'url', required: false }
            }
          }
        };
        
        // Variables dynamiques disponibles
        const variables = [
          { id: 'post_title', label: __('Titre de la page', 'boss-seo'), example: 'Mon article de blog' },
          { id: 'post_content', label: __('Contenu de la page', 'boss-seo'), example: 'Contenu de l\'article...' },
          { id: 'post_excerpt', label: __('Extrait', 'boss-seo'), example: 'Résumé de l\'article...' },
          { id: 'post_date', label: __('Date de publication', 'boss-seo'), example: '2023-06-15' },
          { id: 'post_modified', label: __('Date de modification', 'boss-seo'), example: '2023-06-20' },
          { id: 'post_author', label: __('Auteur', 'boss-seo'), example: 'John Doe' },
          { id: 'site_name', label: __('Nom du site', 'boss-seo'), example: 'Mon Site Web' },
          { id: 'site_url', label: __('URL du site', 'boss-seo'), example: 'https://monsite.com' },
          { id: 'featured_image', label: __('Image à la une', 'boss-seo'), example: 'https://monsite.com/image.jpg' },
          { id: 'post_permalink', label: __('URL permanente', 'boss-seo'), example: 'https://monsite.com/article' }
        ];
        
        setSchemaStructure(structures[currentSchema.type] || {});
        setAvailableVariables(variables);
        
        // Initialiser les propriétés si elles n'existent pas
        if (!currentSchema.properties) {
          setCurrentSchema({
            ...currentSchema,
            properties: {}
          });
        }
        
        setIsLoading(false);
      }, 500);
    }
  }, [currentSchema.type]);

  // Fonction pour mettre à jour une propriété du schéma
  const updateSchemaProperty = (path, value) => {
    const newSchema = { ...currentSchema };
    
    // Diviser le chemin en segments
    const segments = path.split('.');
    let current = newSchema.properties;
    
    // Naviguer jusqu'au parent de la propriété à mettre à jour
    for (let i = 0; i < segments.length - 1; i++) {
      if (!current[segments[i]]) {
        current[segments[i]] = {};
      }
      current = current[segments[i]];
    }
    
    // Mettre à jour la propriété
    current[segments[segments.length - 1]] = value;
    
    setCurrentSchema(newSchema);
  };

  // Fonction pour valider le schéma
  const validateSchema = () => {
    const errors = [];
    
    // Vérifier le nom du schéma
    if (!currentSchema.name) {
      errors.push(__('Le nom du schéma est requis', 'boss-seo'));
    }
    
    // Vérifier les propriétés requises
    const checkRequiredProperties = (structure, properties, path = '') => {
      Object.entries(structure).forEach(([key, value]) => {
        if (key === '@type') return;
        
        const currentPath = path ? `${path}.${key}` : key;
        
        if (typeof value === 'object' && !value.type) {
          // C'est un objet imbriqué
          checkRequiredProperties(value, properties[key] || {}, currentPath);
        } else if (value.required && (!properties || !properties[key])) {
          errors.push(__(`La propriété "${currentPath}" est requise`, 'boss-seo'));
        }
      });
    };
    
    checkRequiredProperties(schemaStructure, currentSchema.properties || {});
    
    setValidationErrors(errors);
    return errors.length === 0;
  };

  // Fonction pour sauvegarder le schéma
  const handleSave = () => {
    if (validateSchema()) {
      onSave(currentSchema);
    }
  };

  // Fonction pour générer le code JSON-LD
  const generateJsonLd = () => {
    const jsonLd = {
      '@context': 'https://schema.org',
      '@type': schemaStructure['@type']
    };
    
    // Fonction récursive pour construire l'objet JSON-LD
    const buildJsonLd = (structure, properties, target) => {
      Object.entries(structure).forEach(([key, value]) => {
        if (key === '@type') {
          target['@type'] = value;
        } else if (typeof value === 'object' && !value.type) {
          // C'est un objet imbriqué
          target[key] = { '@type': value['@type'] };
          buildJsonLd(value, properties[key] || {}, target[key]);
        } else if (properties && properties[key]) {
          // C'est une propriété simple
          target[key] = properties[key];
        }
      });
    };
    
    buildJsonLd(schemaStructure, currentSchema.properties || {}, jsonLd);
    
    return JSON.stringify(jsonLd, null, 2);
  };

  // Obtenir les détails du type de schéma
  const schemaTypeDetails = schemaTypes.find(type => type.id === currentSchema.type) || {
    name: currentSchema.type,
    icon: 'admin-generic',
    color: 'boss-gray'
  };

  return (
    <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
      {/* Panneau d'édition */}
      <div className="lg:boss-col-span-2">
        <Card className="boss-mb-6">
          <CardHeader className="boss-border-b boss-border-gray-200">
            <div className="boss-flex boss-justify-between boss-items-center">
              <div className="boss-flex boss-items-center">
                <div className={`boss-bg-${schemaTypeDetails.color}/10 boss-p-2 boss-rounded-lg boss-mr-3`}>
                  <Dashicon icon={schemaTypeDetails.icon} className={`boss-text-${schemaTypeDetails.color} boss-text-xl`} />
                </div>
                <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                  {currentSchema.id ? 
                    __('Modifier le schéma', 'boss-seo') : 
                    __('Nouveau schéma', 'boss-seo')} - {schemaTypeDetails.name}
                </h2>
              </div>
              <div className="boss-flex boss-space-x-2">
                <Button
                  isSecondary
                  onClick={onCancel}
                >
                  {__('Annuler', 'boss-seo')}
                </Button>
                <Button
                  isPrimary
                  onClick={handleSave}
                >
                  {__('Enregistrer', 'boss-seo')}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardBody>
            {isLoading ? (
              <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
                <Dashicon icon="update" className="boss-animate-spin boss-text-boss-primary boss-text-2xl" />
              </div>
            ) : (
              <div>
                {validationErrors.length > 0 && (
                  <Notice status="error" isDismissible={false} className="boss-mb-4">
                    <ul className="boss-list-disc boss-pl-4">
                      {validationErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </Notice>
                )}
                
                <TextControl
                  label={__('Nom du schéma', 'boss-seo')}
                  value={currentSchema.name}
                  onChange={(value) => setCurrentSchema({ ...currentSchema, name: value })}
                  className="boss-mb-4"
                />
                
                <ToggleControl
                  label={__('Activer ce schéma', 'boss-seo')}
                  checked={currentSchema.active}
                  onChange={(value) => setCurrentSchema({ ...currentSchema, active: value })}
                  className="boss-mb-4"
                />
                
                <div className="boss-mb-6">
                  <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                    {__('Propriétés du schéma', 'boss-seo')}
                  </h3>
                  
                  <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-bg-gray-50">
                    <SchemaPropertyField
                      structure={schemaStructure}
                      properties={currentSchema.properties || {}}
                      onChange={updateSchemaProperty}
                      variables={availableVariables}
                      path=""
                    />
                  </div>
                </div>
              </div>
            )}
          </CardBody>
        </Card>
      </div>
      
      {/* Panneau de prévisualisation */}
      <div>
        <Card>
          <CardHeader className="boss-border-b boss-border-gray-200">
            <div className="boss-flex boss-justify-between boss-items-center">
              <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                {__('Prévisualisation', 'boss-seo')}
              </h2>
              <div className="boss-flex boss-space-x-2">
                <Button
                  isSmall
                  isPrimary={previewMode === 'visual'}
                  isSecondary={previewMode !== 'visual'}
                  onClick={() => setPreviewMode('visual')}
                >
                  {__('Visuel', 'boss-seo')}
                </Button>
                <Button
                  isSmall
                  isPrimary={previewMode === 'code'}
                  isSecondary={previewMode !== 'code'}
                  onClick={() => setPreviewMode('code')}
                >
                  {__('Code', 'boss-seo')}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardBody>
            {isLoading ? (
              <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
                <Dashicon icon="update" className="boss-animate-spin boss-text-boss-primary boss-text-2xl" />
              </div>
            ) : (
              <SchemaPreview
                schema={currentSchema}
                schemaStructure={schemaStructure}
                mode={previewMode}
                jsonLd={generateJsonLd()}
              />
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default SchemaBuilder;
