import { createContext, useState, useContext, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import { Button, Modal } from '@wordpress/components';
import TourGuide from './TourGuide';

// Créer le contexte d'aide
const HelpContext = createContext();

/**
 * Fournisseur de contexte d'aide
 * 
 * @param {Object} props - Propriétés du composant
 * @param {React.ReactNode} props.children - Contenu enfant
 * @returns {React.ReactElement} Composant HelpProvider
 */
export const HelpProvider = ({ children }) => {
  // États
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [helpContent, setHelpContent] = useState(null);
  const [activeTour, setActiveTour] = useState(null);
  const [showTour, setShowTour] = useState(false);
  const [helpPreferences, setHelpPreferences] = useState({
    showTooltips: true,
    showTourOnFirstVisit: true,
    showHelpButtons: true
  });
  
  // Charger les préférences d'aide depuis le localStorage
  useEffect(() => {
    const savedPreferences = localStorage.getItem('boss_help_preferences');
    if (savedPreferences) {
      try {
        setHelpPreferences(JSON.parse(savedPreferences));
      } catch (e) {
        console.error('Erreur lors du chargement des préférences d\'aide', e);
      }
    }
  }, []);
  
  // Sauvegarder les préférences d'aide dans le localStorage
  useEffect(() => {
    localStorage.setItem('boss_help_preferences', JSON.stringify(helpPreferences));
  }, [helpPreferences]);
  
  // Ouvrir la modal d'aide
  const openHelp = (content) => {
    setHelpContent(content);
    setShowHelpModal(true);
  };
  
  // Fermer la modal d'aide
  const closeHelp = () => {
    setShowHelpModal(false);
  };
  
  // Démarrer un tour guidé
  const startTour = (tour) => {
    setActiveTour(tour);
    setShowTour(true);
  };
  
  // Terminer un tour guidé
  const endTour = () => {
    setShowTour(false);
    setActiveTour(null);
  };
  
  // Mettre à jour les préférences d'aide
  const updateHelpPreferences = (preferences) => {
    setHelpPreferences({
      ...helpPreferences,
      ...preferences
    });
  };
  
  // Valeur du contexte
  const contextValue = {
    openHelp,
    closeHelp,
    startTour,
    endTour,
    helpPreferences,
    updateHelpPreferences
  };
  
  return (
    <HelpContext.Provider value={contextValue}>
      {children}
      
      {/* Modal d'aide */}
      {showHelpModal && helpContent && (
        <Modal
          title={helpContent.title || __('Aide', 'boss-seo')}
          onRequestClose={closeHelp}
          className="boss-help-modal"
        >
          <div className="boss-p-6">
            <div className="boss-prose boss-max-w-none boss-mb-6">
              {typeof helpContent.content === 'string' ? (
                <div dangerouslySetInnerHTML={{ __html: helpContent.content }} />
              ) : (
                helpContent.content
              )}
            </div>
            
            {helpContent.links && helpContent.links.length > 0 && (
              <div className="boss-mt-4 boss-pt-4 boss-border-t boss-border-gray-200">
                <h3 className="boss-text-md boss-font-medium boss-text-boss-dark boss-mb-2">
                  {__('Liens utiles', 'boss-seo')}
                </h3>
                <ul className="boss-space-y-2">
                  {helpContent.links.map((link, index) => (
                    <li key={index}>
                      <a 
                        href={link.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="boss-text-blue-600 boss-hover:boss-text-blue-800 boss-hover:boss-underline"
                      >
                        {link.text}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            <div className="boss-flex boss-justify-between boss-mt-6">
              <Button
                isSecondary
                onClick={closeHelp}
              >
                {__('Fermer', 'boss-seo')}
              </Button>
              
              {helpContent.docUrl && (
                <Button
                  isPrimary
                  href={helpContent.docUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {__('Documentation complète', 'boss-seo')}
                </Button>
              )}
            </div>
          </div>
        </Modal>
      )}
      
      {/* Tour guidé */}
      {showTour && activeTour && (
        <TourGuide
          steps={activeTour.steps}
          isOpen={showTour}
          onClose={endTour}
          onComplete={endTour}
          tourId={activeTour.id || 'default-tour'}
        />
      )}
    </HelpContext.Provider>
  );
};

/**
 * Hook pour utiliser le contexte d'aide
 * 
 * @returns {Object} Contexte d'aide
 */
export const useHelp = () => {
  const context = useContext(HelpContext);
  if (!context) {
    throw new Error('useHelp doit être utilisé à l\'intérieur d\'un HelpProvider');
  }
  return context;
};

/**
 * Bouton d'aide contextuelle
 * 
 * @param {Object} props - Propriétés du composant
 * @param {string} props.title - Titre de l'aide
 * @param {string|React.ReactNode} props.content - Contenu de l'aide
 * @param {Array} props.links - Liens utiles
 * @param {string} props.docUrl - URL de la documentation complète
 * @param {boolean} props.showAsIcon - Afficher comme une icône
 * @param {string} props.className - Classes CSS additionnelles
 * @returns {React.ReactElement} Composant HelpButton
 */
export const HelpButton = ({ 
  title, 
  content, 
  links, 
  docUrl, 
  showAsIcon = true,
  className = ''
}) => {
  const { openHelp, helpPreferences } = useHelp();
  
  // Si les boutons d'aide sont désactivés, ne rien afficher
  if (!helpPreferences.showHelpButtons) {
    return null;
  }
  
  const handleClick = () => {
    openHelp({ title, content, links, docUrl });
  };
  
  return showAsIcon ? (
    <button
      className={`boss-text-boss-blue-500 boss-hover:boss-text-boss-blue-700 focus:boss-outline-none ${className}`}
      onClick={handleClick}
      aria-label={__('Aide', 'boss-seo')}
    >
      <span className="dashicons dashicons-editor-help"></span>
    </button>
  ) : (
    <Button
      isSecondary
      onClick={handleClick}
      className={className}
    >
      <span className="dashicons dashicons-editor-help boss-mr-1"></span>
      {__('Aide', 'boss-seo')}
    </Button>
  );
};

export default HelpContext;
