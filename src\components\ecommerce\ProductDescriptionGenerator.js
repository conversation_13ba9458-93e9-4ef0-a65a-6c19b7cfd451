import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  SelectControl,
  TextControl,
  TextareaControl,
  RangeControl,
  ToggleControl,
  Modal,
  Notice,
  Spinner,
  TabPanel
} from '@wordpress/components';

// Importer le service
import EcommerceService from '../../services/EcommerceService';

const ProductDescriptionGenerator = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [generatedContent, setGeneratedContent] = useState({
    title: '',
    shortDescription: '',
    longDescription: '',
    metaDescription: '',
    keywords: []
  });
  const [generationSettings, setGenerationSettings] = useState({
    tone: 'professional',
    length: 'medium',
    includeKeywords: true,
    includeBenefits: true,
    includeFeatures: true,
    includeSpecs: true,
    optimizeForSEO: true,
    creativityLevel: 50
  });

  // Créer une instance du service
  const ecommerceService = new EcommerceService();

  // Charger les données
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Récupérer les produits
        const productsResponse = await ecommerceService.getProducts();

        // Extraire les catégories uniques des produits
        const uniqueCategories = [];
        const categoryIds = new Set();

        productsResponse.products.forEach(product => {
          if (product.categoryId && !categoryIds.has(product.categoryId)) {
            categoryIds.add(product.categoryId);
            uniqueCategories.push({
              id: product.categoryId,
              name: product.category
            });
          }
        });

        // Trier les catégories par nom
        uniqueCategories.sort((a, b) => a.name.localeCompare(b.name));

        // Mettre à jour les états
        setCategories(uniqueCategories);
        setProducts(productsResponse.products || []);
      } catch (err) {
        console.error('Erreur lors du chargement des produits:', err);

        // Vérifier si c'est une erreur spécifique de WooCommerce
        if (err && err.code) {
          if (err.code === 'woocommerce_not_available') {
            // WooCommerce n'est pas installé ou activé
            setError(__('WooCommerce n\'est pas installé ou activé. Veuillez installer et activer WooCommerce pour utiliser cette fonctionnalité.', 'boss-seo'));
          } else if (err.code === 'no_products_available') {
            // Aucun produit n'est disponible
            setError(__('Aucun produit n\'est disponible dans WooCommerce. Veuillez ajouter des produits pour utiliser cette fonctionnalité.', 'boss-seo'));
          } else {
            // Autre erreur
            setError(err.message || __('Erreur lors du chargement des produits. Veuillez réessayer.', 'boss-seo'));
          }
        } else {
          // Erreur générique
          setError(__('Erreur lors du chargement des produits. Veuillez réessayer.', 'boss-seo'));
        }

        // Réinitialiser les états
        setCategories([]);
        setProducts([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Fonction pour filtrer les produits
  const getFilteredProducts = () => {
    return products.filter(product => {
      // Filtrer par catégorie
      const matchesCategory = selectedCategory === 'all' || product.categoryId === parseInt(selectedCategory);

      // Filtrer par recherche
      const matchesSearch = searchQuery === '' ||
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.category.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesCategory && matchesSearch;
    });
  };

  // Obtenir les produits filtrés
  const filteredProducts = getFilteredProducts();

  // Fonction pour générer du contenu avec l'IA
  const generateContent = async () => {
    if (!selectedProduct) return;

    try {
      setIsGenerating(true);
      setError(null);

      // Appeler le service pour générer la description du produit
      const response = await ecommerceService.generateProductDescription(selectedProduct.id, generationSettings);

      // Mettre à jour l'état avec le contenu généré
      setGeneratedContent({
        title: response.title || '',
        shortDescription: response.shortDescription || '',
        longDescription: response.longDescription || '',
        metaDescription: response.metaDescription || '',
        keywords: response.keywords || []
      });
    } catch (err) {
      console.error('Erreur lors de la génération du contenu:', err);
      setError(__('Erreur lors de la génération du contenu. Veuillez réessayer.', 'boss-seo'));

      // Générer du contenu fictif en cas d'erreur (pour le développement)
      // À supprimer en production
      const tones = {
        professional: 'professionnel et informatif',
        casual: 'décontracté et amical',
        enthusiastic: 'enthousiaste et énergique',
        luxury: 'luxueux et exclusif',
        technical: 'technique et détaillé'
      };

      const lengths = {
        short: 'court',
        medium: 'moyen',
        long: 'long'
      };

      const tone = tones[generationSettings.tone];
      const length = lengths[generationSettings.length];

      // Titre optimisé
      const title = `${selectedProduct.name} - ${generationSettings.optimizeForSEO ? 'Optimisé pour le SEO' : 'Version standard'}`;

      // Description courte
      let shortDescription = `Découvrez notre ${selectedProduct.name} ${generationSettings.includeBenefits ? 'qui vous offre des avantages exceptionnels' : ''} ${generationSettings.includeFeatures ? 'avec ses fonctionnalités avancées' : ''}.`;

      // Description longue
      let longDescription = `
        <h2>À propos de ce produit</h2>
        <p>Notre ${selectedProduct.name} est conçu pour répondre à vos besoins avec un style ${tone}.</p>

        ${generationSettings.includeFeatures ? `
        <h3>Caractéristiques principales</h3>
        <ul>
          <li>Caractéristique premium 1</li>
          <li>Caractéristique innovante 2</li>
          <li>Caractéristique exclusive 3</li>
        </ul>
        ` : ''}

        ${generationSettings.includeBenefits ? `
        <h3>Avantages</h3>
        <p>Ce produit vous offre de nombreux avantages :</p>
        <ul>
          <li>Avantage significatif 1</li>
          <li>Avantage important 2</li>
          <li>Avantage unique 3</li>
        </ul>
        ` : ''}

        ${generationSettings.includeSpecs ? `
        <h3>Spécifications techniques</h3>
        <ul>
          ${selectedProduct.attributes.map(attr => `<li><strong>${attr.name}:</strong> ${attr.value}</li>`).join('')}
          <li><strong>Dimensions:</strong> 10 x 20 x 5 cm</li>
          <li><strong>Poids:</strong> 250g</li>
        </ul>
        ` : ''}

        <p>Commandez dès maintenant pour profiter de tous les avantages de ce produit exceptionnel !</p>
      `;

      // Meta description
      const metaDescription = `Découvrez notre ${selectedProduct.name} ${generationSettings.optimizeForSEO ? 'avec livraison gratuite et garantie de satisfaction' : ''} - Prix: ${selectedProduct.price}€`;

      // Mots-clés
      const keywords = [
        selectedProduct.name.toLowerCase(),
        selectedProduct.category.toLowerCase(),
        `meilleur ${selectedProduct.category.toLowerCase()}`,
        `${selectedProduct.category.toLowerCase()} pas cher`,
        `${selectedProduct.category.toLowerCase()} qualité`
      ];

      setGeneratedContent({
        title,
        shortDescription,
        longDescription,
        metaDescription,
        keywords
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Fonction pour appliquer le contenu généré
  const applyGeneratedContent = async () => {
    if (!selectedProduct || !generatedContent) return;

    try {
      setIsLoading(true);
      setError(null);

      // Appeler le service pour appliquer le contenu généré
      const response = await ecommerceService.applyGeneratedContent(selectedProduct.id, generatedContent);

      // Mettre à jour les produits dans l'état local
      const updatedProducts = products.map(product => {
        if (product.id === selectedProduct.id) {
          return {
            ...product,
            name: generatedContent.title,
            shortDescription: generatedContent.shortDescription,
            longDescription: generatedContent.longDescription,
            metaDescription: generatedContent.metaDescription,
            keywords: generatedContent.keywords
          };
        }
        return product;
      });

      // Mettre à jour l'état
      setProducts(updatedProducts);
      setSelectedProduct({
        ...selectedProduct,
        name: generatedContent.title,
        shortDescription: generatedContent.shortDescription,
        longDescription: generatedContent.longDescription,
        metaDescription: generatedContent.metaDescription,
        keywords: generatedContent.keywords
      });

      // Afficher le message de succès
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (err) {
      console.error('Erreur lors de l\'application du contenu généré:', err);
      setError(__('Erreur lors de l\'application du contenu généré. Veuillez réessayer.', 'boss-seo'));

      // Simuler l'application du contenu en cas d'erreur (pour le développement)
      // À supprimer en production
      const updatedProducts = products.map(product => {
        if (product.id === selectedProduct.id) {
          return {
            ...product,
            name: generatedContent.title,
            shortDescription: generatedContent.shortDescription,
            longDescription: generatedContent.longDescription,
            metaDescription: generatedContent.metaDescription,
            keywords: generatedContent.keywords
          };
        }
        return product;
      });

      setProducts(updatedProducts);
      setSelectedProduct({
        ...selectedProduct,
        name: generatedContent.title,
        shortDescription: generatedContent.shortDescription,
        longDescription: generatedContent.longDescription,
        metaDescription: generatedContent.metaDescription,
        keywords: generatedContent.keywords
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {error && (
            <div className="boss-text-center boss-p-8 boss-bg-white boss-rounded-lg boss-shadow boss-mb-6">
              <Dashicon icon="warning" size={36} className="boss-text-yellow-500 boss-mb-4" />
              <h2 className="boss-text-xl boss-font-bold boss-mb-2">{__('Attention', 'boss-seo')}</h2>
              <p className="boss-text-gray-600 boss-mb-4">{error}</p>
              {error.includes('WooCommerce') && (
                <a
                  href="https://wordpress.org/plugins/woocommerce/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="boss-inline-block boss-bg-blue-500 boss-text-white boss-px-4 boss-py-2 boss-rounded boss-hover:boss-bg-blue-600 boss-transition"
                >
                  {__('Installer WooCommerce', 'boss-seo')}
                </a>
              )}
            </div>
          )}

          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {__('Le contenu a été appliqué avec succès !', 'boss-seo')}
            </Notice>
          )}

          {!error && (
            <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
              {/* Panneau de sélection de produit */}
              <div>
              <Card className="boss-mb-6">
                <CardHeader className="boss-border-b boss-border-gray-200">
                  <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                    {__('Sélection du produit', 'boss-seo')}
                  </h2>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    <TextControl
                      placeholder={__('Rechercher un produit...', 'boss-seo')}
                      value={searchQuery}
                      onChange={setSearchQuery}
                    />

                    <SelectControl
                      label={__('Catégorie', 'boss-seo')}
                      value={selectedCategory}
                      options={[
                        { label: __('Toutes les catégories', 'boss-seo'), value: 'all' },
                        ...categories.map(category => ({
                          label: category.name,
                          value: category.id.toString()
                        }))
                      ]}
                      onChange={setSelectedCategory}
                    />

                    <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-max-h-96 boss-overflow-y-auto">
                      <div className="boss-divide-y boss-divide-gray-200">
                        {filteredProducts.length === 0 ? (
                          <div className="boss-p-4 boss-text-center boss-text-boss-gray">
                            {__('Aucun produit trouvé.', 'boss-seo')}
                          </div>
                        ) : (
                          filteredProducts.map(product => (
                            <div
                              key={product.id}
                              className={`boss-p-4 boss-cursor-pointer boss-transition-colors ${
                                selectedProduct && selectedProduct.id === product.id
                                  ? 'boss-bg-blue-50'
                                  : 'boss-hover:boss-bg-gray-50'
                              }`}
                              onClick={() => setSelectedProduct(product)}
                            >
                              <div className="boss-font-medium boss-text-boss-dark boss-mb-1">{product.name}</div>
                              <div className="boss-text-sm boss-text-boss-gray boss-mb-1">{product.category}</div>
                              <div className="boss-text-sm boss-text-boss-primary">{product.price} €</div>
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {selectedProduct && (
                <Card>
                  <CardHeader className="boss-border-b boss-border-gray-200">
                    <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                      {__('Paramètres de génération', 'boss-seo')}
                    </h2>
                  </CardHeader>
                  <CardBody>
                    <div className="boss-space-y-4">
                      <SelectControl
                        label={__('Ton', 'boss-seo')}
                        value={generationSettings.tone}
                        options={[
                          { label: __('Professionnel', 'boss-seo'), value: 'professional' },
                          { label: __('Décontracté', 'boss-seo'), value: 'casual' },
                          { label: __('Enthousiaste', 'boss-seo'), value: 'enthusiastic' },
                          { label: __('Luxueux', 'boss-seo'), value: 'luxury' },
                          { label: __('Technique', 'boss-seo'), value: 'technical' }
                        ]}
                        onChange={(value) => setGenerationSettings({ ...generationSettings, tone: value })}
                      />

                      <SelectControl
                        label={__('Longueur', 'boss-seo')}
                        value={generationSettings.length}
                        options={[
                          { label: __('Courte', 'boss-seo'), value: 'short' },
                          { label: __('Moyenne', 'boss-seo'), value: 'medium' },
                          { label: __('Longue', 'boss-seo'), value: 'long' }
                        ]}
                        onChange={(value) => setGenerationSettings({ ...generationSettings, length: value })}
                      />

                      <RangeControl
                        label={__('Niveau de créativité', 'boss-seo')}
                        value={generationSettings.creativityLevel}
                        onChange={(value) => setGenerationSettings({ ...generationSettings, creativityLevel: value })}
                        min={0}
                        max={100}
                      />

                      <ToggleControl
                        label={__('Inclure les mots-clés', 'boss-seo')}
                        checked={generationSettings.includeKeywords}
                        onChange={(checked) => setGenerationSettings({ ...generationSettings, includeKeywords: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure les avantages', 'boss-seo')}
                        checked={generationSettings.includeBenefits}
                        onChange={(checked) => setGenerationSettings({ ...generationSettings, includeBenefits: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure les fonctionnalités', 'boss-seo')}
                        checked={generationSettings.includeFeatures}
                        onChange={(checked) => setGenerationSettings({ ...generationSettings, includeFeatures: checked })}
                      />

                      <ToggleControl
                        label={__('Inclure les spécifications', 'boss-seo')}
                        checked={generationSettings.includeSpecs}
                        onChange={(checked) => setGenerationSettings({ ...generationSettings, includeSpecs: checked })}
                      />

                      <ToggleControl
                        label={__('Optimiser pour le SEO', 'boss-seo')}
                        checked={generationSettings.optimizeForSEO}
                        onChange={(checked) => setGenerationSettings({ ...generationSettings, optimizeForSEO: checked })}
                      />
                    </div>
                  </CardBody>
                  <CardFooter className="boss-border-t boss-border-gray-200">
                    <Button
                      isPrimary
                      onClick={generateContent}
                      isBusy={isGenerating}
                      disabled={isGenerating}
                      className="boss-w-full"
                    >
                      {isGenerating ? __('Génération en cours...', 'boss-seo') : __('Générer le contenu', 'boss-seo')}
                    </Button>
                  </CardFooter>
                </Card>
              )}
            </div>

            {/* Panneau de contenu généré */}
            <div className="lg:boss-col-span-2">
              {selectedProduct ? (
                <Card>
                  <CardHeader className="boss-border-b boss-border-gray-200">
                    <div className="boss-flex boss-justify-between boss-items-center">
                      <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                        {__('Contenu du produit', 'boss-seo')}
                      </h2>
                      {generatedContent.title && (
                        <Button
                          isPrimary
                          onClick={applyGeneratedContent}
                        >
                          {__('Appliquer le contenu', 'boss-seo')}
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardBody>
                    <TabPanel
                      className="boss-mb-6"
                      activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
                      tabs={[
                        {
                          name: 'original',
                          title: __('Contenu original', 'boss-seo'),
                          className: 'boss-font-medium boss-px-4 boss-py-2'
                        },
                        {
                          name: 'generated',
                          title: __('Contenu généré', 'boss-seo'),
                          className: 'boss-font-medium boss-px-4 boss-py-2'
                        }
                      ]}
                    >
                      {(tab) => {
                        if (tab.name === 'original') {
                          return (
                            <div className="boss-space-y-6">
                              <div>
                                <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                                  {__('Titre', 'boss-seo')}
                                </h3>
                                <div className="boss-p-3 boss-bg-gray-50 boss-rounded-lg">
                                  {selectedProduct.name}
                                </div>
                              </div>

                              <div>
                                <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                                  {__('Description courte', 'boss-seo')}
                                </h3>
                                <div className="boss-p-3 boss-bg-gray-50 boss-rounded-lg">
                                  {selectedProduct.shortDescription}
                                </div>
                              </div>

                              <div>
                                <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                                  {__('Description longue', 'boss-seo')}
                                </h3>
                                <div className="boss-p-3 boss-bg-gray-50 boss-rounded-lg">
                                  {selectedProduct.longDescription}
                                </div>
                              </div>

                              <div>
                                <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                                  {__('Meta description', 'boss-seo')}
                                </h3>
                                <div className="boss-p-3 boss-bg-gray-50 boss-rounded-lg">
                                  {selectedProduct.metaDescription}
                                </div>
                              </div>

                              <div>
                                <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                                  {__('Mots-clés', 'boss-seo')}
                                </h3>
                                <div className="boss-p-3 boss-bg-gray-50 boss-rounded-lg">
                                  <div className="boss-flex boss-flex-wrap boss-gap-2">
                                    {selectedProduct.keywords.map((keyword, index) => (
                                      <span
                                        key={index}
                                        className="boss-px-2 boss-py-1 boss-bg-gray-200 boss-text-boss-gray boss-text-sm boss-rounded"
                                      >
                                        {keyword}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        } else if (tab.name === 'generated') {
                          return (
                            <div className="boss-space-y-6">
                              {!generatedContent.title ? (
                                <div className="boss-text-center boss-py-8 boss-text-boss-gray">
                                  {__('Cliquez sur "Générer le contenu" pour créer du contenu optimisé.', 'boss-seo')}
                                </div>
                              ) : (
                                <>
                                  <div>
                                    <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                                      {__('Titre', 'boss-seo')}
                                    </h3>
                                    <div className="boss-p-3 boss-bg-green-50 boss-rounded-lg">
                                      {generatedContent.title}
                                    </div>
                                  </div>

                                  <div>
                                    <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                                      {__('Description courte', 'boss-seo')}
                                    </h3>
                                    <div className="boss-p-3 boss-bg-green-50 boss-rounded-lg">
                                      {generatedContent.shortDescription}
                                    </div>
                                  </div>

                                  <div>
                                    <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                                      {__('Description longue', 'boss-seo')}
                                    </h3>
                                    <div
                                      className="boss-p-3 boss-bg-green-50 boss-rounded-lg"
                                      dangerouslySetInnerHTML={{ __html: generatedContent.longDescription }}
                                    />
                                  </div>

                                  <div>
                                    <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                                      {__('Meta description', 'boss-seo')}
                                    </h3>
                                    <div className="boss-p-3 boss-bg-green-50 boss-rounded-lg">
                                      {generatedContent.metaDescription}
                                    </div>
                                  </div>

                                  <div>
                                    <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                                      {__('Mots-clés', 'boss-seo')}
                                    </h3>
                                    <div className="boss-p-3 boss-bg-green-50 boss-rounded-lg">
                                      <div className="boss-flex boss-flex-wrap boss-gap-2">
                                        {generatedContent.keywords.map((keyword, index) => (
                                          <span
                                            key={index}
                                            className="boss-px-2 boss-py-1 boss-bg-green-200 boss-text-green-800 boss-text-sm boss-rounded"
                                          >
                                            {keyword}
                                          </span>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                </>
                              )}
                            </div>
                          );
                        }
                      }}
                    </TabPanel>
                  </CardBody>
                </Card>
              ) : (
                <Card>
                  <CardBody>
                    <div className="boss-text-center boss-py-8">
                      <div className="boss-text-5xl boss-text-boss-gray boss-mb-4">
                        <Dashicon icon="admin-appearance" />
                      </div>
                      <h2 className="boss-text-xl boss-font-bold boss-mb-4">
                        {__('Sélectionnez un produit', 'boss-seo')}
                      </h2>
                      <p className="boss-text-boss-gray boss-mb-6">
                        {__('Veuillez sélectionner un produit pour générer du contenu optimisé.', 'boss-seo')}
                      </p>
                    </div>
                  </CardBody>
                </Card>
              )}
            </div>
          </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProductDescriptionGenerator;
