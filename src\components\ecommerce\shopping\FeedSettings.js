import { __ } from '@wordpress/i18n';
import { 
  Card, 
  CardHeader, 
  CardBody, 
  CardFooter, 
  Button, 
  TextControl, 
  SelectControl, 
  ToggleControl,
  CheckboxControl
} from '@wordpress/components';

const FeedSettings = ({ 
  feedSettings, 
  setFeedSettings, 
  handleGenerateFeed, 
  isGenerating 
}) => {
  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Paramètres du flux', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        <div className="boss-space-y-4">
          <TextControl
            label={__('Nom du flux', 'boss-seo')}
            value={feedSettings.name}
            onChange={(value) => setFeedSettings({ ...feedSettings, name: value })}
          />
          
          <TextControl
            label={__('ID du compte marchand', 'boss-seo')}
            value={feedSettings.merchantId}
            onChange={(value) => setFeedSettings({ ...feedSettings, merchantId: value })}
          />
          
          <SelectControl
            label={__('Pays cible', 'boss-seo')}
            value={feedSettings.country}
            options={[
              { label: 'France', value: 'FR' },
              { label: 'Belgique', value: 'BE' },
              { label: 'Suisse', value: 'CH' },
              { label: 'Canada', value: 'CA' },
              { label: 'États-Unis', value: 'US' },
              { label: 'Royaume-Uni', value: 'GB' },
              { label: 'Allemagne', value: 'DE' },
              { label: 'Espagne', value: 'ES' },
              { label: 'Italie', value: 'IT' }
            ]}
            onChange={(value) => setFeedSettings({ ...feedSettings, country: value })}
          />
          
          <SelectControl
            label={__('Langue', 'boss-seo')}
            value={feedSettings.language}
            options={[
              { label: 'Français', value: 'fr' },
              { label: 'Anglais', value: 'en' },
              { label: 'Allemand', value: 'de' },
              { label: 'Espagnol', value: 'es' },
              { label: 'Italien', value: 'it' }
            ]}
            onChange={(value) => setFeedSettings({ ...feedSettings, language: value })}
          />
          
          <SelectControl
            label={__('Devise', 'boss-seo')}
            value={feedSettings.currency}
            options={[
              { label: 'Euro (EUR)', value: 'EUR' },
              { label: 'Dollar américain (USD)', value: 'USD' },
              { label: 'Livre sterling (GBP)', value: 'GBP' },
              { label: 'Franc suisse (CHF)', value: 'CHF' },
              { label: 'Dollar canadien (CAD)', value: 'CAD' }
            ]}
            onChange={(value) => setFeedSettings({ ...feedSettings, currency: value })}
          />
          
          <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4">
            <h3 className="boss-text-md boss-font-semibold boss-mb-3">
              {__('Inclure dans le flux', 'boss-seo')}
            </h3>
            
            <div className="boss-space-y-2">
              <CheckboxControl
                label={__('Tous les produits', 'boss-seo')}
                checked={feedSettings.includeAll}
                onChange={(checked) => setFeedSettings({ ...feedSettings, includeAll: checked })}
              />
              
              <CheckboxControl
                label={__('Produits en stock uniquement', 'boss-seo')}
                checked={feedSettings.includeInStock}
                onChange={(checked) => setFeedSettings({ ...feedSettings, includeInStock: checked })}
              />
              
              <CheckboxControl
                label={__('Produits avec images uniquement', 'boss-seo')}
                checked={feedSettings.includeWithImages}
                onChange={(checked) => setFeedSettings({ ...feedSettings, includeWithImages: checked })}
              />
              
              <CheckboxControl
                label={__('Produits avec prix uniquement', 'boss-seo')}
                checked={feedSettings.includeWithPrice}
                onChange={(checked) => setFeedSettings({ ...feedSettings, includeWithPrice: checked })}
              />
            </div>
          </div>
          
          <ToggleControl
            label={__('Mise à jour automatique', 'boss-seo')}
            help={__('Mettre à jour automatiquement le flux chaque jour', 'boss-seo')}
            checked={feedSettings.autoUpdate}
            onChange={(checked) => setFeedSettings({ ...feedSettings, autoUpdate: checked })}
          />
        </div>
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200">
        <Button
          isPrimary
          onClick={handleGenerateFeed}
          isBusy={isGenerating}
          disabled={isGenerating}
          className="boss-w-full"
        >
          {isGenerating ? __('Génération en cours...', 'boss-seo') : __('Générer le flux', 'boss-seo')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default FeedSettings;
