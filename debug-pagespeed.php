<?php
/**
 * Script de diagnostic pour l'API Google PageSpeed Insights
 * À placer dans le dossier du plugin Boss SEO
 * 
 * Usage: wp-admin/admin.php?page=debug-pagespeed
 */

// Sécurité WordPress
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Vérifier les permissions
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'Accès refusé' );
}

echo '<div class="wrap">';
echo '<h1>🔍 Diagnostic API Google PageSpeed Insights</h1>';

// Récupérer la configuration actuelle
$external_services = get_option( 'boss_optimizer_external_services', array() );
$api_key = isset( $external_services['google_pagespeed']['api_key'] ) ? $external_services['google_pagespeed']['api_key'] : '';

// Fallback vers l'ancienne structure
if ( empty( $api_key ) ) {
    $api_key = get_option( 'boss_optimizer_pagespeed_api_key', '' );
}

echo '<div class="notice notice-info"><p><strong>ℹ️ Ce diagnostic va tester votre configuration PageSpeed Insights</strong></p></div>';

// Section 1: Configuration
echo '<h2>📋 1. Configuration actuelle</h2>';
echo '<table class="wp-list-table widefat fixed striped">';
echo '<thead><tr><th>Paramètre</th><th>Valeur</th><th>Statut</th></tr></thead>';
echo '<tbody>';

// Clé API
$api_key_status = '';
$api_key_display = '';
if ( empty( $api_key ) ) {
    $api_key_status = '<span style="color: red;">❌ Manquante</span>';
    $api_key_display = '<em>Non configurée</em>';
} else {
    $api_key_length = strlen( $api_key );
    $api_key_display = substr( $api_key, 0, 8 ) . str_repeat( '•', max( 0, $api_key_length - 8 ) );
    
    if ( $api_key_length < 30 ) {
        $api_key_status = '<span style="color: orange;">⚠️ Trop courte (' . $api_key_length . ' caractères)</span>';
    } else {
        $api_key_status = '<span style="color: green;">✅ Configurée (' . $api_key_length . ' caractères)</span>';
    }
}

echo '<tr>';
echo '<td><strong>Clé API PageSpeed</strong></td>';
echo '<td><code>' . esc_html( $api_key_display ) . '</code></td>';
echo '<td>' . $api_key_status . '</td>';
echo '</tr>';

// URL du site
$site_url = home_url();
$is_localhost = ( strpos( $site_url, 'localhost' ) !== false || strpos( $site_url, '127.0.0.1' ) !== false || strpos( $site_url, '.local' ) !== false );

echo '<tr>';
echo '<td><strong>URL du site</strong></td>';
echo '<td><a href="' . esc_url( $site_url ) . '" target="_blank">' . esc_html( $site_url ) . '</a></td>';
echo '<td>' . ( $is_localhost ? '<span style="color: orange;">⚠️ Localhost (peut poser problème)</span>' : '<span style="color: green;">✅ URL publique</span>' ) . '</td>';
echo '</tr>';

// WP_DEBUG
$wp_debug = defined( 'WP_DEBUG' ) && WP_DEBUG;
echo '<tr>';
echo '<td><strong>Mode Debug WordPress</strong></td>';
echo '<td>' . ( $wp_debug ? 'Activé' : 'Désactivé' ) . '</td>';
echo '<td>' . ( $wp_debug ? '<span style="color: green;">✅ Logs disponibles</span>' : '<span style="color: gray;">ℹ️ Pas de logs détaillés</span>' ) . '</td>';
echo '</tr>';

echo '</tbody></table>';

// Section 2: Test de la clé API
if ( ! empty( $api_key ) ) {
    echo '<h2>🧪 2. Test de la clé API</h2>';
    
    try {
        // Utiliser le service de vérification amélioré
        if ( ! class_exists( 'Boss_Optimizer_External_Services' ) ) {
            require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-optimizer-external-services.php';
        }
        
        $external_services_class = new Boss_Optimizer_External_Services();
        
        // Utiliser la réflexion pour accéder à la méthode privée
        $reflection = new ReflectionClass( $external_services_class );
        $method = $reflection->getMethod( 'verify_pagespeed_api_key' );
        $method->setAccessible( true );
        
        $verification_result = $method->invoke( $external_services_class, $api_key );
        
        if ( $verification_result['success'] ) {
            echo '<div class="notice notice-success"><p><strong>✅ ' . esc_html( $verification_result['message'] ) . '</strong></p></div>';
            
            if ( isset( $verification_result['debug'] ) ) {
                echo '<details style="margin: 10px 0;"><summary><strong>Détails du test</strong></summary>';
                echo '<pre style="background: #f0f0f0; padding: 10px; border-radius: 4px; overflow-x: auto;">';
                echo esc_html( print_r( $verification_result['debug'], true ) );
                echo '</pre></details>';
            }
        } else {
            echo '<div class="notice notice-error"><p><strong>❌ ' . esc_html( $verification_result['message'] ) . '</strong></p></div>';
            
            if ( isset( $verification_result['debug'] ) ) {
                echo '<details style="margin: 10px 0;"><summary><strong>Détails de l\'erreur</strong></summary>';
                echo '<pre style="background: #ffe6e6; padding: 10px; border-radius: 4px; overflow-x: auto;">';
                echo esc_html( print_r( $verification_result['debug'], true ) );
                echo '</pre></details>';
            }
        }
        
    } catch ( Exception $e ) {
        echo '<div class="notice notice-error"><p><strong>❌ Erreur lors du test: ' . esc_html( $e->getMessage() ) . '</strong></p></div>';
    }
    
    // Section 3: Test avec le gestionnaire PageSpeed
    echo '<h2>⚙️ 3. Test du gestionnaire PageSpeed</h2>';
    
    try {
        if ( ! class_exists( 'Boss_PageSpeed_Manager' ) ) {
            require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-pagespeed-manager.php';
        }
        
        $pagespeed_manager = new Boss_PageSpeed_Manager();
        $test_result = $pagespeed_manager->test_api_key();
        
        if ( $test_result['success'] ) {
            echo '<div class="notice notice-success"><p><strong>✅ ' . esc_html( $test_result['message'] ) . '</strong></p></div>';
            
            if ( isset( $test_result['debug'] ) ) {
                echo '<details style="margin: 10px 0;"><summary><strong>Détails du gestionnaire</strong></summary>';
                echo '<pre style="background: #f0f0f0; padding: 10px; border-radius: 4px; overflow-x: auto;">';
                echo esc_html( print_r( $test_result['debug'], true ) );
                echo '</pre></details>';
            }
            
            if ( isset( $test_result['data'] ) ) {
                echo '<details style="margin: 10px 0;"><summary><strong>Données de test récupérées</strong></summary>';
                echo '<div style="background: #e7f3ff; padding: 10px; border-radius: 4px;">';
                
                if ( isset( $test_result['data']['scores'] ) ) {
                    echo '<h4>Scores par catégorie:</h4>';
                    foreach ( $test_result['data']['scores'] as $category => $score_data ) {
                        echo '<p><strong>' . esc_html( $score_data['title'] ) . ':</strong> ' . esc_html( $score_data['score'] ) . '/100</p>';
                    }
                }
                
                if ( isset( $test_result['data']['core_web_vitals'] ) ) {
                    echo '<h4>Core Web Vitals:</h4>';
                    foreach ( $test_result['data']['core_web_vitals'] as $metric => $metric_data ) {
                        echo '<p><strong>' . esc_html( $metric_data['description'] ) . ':</strong> ' . 
                             esc_html( $metric_data['value'] ) . esc_html( $metric_data['unit'] ) . 
                             ' (' . esc_html( $metric_data['status'] ) . ')</p>';
                    }
                }
                
                echo '</div></details>';
            }
        } else {
            echo '<div class="notice notice-error"><p><strong>❌ ' . esc_html( $test_result['message'] ) . '</strong></p></div>';
            
            if ( isset( $test_result['debug'] ) ) {
                echo '<details style="margin: 10px 0;"><summary><strong>Détails de l\'erreur du gestionnaire</strong></summary>';
                echo '<pre style="background: #ffe6e6; padding: 10px; border-radius: 4px; overflow-x: auto;">';
                echo esc_html( print_r( $test_result['debug'], true ) );
                echo '</pre></details>';
            }
        }
        
    } catch ( Exception $e ) {
        echo '<div class="notice notice-error"><p><strong>❌ Erreur du gestionnaire PageSpeed: ' . esc_html( $e->getMessage() ) . '</strong></p></div>';
    }
    
} else {
    echo '<div class="notice notice-warning"><p><strong>⚠️ Impossible de tester : aucune clé API configurée</strong></p></div>';
}

// Section 4: Recommandations
echo '<h2>💡 4. Recommandations</h2>';

if ( empty( $api_key ) ) {
    echo '<div class="notice notice-info">';
    echo '<p><strong>Pour configurer l\'API PageSpeed Insights :</strong></p>';
    echo '<ol>';
    echo '<li>Allez sur <a href="https://console.cloud.google.com/apis/library/pagespeedonline.googleapis.com" target="_blank">Google Cloud Console</a></li>';
    echo '<li>Activez l\'API PageSpeed Insights</li>';
    echo '<li>Créez une clé API dans "Identifiants"</li>';
    echo '<li>Copiez la clé dans <a href="' . admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' ) . '">Boss SEO > Paramètres > Services externes</a></li>';
    echo '</ol>';
    echo '</div>';
} elseif ( $is_localhost ) {
    echo '<div class="notice notice-warning">';
    echo '<p><strong>⚠️ Votre site est en localhost</strong></p>';
    echo '<p>Google PageSpeed Insights ne peut pas analyser les sites localhost. Pour tester :</p>';
    echo '<ul>';
    echo '<li>Déployez votre site sur un serveur public</li>';
    echo '<li>Ou utilisez un service comme ngrok pour exposer votre localhost</li>';
    echo '</ul>';
    echo '</div>';
} else {
    echo '<div class="notice notice-success">';
    echo '<p><strong>✅ Configuration recommandée</strong></p>';
    echo '<p>Votre configuration semble correcte. Si vous rencontrez encore des problèmes :</p>';
    echo '<ul>';
    echo '<li>Vérifiez que l\'API PageSpeed Insights est bien activée dans votre projet Google Cloud</li>';
    echo '<li>Assurez-vous que votre clé API a les bonnes permissions</li>';
    echo '<li>Vérifiez les quotas de votre API dans Google Cloud Console</li>';
    echo '</ul>';
    echo '</div>';
}

// Actions
echo '<h2>🔧 5. Actions</h2>';
echo '<p>';
echo '<a href="' . admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' ) . '" class="button button-primary">⚙️ Configurer les services externes</a> ';
echo '<a href="' . admin_url( 'admin.php?page=boss-seo-technical' ) . '" class="button">📊 Aller à l\'analyse technique</a> ';
echo '<a href="' . admin_url( 'admin.php?page=debug-pagespeed' ) . '" class="button">🔄 Actualiser le diagnostic</a>';
echo '</p>';

echo '</div>';

// CSS pour améliorer l'affichage
echo '<style>
.wp-list-table td { vertical-align: top; }
.wp-list-table code { background: #f1f1f1; padding: 2px 4px; border-radius: 2px; }
details { border: 1px solid #ddd; border-radius: 4px; padding: 10px; }
details summary { cursor: pointer; font-weight: bold; margin-bottom: 10px; }
details[open] summary { margin-bottom: 10px; }
</style>';
?>
