<?php
/**
 * Classe pour gérer les points de terminaison REST API du module de paramètres.
 *
 * @link       https://bossseo.com
 * @since      1.0.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/api
 */

/**
 * Classe pour gérer les points de terminaison REST API du module de paramètres.
 *
 * Cette classe gère les points de terminaison REST API pour le module de paramètres,
 * permettant de récupérer et de mettre à jour les paramètres du plugin.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/api
 * <AUTHOR> SEO Team
 */
class Boss_Settings_API {

    /**
     * Le nom du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Boss_SEO_Settings    $settings    Instance de la classe de paramètres.
     */
    protected $settings;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.0.0
     * @param    string             $plugin_name    Le nom du plugin.
     * @param    string             $version        La version du plugin.
     * @param    Boss_SEO_Settings  $settings       Instance de la classe de paramètres.
     */
    public function __construct( $plugin_name, $version, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;
    }

    /**
     * Enregistre les points de terminaison REST API.
     *
     * @since    1.0.0
     */
    public function register_routes() {
        // Point de terminaison pour les paramètres généraux
        register_rest_route( 'boss-seo/v1', '/settings/general', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_general_settings' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => array( $this, 'update_general_settings' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => $this->get_general_settings_args(),
            ),
        ) );
        
        // Point de terminaison pour les paramètres avancés
        register_rest_route( 'boss-seo/v1', '/settings/advanced', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_advanced_settings' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => array( $this, 'update_advanced_settings' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => $this->get_advanced_settings_args(),
            ),
        ) );
        
        // Point de terminaison pour les paramètres de sauvegarde
        register_rest_route( 'boss-seo/v1', '/settings/backup', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_backup_settings' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => array( $this, 'update_backup_settings' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => $this->get_backup_settings_args(),
            ),
        ) );
        
        // Point de terminaison pour les sauvegardes
        register_rest_route( 'boss-seo/v1', '/backups', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_backups' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'create_backup' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => $this->get_create_backup_args(),
            ),
        ) );
        
        // Point de terminaison pour une sauvegarde spécifique
        register_rest_route( 'boss-seo/v1', '/backups/(?P<name>[a-zA-Z0-9-_]+)', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_backup' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'name' => array(
                        'required'          => true,
                        'sanitize_callback' => 'sanitize_text_field',
                    ),
                ),
            ),
            array(
                'methods'             => WP_REST_Server::DELETABLE,
                'callback'            => array( $this, 'delete_backup' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'name' => array(
                        'required'          => true,
                        'sanitize_callback' => 'sanitize_text_field',
                    ),
                ),
            ),
        ) );
        
        // Point de terminaison pour restaurer une sauvegarde
        register_rest_route( 'boss-seo/v1', '/backups/(?P<name>[a-zA-Z0-9-_]+)/restore', array(
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => array( $this, 'restore_backup' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'name' => array(
                        'required'          => true,
                        'sanitize_callback' => 'sanitize_text_field',
                    ),
                ),
            ),
        ) );
        
        // Point de terminaison pour les préférences utilisateur
        register_rest_route( 'boss-seo/v1', '/user-preferences', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_user_preferences' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => array( $this, 'update_user_preferences' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => $this->get_user_preferences_args(),
            ),
        ) );
        
        // Point de terminaison pour réinitialiser les préférences utilisateur
        register_rest_route( 'boss-seo/v1', '/user-preferences/reset', array(
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => array( $this, 'reset_user_preferences' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );
        
        // Point de terminaison pour marquer une notification comme lue
        register_rest_route( 'boss-seo/v1', '/notifications/(?P<id>[a-zA-Z0-9-_]+)/read', array(
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => array( $this, 'mark_notification_read' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'id' => array(
                        'required'          => true,
                        'sanitize_callback' => 'sanitize_text_field',
                    ),
                ),
            ),
        ) );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.0.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les paramètres généraux.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_general_settings( $request ) {
        $settings = $this->settings->get_general_settings()->get_settings();
        
        return rest_ensure_response( $settings );
    }

    /**
     * Met à jour les paramètres généraux.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_general_settings( $request ) {
        $settings = $request->get_params();
        
        $result = $this->settings->get_general_settings()->save_settings( $settings );
        
        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Paramètres généraux enregistrés avec succès.', 'boss-seo' ),
                'settings' => $this->settings->get_general_settings()->get_settings(),
            ) );
        } else {
            return new WP_Error(
                'settings_update_failed',
                __( 'Impossible d\'enregistrer les paramètres généraux.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère les arguments pour les paramètres généraux.
     *
     * @since    1.0.0
     * @return   array    Les arguments.
     */
    public function get_general_settings_args() {
        return array(
            'general' => array(
                'type'              => 'object',
                'sanitize_callback' => null,
                'validate_callback' => null,
            ),
            'content' => array(
                'type'              => 'object',
                'sanitize_callback' => null,
                'validate_callback' => null,
            ),
        );
    }

    /**
     * Récupère les paramètres avancés.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_advanced_settings( $request ) {
        $settings = $this->settings->get_advanced_settings()->get_settings();
        
        return rest_ensure_response( $settings );
    }

    /**
     * Met à jour les paramètres avancés.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_advanced_settings( $request ) {
        $settings = $request->get_params();
        
        $result = $this->settings->get_advanced_settings()->save_settings( $settings );
        
        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Paramètres avancés enregistrés avec succès.', 'boss-seo' ),
                'settings' => $this->settings->get_advanced_settings()->get_settings(),
            ) );
        } else {
            return new WP_Error(
                'settings_update_failed',
                __( 'Impossible d\'enregistrer les paramètres avancés.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère les arguments pour les paramètres avancés.
     *
     * @since    1.0.0
     * @return   array    Les arguments.
     */
    public function get_advanced_settings_args() {
        return array(
            'advanced' => array(
                'type'              => 'object',
                'sanitize_callback' => null,
                'validate_callback' => null,
            ),
        );
    }

    /**
     * Récupère les paramètres de sauvegarde.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_backup_settings( $request ) {
        $settings = $this->settings->get_backup_manager()->get_settings();
        
        return rest_ensure_response( $settings );
    }

    /**
     * Met à jour les paramètres de sauvegarde.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_backup_settings( $request ) {
        $settings = $request->get_params();
        
        $result = $this->settings->get_backup_manager()->save_settings( $settings );
        
        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Paramètres de sauvegarde enregistrés avec succès.', 'boss-seo' ),
                'settings' => $this->settings->get_backup_manager()->get_settings(),
            ) );
        } else {
            return new WP_Error(
                'settings_update_failed',
                __( 'Impossible d\'enregistrer les paramètres de sauvegarde.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère les arguments pour les paramètres de sauvegarde.
     *
     * @since    1.0.0
     * @return   array    Les arguments.
     */
    public function get_backup_settings_args() {
        return array(
            'backup' => array(
                'type'              => 'object',
                'sanitize_callback' => null,
                'validate_callback' => null,
            ),
        );
    }

    /**
     * Récupère la liste des sauvegardes.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_backups( $request ) {
        $backups = $this->settings->get_backup_manager()->get_backups();
        
        return rest_ensure_response( $backups );
    }

    /**
     * Crée une sauvegarde.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function create_backup( $request ) {
        $name = $request->get_param( 'name' );
        $include_settings = $request->get_param( 'include_settings' );
        $include_data = $request->get_param( 'include_data' );
        
        $result = $this->settings->get_backup_manager()->create_backup( $name, $include_settings, $include_data );
        
        if ( is_wp_error( $result ) ) {
            return $result;
        }
        
        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Sauvegarde créée avec succès.', 'boss-seo' ),
            'backup' => $result,
        ) );
    }

    /**
     * Récupère les arguments pour la création d'une sauvegarde.
     *
     * @since    1.0.0
     * @return   array    Les arguments.
     */
    public function get_create_backup_args() {
        return array(
            'name' => array(
                'type'              => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'validate_callback' => null,
            ),
            'include_settings' => array(
                'type'              => 'boolean',
                'default'           => true,
                'sanitize_callback' => null,
                'validate_callback' => null,
            ),
            'include_data' => array(
                'type'              => 'boolean',
                'default'           => true,
                'sanitize_callback' => null,
                'validate_callback' => null,
            ),
        );
    }

    /**
     * Récupère une sauvegarde.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_backup( $request ) {
        $name = $request->get_param( 'name' );
        
        $backups = $this->settings->get_backup_manager()->get_backups();
        
        foreach ( $backups as $backup ) {
            if ( $backup['name'] === $name ) {
                return rest_ensure_response( $backup );
            }
        }
        
        return new WP_Error(
            'backup_not_found',
            __( 'La sauvegarde demandée n\'existe pas.', 'boss-seo' ),
            array( 'status' => 404 )
        );
    }

    /**
     * Supprime une sauvegarde.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_backup( $request ) {
        $name = $request->get_param( 'name' );
        
        $result = $this->settings->get_backup_manager()->delete_backup( $name );
        
        if ( is_wp_error( $result ) ) {
            return $result;
        }
        
        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Sauvegarde supprimée avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Restaure une sauvegarde.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function restore_backup( $request ) {
        $name = $request->get_param( 'name' );
        
        $result = $this->settings->get_backup_manager()->restore_backup( $name );
        
        if ( is_wp_error( $result ) ) {
            return $result;
        }
        
        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Sauvegarde restaurée avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les préférences utilisateur.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_user_preferences( $request ) {
        $user_id = get_current_user_id();
        
        $preferences = $this->settings->get_user_preferences()->get_user_preferences( $user_id );
        
        return rest_ensure_response( $preferences );
    }

    /**
     * Met à jour les préférences utilisateur.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_user_preferences( $request ) {
        $user_id = get_current_user_id();
        $preferences = $request->get_params();
        
        $result = $this->settings->get_user_preferences()->save_user_preferences( $user_id, $preferences );
        
        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Préférences utilisateur enregistrées avec succès.', 'boss-seo' ),
                'preferences' => $this->settings->get_user_preferences()->get_user_preferences( $user_id ),
            ) );
        } else {
            return new WP_Error(
                'preferences_update_failed',
                __( 'Impossible d\'enregistrer les préférences utilisateur.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère les arguments pour les préférences utilisateur.
     *
     * @since    1.0.0
     * @return   array    Les arguments.
     */
    public function get_user_preferences_args() {
        return array(
            'dashboard' => array(
                'type'              => 'object',
                'sanitize_callback' => null,
                'validate_callback' => null,
            ),
            'notifications' => array(
                'type'              => 'object',
                'sanitize_callback' => null,
                'validate_callback' => null,
            ),
            'display' => array(
                'type'              => 'object',
                'sanitize_callback' => null,
                'validate_callback' => null,
            ),
            'editor' => array(
                'type'              => 'object',
                'sanitize_callback' => null,
                'validate_callback' => null,
            ),
        );
    }

    /**
     * Réinitialise les préférences utilisateur.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function reset_user_preferences( $request ) {
        $user_id = get_current_user_id();
        
        $result = $this->settings->get_user_preferences()->reset_user_preferences( $user_id );
        
        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Préférences utilisateur réinitialisées avec succès.', 'boss-seo' ),
                'preferences' => $this->settings->get_user_preferences()->get_user_preferences( $user_id ),
            ) );
        } else {
            return new WP_Error(
                'preferences_reset_failed',
                __( 'Impossible de réinitialiser les préférences utilisateur.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Marque une notification comme lue.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function mark_notification_read( $request ) {
        $user_id = get_current_user_id();
        $notification_id = $request->get_param( 'id' );
        
        $result = $this->settings->get_user_preferences()->mark_notification_read( $user_id, $notification_id );
        
        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Notification marquée comme lue.', 'boss-seo' ),
            ) );
        } else {
            return new WP_Error(
                'notification_mark_read_failed',
                __( 'Impossible de marquer la notification comme lue.', 'boss-seo' ),
                array( 'status' => 500 )
            );
        }
    }
}
