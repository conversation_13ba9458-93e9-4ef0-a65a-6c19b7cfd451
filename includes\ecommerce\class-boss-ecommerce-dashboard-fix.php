<?php
/**
 * Classe pour le tableau de bord e-commerce.
 *
 * Cette classe gère le tableau de bord e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe pour le tableau de bord e-commerce.
 *
 * Cette classe gère le tableau de bord e-commerce.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Ecommerce_Dashboard_Fix {

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/dashboard/top-products',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_top_products' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/dashboard/top-categories',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_top_categories' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/dashboard/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_dashboard_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les produits les plus performants via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_top_products( $request ) {
        $period = $request->get_param( 'period' ) ? sanitize_text_field( $request->get_param( 'period' ) ) : '30days';
        
        // Vérifier si WooCommerce est installé et activé
        if ( ! post_type_exists( 'product' ) || ! function_exists( 'wc_get_product' ) ) {
            // Si WooCommerce n'est pas disponible, retourner des données fictives
            return rest_ensure_response( array(
                'products' => array(
                    array( 'id' => 1, 'name' => 'Smartphone XYZ', 'category' => 'Électronique', 'score' => 92, 'clicks' => 1250, 'conversions' => 78 ),
                    array( 'id' => 2, 'name' => 'T-shirt Premium', 'category' => 'Vêtements', 'score' => 88, 'clicks' => 980, 'conversions' => 65 ),
                    array( 'id' => 3, 'name' => 'Casque Audio Pro', 'category' => 'Électronique', 'score' => 85, 'clicks' => 870, 'conversions' => 52 ),
                    array( 'id' => 4, 'name' => 'Chaussures de Sport', 'category' => 'Sports', 'score' => 83, 'clicks' => 760, 'conversions' => 48 ),
                    array( 'id' => 5, 'name' => 'Lampe Design', 'category' => 'Maison', 'score' => 80, 'clicks' => 650, 'conversions' => 41 )
                )
            ) );
        }

        // Ici, vous implémenteriez la logique pour récupérer les produits les plus performants
        // Pour l'instant, nous retournons des données fictives
        $top_products = array(
            array( 'id' => 1, 'name' => 'Smartphone XYZ', 'category' => 'Électronique', 'score' => 92, 'clicks' => 1250, 'conversions' => 78 ),
            array( 'id' => 2, 'name' => 'T-shirt Premium', 'category' => 'Vêtements', 'score' => 88, 'clicks' => 980, 'conversions' => 65 ),
            array( 'id' => 3, 'name' => 'Casque Audio Pro', 'category' => 'Électronique', 'score' => 85, 'clicks' => 870, 'conversions' => 52 ),
            array( 'id' => 4, 'name' => 'Chaussures de Sport', 'category' => 'Sports', 'score' => 83, 'clicks' => 760, 'conversions' => 48 ),
            array( 'id' => 5, 'name' => 'Lampe Design', 'category' => 'Maison', 'score' => 80, 'clicks' => 650, 'conversions' => 41 )
        );

        return rest_ensure_response( array( 'products' => $top_products ) );
    }

    /**
     * Récupère les catégories les plus performantes via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_top_categories( $request ) {
        $period = $request->get_param( 'period' ) ? sanitize_text_field( $request->get_param( 'period' ) ) : '30days';
        
        // Vérifier si WooCommerce est installé et activé
        if ( ! post_type_exists( 'product' ) || ! function_exists( 'wc_get_product' ) ) {
            // Si WooCommerce n'est pas disponible, retourner des données fictives
            return rest_ensure_response( array(
                'categories' => array(
                    array( 'name' => 'Électronique', 'clicks' => 3850, 'conversions' => 245, 'conversionRate' => 6.36 ),
                    array( 'name' => 'Vêtements', 'clicks' => 3200, 'conversions' => 210, 'conversionRate' => 6.56 ),
                    array( 'name' => 'Sports', 'clicks' => 1950, 'conversions' => 135, 'conversionRate' => 6.92 ),
                    array( 'name' => 'Maison', 'clicks' => 1650, 'conversions' => 98, 'conversionRate' => 5.94 ),
                    array( 'name' => 'Livres', 'clicks' => 980, 'conversions' => 52, 'conversionRate' => 5.31 )
                )
            ) );
        }

        // Ici, vous implémenteriez la logique pour récupérer les catégories les plus performantes
        // Pour l'instant, nous retournons des données fictives
        $top_categories = array(
            array( 'name' => 'Électronique', 'clicks' => 3850, 'conversions' => 245, 'conversionRate' => 6.36 ),
            array( 'name' => 'Vêtements', 'clicks' => 3200, 'conversions' => 210, 'conversionRate' => 6.56 ),
            array( 'name' => 'Sports', 'clicks' => 1950, 'conversions' => 135, 'conversionRate' => 6.92 ),
            array( 'name' => 'Maison', 'clicks' => 1650, 'conversions' => 98, 'conversionRate' => 5.94 ),
            array( 'name' => 'Livres', 'clicks' => 980, 'conversions' => 52, 'conversionRate' => 5.31 )
        );

        return rest_ensure_response( array( 'categories' => $top_categories ) );
    }

    /**
     * Récupère les statistiques du tableau de bord via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_dashboard_stats( $request ) {
        $period = $request->get_param( 'period' ) ? sanitize_text_field( $request->get_param( 'period' ) ) : '30days';
        
        // Vérifier si WooCommerce est installé et activé
        if ( ! post_type_exists( 'product' ) || ! function_exists( 'wc_get_product' ) ) {
            // Si WooCommerce n'est pas disponible, retourner des données fictives
            return rest_ensure_response( array(
                'stats' => array(
                    'total' => 248,
                    'optimized' => 142,
                    'needsAttention' => 78,
                    'critical' => 28,
                    'categories' => array(
                        array( 'name' => 'Électronique', 'count' => 65, 'optimized' => 42, 'needsAttention' => 18, 'critical' => 5 ),
                        array( 'name' => 'Vêtements', 'count' => 87, 'optimized' => 53, 'needsAttention' => 24, 'critical' => 10 ),
                        array( 'name' => 'Maison', 'count' => 45, 'optimized' => 28, 'needsAttention' => 12, 'critical' => 5 ),
                        array( 'name' => 'Sports', 'count' => 32, 'optimized' => 15, 'needsAttention' => 14, 'critical' => 3 ),
                        array( 'name' => 'Livres', 'count' => 19, 'optimized' => 4, 'needsAttention' => 10, 'critical' => 5 )
                    )
                )
            ) );
        }

        // Ici, vous implémenteriez la logique pour récupérer les statistiques du tableau de bord
        // Pour l'instant, nous retournons des données fictives
        $stats = array(
            'total' => 248,
            'optimized' => 142,
            'needsAttention' => 78,
            'critical' => 28,
            'categories' => array(
                array( 'name' => 'Électronique', 'count' => 65, 'optimized' => 42, 'needsAttention' => 18, 'critical' => 5 ),
                array( 'name' => 'Vêtements', 'count' => 87, 'optimized' => 53, 'needsAttention' => 24, 'critical' => 10 ),
                array( 'name' => 'Maison', 'count' => 45, 'optimized' => 28, 'needsAttention' => 12, 'critical' => 5 ),
                array( 'name' => 'Sports', 'count' => 32, 'optimized' => 15, 'needsAttention' => 14, 'critical' => 3 ),
                array( 'name' => 'Livres', 'count' => 19, 'optimized' => 4, 'needsAttention' => 10, 'critical' => 5 )
            )
        );

        return rest_ensure_response( array( 'stats' => $stats ) );
    }
}
