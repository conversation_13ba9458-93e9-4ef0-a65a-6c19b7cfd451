<?php
/**
 * Générateur de schémas structurés pour Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe de génération de schémas Schema.org.
 *
 * Cette classe génère automatiquement des schémas structurés
 * conformes aux standards Schema.org.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Schema_Generator {

    /**
     * Génère un schéma structuré pour un post.
     *
     * @since    1.2.0
     * @param    int       $post_id      ID du post.
     * @param    string    $schema_type  Type de schéma.
     * @return   array                   Schéma généré.
     */
    public function generate_schema_markup( $post_id, $schema_type = 'auto' ) {
        $post = get_post( $post_id );
        
        if ( ! $post ) {
            return array();
        }
        
        // Déterminer le type de schéma automatiquement
        if ( $schema_type === 'auto' ) {
            $schema_type = $this->detect_schema_type( $post );
        }
        
        switch ( $schema_type ) {
            case 'article':
                return $this->generate_article_schema( $post );
            case 'webpage':
                return $this->generate_webpage_schema( $post );
            case 'product':
                return $this->generate_product_schema( $post );
            case 'organization':
                return $this->generate_organization_schema( $post );
            case 'person':
                return $this->generate_person_schema( $post );
            default:
                return $this->generate_article_schema( $post );
        }
    }

    /**
     * Détecte automatiquement le type de schéma approprié.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à analyser.
     * @return   string             Type de schéma détecté.
     */
    private function detect_schema_type( $post ) {
        // Détecter selon le type de post
        if ( $post->post_type === 'product' ) {
            return 'product';
        }
        
        if ( $post->post_type === 'page' ) {
            return 'webpage';
        }
        
        // Détecter selon le contenu
        $content = strtolower( $post->post_content );
        
        if ( strpos( $content, 'prix' ) !== false || strpos( $content, 'acheter' ) !== false ) {
            return 'product';
        }
        
        // Par défaut, article
        return 'article';
    }

    /**
     * Génère un schéma Article.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à traiter.
     * @return   array              Schéma Article.
     */
    private function generate_article_schema( $post ) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $this->get_seo_title( $post ),
            'description' => $this->get_meta_description( $post ),
            'url' => get_permalink( $post->ID ),
            'datePublished' => get_the_date( 'c', $post->ID ),
            'dateModified' => get_the_modified_date( 'c', $post->ID ),
            'author' => $this->get_author_schema( $post ),
            'publisher' => $this->get_publisher_schema(),
            'mainEntityOfPage' => array(
                '@type' => 'WebPage',
                '@id' => get_permalink( $post->ID )
            )
        );
        
        // Ajouter l'image si disponible
        $image = $this->get_featured_image( $post );
        if ( $image ) {
            $schema['image'] = $image;
        }
        
        // Ajouter les mots-clés
        $keywords = $this->get_keywords( $post );
        if ( ! empty( $keywords ) ) {
            $schema['keywords'] = implode( ', ', $keywords );
        }
        
        // Ajouter la catégorie
        $categories = get_the_category( $post->ID );
        if ( ! empty( $categories ) ) {
            $schema['articleSection'] = $categories[0]->name;
        }
        
        return $schema;
    }

    /**
     * Génère un schéma WebPage.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à traiter.
     * @return   array              Schéma WebPage.
     */
    private function generate_webpage_schema( $post ) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => $this->get_seo_title( $post ),
            'description' => $this->get_meta_description( $post ),
            'url' => get_permalink( $post->ID ),
            'datePublished' => get_the_date( 'c', $post->ID ),
            'dateModified' => get_the_modified_date( 'c', $post->ID ),
            'author' => $this->get_author_schema( $post ),
            'publisher' => $this->get_publisher_schema()
        );
        
        // Ajouter l'image si disponible
        $image = $this->get_featured_image( $post );
        if ( $image ) {
            $schema['image'] = $image;
        }
        
        return $schema;
    }

    /**
     * Génère un schéma Product.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à traiter.
     * @return   array              Schéma Product.
     */
    private function generate_product_schema( $post ) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Product',
            'name' => $this->get_seo_title( $post ),
            'description' => $this->get_meta_description( $post ),
            'url' => get_permalink( $post->ID )
        );
        
        // Ajouter l'image si disponible
        $image = $this->get_featured_image( $post );
        if ( $image ) {
            $schema['image'] = $image;
        }
        
        // Ajouter des informations produit si disponibles (WooCommerce)
        if ( function_exists( 'wc_get_product' ) ) {
            $product = wc_get_product( $post->ID );
            if ( $product ) {
                $schema['offers'] = array(
                    '@type' => 'Offer',
                    'price' => $product->get_price(),
                    'priceCurrency' => get_woocommerce_currency(),
                    'availability' => $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'
                );
                
                if ( $product->get_sku() ) {
                    $schema['sku'] = $product->get_sku();
                }
            }
        }
        
        return $schema;
    }

    /**
     * Génère un schéma Organization.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à traiter.
     * @return   array              Schéma Organization.
     */
    private function generate_organization_schema( $post ) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => get_bloginfo( 'name' ),
            'description' => get_bloginfo( 'description' ),
            'url' => home_url(),
            'logo' => array(
                '@type' => 'ImageObject',
                'url' => $this->get_site_logo()
            )
        );
        
        return $schema;
    }

    /**
     * Génère un schéma Person.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à traiter.
     * @return   array              Schéma Person.
     */
    private function generate_person_schema( $post ) {
        $author = get_userdata( $post->post_author );
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Person',
            'name' => $author->display_name,
            'url' => get_author_posts_url( $author->ID )
        );
        
        // Ajouter la description si disponible
        $description = get_user_meta( $author->ID, 'description', true );
        if ( $description ) {
            $schema['description'] = $description;
        }
        
        return $schema;
    }

    /**
     * Récupère le titre SEO optimisé.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à traiter.
     * @return   string             Titre SEO.
     */
    private function get_seo_title( $post ) {
        $seo_title = get_post_meta( $post->ID, '_boss_seo_title', true );
        return ! empty( $seo_title ) ? $seo_title : get_the_title( $post->ID );
    }

    /**
     * Récupère la meta description.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à traiter.
     * @return   string             Meta description.
     */
    private function get_meta_description( $post ) {
        $meta_desc = get_post_meta( $post->ID, '_boss_seo_meta_description', true );
        if ( ! empty( $meta_desc ) ) {
            return $meta_desc;
        }
        
        // Générer automatiquement depuis le contenu
        $content = wp_strip_all_tags( $post->post_content );
        return wp_trim_words( $content, 20 );
    }

    /**
     * Récupère les mots-clés.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à traiter.
     * @return   array              Mots-clés.
     */
    private function get_keywords( $post ) {
        $keywords = array();
        
        $focus_keyword = get_post_meta( $post->ID, '_boss_seo_focus_keyword', true );
        if ( $focus_keyword ) {
            $keywords[] = $focus_keyword;
        }
        
        $secondary_keywords = get_post_meta( $post->ID, '_boss_seo_secondary_keywords', true );
        if ( $secondary_keywords ) {
            $secondary = array_map( 'trim', explode( ',', $secondary_keywords ) );
            $keywords = array_merge( $keywords, $secondary );
        }
        
        return array_filter( $keywords );
    }

    /**
     * Récupère l'image à la une.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à traiter.
     * @return   array|null         Données de l'image.
     */
    private function get_featured_image( $post ) {
        $image_id = get_post_thumbnail_id( $post->ID );
        if ( ! $image_id ) {
            return null;
        }
        
        $image_url = wp_get_attachment_image_url( $image_id, 'large' );
        $image_meta = wp_get_attachment_metadata( $image_id );
        
        return array(
            '@type' => 'ImageObject',
            'url' => $image_url,
            'width' => $image_meta['width'] ?? null,
            'height' => $image_meta['height'] ?? null
        );
    }

    /**
     * Récupère le schéma de l'auteur.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à traiter.
     * @return   array              Schéma de l'auteur.
     */
    private function get_author_schema( $post ) {
        $author = get_userdata( $post->post_author );
        
        return array(
            '@type' => 'Person',
            'name' => $author->display_name,
            'url' => get_author_posts_url( $author->ID )
        );
    }

    /**
     * Récupère le schéma de l'éditeur.
     *
     * @since    1.2.0
     * @return   array    Schéma de l'éditeur.
     */
    private function get_publisher_schema() {
        return array(
            '@type' => 'Organization',
            'name' => get_bloginfo( 'name' ),
            'url' => home_url(),
            'logo' => array(
                '@type' => 'ImageObject',
                'url' => $this->get_site_logo()
            )
        );
    }

    /**
     * Récupère le logo du site.
     *
     * @since    1.2.0
     * @return   string    URL du logo.
     */
    private function get_site_logo() {
        $custom_logo_id = get_theme_mod( 'custom_logo' );
        if ( $custom_logo_id ) {
            return wp_get_attachment_image_url( $custom_logo_id, 'full' );
        }
        
        // Logo par défaut
        return get_site_icon_url( 512 ) ?: home_url( '/wp-content/uploads/logo.png' );
    }

    /**
     * Valide un schéma généré.
     *
     * @since    1.2.0
     * @param    array    $schema    Schéma à valider.
     * @return   array              Résultat de la validation.
     */
    public function validate_schema( $schema ) {
        $errors = array();
        $warnings = array();
        
        // Vérifications de base
        if ( ! isset( $schema['@context'] ) ) {
            $errors[] = 'Contexte @context manquant';
        }
        
        if ( ! isset( $schema['@type'] ) ) {
            $errors[] = 'Type @type manquant';
        }
        
        // Vérifications spécifiques par type
        switch ( $schema['@type'] ?? '' ) {
            case 'Article':
                if ( ! isset( $schema['headline'] ) ) {
                    $errors[] = 'Propriété headline manquante pour Article';
                }
                if ( ! isset( $schema['author'] ) ) {
                    $warnings[] = 'Propriété author recommandée pour Article';
                }
                break;
                
            case 'Product':
                if ( ! isset( $schema['name'] ) ) {
                    $errors[] = 'Propriété name manquante pour Product';
                }
                if ( ! isset( $schema['offers'] ) ) {
                    $warnings[] = 'Propriété offers recommandée pour Product';
                }
                break;
        }
        
        return array(
            'valid' => empty( $errors ),
            'errors' => $errors,
            'warnings' => $warnings
        );
    }
}
